#!/usr/bin/env python3
"""
Test script for the QUBO Web Interface
Tests the Flask application endpoints and functionality
"""

import requests
import time
import json

# Web interface URL
WEB_URL = "http://localhost:5001"

def test_web_interface():
    """Test the web interface functionality"""
    
    print("🧪 Testing QUBO Web Interface")
    print("=" * 50)
    
    try:
        # Test 1: Main page
        print("\n1. Testing main page...")
        response = requests.get(WEB_URL)
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            print("   ✅ Main page loads successfully")
            if "QUBO Matrix Problem Solver" in response.text:
                print("   ✅ Page content is correct")
            else:
                print("   ❌ Page content missing")
        else:
            print("   ❌ Main page failed to load")
        
        # Test 2: API status endpoint
        print("\n2. Testing API status endpoint...")
        response = requests.get(f"{WEB_URL}/api/status")
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"   API Running: {data.get('api_running')}")
            print(f"   Sample Key Available: {data.get('sample_key') is not None}")
            print("   ✅ API status endpoint working")
        else:
            print("   ❌ API status endpoint failed")
        
        # Test 3: Sample matrices endpoint
        print("\n3. Testing sample matrices endpoint...")
        response = requests.get(f"{WEB_URL}/api/sample-matrices")
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"   Sample matrices available: {len(data)}")
            print("   ✅ Sample matrices endpoint working")
        else:
            print("   ❌ Sample matrices endpoint failed")
        
        # Test 4: Form submission (simulate)
        print("\n4. Testing form submission...")
        
        # First get a sample API key
        status_response = requests.get(f"{WEB_URL}/api/status")
        if status_response.status_code == 200:
            api_key = status_response.json().get('sample_key')
            
            if api_key:
                # Submit a simple QUBO problem
                form_data = {
                    'matrix': '[[1, -2], [-2, 1]]',
                    'api_key': api_key,
                    'solver': 'easy',
                    'time_limit': '5'
                }
                
                response = requests.post(f"{WEB_URL}/solve", data=form_data, allow_redirects=False)
                print(f"   Status: {response.status_code}")
                
                if response.status_code in [200, 302]:  # 302 is redirect to results
                    print("   ✅ Form submission successful")
                    
                    # If it's a redirect, follow it
                    if response.status_code == 302:
                        print("   ✅ Redirected to results page")
                else:
                    print("   ❌ Form submission failed")
            else:
                print("   ⚠️  No API key available for testing")
        
        # Test 5: Error handling (invalid matrix)
        print("\n5. Testing error handling...")
        form_data = {
            'matrix': '[[1, 2], [3]]',  # Invalid non-square matrix
            'api_key': 'test_key',
            'solver': 'easy',
            'time_limit': '5'
        }
        
        response = requests.post(f"{WEB_URL}/solve", data=form_data, allow_redirects=False)
        print(f"   Status: {response.status_code}")
        if response.status_code == 302:  # Should redirect back with error
            print("   ✅ Error handling working (redirected with error)")
        else:
            print("   ❌ Error handling not working as expected")
        
        print("\n" + "=" * 50)
        print("✅ Web interface testing completed!")
        print(f"🌐 Web interface is available at: {WEB_URL}")
        
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to web interface. Please ensure it's running at http://localhost:5001")
    except Exception as e:
        print(f"❌ Error during testing: {e}")

def test_browser_features():
    """Test browser-specific features"""
    print("\n🌐 Browser Feature Tests")
    print("-" * 30)
    
    # Test main page HTML structure
    try:
        response = requests.get(WEB_URL)
        html = response.text
        
        # Check for key elements
        checks = [
            ("Bootstrap CSS", "bootstrap" in html.lower()),
            ("Font Awesome", "font-awesome" in html.lower() or "fontawesome" in html.lower()),
            ("Matrix Input", 'id="matrix"' in html),
            ("API Key Input", 'id="api_key"' in html),
            ("Solver Selection", 'id="solver"' in html),
            ("Submit Button", 'type="submit"' in html),
            ("JavaScript", "<script>" in html),
        ]
        
        for check_name, result in checks:
            status = "✅" if result else "❌"
            print(f"   {status} {check_name}")
        
    except Exception as e:
        print(f"   ❌ Error checking HTML structure: {e}")

if __name__ == "__main__":
    test_web_interface()
    test_browser_features()
    
    print("\n" + "=" * 50)
    print("🎉 Testing complete!")
    print("\n📋 Manual Testing Checklist:")
    print("   1. Open http://localhost:5001 in your browser")
    print("   2. Check that the API status shows 'Online'")
    print("   3. Try loading a sample matrix")
    print("   4. Submit a QUBO problem")
    print("   5. Verify the results page displays correctly")
    print("   6. Test error handling with invalid input")
