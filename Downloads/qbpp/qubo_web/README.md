# QUBO++ Web Interface

A user-friendly Flask web application that provides a graphical interface for testing QUBO (Quadratic Unconstrained Binary Optimization) problems using the QUBO++ API.

## Features

### 🎯 **User-Friendly Interface**
- **Intuitive Matrix Input**: JSON format with real-time validation
- **Sample Matrices**: Pre-loaded examples for quick testing
- **API Key Management**: Automatic sample key retrieval
- **Responsive Design**: Works on desktop and mobile devices

### 🔧 **Solver Configuration**
- **Multiple Solvers**: Choose between EasySolver and ExhaustiveSolver
- **Time Limits**: Configurable solving time limits
- **Real-time Validation**: Matrix format and size validation
- **Smart Recommendations**: Solver suggestions based on problem size

### 📊 **Rich Results Display**
- **Variable Assignments**: Visual grid showing binary variable values
- **Performance Metrics**: Solve time, objective value, matrix size
- **Solution Analysis**: Efficiency ratings and complexity assessment
- **Export Options**: Copy results or download as JSON

### 🛡️ **Robust Error Handling**
- **Input Validation**: Comprehensive matrix and parameter validation
- **API Communication**: Graceful handling of API errors
- **User Feedback**: Clear error messages and success notifications
- **Fallback Options**: Offline mode indicators and recovery suggestions

## Installation

### Prerequisites

1. **QUBO++ API Server**: The Flask app requires the QUBO++ API to be running
   ```bash
   # Start the QUBO++ API first (from the qubo_api directory)
   cd ../qubo_api
   source venv/bin/activate
   python main.py
   ```

2. **Python 3.7+**: Required for Flask application

### Setup

1. **Create virtual environment:**
   ```bash
   cd qubo_web
   python3 -m venv venv
   source venv/bin/activate
   ```

2. **Install dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

3. **Run the web application:**
   ```bash
   python app.py
   ```

4. **Access the interface:**
   Open your browser and go to `http://localhost:5000`

## Usage Guide

### 1. **Getting Started**
- Open the web interface at `http://localhost:5000`
- The API status indicator shows if the QUBO++ API is accessible
- A sample API key is automatically loaded if available

### 2. **Entering a QUBO Matrix**
- **Manual Entry**: Type your matrix in JSON format in the text area
- **Sample Matrices**: Click any sample matrix button to load examples
- **Format**: Use 2D JSON arrays like `[[1, -2], [-2, 1]]`
- **Validation**: Real-time validation shows matrix status and size

### 3. **Configuring the Solver**
- **EasySolver**: Fast heuristic solver for larger problems (up to 100×100)
- **ExhaustiveSolver**: Exact solver for smaller problems (up to 20×20)
- **Time Limit**: Set maximum solving time (1-300 seconds, EasySolver only)

### 4. **Solving Problems**
- Click "Solve QUBO Problem" to submit your request
- The interface shows a loading state during solving
- Results appear on a new page with detailed analysis

### 5. **Interpreting Results**
- **Variable Grid**: Visual representation of binary variable assignments
- **Metrics**: Objective value, solve time, and solver information
- **Performance**: Efficiency rating and complexity assessment
- **Export**: Copy results to clipboard or download as JSON

## Example Problems

### Simple 2×2 Problem
```json
[[1, -2], [-2, 1]]
```
**Description**: Basic optimization problem
**Recommended**: Any solver

### Max-Cut Problem
```json
[[2, -1, -1], [-1, 2, -1], [-1, -1, 2]]
```
**Description**: Graph partitioning with 3 nodes
**Recommended**: ExhaustiveSolver for exact solution

### Larger Problem
```json
[[2, -1, 0, -1], [-1, 2, -1, 0], [0, -1, 2, -1], [-1, 0, -1, 2]]
```
**Description**: 4×4 optimization problem
**Recommended**: EasySolver for speed

## API Integration

The web interface communicates with the QUBO++ API through the following endpoints:

- **Health Check**: `GET /health` - Check API status
- **Sample Key**: `GET /test-key` - Get API key for testing
- **Solve QUBO**: `POST /solve-qubo` - Submit QUBO problems

### Error Handling

The interface handles various error conditions:
- **API Offline**: Shows status indicators and helpful messages
- **Invalid Matrix**: Real-time validation with specific error messages
- **Authentication**: Clear feedback for API key issues
- **Timeouts**: Graceful handling of long-running solvers

## Technical Details

### Architecture
- **Frontend**: HTML5, CSS3, Bootstrap 5, JavaScript
- **Backend**: Flask (Python)
- **API Communication**: HTTP requests to QUBO++ API
- **Validation**: Client-side and server-side input validation

### Browser Compatibility
- **Modern Browsers**: Chrome, Firefox, Safari, Edge
- **Mobile Support**: Responsive design for tablets and phones
- **JavaScript**: ES6+ features with fallbacks

### Security
- **Input Sanitization**: All user inputs are validated and sanitized
- **API Key Handling**: Secure transmission of authentication tokens
- **Error Information**: No sensitive data exposed in error messages

## Troubleshooting

### Common Issues

1. **API Not Accessible**
   - Ensure QUBO++ API is running on `http://localhost:8000`
   - Check firewall settings and port availability
   - Verify API health at `http://localhost:8000/health`

2. **Matrix Validation Errors**
   - Ensure matrix is square (same number of rows and columns)
   - Use valid JSON format with proper brackets and commas
   - All elements must be numbers (integers or floats)

3. **Solver Timeouts**
   - Reduce matrix size for ExhaustiveSolver (max 20×20)
   - Increase time limit for EasySolver
   - Try different solver types for your problem size

4. **Performance Issues**
   - Large matrices (>50×50) may be slow with EasySolver
   - ExhaustiveSolver is exponential in complexity
   - Consider problem-specific optimizations

### Getting Help

- **Check API Status**: Use the status indicator in the interface
- **Validate Input**: Use the real-time matrix validation
- **Try Samples**: Start with provided sample matrices
- **Check Logs**: Browser console and Flask logs for detailed errors

## Development

### Project Structure
```
qubo_web/
├── app.py              # Main Flask application
├── requirements.txt    # Python dependencies
├── README.md          # This file
└── templates/         # HTML templates
    ├── base.html      # Base template with common layout
    ├── index.html     # Main form page
    ├── result.html    # Results display page
    └── error.html     # Error page template
```

### Customization
- **Styling**: Modify CSS in `templates/base.html`
- **Samples**: Add new sample matrices in `app.py`
- **Validation**: Extend validation logic in `validate_matrix()`
- **API URL**: Change `QUBO_API_URL` in `app.py`

The web interface provides a complete, user-friendly solution for testing QUBO optimization problems without requiring direct API knowledge or command-line tools.
