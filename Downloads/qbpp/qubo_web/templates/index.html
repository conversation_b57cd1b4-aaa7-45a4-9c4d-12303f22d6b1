{% extends "base.html" %}

{% block title %}QUBO Matrix Solver - QUBO++ Web Interface{% endblock %}

{% block content %}
<div class="row">
    <!-- Main Form -->
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h4 class="mb-0">
                    <i class="fas fa-matrix me-2"></i>
                    QUBO Matrix Problem Solver
                </h4>
            </div>
            <div class="card-body">
                <form method="POST" action="{{ url_for('solve_qubo') }}" id="qubo-form">
                    <!-- API Key Input -->
                    <div class="mb-4">
                        <label for="api_key" class="form-label">
                            <i class="fas fa-key me-2"></i>
                            API Key
                        </label>
                        <div class="input-group">
                            <input type="text" class="form-control" id="api_key" name="api_key" 
                                   placeholder="Enter your API key" required
                                   value="{{ sample_key if sample_key else '' }}">
                            <button type="button" class="btn btn-outline-secondary" id="get-sample-key">
                                <i class="fas fa-refresh me-1"></i>
                                Get Sample Key
                            </button>
                        </div>
                        <div class="form-text">
                            {% if sample_key %}
                                <i class="fas fa-check-circle text-success me-1"></i>
                                Sample API key loaded automatically
                            {% else %}
                                <i class="fas fa-exclamation-triangle text-warning me-1"></i>
                                API key required for authentication
                            {% endif %}
                        </div>
                    </div>

                    <!-- Matrix Input -->
                    <div class="mb-4">
                        <label for="matrix" class="form-label">
                            <i class="fas fa-table me-2"></i>
                            QUBO Matrix (JSON format)
                        </label>
                        <textarea class="form-control matrix-input" id="matrix" name="matrix" rows="8" 
                                  placeholder="Enter your QUBO matrix as a 2D JSON array, e.g.:&#10;[[1, -2], [-2, 1]]" required></textarea>
                        <div class="form-text">
                            Enter a square matrix as a JSON array. Each row should have the same number of elements.
                        </div>
                    </div>

                    <!-- Sample Matrices -->
                    <div class="mb-4">
                        <label class="form-label">
                            <i class="fas fa-lightbulb me-2"></i>
                            Sample Matrices
                        </label>
                        <div class="d-flex flex-wrap">
                            {% for name, matrix in sample_matrices.items() %}
                            <button type="button" class="btn btn-outline-info sample-matrix-btn" 
                                    data-matrix="{{ matrix | tojson | e }}">
                                {{ name }}
                            </button>
                            {% endfor %}
                        </div>
                    </div>

                    <!-- Solver Configuration -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <label for="solver" class="form-label">
                                <i class="fas fa-cogs me-2"></i>
                                Solver Type
                            </label>
                            <select class="form-select" id="solver" name="solver">
                                <option value="easy" selected>EasySolver (Fast, Heuristic)</option>
                                <option value="exhaustive">ExhaustiveSolver (Exact, Slow)</option>
                            </select>
                            <div class="form-text">
                                <small>
                                    <strong>EasySolver:</strong> Fast heuristic for larger problems (up to 100x100)<br>
                                    <strong>ExhaustiveSolver:</strong> Exact solution for small problems (up to 20x20)
                                </small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <label for="time_limit" class="form-label">
                                <i class="fas fa-clock me-2"></i>
                                Time Limit (seconds)
                            </label>
                            <input type="number" class="form-control" id="time_limit" name="time_limit" 
                                   value="10" min="1" max="300" step="0.1">
                            <div class="form-text">
                                Maximum solving time (1-300 seconds, only for EasySolver)
                            </div>
                        </div>
                    </div>

                    <!-- Submit Button -->
                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary btn-lg" id="solve-btn">
                            <span class="btn-text">
                                <i class="fas fa-play me-2"></i>
                                Solve QUBO Problem
                            </span>
                            <span class="loading-spinner">
                                <i class="fas fa-spinner fa-spin me-2"></i>
                                Solving...
                            </span>
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Information Panel -->
    <div class="col-lg-4">
        <!-- API Status -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-server me-2"></i>
                    API Status
                </h5>
            </div>
            <div class="card-body">
                <div id="api-status-info">
                    <div class="d-flex align-items-center mb-2">
                        <span class="status-indicator" id="status-dot"></span>
                        <span id="status-text">Checking...</span>
                    </div>
                    <small class="text-muted" id="api-url">{{ QUBO_API_URL if QUBO_API_URL else 'http://localhost:8000' }}</small>
                </div>
            </div>
        </div>

        <!-- Help & Examples -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-question-circle me-2"></i>
                    Help & Examples
                </h5>
            </div>
            <div class="card-body">
                <h6>Matrix Format</h6>
                <p class="small">Enter matrices as JSON arrays:</p>
                <pre class="small bg-light p-2 rounded">[[1, -2], [-2, 1]]</pre>
                
                <h6 class="mt-3">Problem Types</h6>
                <ul class="small">
                    <li><strong>Optimization:</strong> Find variable assignments that minimize the objective</li>
                    <li><strong>Max-Cut:</strong> Graph partitioning problems</li>
                    <li><strong>Knapsack:</strong> Resource allocation problems</li>
                    <li><strong>Partitioning:</strong> Set division problems</li>
                </ul>
                
                <h6 class="mt-3">Solver Guidelines</h6>
                <ul class="small">
                    <li><strong>Small problems (&lt;20x20):</strong> Use ExhaustiveSolver</li>
                    <li><strong>Large problems:</strong> Use EasySolver</li>
                    <li><strong>Time-critical:</strong> Set appropriate time limits</li>
                </ul>
            </div>
        </div>

        <!-- Matrix Validation -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-check-circle me-2"></i>
                    Matrix Validation
                </h5>
            </div>
            <div class="card-body">
                <div id="matrix-validation">
                    <div class="text-muted">
                        <i class="fas fa-info-circle me-2"></i>
                        Enter a matrix to see validation results
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Check API status
    checkApiStatus();
    
    // Matrix validation
    const matrixInput = document.getElementById('matrix');
    const validationDiv = document.getElementById('matrix-validation');
    
    matrixInput.addEventListener('input', validateMatrix);
    
    // Sample matrix buttons
    document.querySelectorAll('.sample-matrix-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            const matrix = JSON.parse(this.dataset.matrix);
            matrixInput.value = JSON.stringify(matrix, null, 2);
            validateMatrix();
        });
    });
    
    // Get sample key button
    document.getElementById('get-sample-key').addEventListener('click', getSampleKey);
    
    // Solver type change
    document.getElementById('solver').addEventListener('change', function() {
        const timeLimitInput = document.getElementById('time_limit');
        if (this.value === 'exhaustive') {
            timeLimitInput.disabled = true;
            timeLimitInput.parentElement.style.opacity = '0.5';
        } else {
            timeLimitInput.disabled = false;
            timeLimitInput.parentElement.style.opacity = '1';
        }
    });
});

function checkApiStatus() {
    fetch('/api/status')
        .then(response => response.json())
        .then(data => {
            const statusDot = document.getElementById('status-dot');
            const statusText = document.getElementById('status-text');
            const apiStatusIndicator = document.getElementById('api-status-indicator');
            const apiStatusText = document.getElementById('api-status-text');
            
            if (data.api_running) {
                statusDot.className = 'status-indicator status-online';
                statusText.textContent = 'API Online';
                apiStatusIndicator.className = 'status-indicator status-online';
                apiStatusText.textContent = 'API Online';
                
                if (data.sample_key && !document.getElementById('api_key').value) {
                    document.getElementById('api_key').value = data.sample_key;
                }
            } else {
                statusDot.className = 'status-indicator status-offline';
                statusText.textContent = 'API Offline';
                apiStatusIndicator.className = 'status-indicator status-offline';
                apiStatusText.textContent = 'API Offline';
            }
        })
        .catch(error => {
            console.error('Error checking API status:', error);
        });
}

function validateMatrix() {
    const matrixInput = document.getElementById('matrix');
    const validationDiv = document.getElementById('matrix-validation');
    const value = matrixInput.value.trim();
    
    if (!value) {
        validationDiv.innerHTML = '<div class="text-muted"><i class="fas fa-info-circle me-2"></i>Enter a matrix to see validation results</div>';
        return;
    }
    
    try {
        const matrix = JSON.parse(value);
        
        if (!Array.isArray(matrix)) {
            throw new Error('Matrix must be an array');
        }
        
        if (matrix.length === 0) {
            throw new Error('Matrix cannot be empty');
        }
        
        const n = matrix.length;
        for (let i = 0; i < n; i++) {
            if (!Array.isArray(matrix[i])) {
                throw new Error(`Row ${i} must be an array`);
            }
            if (matrix[i].length !== n) {
                throw new Error(`Matrix must be square. Row ${i} has ${matrix[i].length} elements, expected ${n}`);
            }
            for (let j = 0; j < n; j++) {
                if (typeof matrix[i][j] !== 'number') {
                    throw new Error(`Element at (${i},${j}) must be a number`);
                }
            }
        }
        
        validationDiv.innerHTML = `
            <div class="text-success">
                <i class="fas fa-check-circle me-2"></i>
                Valid ${n}×${n} matrix
            </div>
            <small class="text-muted">
                ${n * n} elements, ${n} variables
            </small>
        `;
        
    } catch (error) {
        validationDiv.innerHTML = `
            <div class="text-danger">
                <i class="fas fa-exclamation-triangle me-2"></i>
                ${error.message}
            </div>
        `;
    }
}

function getSampleKey() {
    fetch('/api/status')
        .then(response => response.json())
        .then(data => {
            if (data.sample_key) {
                document.getElementById('api_key').value = data.sample_key;
            } else {
                alert('Unable to get sample key. Please ensure the API is running.');
            }
        })
        .catch(error => {
            console.error('Error getting sample key:', error);
            alert('Error getting sample key. Please check the console for details.');
        });
}
</script>
{% endblock %}
