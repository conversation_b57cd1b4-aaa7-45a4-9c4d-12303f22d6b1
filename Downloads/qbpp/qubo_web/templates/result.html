{% extends "base.html" %}

{% block title %}QUBO Solution Results - QUBO++ Web Interface{% endblock %}

{% block content %}
<div class="row">
    <!-- Results -->
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h4 class="mb-0">
                    {% if result.success %}
                        <i class="fas fa-check-circle text-success me-2"></i>
                        Solution Found
                    {% else %}
                        <i class="fas fa-exclamation-triangle text-warning me-2"></i>
                        Solution Failed
                    {% endif %}
                </h4>
            </div>
            <div class="card-body">
                {% if result.success %}
                    <!-- Variable Assignments -->
                    <div class="mb-4">
                        <h5>
                            <i class="fas fa-list-ol me-2"></i>
                            Variable Assignments
                        </h5>
                        <div class="variable-grid">
                            {% for i, value in enumerate(result.variables) %}
                            <div class="variable-item {% if value == 1 %}active{% endif %}">
                                <div class="fw-bold">x{{ i }}</div>
                                <div class="fs-4">{{ value }}</div>
                            </div>
                            {% endfor %}
                        </div>
                        <div class="text-muted small">
                            <i class="fas fa-info-circle me-1"></i>
                            Green variables are set to 1, gray variables are set to 0
                        </div>
                    </div>

                    <!-- Solution Metrics -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card bg-primary text-white">
                                <div class="card-body text-center">
                                    <h3 class="mb-1">{{ "%.6g"|format(result.energy) }}</h3>
                                    <small>Objective Value</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-success text-white">
                                <div class="card-body text-center">
                                    <h3 class="mb-1">{{ "%.3f"|format(result.solver_time) }}s</h3>
                                    <small>Solve Time</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-info text-white">
                                <div class="card-body text-center">
                                    <h3 class="mb-1">{{ result.matrix_size }}</h3>
                                    <small>Matrix Size</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-secondary text-white">
                                <div class="card-body text-center">
                                    <h3 class="mb-1">{{ result.solver_type.replace('Solver', '') }}</h3>
                                    <small>Solver Used</small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Solution Vector -->
                    <div class="mb-4">
                        <h5>
                            <i class="fas fa-vector-square me-2"></i>
                            Solution Vector
                        </h5>
                        <div class="bg-light p-3 rounded">
                            <code class="fs-6">x = [{{ result.variables | join(', ') }}]</code>
                        </div>
                    </div>

                    <!-- Objective Calculation -->
                    <div class="mb-4">
                        <h5>
                            <i class="fas fa-calculator me-2"></i>
                            Objective Function Evaluation
                        </h5>
                        <div class="bg-light p-3 rounded">
                            <div class="mb-2">
                                <strong>QUBO Formulation:</strong> minimize x<sup>T</sup>Qx
                            </div>
                            <div class="mb-2">
                                <strong>Result:</strong> {{ "%.6g"|format(result.energy) }}
                            </div>
                            {% if result.solver_type == "ExhaustiveSolver" %}
                            <div class="text-success">
                                <i class="fas fa-medal me-1"></i>
                                <strong>Guaranteed optimal solution</strong>
                            </div>
                            {% else %}
                            <div class="text-info">
                                <i class="fas fa-lightning me-1"></i>
                                <strong>Heuristic solution (may not be optimal)</strong>
                            </div>
                            {% endif %}
                        </div>
                    </div>

                {% else %}
                    <!-- Error Information -->
                    <div class="alert alert-danger">
                        <h5 class="alert-heading">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            Solving Failed
                        </h5>
                        <p class="mb-0">{{ result.error_message or "Unknown error occurred" }}</p>
                    </div>
                {% endif %}

                <!-- Action Buttons -->
                <div class="d-flex gap-2">
                    <a href="{{ url_for('index') }}" class="btn btn-primary">
                        <i class="fas fa-arrow-left me-2"></i>
                        Solve Another Problem
                    </a>
                    <button type="button" class="btn btn-outline-secondary" onclick="copyResults()">
                        <i class="fas fa-copy me-2"></i>
                        Copy Results
                    </button>
                    <button type="button" class="btn btn-outline-info" onclick="downloadResults()">
                        <i class="fas fa-download me-2"></i>
                        Download JSON
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Problem Information -->
    <div class="col-lg-4">
        <!-- Input Matrix -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-table me-2"></i>
                    Input Matrix
                </h5>
            </div>
            <div class="card-body">
                <div class="matrix-display">
                    <table class="table table-sm table-bordered text-center">
                        {% for row in matrix %}
                        <tr>
                            {% for val in row %}
                            <td class="{% if val > 0 %}table-success{% elif val < 0 %}table-danger{% else %}table-light{% endif %}">
                                {{ val }}
                            </td>
                            {% endfor %}
                        </tr>
                        {% endfor %}
                    </table>
                </div>
                <div class="text-muted small">
                    <i class="fas fa-info-circle me-1"></i>
                    {{ matrix|length }}×{{ matrix|length }} QUBO matrix
                </div>
            </div>
        </div>

        <!-- Solver Information -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-cogs me-2"></i>
                    Solver Configuration
                </h5>
            </div>
            <div class="card-body">
                <div class="mb-2">
                    <strong>Solver Type:</strong> {{ result.solver_type if result.success else solver.title() + "Solver" }}
                </div>
                {% if solver == "easy" %}
                <div class="mb-2">
                    <strong>Time Limit:</strong> {{ time_limit }} seconds
                </div>
                {% endif %}
                <div class="mb-2">
                    <strong>Problem Size:</strong> {{ matrix|length }} variables
                </div>
                {% if result.success %}
                <div class="mb-2">
                    <strong>Actual Time:</strong> {{ "%.6f"|format(result.solver_time) }} seconds
                </div>
                {% endif %}
            </div>
        </div>

        <!-- Performance Analysis -->
        {% if result.success %}
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-chart-line me-2"></i>
                    Performance Analysis
                </h5>
            </div>
            <div class="card-body">
                {% set solve_time = result.solver_time %}
                {% set matrix_size = matrix|length %}
                
                <div class="mb-2">
                    <strong>Efficiency:</strong>
                    {% if solve_time < 0.001 %}
                        <span class="badge bg-success">Excellent</span>
                    {% elif solve_time < 0.1 %}
                        <span class="badge bg-success">Very Good</span>
                    {% elif solve_time < 1.0 %}
                        <span class="badge bg-info">Good</span>
                    {% elif solve_time < 10.0 %}
                        <span class="badge bg-warning">Moderate</span>
                    {% else %}
                        <span class="badge bg-danger">Slow</span>
                    {% endif %}
                </div>
                
                <div class="mb-2">
                    <strong>Complexity:</strong>
                    {% if matrix_size <= 5 %}
                        <span class="badge bg-success">Small</span>
                    {% elif matrix_size <= 15 %}
                        <span class="badge bg-info">Medium</span>
                    {% elif matrix_size <= 50 %}
                        <span class="badge bg-warning">Large</span>
                    {% else %}
                        <span class="badge bg-danger">Very Large</span>
                    {% endif %}
                </div>
                
                <div class="mb-2">
                    <strong>Variables Set:</strong> {{ result.variables.count(1) }}/{{ result.variables|length }}
                </div>
                
                <div class="progress mb-2">
                    <div class="progress-bar" role="progressbar" 
                         style="width: {{ (result.variables.count(1) / result.variables|length * 100)|round }}%">
                        {{ (result.variables.count(1) / result.variables|length * 100)|round }}%
                    </div>
                </div>
            </div>
        </div>
        {% endif %}
    </div>
</div>

<!-- Hidden data for JavaScript -->
<script type="application/json" id="result-data">
{{ result | tojson }}
</script>

<script type="application/json" id="matrix-data">
{{ matrix | tojson }}
</script>
{% endblock %}

{% block scripts %}
<script>
function copyResults() {
    const resultData = JSON.parse(document.getElementById('result-data').textContent);
    const matrixData = JSON.parse(document.getElementById('matrix-data').textContent);
    
    const copyText = `QUBO Problem Solution
===================
Matrix: ${JSON.stringify(matrixData)}
Variables: [${resultData.variables.join(', ')}]
Objective Value: ${resultData.energy}
Solver: ${resultData.solver_type}
Time: ${resultData.solver_time} seconds
Success: ${resultData.success}`;
    
    navigator.clipboard.writeText(copyText).then(() => {
        // Show success message
        const btn = event.target.closest('button');
        const originalText = btn.innerHTML;
        btn.innerHTML = '<i class="fas fa-check me-2"></i>Copied!';
        btn.classList.add('btn-success');
        btn.classList.remove('btn-outline-secondary');
        
        setTimeout(() => {
            btn.innerHTML = originalText;
            btn.classList.remove('btn-success');
            btn.classList.add('btn-outline-secondary');
        }, 2000);
    }).catch(err => {
        console.error('Failed to copy: ', err);
        alert('Failed to copy to clipboard');
    });
}

function downloadResults() {
    const resultData = JSON.parse(document.getElementById('result-data').textContent);
    const matrixData = JSON.parse(document.getElementById('matrix-data').textContent);
    
    const downloadData = {
        timestamp: new Date().toISOString(),
        input_matrix: matrixData,
        result: resultData
    };
    
    const dataStr = JSON.stringify(downloadData, null, 2);
    const dataBlob = new Blob([dataStr], {type: 'application/json'});
    
    const link = document.createElement('a');
    link.href = URL.createObjectURL(dataBlob);
    link.download = `qubo_result_${new Date().toISOString().slice(0,19).replace(/:/g, '-')}.json`;
    link.click();
}
</script>
{% endblock %}
