{% extends "base.html" %}

{% block title %}Error {{ error_code }} - QUBO++ Web Interface{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-lg-6">
        <div class="card">
            <div class="card-body text-center py-5">
                <div class="mb-4">
                    <i class="fas fa-exclamation-triangle text-warning" style="font-size: 4rem;"></i>
                </div>
                
                <h1 class="display-4 text-muted">{{ error_code }}</h1>
                <h4 class="mb-4">{{ error_message }}</h4>
                
                {% if error_code == 404 %}
                <p class="text-muted mb-4">
                    The page you're looking for doesn't exist. It might have been moved, deleted, or you entered the wrong URL.
                </p>
                {% elif error_code == 500 %}
                <p class="text-muted mb-4">
                    Something went wrong on our end. Please try again later or contact support if the problem persists.
                </p>
                {% else %}
                <p class="text-muted mb-4">
                    An unexpected error occurred. Please try again or go back to the main page.
                </p>
                {% endif %}
                
                <div class="d-flex gap-2 justify-content-center">
                    <a href="{{ url_for('index') }}" class="btn btn-primary">
                        <i class="fas fa-home me-2"></i>
                        Go Home
                    </a>
                    <button type="button" class="btn btn-outline-secondary" onclick="history.back()">
                        <i class="fas fa-arrow-left me-2"></i>
                        Go Back
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
