#!/usr/bin/env python3
"""
Flask Web Application for QUBO++ API
Provides a user-friendly web interface for testing QUBO matrix problems
"""

from flask import Flask, render_template, request, jsonify, flash, redirect, url_for
import requests
import json
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Flask(__name__)
app.secret_key = 'qubo_web_secret_key_2024'  # Change this in production

# Configuration
QUBO_API_URL = "http://localhost:8000"

def validate_matrix(matrix_str):
    """Validate and parse the matrix input"""
    try:
        # Try to parse as JSON
        matrix = json.loads(matrix_str)
        
        # Check if it's a list of lists
        if not isinstance(matrix, list):
            return None, "Matrix must be a 2D array"
        
        if not matrix:
            return None, "Matrix cannot be empty"
        
        # Check if all rows are lists
        for i, row in enumerate(matrix):
            if not isinstance(row, list):
                return None, f"Row {i} must be a list"
        
        # Check if matrix is square
        n = len(matrix)
        for i, row in enumerate(matrix):
            if len(row) != n:
                return None, f"Matrix must be square. Row {i} has {len(row)} elements, expected {n}"
        
        # Check if all elements are numbers
        for i, row in enumerate(matrix):
            for j, val in enumerate(row):
                if not isinstance(val, (int, float)):
                    return None, f"Element at position ({i},{j}) must be a number"
        
        return matrix, None
        
    except json.JSONDecodeError as e:
        return None, f"Invalid JSON format: {str(e)}"
    except Exception as e:
        return None, f"Error parsing matrix: {str(e)}"

def get_sample_api_key():
    """Get a sample API key from the QUBO API"""
    try:
        response = requests.get(f"{QUBO_API_URL}/test-key", timeout=5)
        if response.status_code == 200:
            return response.json().get("sample_key")
        else:
            logger.error(f"Failed to get API key: {response.status_code}")
            return None
    except Exception as e:
        logger.error(f"Error getting API key: {e}")
        return None

def check_api_status():
    """Check if the QUBO API is running"""
    try:
        response = requests.get(f"{QUBO_API_URL}/health", timeout=5)
        return response.status_code == 200
    except:
        return False

@app.route('/')
def index():
    """Main page with QUBO matrix input form"""
    # Check API status
    api_running = check_api_status()
    sample_key = get_sample_api_key() if api_running else None
    
    # Sample matrices for demonstration
    sample_matrices = {
        "Simple 2x2": [[1, -2], [-2, 1]],
        "Max-Cut 3x3": [[2, -1, -1], [-1, 2, -1], [-1, -1, 2]],
        "Random 4x4": [[2, -1, 0, -1], [-1, 2, -1, 0], [0, -1, 2, -1], [-1, 0, -1, 2]]
    }
    
    return render_template('index.html', 
                         api_running=api_running,
                         sample_key=sample_key,
                         sample_matrices=sample_matrices)

@app.route('/solve', methods=['POST'])
def solve_qubo():
    """Handle QUBO problem solving"""
    try:
        # Get form data
        matrix_str = request.form.get('matrix', '').strip()
        api_key = request.form.get('api_key', '').strip()
        solver = request.form.get('solver', 'easy')
        time_limit = request.form.get('time_limit', '10')
        
        # Validate inputs
        if not matrix_str:
            flash('Please enter a QUBO matrix', 'error')
            return redirect(url_for('index'))
        
        if not api_key:
            flash('Please enter an API key', 'error')
            return redirect(url_for('index'))
        
        # Validate and parse matrix
        matrix, error = validate_matrix(matrix_str)
        if error:
            flash(f'Matrix validation error: {error}', 'error')
            return redirect(url_for('index'))
        
        # Validate time limit
        try:
            time_limit_float = float(time_limit)
            if time_limit_float <= 0 or time_limit_float > 300:
                flash('Time limit must be between 0 and 300 seconds', 'error')
                return redirect(url_for('index'))
        except ValueError:
            flash('Time limit must be a valid number', 'error')
            return redirect(url_for('index'))
        
        # Prepare API request
        headers = {'X-API-Key': api_key, 'Content-Type': 'application/json'}
        data = {
            'matrix': matrix,
            'solver': solver,
            'time_limit': time_limit_float
        }
        
        # Make API request
        logger.info(f"Solving QUBO problem: {len(matrix)}x{len(matrix)} matrix, solver={solver}")
        response = requests.post(f"{QUBO_API_URL}/solve-qubo", 
                               headers=headers, 
                               json=data, 
                               timeout=time_limit_float + 10)
        
        if response.status_code == 200:
            result = response.json()
            return render_template('result.html', 
                                 result=result, 
                                 matrix=matrix,
                                 solver=solver,
                                 time_limit=time_limit_float)
        else:
            error_msg = response.json().get('detail', 'Unknown error')
            flash(f'API Error ({response.status_code}): {error_msg}', 'error')
            return redirect(url_for('index'))
            
    except requests.exceptions.Timeout:
        flash('Request timed out. Try reducing the time limit or matrix size.', 'error')
        return redirect(url_for('index'))
    except requests.exceptions.ConnectionError:
        flash('Cannot connect to QUBO API. Please ensure the API server is running.', 'error')
        return redirect(url_for('index'))
    except Exception as e:
        logger.error(f"Error solving QUBO: {e}")
        flash(f'Unexpected error: {str(e)}', 'error')
        return redirect(url_for('index'))

@app.route('/api/sample-matrices')
def get_sample_matrices():
    """API endpoint to get sample matrices"""
    matrices = {
        "simple": {
            "name": "Simple 2x2 Problem",
            "matrix": [[1, -2], [-2, 1]],
            "description": "Basic QUBO problem: minimize x₀ - 4x₀x₁ + x₁"
        },
        "maxcut": {
            "name": "Max-Cut Problem",
            "matrix": [[2, -1, -1], [-1, 2, -1], [-1, -1, 2]],
            "description": "Graph partitioning problem with 3 nodes"
        },
        "partition": {
            "name": "Number Partitioning",
            "matrix": [[4, -6, -6], [-6, 4, -6], [-6, -6, 4]],
            "description": "Partition numbers into equal-sum subsets"
        },
        "large": {
            "name": "Larger Problem",
            "matrix": [
                [2, -1, 0, -1],
                [-1, 2, -1, 0],
                [0, -1, 2, -1],
                [-1, 0, -1, 2]
            ],
            "description": "4x4 matrix for testing larger problems"
        }
    }
    return jsonify(matrices)

@app.route('/api/status')
def api_status():
    """Check API status endpoint"""
    running = check_api_status()
    sample_key = get_sample_api_key() if running else None
    
    return jsonify({
        'api_running': running,
        'sample_key': sample_key,
        'api_url': QUBO_API_URL
    })

@app.errorhandler(404)
def not_found(error):
    """Handle 404 errors"""
    return render_template('error.html', 
                         error_code=404,
                         error_message="Page not found"), 404

@app.errorhandler(500)
def internal_error(error):
    """Handle 500 errors"""
    return render_template('error.html',
                         error_code=500,
                         error_message="Internal server error"), 500

if __name__ == '__main__':
    print("🚀 Starting QUBO Web Interface")
    print(f"📡 QUBO API URL: {QUBO_API_URL}")
    print("🌐 Web interface will be available at: http://localhost:5001")

    # Check if API is running
    if check_api_status():
        print("✅ QUBO API is running and accessible")
    else:
        print("⚠️  QUBO API is not accessible - some features may not work")

    app.run(debug=True, host='0.0.0.0', port=5001)
