# QUBO++ RESTful API

A Python RESTful API that integrates with the QUBO++ library to solve Quadratic Unconstrained Binary Optimization (QUBO) problems.

## Features

- **API Key Authentication**: 100 pre-generated API keys for secure access
- **Multiple Solvers**: Support for EasySolver and ExhaustiveSolver from QUBO++
- **Input Validation**: Comprehensive validation of QUBO matrices and parameters
- **Error Handling**: Robust error handling with detailed error messages
- **Logging**: Complete request/response logging for monitoring and debugging
- **FastAPI Framework**: Modern, fast, and well-documented API with automatic OpenAPI docs

## Installation

### Prerequisites

- macOS with Homebrew
- Python 3.7+
- Intel TBB (Threading Building Blocks)
- Boost libraries
- QUBO++ library (included in this project)

### Setup

1. **Install dependencies:**
   ```bash
   # Install Homebrew (if not already installed)
   /bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"
   
   # Install required libraries
   eval "$(/opt/homebrew/bin/brew shellenv)"
   brew install tbb boost
   ```

2. **Set up Python environment:**
   ```bash
   cd qubo_api
   python3 -m venv venv
   source venv/bin/activate
   pip install -r requirements.txt
   ```

3. **Build the C++ extension:**
   ```bash
   eval "$(/opt/homebrew/bin/brew shellenv)"
   export CPLUS_INCLUDE_PATH="/opt/homebrew/include:$CPLUS_INCLUDE_PATH"
   export LIBRARY_PATH="/opt/homebrew/lib:$LIBRARY_PATH"
   python setup.py build_ext --inplace
   ```

4. **Run the API:**
   ```bash
   eval "$(/opt/homebrew/bin/brew shellenv)"
   export CPLUS_INCLUDE_PATH="/opt/homebrew/include:$CPLUS_INCLUDE_PATH"
   export LIBRARY_PATH="/opt/homebrew/lib:$LIBRARY_PATH"
   export LD_LIBRARY_PATH="/opt/homebrew/lib:$LD_LIBRARY_PATH"
   python main.py
   ```

The API will be available at `http://localhost:8000`

## API Endpoints

### Authentication

All endpoints (except `/test-key` and `/health`) require an API key in the `X-API-Key` header.

### Available Endpoints

- **GET /** - API information and available endpoints
- **GET /health** - Health check and solver status
- **GET /test-key** - Get a sample API key for testing (no auth required)
- **GET /api-keys** - Get API key information (requires auth)
- **POST /solve-qubo** - Solve a QUBO problem (requires auth)

### Interactive Documentation

Visit `http://localhost:8000/docs` for interactive API documentation (Swagger UI)
Visit `http://localhost:8000/redoc` for alternative documentation (ReDoc)

## Usage Examples

### Get a Test API Key

```bash
curl http://localhost:8000/test-key
```

### Solve a Simple QUBO Problem

```bash
curl -X POST "http://localhost:8000/solve-qubo" \
  -H "X-API-Key: YOUR_API_KEY" \
  -H "Content-Type: application/json" \
  -d '{
    "matrix": [[1, -2], [-2, 1]],
    "solver": "easy",
    "time_limit": 10.0
  }'
```

### Python Example

```python
import requests

# Get API key
response = requests.get("http://localhost:8000/test-key")
api_key = response.json()["sample_key"]

# Solve QUBO problem
headers = {"X-API-Key": api_key}
data = {
    "matrix": [[2, -1, 0], [-1, 2, -1], [0, -1, 2]],
    "solver": "exhaustive"
}

response = requests.post("http://localhost:8000/solve-qubo", 
                        headers=headers, json=data)
result = response.json()

if result["success"]:
    print(f"Variables: {result['variables']}")
    print(f"Energy: {result['energy']}")
    print(f"Solver time: {result['solver_time']:.6f}s")
else:
    print(f"Error: {result['error_message']}")
```

## Request/Response Format

### QUBO Request

```json
{
  "matrix": [[1, -2], [-2, 1]],
  "solver": "easy",
  "time_limit": 10.0
}
```

- **matrix**: 2D array representing the QUBO matrix (must be square)
- **solver**: "easy" or "exhaustive"
- **time_limit**: Time limit in seconds (for EasySolver only, max 300s)

### QUBO Response

```json
{
  "success": true,
  "variables": [0, 1],
  "energy": -1.0,
  "solver_time": 0.001234,
  "solver_type": "EasySolver",
  "matrix_size": 2
}
```

## Solver Information

### EasySolver
- **Type**: Heuristic solver
- **Best for**: Larger problems (up to 100x100)
- **Features**: Time limit support, parallel processing
- **Speed**: Fast, but may not find optimal solution

### ExhaustiveSolver
- **Type**: Exact solver
- **Best for**: Small problems (up to 20x20)
- **Features**: Guaranteed optimal solution
- **Speed**: Slower, but finds exact optimum

## Testing

Run the test suite:

```bash
source venv/bin/activate
python test_api.py
```

## Error Handling

The API provides detailed error messages for:
- Invalid API keys
- Malformed QUBO matrices
- Solver failures
- Invalid parameters
- Server errors

## Logging

All requests and responses are logged to:
- Console output
- `qubo_api.log` file

## Security

- 100 unique API keys generated at startup
- API key validation on all protected endpoints
- Input validation and sanitization
- Rate limiting can be added if needed

## Performance

- C++ backend for optimal performance
- Parallel processing with Intel TBB
- Efficient memory management
- Fast JSON serialization/deserialization
