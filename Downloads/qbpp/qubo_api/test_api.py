#!/usr/bin/env python3
"""
Test script for the QUBO API
"""

import requests
import json
import time

# API base URL
BASE_URL = "http://localhost:8000"

def get_sample_api_key():
    """Get a sample API key from the running API server"""
    try:
        response = requests.get(f"{BASE_URL}/test-key")
        if response.status_code == 200:
            return response.json()["sample_key"]
        else:
            print(f"Failed to get API key: {response.status_code}")
            return None
    except Exception as e:
        print(f"Error getting API key: {e}")
        return None

def test_api():
    """Test the QUBO API functionality"""

    # Get a sample API key
    api_key = get_sample_api_key()
    headers = {"X-API-Key": api_key}
    
    print("🧪 Testing QUBO++ API")
    print("=" * 50)
    
    # Test 1: Health check
    print("\n1. Testing health check...")
    try:
        response = requests.get(f"{BASE_URL}/health")
        print(f"   Status: {response.status_code}")
        print(f"   Response: {response.json()}")
    except Exception as e:
        print(f"   Error: {e}")
    
    # Test 2: Root endpoint
    print("\n2. Testing root endpoint...")
    try:
        response = requests.get(f"{BASE_URL}/")
        print(f"   Status: {response.status_code}")
        print(f"   Response: {response.json()}")
    except Exception as e:
        print(f"   Error: {e}")
    
    # Test 3: API key info
    print("\n3. Testing API key info...")
    try:
        response = requests.get(f"{BASE_URL}/api-keys", headers=headers)
        print(f"   Status: {response.status_code}")
        print(f"   Response: {response.json()}")
    except Exception as e:
        print(f"   Error: {e}")
    
    # Test 4: Simple QUBO problem (2x2 matrix)
    print("\n4. Testing simple QUBO problem (2x2 matrix)...")
    try:
        qubo_data = {
            "matrix": [[1, -2], [-2, 1]],
            "solver": "easy",
            "time_limit": 5.0
        }
        response = requests.post(f"{BASE_URL}/solve-qubo", 
                               headers=headers, 
                               json=qubo_data)
        print(f"   Status: {response.status_code}")
        result = response.json()
        print(f"   Success: {result.get('success')}")
        if result.get('success'):
            print(f"   Variables: {result.get('variables')}")
            print(f"   Energy: {result.get('energy')}")
            print(f"   Solver time: {result.get('solver_time'):.6f}s")
            print(f"   Solver type: {result.get('solver_type')}")
        else:
            print(f"   Error: {result.get('error_message')}")
    except Exception as e:
        print(f"   Error: {e}")
    
    # Test 5: Larger QUBO problem (4x4 matrix)
    print("\n5. Testing larger QUBO problem (4x4 matrix)...")
    try:
        qubo_data = {
            "matrix": [
                [2, -1, 0, -1],
                [-1, 2, -1, 0],
                [0, -1, 2, -1],
                [-1, 0, -1, 2]
            ],
            "solver": "easy",
            "time_limit": 10.0
        }
        response = requests.post(f"{BASE_URL}/solve-qubo", 
                               headers=headers, 
                               json=qubo_data)
        print(f"   Status: {response.status_code}")
        result = response.json()
        print(f"   Success: {result.get('success')}")
        if result.get('success'):
            print(f"   Variables: {result.get('variables')}")
            print(f"   Energy: {result.get('energy')}")
            print(f"   Solver time: {result.get('solver_time'):.6f}s")
            print(f"   Solver type: {result.get('solver_type')}")
        else:
            print(f"   Error: {result.get('error_message')}")
    except Exception as e:
        print(f"   Error: {e}")
    
    # Test 6: Exhaustive solver (small problem)
    print("\n6. Testing exhaustive solver (3x3 matrix)...")
    try:
        qubo_data = {
            "matrix": [
                [1, -1, 0],
                [-1, 1, -1],
                [0, -1, 1]
            ],
            "solver": "exhaustive"
        }
        response = requests.post(f"{BASE_URL}/solve-qubo", 
                               headers=headers, 
                               json=qubo_data)
        print(f"   Status: {response.status_code}")
        result = response.json()
        print(f"   Success: {result.get('success')}")
        if result.get('success'):
            print(f"   Variables: {result.get('variables')}")
            print(f"   Energy: {result.get('energy')}")
            print(f"   Solver time: {result.get('solver_time'):.6f}s")
            print(f"   Solver type: {result.get('solver_type')}")
        else:
            print(f"   Error: {result.get('error_message')}")
    except Exception as e:
        print(f"   Error: {e}")
    
    # Test 7: Invalid API key
    print("\n7. Testing invalid API key...")
    try:
        invalid_headers = {"X-API-Key": "invalid_key_12345"}
        response = requests.get(f"{BASE_URL}/api-keys", headers=invalid_headers)
        print(f"   Status: {response.status_code}")
        print(f"   Response: {response.json()}")
    except Exception as e:
        print(f"   Error: {e}")
    
    # Test 8: Missing API key
    print("\n8. Testing missing API key...")
    try:
        response = requests.get(f"{BASE_URL}/api-keys")
        print(f"   Status: {response.status_code}")
        print(f"   Response: {response.json()}")
    except Exception as e:
        print(f"   Error: {e}")
    
    # Test 9: Invalid matrix (non-square)
    print("\n9. Testing invalid matrix (non-square)...")
    try:
        qubo_data = {
            "matrix": [[1, -2], [-2, 1, 0]],  # Non-square matrix
            "solver": "easy"
        }
        response = requests.post(f"{BASE_URL}/solve-qubo", 
                               headers=headers, 
                               json=qubo_data)
        print(f"   Status: {response.status_code}")
        print(f"   Response: {response.json()}")
    except Exception as e:
        print(f"   Error: {e}")
    
    print("\n" + "=" * 50)
    print("✅ API testing completed!")
    print(f"🔑 Sample API key for manual testing: {api_key}")

if __name__ == "__main__":
    test_api()
