#!/usr/bin/env python3
"""
QUBO++ API Examples
Demonstrates various QUBO problems and their solutions
"""

import requests
import json
import numpy as np

BASE_URL = "http://localhost:8000"

def get_api_key():
    """Get API key from the server"""
    response = requests.get(f"{BASE_URL}/test-key")
    return response.json()["sample_key"]

def solve_qubo(matrix, solver="easy", time_limit=10.0):
    """Solve a QUBO problem using the API"""
    api_key = get_api_key()
    headers = {"X-API-Key": api_key}
    
    data = {
        "matrix": matrix,
        "solver": solver,
        "time_limit": time_limit
    }
    
    response = requests.post(f"{BASE_URL}/solve-qubo", headers=headers, json=data)
    return response.json()

def print_result(problem_name, result):
    """Print the result of a QUBO problem"""
    print(f"\n🔍 {problem_name}")
    print("-" * 50)
    
    if result["success"]:
        print(f"✅ Solution found!")
        print(f"   Variables: {result['variables']}")
        print(f"   Energy: {result['energy']}")
        print(f"   Solver: {result['solver_type']}")
        print(f"   Time: {result['solver_time']:.6f} seconds")
        print(f"   Matrix size: {result['matrix_size']}x{result['matrix_size']}")
    else:
        print(f"❌ Failed: {result.get('error_message', 'Unknown error')}")

def example_1_simple_qubo():
    """Example 1: Simple 2x2 QUBO problem"""
    matrix = [
        [1, -2],
        [-2, 1]
    ]
    
    result = solve_qubo(matrix, solver="easy")
    print_result("Simple 2x2 QUBO Problem", result)
    
    print("\n📝 Problem Description:")
    print("   Minimize: x₀ - 4x₀x₁ + x₁")
    print("   This is equivalent to the matrix:")
    print("   [[ 1, -2],")
    print("    [-2,  1]]")

def example_2_max_cut():
    """Example 2: Max-Cut problem on a small graph"""
    # Graph with edges: (0,1), (1,2), (2,3), (3,0), (0,2)
    # QUBO formulation: maximize sum of cut edges
    # Convert to minimization by negating weights
    matrix = [
        [ 2, -1,  -1,  -1],
        [-1,  2,  -1,   0],
        [-1, -1,   2,  -1],
        [-1,  0,  -1,   2]
    ]
    
    result = solve_qubo(matrix, solver="exhaustive")
    print_result("Max-Cut Problem (4 nodes)", result)
    
    print("\n📝 Problem Description:")
    print("   Find a partition of 4 nodes to maximize cut edges")
    print("   Graph edges: (0,1), (1,2), (2,3), (3,0), (0,2)")
    print("   Variables represent which partition each node belongs to")

def example_3_number_partitioning():
    """Example 3: Number partitioning problem"""
    # Partition numbers [3, 1, 1, 2, 2, 1] into two equal-sum subsets
    numbers = [3, 1, 1, 2, 2, 1]
    target_sum = sum(numbers) // 2  # 5
    
    # QUBO formulation: minimize (sum of selected - target)²
    n = len(numbers)
    matrix = [[0] * n for _ in range(n)]
    
    # Diagonal terms: (aᵢ - target)²
    for i in range(n):
        matrix[i][i] = numbers[i] * (numbers[i] - 2 * target_sum)
    
    # Off-diagonal terms: 2aᵢaⱼ
    for i in range(n):
        for j in range(i + 1, n):
            matrix[i][j] = 2 * numbers[i] * numbers[j]
    
    result = solve_qubo(matrix, solver="exhaustive")
    print_result("Number Partitioning Problem", result)
    
    print("\n📝 Problem Description:")
    print(f"   Partition numbers {numbers} into two equal-sum subsets")
    print(f"   Target sum for each subset: {target_sum}")
    
    if result["success"]:
        subset1 = [numbers[i] for i, x in enumerate(result['variables']) if x == 1]
        subset2 = [numbers[i] for i, x in enumerate(result['variables']) if x == 0]
        print(f"   Subset 1: {subset1} (sum: {sum(subset1)})")
        print(f"   Subset 2: {subset2} (sum: {sum(subset2)})")

def example_4_knapsack():
    """Example 4: 0-1 Knapsack problem"""
    # Items: (value, weight)
    items = [(10, 5), (40, 4), (30, 6), (50, 3)]
    capacity = 10
    
    # QUBO formulation with penalty method
    n = len(items)
    penalty = 100  # Large penalty for constraint violation
    
    matrix = [[0] * n for _ in range(n)]
    
    # Objective: maximize value (minimize negative value)
    for i in range(n):
        matrix[i][i] = -items[i][0]  # Negative value for maximization
    
    # Constraint: weight <= capacity (penalty for violation)
    for i in range(n):
        matrix[i][i] += penalty * items[i][1] * (items[i][1] - 2 * capacity)
        for j in range(i + 1, n):
            matrix[i][j] = 2 * penalty * items[i][1] * items[j][1]
    
    result = solve_qubo(matrix, solver="exhaustive")
    print_result("0-1 Knapsack Problem", result)
    
    print("\n📝 Problem Description:")
    print(f"   Items (value, weight): {items}")
    print(f"   Knapsack capacity: {capacity}")
    
    if result["success"]:
        selected_items = [(i, items[i]) for i, x in enumerate(result['variables']) if x == 1]
        total_value = sum(item[1][0] for item in selected_items)
        total_weight = sum(item[1][1] for item in selected_items)
        print(f"   Selected items: {[f'Item {i}' for i, _ in selected_items]}")
        print(f"   Total value: {total_value}")
        print(f"   Total weight: {total_weight}")

def example_5_vertex_cover():
    """Example 5: Minimum Vertex Cover problem"""
    # Graph with 4 vertices and edges: (0,1), (1,2), (2,3), (3,0)
    edges = [(0, 1), (1, 2), (2, 3), (3, 0)]
    n_vertices = 4
    
    # QUBO formulation: minimize vertices + penalty for uncovered edges
    penalty = 10
    matrix = [[0] * n_vertices for _ in range(n_vertices)]
    
    # Objective: minimize number of selected vertices
    for i in range(n_vertices):
        matrix[i][i] = 1
    
    # Constraint: each edge must be covered
    for u, v in edges:
        matrix[u][u] += penalty
        matrix[v][v] += penalty
        matrix[u][v] += -2 * penalty
    
    result = solve_qubo(matrix, solver="exhaustive")
    print_result("Minimum Vertex Cover Problem", result)
    
    print("\n📝 Problem Description:")
    print(f"   Graph edges: {edges}")
    print("   Find minimum set of vertices that covers all edges")
    
    if result["success"]:
        cover = [i for i, x in enumerate(result['variables']) if x == 1]
        print(f"   Vertex cover: {cover}")
        print(f"   Cover size: {len(cover)}")

def example_6_large_random():
    """Example 6: Larger random QUBO problem"""
    np.random.seed(42)  # For reproducible results
    n = 15
    
    # Generate random symmetric matrix
    matrix = np.random.randint(-5, 6, size=(n, n)).tolist()
    
    # Make it symmetric (QUBO matrices should be symmetric)
    for i in range(n):
        for j in range(i + 1, n):
            matrix[j][i] = matrix[i][j]
    
    result = solve_qubo(matrix, solver="easy", time_limit=5.0)
    print_result(f"Random {n}x{n} QUBO Problem", result)
    
    print("\n📝 Problem Description:")
    print(f"   Random {n}x{n} symmetric matrix with values in [-5, 5]")
    print("   Using EasySolver with 5-second time limit")

def main():
    """Run all examples"""
    print("🚀 QUBO++ API Examples")
    print("=" * 60)
    
    try:
        # Check if API is running
        response = requests.get(f"{BASE_URL}/health")
        if response.status_code != 200:
            print("❌ API is not running. Please start the server first.")
            return
        
        print("✅ API is running and healthy")
        
        # Run examples
        example_1_simple_qubo()
        example_2_max_cut()
        example_3_number_partitioning()
        example_4_knapsack()
        example_5_vertex_cover()
        example_6_large_random()
        
        print("\n" + "=" * 60)
        print("🎉 All examples completed!")
        
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to API. Please ensure the server is running at http://localhost:8000")
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    main()
