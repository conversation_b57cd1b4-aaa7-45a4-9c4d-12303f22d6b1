2025-08-07 19:09:37,338 - main - INFO - QUBO solver initialized successfully
2025-08-07 19:09:37,338 - main - INFO - API startup complete - QUBO solver ready
2025-08-07 19:10:48,451 - main - INFO - Request: GET /health
2025-08-07 19:10:48,452 - main - INFO - Response: 200 - 0.000s
2025-08-07 19:10:48,454 - main - INFO - Request: GET /
2025-08-07 19:10:48,455 - main - INFO - Response: 200 - 0.000s
2025-08-07 19:10:48,456 - main - INFO - Request: GET /api-keys
2025-08-07 19:10:48,457 - main - WARNING - Invalid API key used: DyLwvEgD...
2025-08-07 19:10:48,457 - main - INFO - Response: 401 - 0.001s
2025-08-07 19:10:48,459 - main - INFO - Request: POST /solve-qubo
2025-08-07 19:10:48,459 - main - WARNING - Invalid API key used: DyLwvEgD...
2025-08-07 19:10:48,459 - main - INFO - Response: 401 - 0.001s
2025-08-07 19:10:48,461 - main - INFO - Request: POST /solve-qubo
2025-08-07 19:10:48,461 - main - WARNING - Invalid API key used: DyLwvEgD...
2025-08-07 19:10:48,461 - main - INFO - Response: 401 - 0.001s
2025-08-07 19:10:48,463 - main - INFO - Request: POST /solve-qubo
2025-08-07 19:10:48,463 - main - WARNING - Invalid API key used: DyLwvEgD...
2025-08-07 19:10:48,463 - main - INFO - Response: 401 - 0.001s
2025-08-07 19:10:48,465 - main - INFO - Request: GET /api-keys
2025-08-07 19:10:48,465 - main - WARNING - Invalid API key used: invalid_...
2025-08-07 19:10:48,465 - main - INFO - Response: 401 - 0.000s
2025-08-07 19:10:48,466 - main - INFO - Request: GET /api-keys
2025-08-07 19:10:48,467 - main - WARNING - Request made without API key
2025-08-07 19:10:48,467 - main - INFO - Response: 401 - 0.001s
2025-08-07 19:10:48,468 - main - INFO - Request: POST /solve-qubo
2025-08-07 19:10:48,469 - main - WARNING - Invalid API key used: DyLwvEgD...
2025-08-07 19:10:48,469 - main - INFO - Response: 401 - 0.000s
2025-08-07 19:11:45,290 - main - INFO - QUBO solver initialized successfully
2025-08-07 19:11:45,291 - main - INFO - API startup complete - QUBO solver ready
2025-08-07 19:12:01,781 - main - INFO - Request: GET /test-key
2025-08-07 19:12:01,782 - main - INFO - Test API key requested
2025-08-07 19:12:01,782 - main - INFO - Response: 200 - 0.000s
2025-08-07 19:12:01,783 - main - INFO - Request: GET /health
2025-08-07 19:12:01,784 - main - INFO - Response: 200 - 0.000s
2025-08-07 19:12:01,785 - main - INFO - Request: GET /
2025-08-07 19:12:01,785 - main - INFO - Response: 200 - 0.000s
2025-08-07 19:12:01,786 - main - INFO - Request: GET /api-keys
2025-08-07 19:12:01,786 - main - INFO - API key information requested
2025-08-07 19:12:01,786 - main - INFO - Response: 200 - 0.000s
2025-08-07 19:12:01,787 - main - INFO - Request: POST /solve-qubo
2025-08-07 19:12:01,789 - main - INFO - QUBO solve request: 2x2 matrix, solver=easy
2025-08-07 19:12:06,789 - main - INFO - Solve successful: energy=0.0, time=5.000s
2025-08-07 19:12:06,790 - main - INFO - Response: 200 - 5.002s
2025-08-07 19:12:06,792 - main - INFO - Request: POST /solve-qubo
2025-08-07 19:12:06,793 - main - INFO - QUBO solve request: 4x4 matrix, solver=easy
2025-08-07 19:12:16,793 - main - INFO - Solve successful: energy=0.0, time=10.000s
2025-08-07 19:12:16,793 - main - INFO - Response: 200 - 10.001s
2025-08-07 19:12:16,797 - main - INFO - Request: POST /solve-qubo
2025-08-07 19:12:16,797 - main - INFO - QUBO solve request: 3x3 matrix, solver=exhaustive
2025-08-07 19:12:16,797 - main - INFO - Solve successful: energy=0.0, time=0.000s
2025-08-07 19:12:16,797 - main - INFO - Response: 200 - 0.001s
2025-08-07 19:12:16,799 - main - INFO - Request: GET /api-keys
2025-08-07 19:12:16,799 - main - WARNING - Invalid API key used: invalid_...
2025-08-07 19:12:16,800 - main - INFO - Response: 401 - 0.001s
2025-08-07 19:12:16,801 - main - INFO - Request: GET /api-keys
2025-08-07 19:12:16,801 - main - WARNING - Request made without API key
2025-08-07 19:12:16,801 - main - INFO - Response: 401 - 0.000s
2025-08-07 19:12:16,803 - main - INFO - Request: POST /solve-qubo
2025-08-07 19:12:16,803 - main - INFO - Response: 422 - 0.001s
2025-08-07 19:13:42,787 - main - INFO - Request: GET /health
2025-08-07 19:13:42,788 - main - INFO - Response: 200 - 0.001s
2025-08-07 19:13:42,790 - main - INFO - Request: GET /test-key
2025-08-07 19:13:42,790 - main - INFO - Test API key requested
2025-08-07 19:13:42,791 - main - INFO - Response: 200 - 0.001s
2025-08-07 19:13:42,793 - main - INFO - Request: POST /solve-qubo
2025-08-07 19:13:42,795 - main - INFO - QUBO solve request: 2x2 matrix, solver=easy
2025-08-07 19:13:52,795 - main - INFO - Solve successful: energy=0.0, time=10.000s
2025-08-07 19:13:52,795 - main - INFO - Response: 200 - 10.002s
2025-08-07 19:13:52,798 - main - INFO - Request: GET /test-key
2025-08-07 19:13:52,798 - main - INFO - Test API key requested
2025-08-07 19:13:52,799 - main - INFO - Response: 200 - 0.000s
2025-08-07 19:13:52,800 - main - INFO - Request: POST /solve-qubo
2025-08-07 19:13:52,801 - main - INFO - QUBO solve request: 4x4 matrix, solver=exhaustive
2025-08-07 19:13:52,801 - main - INFO - Solve successful: energy=0.0, time=0.000s
2025-08-07 19:13:52,801 - main - INFO - Response: 200 - 0.001s
2025-08-07 19:13:52,802 - main - INFO - Request: GET /test-key
2025-08-07 19:13:52,803 - main - INFO - Test API key requested
2025-08-07 19:13:52,803 - main - INFO - Response: 200 - 0.000s
2025-08-07 19:13:52,805 - main - INFO - Request: POST /solve-qubo
2025-08-07 19:13:52,806 - main - INFO - QUBO solve request: 6x6 matrix, solver=exhaustive
2025-08-07 19:13:52,807 - main - INFO - Solve successful: energy=-25.0, time=0.001s
2025-08-07 19:13:52,808 - main - INFO - Response: 200 - 0.003s
2025-08-07 19:13:52,826 - main - INFO - Request: GET /test-key
2025-08-07 19:13:52,826 - main - INFO - Test API key requested
2025-08-07 19:13:52,827 - main - INFO - Response: 200 - 0.000s
2025-08-07 19:13:52,830 - main - INFO - Request: POST /solve-qubo
2025-08-07 19:13:52,830 - main - INFO - QUBO solve request: 4x4 matrix, solver=exhaustive
2025-08-07 19:13:52,830 - main - INFO - Solve successful: energy=-10070.0, time=0.000s
2025-08-07 19:13:52,831 - main - INFO - Response: 200 - 0.001s
2025-08-07 19:13:52,837 - main - INFO - Request: GET /test-key
2025-08-07 19:13:52,837 - main - INFO - Test API key requested
2025-08-07 19:13:52,837 - main - INFO - Response: 200 - 0.000s
2025-08-07 19:13:52,838 - main - INFO - Request: POST /solve-qubo
2025-08-07 19:13:52,839 - main - INFO - QUBO solve request: 4x4 matrix, solver=exhaustive
2025-08-07 19:13:52,839 - main - INFO - Solve successful: energy=0.0, time=0.000s
2025-08-07 19:13:52,839 - main - INFO - Response: 200 - 0.001s
2025-08-07 19:13:53,981 - main - INFO - Request: GET /test-key
2025-08-07 19:13:53,982 - main - INFO - Test API key requested
2025-08-07 19:13:53,982 - main - INFO - Response: 200 - 0.001s
2025-08-07 19:13:53,985 - main - INFO - Request: POST /solve-qubo
2025-08-07 19:13:53,986 - main - INFO - QUBO solve request: 15x15 matrix, solver=easy
2025-08-07 19:13:58,988 - main - INFO - Solve successful: energy=-49.0, time=5.002s
2025-08-07 19:13:58,988 - main - INFO - Response: 200 - 5.004s
2025-08-07 19:25:59,871 - main - INFO - Request: GET /health
2025-08-07 19:25:59,873 - main - INFO - Response: 200 - 0.002s
2025-08-07 19:26:28,465 - main - INFO - Request: GET /health
2025-08-07 19:26:28,466 - main - INFO - Response: 200 - 0.000s
2025-08-07 19:26:32,135 - main - INFO - Request: GET /health
2025-08-07 19:26:32,135 - main - INFO - Response: 200 - 0.001s
2025-08-07 19:26:43,630 - main - INFO - Request: GET /health
2025-08-07 19:26:43,633 - main - INFO - Response: 200 - 0.004s
2025-08-07 19:26:43,639 - main - INFO - Request: GET /test-key
2025-08-07 19:26:43,639 - main - INFO - Test API key requested
2025-08-07 19:26:43,639 - main - INFO - Response: 200 - 0.001s
2025-08-07 19:26:43,864 - main - INFO - Request: GET /health
2025-08-07 19:26:43,865 - main - INFO - Response: 200 - 0.002s
2025-08-07 19:26:43,871 - main - INFO - Request: GET /test-key
2025-08-07 19:26:43,872 - main - INFO - Test API key requested
2025-08-07 19:26:43,872 - main - INFO - Response: 200 - 0.001s
2025-08-07 19:27:15,189 - main - INFO - Request: POST /solve-qubo
2025-08-07 19:27:15,191 - main - INFO - QUBO solve request: 3x3 matrix, solver=easy
2025-08-07 19:27:25,192 - main - INFO - Solve successful: energy=0.0, time=10.000s
2025-08-07 19:27:25,192 - main - INFO - Response: 200 - 10.003s
2025-08-07 19:27:25,193 - main - INFO - Request: GET /health
2025-08-07 19:27:25,193 - main - INFO - Request: GET /health
2025-08-07 19:27:25,193 - main - INFO - Response: 200 - 0.000s
2025-08-07 19:27:25,193 - main - INFO - Response: 200 - 0.000s
2025-08-07 19:27:25,202 - main - INFO - Request: GET /test-key
2025-08-07 19:27:25,202 - main - INFO - Test API key requested
2025-08-07 19:27:25,203 - main - INFO - Response: 200 - 0.000s
2025-08-07 19:27:25,208 - main - INFO - Request: GET /health
2025-08-07 19:27:25,208 - main - INFO - Response: 200 - 0.001s
2025-08-07 19:27:25,210 - main - INFO - Request: GET /health
2025-08-07 19:27:25,210 - main - INFO - Request: GET /test-key
2025-08-07 19:27:25,210 - main - INFO - Response: 200 - 0.000s
2025-08-07 19:27:25,210 - main - INFO - Test API key requested
2025-08-07 19:27:25,210 - main - INFO - Response: 200 - 0.000s
2025-08-07 19:27:25,211 - main - INFO - Request: GET /test-key
2025-08-07 19:27:25,211 - main - INFO - Test API key requested
2025-08-07 19:27:25,211 - main - INFO - Response: 200 - 0.000s
2025-08-07 19:27:25,214 - main - INFO - Request: POST /solve-qubo
2025-08-07 19:27:25,215 - main - INFO - QUBO solve request: 2x2 matrix, solver=easy
2025-08-07 19:27:30,215 - main - INFO - Solve successful: energy=0.0, time=5.000s
2025-08-07 19:27:30,216 - main - INFO - Response: 200 - 5.001s
2025-08-07 19:27:30,216 - main - INFO - Request: GET /health
2025-08-07 19:27:30,216 - main - INFO - Response: 200 - 0.000s
2025-08-07 19:27:30,218 - main - INFO - Request: GET /test-key
2025-08-07 19:27:30,219 - main - INFO - Test API key requested
2025-08-07 19:27:30,219 - main - INFO - Response: 200 - 0.000s
2025-08-07 19:27:30,223 - main - INFO - Request: GET /health
2025-08-07 19:27:30,223 - main - INFO - Response: 200 - 0.000s
2025-08-07 19:27:30,224 - main - INFO - Request: GET /test-key
2025-08-07 19:27:30,224 - main - INFO - Test API key requested
2025-08-07 19:27:30,224 - main - INFO - Response: 200 - 0.000s
2025-08-07 19:27:39,228 - main - INFO - Request: POST /solve-qubo
2025-08-07 19:27:39,229 - main - INFO - QUBO solve request: 4x4 matrix, solver=easy
2025-08-07 19:27:49,229 - main - INFO - Solve successful: energy=0.0, time=10.000s
2025-08-07 19:27:49,229 - main - INFO - Response: 200 - 10.002s
2025-08-07 19:27:49,237 - main - INFO - Request: GET /health
2025-08-07 19:27:49,238 - main - INFO - Response: 200 - 0.001s
2025-08-07 19:27:49,241 - main - INFO - Request: GET /test-key
2025-08-07 19:27:49,241 - main - INFO - Test API key requested
2025-08-07 19:27:49,241 - main - INFO - Response: 200 - 0.000s
2025-08-07 19:27:49,290 - main - INFO - Request: GET /health
2025-08-07 19:27:49,291 - main - INFO - Response: 200 - 0.001s
2025-08-07 19:27:49,299 - main - INFO - Request: GET /test-key
2025-08-07 19:27:49,300 - main - INFO - Test API key requested
2025-08-07 19:27:49,301 - main - INFO - Response: 200 - 0.002s
2025-08-07 19:31:05,498 - main - INFO - Request: GET /health
2025-08-07 19:31:05,500 - main - INFO - Response: 200 - 0.003s
2025-08-07 19:31:05,506 - main - INFO - Request: GET /test-key
2025-08-07 19:31:05,507 - main - INFO - Test API key requested
2025-08-07 19:31:05,508 - main - INFO - Response: 200 - 0.001s
2025-08-07 19:32:59,006 - main - INFO - Request: GET /health
2025-08-07 19:32:59,008 - main - INFO - Response: 200 - 0.001s
2025-08-07 19:32:59,011 - main - INFO - Request: GET /test-key
2025-08-07 19:32:59,011 - main - INFO - Test API key requested
2025-08-07 19:32:59,011 - main - INFO - Response: 200 - 0.001s
2025-08-07 19:32:59,120 - main - INFO - Request: GET /health
2025-08-07 19:32:59,120 - main - INFO - Response: 200 - 0.001s
2025-08-07 19:32:59,128 - main - INFO - Request: GET /test-key
2025-08-07 19:32:59,128 - main - INFO - Test API key requested
2025-08-07 19:32:59,129 - main - INFO - Response: 200 - 0.001s
2025-08-07 19:33:02,512 - main - INFO - Request: POST /solve-qubo
2025-08-07 19:33:02,514 - main - INFO - QUBO solve request: 4x4 matrix, solver=easy
2025-08-07 19:33:12,516 - main - INFO - Solve successful: energy=0.0, time=10.001s
2025-08-07 19:33:12,516 - main - INFO - Response: 200 - 10.004s
2025-08-07 19:33:12,524 - main - INFO - Request: GET /health
2025-08-07 19:33:12,524 - main - INFO - Response: 200 - 0.000s
2025-08-07 19:33:12,525 - main - INFO - Request: GET /test-key
2025-08-07 19:33:12,525 - main - INFO - Test API key requested
2025-08-07 19:33:12,525 - main - INFO - Response: 200 - 0.000s
2025-08-07 19:33:12,560 - main - INFO - Request: GET /health
2025-08-07 19:33:12,560 - main - INFO - Response: 200 - 0.001s
2025-08-07 19:33:12,564 - main - INFO - Request: GET /test-key
2025-08-07 19:33:12,566 - main - INFO - Test API key requested
2025-08-07 19:33:12,569 - main - INFO - Response: 200 - 0.005s
2025-08-07 19:33:37,200 - main - INFO - Request: GET /health
2025-08-07 19:33:37,201 - main - INFO - Response: 200 - 0.001s
2025-08-07 19:34:00,082 - main - INFO - Request: GET /health
2025-08-07 19:34:00,083 - main - INFO - Response: 200 - 0.000s
2025-08-07 19:34:00,084 - main - INFO - Request: GET /test-key
2025-08-07 19:34:00,084 - main - INFO - Test API key requested
2025-08-07 19:34:00,084 - main - INFO - Response: 200 - 0.000s
2025-08-07 19:34:07,162 - main - INFO - Request: POST /solve-qubo
2025-08-07 19:34:07,167 - main - INFO - QUBO solve request: 2x2 matrix, solver=easy
2025-08-07 19:34:12,167 - main - INFO - Solve successful: energy=0.0, time=5.000s
2025-08-07 19:34:12,168 - main - INFO - Response: 200 - 5.006s
2025-08-07 19:34:44,164 - main - INFO - Request: GET /health
2025-08-07 19:34:44,165 - main - INFO - Response: 200 - 0.001s
2025-08-07 19:34:44,166 - main - INFO - Request: GET /test-key
2025-08-07 19:34:44,166 - main - INFO - Test API key requested
2025-08-07 19:34:44,166 - main - INFO - Response: 200 - 0.000s
2025-08-07 19:34:44,175 - main - INFO - Request: GET /health
2025-08-07 19:34:44,175 - main - INFO - Response: 200 - 0.000s
2025-08-07 19:34:44,176 - main - INFO - Request: GET /test-key
2025-08-07 19:34:44,177 - main - INFO - Test API key requested
2025-08-07 19:34:44,177 - main - INFO - Response: 200 - 0.000s
2025-08-07 19:34:44,182 - main - INFO - Request: GET /health
2025-08-07 19:34:44,182 - main - INFO - Response: 200 - 0.000s
2025-08-07 19:34:44,183 - main - INFO - Request: GET /test-key
2025-08-07 19:34:44,184 - main - INFO - Test API key requested
2025-08-07 19:34:44,184 - main - INFO - Response: 200 - 0.000s
2025-08-07 19:34:44,189 - main - INFO - Request: POST /solve-qubo
2025-08-07 19:34:44,189 - main - INFO - QUBO solve request: 2x2 matrix, solver=easy
2025-08-07 19:34:49,191 - main - INFO - Solve successful: energy=0.0, time=5.002s
2025-08-07 19:34:49,192 - main - INFO - Response: 200 - 5.003s
2025-08-07 19:34:49,200 - main - INFO - Request: GET /health
2025-08-07 19:34:49,200 - main - INFO - Response: 200 - 0.000s
2025-08-07 19:34:49,202 - main - INFO - Request: GET /test-key
2025-08-07 19:34:49,202 - main - INFO - Test API key requested
2025-08-07 19:34:49,202 - main - INFO - Response: 200 - 0.000s
2025-08-07 19:35:05,252 - main - INFO - Request: POST /solve-qubo
2025-08-07 19:35:05,254 - main - INFO - QUBO solve request: 4x4 matrix, solver=easy
2025-08-07 19:35:15,255 - main - INFO - Solve successful: energy=0.0, time=10.000s
2025-08-07 19:35:15,255 - main - INFO - Response: 200 - 10.003s
2025-08-07 19:35:46,607 - main - INFO - Request: POST /solve-qubo
2025-08-07 19:35:46,609 - main - INFO - QUBO solve request: 4x4 matrix, solver=exhaustive
2025-08-07 19:35:46,609 - main - INFO - Solve successful: energy=0.0, time=0.000s
2025-08-07 19:35:46,610 - main - INFO - Response: 200 - 0.003s
2025-08-07 19:35:59,551 - main - INFO - Request: GET /health
2025-08-07 19:35:59,552 - main - INFO - Response: 200 - 0.001s
2025-08-07 19:35:59,555 - main - INFO - Request: GET /test-key
2025-08-07 19:35:59,555 - main - INFO - Test API key requested
2025-08-07 19:35:59,556 - main - INFO - Response: 200 - 0.001s
2025-08-07 19:35:59,586 - main - INFO - Request: GET /health
2025-08-07 19:35:59,587 - main - INFO - Response: 200 - 0.001s
2025-08-07 19:35:59,589 - main - INFO - Request: GET /test-key
2025-08-07 19:35:59,589 - main - INFO - Test API key requested
2025-08-07 19:35:59,590 - main - INFO - Response: 200 - 0.000s
2025-08-07 19:36:06,190 - main - INFO - Request: POST /solve-qubo
2025-08-07 19:36:06,192 - main - INFO - QUBO solve request: 4x4 matrix, solver=easy
2025-08-07 19:36:16,192 - main - INFO - Solve successful: energy=0.0, time=10.000s
2025-08-07 19:36:16,194 - main - INFO - Response: 200 - 10.005s
2025-08-07 19:36:24,640 - main - INFO - Request: GET /health
2025-08-07 19:36:24,641 - main - INFO - Response: 200 - 0.001s
2025-08-07 19:36:24,644 - main - INFO - Request: GET /test-key
2025-08-07 19:36:24,644 - main - INFO - Test API key requested
2025-08-07 19:36:24,645 - main - INFO - Response: 200 - 0.001s
2025-08-07 19:36:24,673 - main - INFO - Request: GET /health
2025-08-07 19:36:24,674 - main - INFO - Response: 200 - 0.001s
2025-08-07 19:36:24,677 - main - INFO - Request: GET /test-key
2025-08-07 19:36:24,677 - main - INFO - Test API key requested
2025-08-07 19:36:24,677 - main - INFO - Response: 200 - 0.001s
2025-08-07 19:36:33,021 - main - INFO - Request: POST /solve-qubo
2025-08-07 19:36:33,025 - main - WARNING - Invalid API key used: Rc7AtU3X...
2025-08-07 19:36:33,025 - main - INFO - Response: 401 - 0.005s
2025-08-07 19:36:33,033 - main - INFO - Request: GET /health
2025-08-07 19:36:33,034 - main - INFO - Response: 200 - 0.001s
2025-08-07 19:36:33,037 - main - INFO - Request: GET /test-key
2025-08-07 19:36:33,037 - main - INFO - Test API key requested
2025-08-07 19:36:33,037 - main - INFO - Response: 200 - 0.001s
2025-08-07 19:36:33,067 - main - INFO - Request: GET /health
2025-08-07 19:36:33,067 - main - INFO - Response: 200 - 0.001s
2025-08-07 19:36:33,071 - main - INFO - Request: GET /test-key
2025-08-07 19:36:33,071 - main - INFO - Test API key requested
2025-08-07 19:36:33,071 - main - INFO - Response: 200 - 0.000s
2025-08-07 19:36:36,358 - main - INFO - Request: GET /health
2025-08-07 19:36:36,359 - main - INFO - Response: 200 - 0.001s
2025-08-07 19:36:36,363 - main - INFO - Request: GET /test-key
2025-08-07 19:36:36,363 - main - INFO - Test API key requested
2025-08-07 19:36:36,363 - main - INFO - Response: 200 - 0.001s
