#include <pybind11/pybind11.h>
#include <pybind11/stl.h>
#include <pybind11/numpy.h>
#include <vector>
#include <chrono>
#include <stdexcept>
#include <iostream>

// Include QUBO++ headers
#include "qbpp.hpp"
#include "qbpp_easy_solver.hpp"
#include "qbpp_exhaustive_solver.hpp"

struct SolverResult {
    std::vector<int> variables;
    double energy;
    double solve_time;
    std::string solver_type;
    bool success;
    std::string error_message;
};

class QUBOSolver {
public:
    SolverResult solve_with_easy_solver(const std::vector<std::vector<double>>& matrix, double time_limit = 10.0) {
        auto start_time = std::chrono::high_resolution_clock::now();
        SolverResult result;
        result.solver_type = "EasySolver";
        result.success = false;
        
        try {
            int n = matrix.size();
            if (n == 0) {
                result.error_message = "Empty matrix provided";
                return result;
            }
            
            // Validate matrix is square
            for (const auto& row : matrix) {
                if (row.size() != n) {
                    result.error_message = "Matrix must be square";
                    return result;
                }
            }
            
            // Create QUBO variables
            std::vector<qbpp::Var> vars;
            for (int i = 0; i < n; i++) {
                vars.push_back(qbpp::var("x" + std::to_string(i)));
            }

            // Build QUBO expression from matrix
            qbpp::Expr objective = qbpp::Expr(0);
            
            for (int i = 0; i < n; i++) {
                for (int j = 0; j < n; j++) {
                    if (matrix[i][j] != 0.0) {
                        if (i == j) {
                            // Linear term
                            objective = objective + matrix[i][j] * vars[i];
                        } else {
                            // Quadratic term (only add upper triangle to avoid double counting)
                            if (i < j) {
                                objective = objective + matrix[i][j] * vars[i] * vars[j];
                            }
                        }
                    }
                }
            }
            
            // Create QUBO model
            auto quad_model = qbpp::QuadModel(simplify_as_binary(reduce(objective)));
            
            // Create and configure solver
            auto solver = qbpp::easy_solver::EasySolver(quad_model);
            solver.set_time_limit(time_limit);
            solver.enable_default_callback();
            
            // Solve
            auto solution = solver.search();
            
            // Extract results
            result.variables.resize(n);
            for (int i = 0; i < n; i++) {
                result.variables[i] = solution.get(vars[i]);
            }
            
            result.energy = solution.energy();
            result.success = true;
            
        } catch (const std::exception& e) {
            result.error_message = std::string("Solver error: ") + e.what();
        } catch (...) {
            result.error_message = "Unknown solver error occurred";
        }
        
        auto end_time = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end_time - start_time);
        result.solve_time = duration.count() / 1000000.0; // Convert to seconds
        
        return result;
    }
    
    SolverResult solve_with_exhaustive_solver(const std::vector<std::vector<double>>& matrix) {
        auto start_time = std::chrono::high_resolution_clock::now();
        SolverResult result;
        result.solver_type = "ExhaustiveSolver";
        result.success = false;
        
        try {
            int n = matrix.size();
            if (n == 0) {
                result.error_message = "Empty matrix provided";
                return result;
            }
            
            // Validate matrix is square
            for (const auto& row : matrix) {
                if (row.size() != n) {
                    result.error_message = "Matrix must be square";
                    return result;
                }
            }
            
            // Limit size for exhaustive solver
            if (n > 20) {
                result.error_message = "Matrix too large for exhaustive solver (max 20x20)";
                return result;
            }
            
            // Create QUBO variables
            std::vector<qbpp::Var> vars;
            for (int i = 0; i < n; i++) {
                vars.push_back(qbpp::var("x" + std::to_string(i)));
            }

            // Build QUBO expression from matrix
            qbpp::Expr objective = qbpp::Expr(0);
            
            for (int i = 0; i < n; i++) {
                for (int j = 0; j < n; j++) {
                    if (matrix[i][j] != 0.0) {
                        if (i == j) {
                            // Linear term
                            objective = objective + matrix[i][j] * vars[i];
                        } else {
                            // Quadratic term (only add upper triangle to avoid double counting)
                            if (i < j) {
                                objective = objective + matrix[i][j] * vars[i] * vars[j];
                            }
                        }
                    }
                }
            }
            
            // Create QUBO model
            auto quad_model = qbpp::QuadModel(simplify_as_binary(reduce(objective)));
            
            // Create and configure solver
            auto solver = qbpp::exhaustive_solver::ExhaustiveSolver(quad_model);
            
            // Solve
            auto solution = solver.search();
            
            // Extract results
            result.variables.resize(n);
            for (int i = 0; i < n; i++) {
                result.variables[i] = solution.get(vars[i]);
            }
            
            result.energy = solution.energy();
            result.success = true;
            
        } catch (const std::exception& e) {
            result.error_message = std::string("Solver error: ") + e.what();
        } catch (...) {
            result.error_message = "Unknown solver error occurred";
        }
        
        auto end_time = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end_time - start_time);
        result.solve_time = duration.count() / 1000000.0; // Convert to seconds
        
        return result;
    }
};

PYBIND11_MODULE(qubo_solver, m) {
    m.doc() = "QUBO++ Python wrapper";
    
    pybind11::class_<SolverResult>(m, "SolverResult")
        .def_readwrite("variables", &SolverResult::variables)
        .def_readwrite("energy", &SolverResult::energy)
        .def_readwrite("solve_time", &SolverResult::solve_time)
        .def_readwrite("solver_type", &SolverResult::solver_type)
        .def_readwrite("success", &SolverResult::success)
        .def_readwrite("error_message", &SolverResult::error_message);
    
    pybind11::class_<QUBOSolver>(m, "QUBOSolver")
        .def(pybind11::init<>())
        .def("solve_with_easy_solver", &QUBOSolver::solve_with_easy_solver,
             "Solve QUBO problem using EasySolver",
             pybind11::arg("matrix"), pybind11::arg("time_limit") = 10.0)
        .def("solve_with_exhaustive_solver", &QUBOSolver::solve_with_exhaustive_solver,
             "Solve QUBO problem using ExhaustiveSolver",
             pybind11::arg("matrix"));
}
