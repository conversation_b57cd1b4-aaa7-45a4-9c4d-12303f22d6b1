"""
QUBO API - RESTful API for solving QUBO problems using QUBO++ library
"""

import logging
import time
from typing import Optional
from fastapi import FastAPI, HTTPException, Depends, Header, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import uvicorn

from models import QUBORequest, QUBOResponse, APIKeyResponse, ErrorResponse
from auth import api_key_manager

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('qubo_api.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Create FastAPI app
app = FastAPI(
    title="QUBO++ API",
    description="RESTful API for solving QUBO problems using the QUBO++ library",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Global solver instance (will be initialized after building the extension)
qubo_solver = None

def get_api_key(x_api_key: Optional[str] = Header(None)) -> str:
    """Dependency to validate API key"""
    if not x_api_key:
        logger.warning("Request made without API key")
        raise HTTPException(
            status_code=401,
            detail="API key required. Include X-API-Key header."
        )
    
    if not api_key_manager.is_valid_key(x_api_key):
        logger.warning(f"Invalid API key used: {x_api_key[:8]}...")
        raise HTTPException(
            status_code=401,
            detail="Invalid API key"
        )
    
    return x_api_key

@app.middleware("http")
async def log_requests(request: Request, call_next):
    """Middleware to log all requests"""
    start_time = time.time()
    
    # Log request
    logger.info(f"Request: {request.method} {request.url.path}")
    
    response = await call_next(request)
    
    # Log response
    process_time = time.time() - start_time
    logger.info(f"Response: {response.status_code} - {process_time:.3f}s")
    
    return response

@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    """Global exception handler"""
    logger.error(f"Unhandled exception: {str(exc)}", exc_info=True)
    return JSONResponse(
        status_code=500,
        content={"error": "Internal server error", "detail": str(exc)}
    )

@app.get("/", response_model=dict)
async def root():
    """Root endpoint with API information"""
    return {
        "message": "QUBO++ API",
        "version": "1.0.0",
        "description": "RESTful API for solving QUBO problems",
        "endpoints": {
            "POST /solve-qubo": "Solve a QUBO problem",
            "GET /api-keys": "Get API key information",
            "GET /health": "Health check"
        }
    }

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    global qubo_solver
    return {
        "status": "healthy",
        "solver_available": qubo_solver is not None,
        "timestamp": time.time()
    }

@app.get("/api-keys", response_model=APIKeyResponse)
async def get_api_key_info(api_key: str = Depends(get_api_key)):
    """Get information about API keys (for testing)"""
    logger.info("API key information requested")
    return APIKeyResponse(
        sample_key=api_key_manager.get_sample_key(),
        total_keys=len(api_key_manager.get_all_keys())
    )

@app.get("/test-key", response_model=APIKeyResponse)
async def get_test_key():
    """Get a sample API key for testing (no authentication required)"""
    logger.info("Test API key requested")
    return APIKeyResponse(
        sample_key=api_key_manager.get_sample_key(),
        total_keys=len(api_key_manager.get_all_keys())
    )

@app.post("/solve-qubo", response_model=QUBOResponse)
async def solve_qubo(
    request: QUBORequest,
    api_key: str = Depends(get_api_key)
):
    """
    Solve a QUBO problem using the specified solver
    
    - **matrix**: 2D array representing the QUBO matrix
    - **solver**: Solver type ('easy' or 'exhaustive')
    - **time_limit**: Time limit in seconds (for EasySolver only)
    """
    global qubo_solver
    
    if qubo_solver is None:
        logger.error("QUBO solver not available")
        raise HTTPException(
            status_code=503,
            detail="QUBO solver not available. Please ensure the C++ extension is built."
        )
    
    logger.info(f"QUBO solve request: {len(request.matrix)}x{len(request.matrix)} matrix, solver={request.solver}")
    
    try:
        # Solve the QUBO problem
        if request.solver == "easy":
            result = qubo_solver.solve_with_easy_solver(request.matrix, request.time_limit)
        else:  # exhaustive
            if len(request.matrix) > 20:
                raise HTTPException(
                    status_code=400,
                    detail="Matrix too large for exhaustive solver (max 20x20)"
                )
            result = qubo_solver.solve_with_exhaustive_solver(request.matrix)
        
        if not result.success:
            logger.error(f"Solver failed: {result.error_message}")
            return QUBOResponse(
                success=False,
                error_message=result.error_message,
                matrix_size=len(request.matrix)
            )
        
        logger.info(f"Solve successful: energy={result.energy}, time={result.solve_time:.3f}s")
        
        return QUBOResponse(
            success=True,
            variables=result.variables,
            energy=result.energy,
            solver_time=result.solve_time,
            solver_type=result.solver_type,
            matrix_size=len(request.matrix)
        )
        
    except Exception as e:
        logger.error(f"Error solving QUBO: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"Error solving QUBO problem: {str(e)}"
        )

def initialize_solver():
    """Initialize the QUBO solver"""
    global qubo_solver
    try:
        import qubo_solver as qs
        qubo_solver = qs.QUBOSolver()
        logger.info("QUBO solver initialized successfully")
        return True
    except ImportError as e:
        logger.error(f"Failed to import QUBO solver: {e}")
        logger.error("Please build the C++ extension first: python setup.py build_ext --inplace")
        return False
    except Exception as e:
        logger.error(f"Failed to initialize QUBO solver: {e}")
        return False

# Initialize solver on startup
@app.on_event("startup")
async def startup_event():
    """Initialize solver when the app starts"""
    success = initialize_solver()
    if success:
        logger.info("API startup complete - QUBO solver ready")
    else:
        logger.warning("API startup complete - QUBO solver not available")

if __name__ == "__main__":
    # Run the API
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=False,  # Disable reload to prevent issues with C++ extension
        log_level="info"
    )
