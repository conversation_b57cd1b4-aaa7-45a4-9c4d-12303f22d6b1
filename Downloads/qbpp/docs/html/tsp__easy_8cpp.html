<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.17"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>QUBO++ Library with QUBO Solver APIs: sample/tsp_easy.cpp File Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/x-mathjax-config">
  MathJax.Hub.Config({
    extensions: ["tex2jax.js", "TeX/AMSmath.js", "TeX/AMSsymbols.js"],
    jax: ["input/TeX","output/HTML-CSS"],
});
</script>
<script type="text/javascript" async="async" src="https://cdn.jsdelivr.net/npm/mathjax@2/MathJax.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">QUBO++ Library with QUBO Solver APIs
   </div>
   <div id="projectbrief">Author: Koji Nakano, License: Non-commercial research and evaluation purposes without any guarantees.</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.17 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_c29eeb5af533606caea8a09e66794740.html">sample</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#nested-classes">Classes</a> &#124;
<a href="#func-members">Functions</a>  </div>
  <div class="headertitle">
<div class="title">tsp_easy.cpp File Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p>Solves randomly generated Traveling Salesman Problem (TSP) using the QUBO++ Easy solver.  
<a href="#details">More...</a></p>
<div class="textblock"><code>#include &lt;boost/program_options.hpp&gt;</code><br />
<code>#include &quot;<a class="el" href="qbpp_8hpp_source.html">qbpp.hpp</a>&quot;</code><br />
<code>#include &quot;<a class="el" href="qbpp__easy__solver_8hpp_source.html">qbpp_easy_solver.hpp</a>&quot;</code><br />
<code>#include &quot;<a class="el" href="qbpp__misc_8hpp_source.html">qbpp_misc.hpp</a>&quot;</code><br />
<code>#include &quot;<a class="el" href="qbpp__tsp_8hpp_source.html">qbpp_tsp.hpp</a>&quot;</code><br />
</div><div class="textblock"><div class="dynheader">
Include dependency graph for tsp_easy.cpp:</div>
<div class="dyncontent">
<div class="center"><img src="tsp__easy_8cpp__incl.png" border="0" usemap="#sample_2tsp__easy_8cpp" alt=""/></div>
<map name="sample_2tsp__easy_8cpp" id="sample_2tsp__easy_8cpp">
<area shape="rect" title="Solves randomly generated Traveling Salesman Problem (TSP) using the QUBO++ Easy solver." alt="" coords="972,5,1116,32"/>
<area shape="rect" title=" " alt="" coords="459,80,637,107"/>
<area shape="rect" href="qbpp_8hpp.html" title="QUBO++, a C++ library for generating expressions for binary and spin variables." alt="" coords="1516,237,1591,263"/>
<area shape="rect" href="qbpp__easy__solver_8hpp.html" title="Easy QUBO Solver for solving QUBO problems." alt="" coords="1870,80,2021,107"/>
<area shape="rect" href="qbpp__misc_8hpp.html" title="A miscellaneous library used for sample programs of the QUBO++ library." alt="" coords="989,155,1099,181"/>
<area shape="rect" href="qbpp__tsp_8hpp.html" title="Generates a QUBO Expression for the Traveling Salesman Problem (TSP) using QUBO++ library." alt="" coords="662,80,762,107"/>
<area shape="rect" title=" " alt="" coords="2983,326,3121,353"/>
<area shape="rect" title=" " alt="" coords="3145,326,3263,353"/>
<area shape="rect" title=" " alt="" coords="1166,326,1309,353"/>
<area shape="rect" title=" " alt="" coords="1333,326,1459,353"/>
<area shape="rect" title=" " alt="" coords="528,326,603,353"/>
<area shape="rect" title=" " alt="" coords="1483,319,1629,360"/>
<area shape="rect" title=" " alt="" coords="1653,319,1800,360"/>
<area shape="rect" title=" " alt="" coords="1825,326,2013,353"/>
<area shape="rect" title=" " alt="" coords="2037,326,2182,353"/>
<area shape="rect" title=" " alt="" coords="2207,319,2345,360"/>
<area shape="rect" title=" " alt="" coords="2369,326,2431,353"/>
<area shape="rect" title=" " alt="" coords="627,326,685,353"/>
<area shape="rect" title=" " alt="" coords="2455,326,2553,353"/>
<area shape="rect" title=" " alt="" coords="2577,326,2644,353"/>
<area shape="rect" title=" " alt="" coords="192,326,264,353"/>
<area shape="rect" title=" " alt="" coords="2905,326,2959,353"/>
<area shape="rect" title=" " alt="" coords="2669,326,2707,353"/>
<area shape="rect" title=" " alt="" coords="5,326,75,353"/>
<area shape="rect" title=" " alt="" coords="2732,326,2791,353"/>
<area shape="rect" title=" " alt="" coords="2815,326,2881,353"/>
<area shape="rect" title=" " alt="" coords="99,326,168,353"/>
<area shape="rect" title=" " alt="" coords="289,326,343,353"/>
<area shape="rect" title=" " alt="" coords="3287,326,3345,353"/>
<area shape="rect" title=" " alt="" coords="709,326,792,353"/>
<area shape="rect" title=" " alt="" coords="817,326,927,353"/>
<area shape="rect" title=" " alt="" coords="952,326,1056,353"/>
<area shape="rect" title=" " alt="" coords="368,326,421,353"/>
<area shape="rect" title=" " alt="" coords="1080,326,1141,353"/>
<area shape="rect" title=" " alt="" coords="445,326,504,353"/>
<area shape="rect" title=" " alt="" coords="1615,237,1779,263"/>
<area shape="rect" title=" " alt="" coords="1426,237,1491,263"/>
<area shape="rect" title=" " alt="" coords="1939,237,1979,263"/>
<area shape="rect" title=" " alt="" coords="1797,155,1872,181"/>
<area shape="rect" title=" " alt="" coords="1947,155,2016,181"/>
<area shape="rect" title=" " alt="" coords="2091,155,2155,181"/>
<area shape="rect" title=" " alt="" coords="700,237,761,263"/>
<area shape="rect" title=" " alt="" coords="786,237,955,263"/>
<area shape="rect" title=" " alt="" coords="1082,229,1230,271"/>
<area shape="rect" title=" " alt="" coords="1254,229,1402,271"/>
<area shape="rect" title=" " alt="" coords="607,237,675,263"/>
</map>
</div>
</div>
<p><a href="tsp__easy_8cpp_source.html">Go to the source code of this file.</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="nested-classes"></a>
Classes</h2></td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">class &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classMyEasySolver.html">MyEasySolver</a></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:a3c04138a5bfe5d72780bb7e82a18e627"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="tsp__easy_8cpp.html#a3c04138a5bfe5d72780bb7e82a18e627">main</a> (int argc, char **argv)</td></tr>
<tr class="memdesc:a3c04138a5bfe5d72780bb7e82a18e627"><td class="mdescLeft">&#160;</td><td class="mdescRight">Main function to generate a random map and solve the Traveling Salesman Problem (TSP) using the Easy Solver.  <a href="tsp__easy_8cpp.html#a3c04138a5bfe5d72780bb7e82a18e627">More...</a><br /></td></tr>
<tr class="separator:a3c04138a5bfe5d72780bb7e82a18e627"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p>Solves randomly generated Traveling Salesman Problem (TSP) using the QUBO++ Easy solver. </p>
<dl class="section author"><dt>Author</dt><dd>Koji Nakano </dd></dl>
<dl class="section version"><dt>Version</dt><dd>2025-01-04 </dd></dl>

<p class="definition">Definition in file <a class="el" href="tsp__easy_8cpp_source.html">tsp_easy.cpp</a>.</p>
</div><h2 class="groupheader">Function Documentation</h2>
<a id="a3c04138a5bfe5d72780bb7e82a18e627"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a3c04138a5bfe5d72780bb7e82a18e627">&#9670;&nbsp;</a></span>main()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int main </td>
          <td>(</td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>argc</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">char **&#160;</td>
          <td class="paramname"><em>argv</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Main function to generate a random map and solve the Traveling Salesman Problem (TSP) using the Easy Solver. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">argc</td><td>Number of command-line arguments. </td></tr>
    <tr><td class="paramname">argv</td><td>List of command-line arguments.</td></tr>
  </table>
  </dd>
</dl>
<p>Generates a random map with the specified number of nodes and random seed, and solves it using the Easy Solver. </p>
<p>The size of the input set</p>
<p>The time limit for the solver</p>
<p>True if node 0 is fixed as the starting node</p>

<p class="definition">Definition at line <a class="el" href="tsp__easy_8cpp_source.html#l00043">43</a> of file <a class="el" href="tsp__easy_8cpp_source.html">tsp_easy.cpp</a>.</p>
<div class="dynheader">
Here is the call graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="tsp__easy_8cpp_a3c04138a5bfe5d72780bb7e82a18e627_cgraph.png" border="0" usemap="#tsp__easy_8cpp_a3c04138a5bfe5d72780bb7e82a18e627_cgraph" alt=""/></div>
<map name="tsp__easy_8cpp_a3c04138a5bfe5d72780bb7e82a18e627_cgraph" id="tsp__easy_8cpp_a3c04138a5bfe5d72780bb7e82a18e627_cgraph">
<area shape="rect" title="Main function to generate a random map and solve the Traveling Salesman Problem (TSP) using the Easy ..." alt="" coords="5,327,56,354"/>
<area shape="rect" href="classqbpp_1_1tsp_1_1DrawSimpleGraph.html#a99955897a283a208c4093d4f747491d5" title="Add an edge to the graph." alt="" coords="104,5,295,47"/>
<area shape="rect" href="classqbpp_1_1tsp_1_1DrawSimpleGraph.html#ae58cb44d06b764de7fae386abf86c0de" title="Add a node to the graph." alt="" coords="104,71,295,112"/>
<area shape="rect" href="classqbpp_1_1tsp_1_1DrawSimpleGraph.html#ae4a6e9be4fa3e068d2dd4a4ace7548b5" title="Draw the graph in a file." alt="" coords="104,136,295,177"/>
<area shape="rect" href="classqbpp_1_1tsp_1_1TSPMap.html#ace1b4039b9447fae50b62c325acefc6e" title="Generate a random map with n nodes." alt="" coords="128,201,271,243"/>
<area shape="rect" href="classqbpp_1_1tsp_1_1TSPSol.html#ab2abe5283ba0f9fefad71b1beee340e3" title="Print the tour." alt="" coords="131,1236,268,1277"/>
<area shape="rect" href="classqbpp_1_1misc_1_1RandomGenerator.html#a513a93d293d9fa08a7fee2bba9cb73ff" title=" " alt="" coords="343,261,543,303"/>
<area shape="rect" href="classqbpp_1_1easy__solver_1_1EasySolver.html#a9a61ebfb6366c734e7af2b08890753f0" title=" " alt="" coords="131,1119,267,1160"/>
<area shape="rect" href="classqbpp_1_1Model.html#a5cf2bb955c3aa25951ca499d7d8635d2" title=" " alt="" coords="1812,647,1971,674"/>
<area shape="rect" href="classqbpp_1_1QuadModel.html#a16af851530b1f63aca9baa8ec67ed01f" title=" " alt="" coords="363,1236,522,1277"/>
<area shape="rect" href="classqbpp_1_1misc_1_1RandomGenerator.html#a1f4f3d675aee33dce174c8222c3bc22a" title=" " alt="" coords="591,312,791,353"/>
<area shape="rect" href="classqbpp_1_1easy__solver_1_1EasySolver.html#ae8fcef1929f94e0a05dd1b039a151d57" title=" " alt="" coords="110,1301,289,1343"/>
<area shape="rect" href="classqbpp_1_1tsp_1_1TSPMap.html#abb5ed5d5beb5b8c639384b42adae35d5" title="Add a node to the map." alt="" coords="371,131,514,172"/>
<area shape="rect" href="classqbpp_1_1tsp_1_1TSPMap.html#a6ca32411c34b040e6f09790f8376b0d7" title="Compute the Euclidean distance between two nodes." alt="" coords="609,142,773,169"/>
<area shape="rect" href="classqbpp_1_1misc_1_1RandomGenerator.html#a95308c2300aeef9a881ba97786367977" title=" " alt="" coords="839,210,1068,237"/>
<area shape="rect" href="classqbpp_1_1tsp_1_1TSPMap.html#a611e61c620a84590c1ff675cc3a6e332" title="Compute the minimum distance between a new node and all other nodes." alt="" coords="371,196,514,237"/>
<area shape="rect" href="classqbpp_1_1misc_1_1RandomGenerator.html#a6b19f6bde3a3b6ff087a908d11ece3c1" title=" " alt="" coords="1122,253,1322,295"/>
<area shape="rect" href="classqbpp_1_1Sol.html#a3ad455870df155ffed4701ce070cf335" title=" " alt="" coords="381,1349,505,1375"/>
<area shape="rect" href="classqbpp_1_1Sol.html#a90c464b3df40d670646cb6f940462275" title=" " alt="" coords="609,1374,772,1401"/>
<area shape="rect" href="namespaceqbpp.html#a4184afa358539dbcf5f35e63b49ce05b" title=" " alt="" coords="637,1323,745,1350"/>
<area shape="rect" href="classqbpp_1_1easy__solver_1_1EasySolver.html#a329a03e4e5c401ec471e0f7f4c42cb51" title=" " alt="" coords="354,1119,531,1160"/>
<area shape="rect" href="classqbpp_1_1easy__solver_1_1SolDelta.html#a4288d75e854df11c4023461d03c319e3" title=" " alt="" coords="610,1152,771,1193"/>
<area shape="rect" href="classqbpp_1_1easy__solver_1_1SolDelta.html#ae5e7dbc16325f4f3b26895fe8686afc5" title=" " alt="" coords="885,1211,1021,1252"/>
<area shape="rect" href="classqbpp_1_1easy__solver_1_1EasySolver.html#a5aca8737256d008df76e3f53cce1f53a" title=" " alt="" coords="1609,925,1764,967"/>
<area shape="rect" href="classqbpp_1_1easy__solver_1_1SolDelta.html#ad0505230c61ae317b5414f06327daeb4" title=" " alt="" coords="880,1093,1027,1135"/>
<area shape="rect" href="classqbpp_1_1easy__solver_1_1PosMinSolDelta.html#aff102bc17a0d06a78a285f1ab2c9632e" title=" " alt="" coords="609,735,772,776"/>
<area shape="rect" href="classqbpp_1_1QuadModel.html#ada71ec32906c9d8c73bd25c4267bafc5" title=" " alt="" coords="1383,1037,1554,1063"/>
<area shape="rect" href="classqbpp_1_1misc_1_1MinSet.html#abfb846a3ff4277f39e5cc4798933ca80" title=" " alt="" coords="1155,1145,1289,1187"/>
<area shape="rect" href="classqbpp_1_1easy__solver_1_1SolDelta.html#a1a107d8974386a07d201e88fb2c9aed9" title=" " alt="" coords="1154,860,1290,901"/>
<area shape="rect" href="classqbpp_1_1Sol.html#abdb4f6b779678720715f7845f264440f" title=" " alt="" coords="1417,1178,1520,1205"/>
<area shape="rect" href="classqbpp_1_1QuadModel.html#a6624038d4ff117176c37de2d1260f471" title=" " alt="" coords="1376,1411,1561,1438"/>
<area shape="rect" href="classqbpp_1_1easy__solver_1_1SolDelta.html#a98373d99b30ddd261919266c2ac59f57" title=" " alt="" coords="1400,925,1537,967"/>
<area shape="rect" href="classqbpp_1_1misc_1_1MinSet.html#aa534cb8cd4b9d3646c243ab8c8d448e1" title=" " alt="" coords="1155,1211,1289,1252"/>
<area shape="rect" href="classqbpp_1_1misc_1_1MinSet.html#a63576a251e5c467e6f4b1eae8a1b1f03" title=" " alt="" coords="1155,1276,1289,1317"/>
<area shape="rect" href="classqbpp_1_1easy__solver_1_1SolDelta.html#ac0f902682f6255272a79333a947e1a24" title=" " alt="" coords="1146,977,1298,1019"/>
<area shape="rect" href="classqbpp_1_1misc_1_1MinSet.html#ad9f980fba77f1f8d017354953a5cb434" title=" " alt="" coords="1155,1341,1289,1383"/>
<area shape="rect" href="classqbpp_1_1easy__solver_1_1SolDelta.html#ac82df38362383a0c302a979c0b394844" title=" " alt="" coords="1398,699,1539,755"/>
<area shape="rect" href="classqbpp_1_1easy__solver_1_1SolDelta.html#a948cb17c3b50104d7dbd333a88909757" title=" " alt="" coords="1393,779,1544,835"/>
<area shape="rect" href="classqbpp_1_1misc_1_1MinHeap.html#aa209280cf2cfd0dd5ee04131bc96a582" title=" " alt="" coords="1395,860,1542,901"/>
<area shape="rect" href="classqbpp_1_1misc_1_1RandomEngine.html#ad836550b67c1c3784cf95a23d43bcd96" title=" " alt="" coords="1116,534,1328,561"/>
<area shape="rect" href="classqbpp_1_1easy__solver_1_1TabuSolDelta.html#abddd3367c047935e82e517c421cb5a9a" title=" " alt="" coords="885,647,1021,688"/>
<area shape="rect" href="classqbpp_1_1misc_1_1Tabu.html#a549866fee09f51bb17a90e3a1831bf77" title=" " alt="" coords="1141,427,1303,454"/>
<area shape="rect" href="classqbpp_1_1misc_1_1Tabu.html#a44a9ecb55678821cc1021e33b4faba72" title=" " alt="" coords="1393,478,1544,505"/>
<area shape="rect" href="classqbpp_1_1easy__solver_1_1EasySolver.html#a011d395b4907d86b49efdcd43fbf6d3b" title=" " alt="" coords="876,947,1031,1003"/>
<area shape="rect" href="classqbpp_1_1easy__solver_1_1SolDelta.html#acf11d3f5b86a20077d4c20dc9c18e044" title=" " alt="" coords="885,881,1021,923"/>
<area shape="rect" href="classqbpp_1_1easy__solver_1_1SolDelta.html#a9b594e0f5962872772bad177ec3a41be" title=" " alt="" coords="1152,795,1292,836"/>
<area shape="rect" href="classqbpp_1_1easy__solver_1_1SolDelta.html#a03de2250fb7d272df91c43dcf9a937c2" title=" " alt="" coords="878,1028,1029,1069"/>
<area shape="rect" href="classqbpp_1_1misc_1_1Tabu.html#ac79f02e24591065f8224dc1aa5355e20" title=" " alt="" coords="878,464,1029,505"/>
<area shape="rect" href="classqbpp_1_1easy__solver_1_1PosMinSolDelta.html#ad7fb2fffb0e77d69983865304fa11008" title=" " alt="" coords="861,529,1045,571"/>
<area shape="rect" href="classqbpp_1_1easy__solver_1_1PosMinSolDelta.html#a66bae180e06d9fb7ae8461ba81111223" title=" " alt="" coords="867,764,1040,805"/>
</map>
</div>

</div>
</div>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.17
</small></address>
</body>
</html>
