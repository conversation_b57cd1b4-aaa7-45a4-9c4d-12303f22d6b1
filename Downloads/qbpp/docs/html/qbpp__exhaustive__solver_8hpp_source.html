<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.17"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>QUBO++ Library with QUBO Solver APIs: include/qbpp_exhaustive_solver.hpp Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/x-mathjax-config">
  MathJax.Hub.Config({
    extensions: ["tex2jax.js", "TeX/AMSmath.js", "TeX/AMSsymbols.js"],
    jax: ["input/TeX","output/HTML-CSS"],
});
</script>
<script type="text/javascript" async="async" src="https://cdn.jsdelivr.net/npm/mathjax@2/MathJax.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">QUBO++ Library with QUBO Solver APIs
   </div>
   <div id="projectbrief">Author: Koji Nakano, License: Non-commercial research and evaluation purposes without any guarantees.</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.17 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_d44c64559bbebec7f509842c48db8b23.html">include</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">qbpp_exhaustive_solver.hpp</div>  </div>
</div><!--header-->
<div class="contents">
<a href="qbpp__exhaustive__solver_8hpp.html">Go to the documentation of this file.</a><div class="fragment"><div class="line"><a name="l00001"></a><span class="lineno">    1</span>&#160; </div>
<div class="line"><a name="l00013"></a><span class="lineno">   13</span>&#160;<span class="preprocessor">#ifndef QBPP_EXHAUSTIVE_SOLVER_HPP</span></div>
<div class="line"><a name="l00014"></a><span class="lineno">   14</span>&#160;<span class="preprocessor">#define QBPP_EXHAUSTIVE_SOLVER_HPP</span></div>
<div class="line"><a name="l00015"></a><span class="lineno">   15</span>&#160; </div>
<div class="line"><a name="l00016"></a><span class="lineno">   16</span>&#160;<span class="preprocessor">#include &lt;tbb/blocked_range.h&gt;</span></div>
<div class="line"><a name="l00017"></a><span class="lineno">   17</span>&#160;<span class="preprocessor">#include &lt;tbb/parallel_for.h&gt;</span></div>
<div class="line"><a name="l00018"></a><span class="lineno">   18</span>&#160; </div>
<div class="line"><a name="l00019"></a><span class="lineno">   19</span>&#160;<span class="preprocessor">#include &lt;boost/circular_buffer.hpp&gt;</span></div>
<div class="line"><a name="l00020"></a><span class="lineno">   20</span>&#160;<span class="preprocessor">#include &lt;random&gt;</span></div>
<div class="line"><a name="l00021"></a><span class="lineno">   21</span>&#160;<span class="preprocessor">#include &lt;set&gt;</span></div>
<div class="line"><a name="l00022"></a><span class="lineno">   22</span>&#160; </div>
<div class="line"><a name="l00023"></a><span class="lineno">   23</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="qbpp_8hpp.html">qbpp.hpp</a>&quot;</span></div>
<div class="line"><a name="l00024"></a><span class="lineno">   24</span>&#160; </div>
<div class="line"><a name="l00025"></a><span class="lineno">   25</span>&#160;<span class="keyword">namespace </span><a class="code" href="namespaceqbpp.html">qbpp</a> {</div>
<div class="line"><a name="l00026"></a><span class="lineno">   26</span>&#160; </div>
<div class="line"><a name="l00027"></a><span class="lineno"><a class="line" href="namespaceqbpp_1_1exhaustive__solver.html">   27</a></span>&#160;<span class="keyword">namespace </span>exhaustive_solver {</div>
<div class="line"><a name="l00028"></a><span class="lineno">   28</span>&#160; </div>
<div class="line"><a name="l00029"></a><span class="lineno">   29</span>&#160; </div>
<div class="line"><a name="l00030"></a><span class="lineno">   30</span>&#160;<span class="keyword">class </span><a class="code" href="classqbpp_1_1exhaustive__solver_1_1SolDelta.html">SolDelta</a>;</div>
<div class="line"><a name="l00031"></a><span class="lineno">   31</span>&#160;<span class="keyword">class </span><a class="code" href="classqbpp_1_1exhaustive__solver_1_1Sol.html">Sol</a>;</div>
<div class="line"><a name="l00032"></a><span class="lineno">   32</span>&#160;<span class="keyword">class </span><a class="code" href="classqbpp_1_1exhaustive__solver_1_1SearchAlgorithm.html">SearchAlgorithm</a>;</div>
<div class="line"><a name="l00033"></a><span class="lineno">   33</span>&#160;<span class="keyword">class </span><a class="code" href="classqbpp_1_1exhaustive__solver_1_1ExhaustiveSolver.html">ExhaustiveSolver</a>;</div>
<div class="line"><a name="l00034"></a><span class="lineno">   34</span>&#160; </div>
<div class="line"><a name="l00035"></a><span class="lineno"><a class="line" href="classqbpp_1_1exhaustive__solver_1_1Sol.html">   35</a></span>&#160;<span class="keyword">class </span><a class="code" href="classqbpp_1_1exhaustive__solver_1_1Sol.html">Sol</a> : <span class="keyword">public</span> <a class="code" href="classqbpp_1_1Sol.html">qbpp::Sol</a> {</div>
<div class="line"><a name="l00036"></a><span class="lineno"><a class="line" href="classqbpp_1_1exhaustive__solver_1_1Sol.html#af2d151df730232c24068ca0e1b227e3c">   36</a></span>&#160;  <span class="keywordtype">bool</span> <a class="code" href="classqbpp_1_1exhaustive__solver_1_1Sol.html#af2d151df730232c24068ca0e1b227e3c">is_all_solutions_</a> = <span class="keyword">false</span>;</div>
<div class="line"><a name="l00037"></a><span class="lineno">   37</span>&#160; </div>
<div class="line"><a name="l00038"></a><span class="lineno"><a class="line" href="classqbpp_1_1exhaustive__solver_1_1Sol.html#a94c59e8bc788aa2916e971e9c8fa1c76">   38</a></span>&#160;  <span class="keywordtype">bool</span> <a class="code" href="classqbpp_1_1exhaustive__solver_1_1Sol.html#a94c59e8bc788aa2916e971e9c8fa1c76">is_optimal_solutions_</a> = <span class="keyword">false</span>;</div>
<div class="line"><a name="l00039"></a><span class="lineno">   39</span>&#160; </div>
<div class="line"><a name="l00040"></a><span class="lineno"><a class="line" href="classqbpp_1_1exhaustive__solver_1_1Sol.html#a960c32dbfa04542000cbdb7d1ad4df8c">   40</a></span>&#160;  std::vector&lt;qbpp::Sol&gt; <a class="code" href="classqbpp_1_1exhaustive__solver_1_1Sol.html#a960c32dbfa04542000cbdb7d1ad4df8c">all_solutions_</a>;</div>
<div class="line"><a name="l00041"></a><span class="lineno">   41</span>&#160; </div>
<div class="line"><a name="l00042"></a><span class="lineno">   42</span>&#160;<span class="keyword">public</span>:</div>
<div class="line"><a name="l00043"></a><span class="lineno"><a class="line" href="classqbpp_1_1exhaustive__solver_1_1Sol.html#ab0722463fa799d5892594aada8e2ec4a">   43</a></span>&#160;  <span class="keyword">explicit</span> <a class="code" href="classqbpp_1_1exhaustive__solver_1_1Sol.html#ab0722463fa799d5892594aada8e2ec4a">Sol</a>(<span class="keyword">const</span> <a class="code" href="classqbpp_1_1QuadModel.html">QuadModel</a> &amp;quad_model) : <a class="code" href="namespaceqbpp.html">qbpp</a>::<a class="code" href="classqbpp_1_1exhaustive__solver_1_1Sol.html">Sol</a>(quad_model) {};</div>
<div class="line"><a name="l00044"></a><span class="lineno">   44</span>&#160; </div>
<div class="line"><a name="l00045"></a><span class="lineno">   45</span>&#160;  <a class="code" href="classqbpp_1_1Sol.html#afb26ebcc595aa1391f18c2968713fdba">Sol</a>(<span class="keyword">const</span> <a class="code" href="classqbpp_1_1exhaustive__solver_1_1Sol.html">Sol</a> &amp;) = <span class="keywordflow">default</span>;</div>
<div class="line"><a name="l00046"></a><span class="lineno">   46</span>&#160; </div>
<div class="line"><a name="l00047"></a><span class="lineno">   47</span>&#160;  <a class="code" href="classqbpp_1_1Sol.html#afb26ebcc595aa1391f18c2968713fdba">Sol</a>(<a class="code" href="classqbpp_1_1exhaustive__solver_1_1Sol.html">Sol</a> &amp;&amp;) = <span class="keywordflow">default</span>;</div>
<div class="line"><a name="l00048"></a><span class="lineno">   48</span>&#160; </div>
<div class="line"><a name="l00049"></a><span class="lineno">   49</span>&#160;  <a class="code" href="classqbpp_1_1exhaustive__solver_1_1Sol.html">Sol</a> &amp;<a class="code" href="classqbpp_1_1exhaustive__solver_1_1Sol.html#aa4bb85bf194586a7cbef21510d796a2f">operator=</a>(<span class="keyword">const</span> <a class="code" href="classqbpp_1_1exhaustive__solver_1_1Sol.html">Sol</a> &amp;) = <span class="keywordflow">default</span>;</div>
<div class="line"><a name="l00050"></a><span class="lineno">   50</span>&#160; </div>
<div class="line"><a name="l00051"></a><span class="lineno">   51</span>&#160;  <a class="code" href="classqbpp_1_1exhaustive__solver_1_1Sol.html">Sol</a> &amp;<a class="code" href="classqbpp_1_1exhaustive__solver_1_1Sol.html#aa4bb85bf194586a7cbef21510d796a2f">operator=</a>(<a class="code" href="classqbpp_1_1exhaustive__solver_1_1Sol.html">Sol</a> &amp;&amp;) = <span class="keywordflow">default</span>;</div>
<div class="line"><a name="l00052"></a><span class="lineno">   52</span>&#160; </div>
<div class="line"><a name="l00053"></a><span class="lineno"><a class="line" href="classqbpp_1_1exhaustive__solver_1_1Sol.html#aeadf2109631b665db6dfed78bc99cf97">   53</a></span>&#160;  std::vector&lt;qbpp::Sol&gt; &amp;<a class="code" href="classqbpp_1_1exhaustive__solver_1_1Sol.html#aeadf2109631b665db6dfed78bc99cf97">get_all_solutions</a>() { <span class="keywordflow">return</span> <a class="code" href="classqbpp_1_1exhaustive__solver_1_1Sol.html#a960c32dbfa04542000cbdb7d1ad4df8c">all_solutions_</a>; }</div>
<div class="line"><a name="l00054"></a><span class="lineno">   54</span>&#160; </div>
<div class="line"><a name="l00055"></a><span class="lineno"><a class="line" href="classqbpp_1_1exhaustive__solver_1_1Sol.html#aaef7f68441965cb36da531de6e08e648">   55</a></span>&#160;  <a class="code" href="classqbpp_1_1Sol.html">qbpp::Sol</a> <a class="code" href="classqbpp_1_1exhaustive__solver_1_1Sol.html#aaef7f68441965cb36da531de6e08e648">get_sol</a>()<span class="keyword"> const </span>{ <span class="keywordflow">return</span> <a class="code" href="classqbpp_1_1exhaustive__solver_1_1Sol.html#a960c32dbfa04542000cbdb7d1ad4df8c">all_solutions_</a>.front(); }</div>
<div class="line"><a name="l00056"></a><span class="lineno">   56</span>&#160; </div>
<div class="line"><a name="l00057"></a><span class="lineno"><a class="line" href="classqbpp_1_1exhaustive__solver_1_1Sol.html#a27015c742388744bf513617e78e129f2">   57</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="classqbpp_1_1exhaustive__solver_1_1Sol.html#a27015c742388744bf513617e78e129f2">all_solution_mode</a>() {</div>
<div class="line"><a name="l00058"></a><span class="lineno">   58</span>&#160;    <a class="code" href="classqbpp_1_1exhaustive__solver_1_1Sol.html#a94c59e8bc788aa2916e971e9c8fa1c76">is_optimal_solutions_</a> = <span class="keyword">false</span>;</div>
<div class="line"><a name="l00059"></a><span class="lineno">   59</span>&#160;    <a class="code" href="classqbpp_1_1exhaustive__solver_1_1Sol.html#af2d151df730232c24068ca0e1b227e3c">is_all_solutions_</a> = <span class="keyword">true</span>;</div>
<div class="line"><a name="l00060"></a><span class="lineno">   60</span>&#160;  }</div>
<div class="line"><a name="l00061"></a><span class="lineno">   61</span>&#160; </div>
<div class="line"><a name="l00062"></a><span class="lineno"><a class="line" href="classqbpp_1_1exhaustive__solver_1_1Sol.html#aff5d82a2fb9dae3014ada25479452053">   62</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="classqbpp_1_1exhaustive__solver_1_1Sol.html#aff5d82a2fb9dae3014ada25479452053">optimal_solution_mode</a>() {</div>
<div class="line"><a name="l00063"></a><span class="lineno">   63</span>&#160;    <a class="code" href="classqbpp_1_1exhaustive__solver_1_1Sol.html#a94c59e8bc788aa2916e971e9c8fa1c76">is_optimal_solutions_</a> = <span class="keyword">true</span>;</div>
<div class="line"><a name="l00064"></a><span class="lineno">   64</span>&#160;    <a class="code" href="classqbpp_1_1exhaustive__solver_1_1Sol.html#af2d151df730232c24068ca0e1b227e3c">is_all_solutions_</a> = <span class="keyword">false</span>;</div>
<div class="line"><a name="l00065"></a><span class="lineno">   65</span>&#160;  }</div>
<div class="line"><a name="l00066"></a><span class="lineno">   66</span>&#160; </div>
<div class="line"><a name="l00067"></a><span class="lineno"><a class="line" href="classqbpp_1_1exhaustive__solver_1_1Sol.html#afba551e637e1ea61ec0c674e6379f436">   67</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="classqbpp_1_1exhaustive__solver_1_1Sol.html#afba551e637e1ea61ec0c674e6379f436">set_all_solutions</a>(std::vector&lt;qbpp::Sol&gt; &amp;&amp;all_solutions) {</div>
<div class="line"><a name="l00068"></a><span class="lineno">   68</span>&#160;    <a class="code" href="classqbpp_1_1exhaustive__solver_1_1Sol.html#a960c32dbfa04542000cbdb7d1ad4df8c">all_solutions_</a> = all_solutions;</div>
<div class="line"><a name="l00069"></a><span class="lineno">   69</span>&#160;    <a class="code" href="classqbpp_1_1Sol.html#a55ecdaaa939441fb5dbf2f00b4f3d9e0">qbpp::Sol::operator=</a>(<a class="code" href="classqbpp_1_1exhaustive__solver_1_1Sol.html#a960c32dbfa04542000cbdb7d1ad4df8c">all_solutions_</a>.front());</div>
<div class="line"><a name="l00070"></a><span class="lineno">   70</span>&#160;  }</div>
<div class="line"><a name="l00071"></a><span class="lineno">   71</span>&#160; </div>
<div class="line"><a name="l00072"></a><span class="lineno"><a class="line" href="classqbpp_1_1exhaustive__solver_1_1Sol.html#a86c91d760b8ae016acad899e82e96241">   72</a></span>&#160;  std::string <a class="code" href="classqbpp_1_1exhaustive__solver_1_1Sol.html#a86c91d760b8ae016acad899e82e96241">str</a>()<span class="keyword"> const </span>{</div>
<div class="line"><a name="l00073"></a><span class="lineno">   73</span>&#160;    <span class="keywordflow">if</span> (<a class="code" href="classqbpp_1_1exhaustive__solver_1_1Sol.html#af2d151df730232c24068ca0e1b227e3c">is_all_solutions_</a> || <a class="code" href="classqbpp_1_1exhaustive__solver_1_1Sol.html#a94c59e8bc788aa2916e971e9c8fa1c76">is_optimal_solutions_</a>) {</div>
<div class="line"><a name="l00074"></a><span class="lineno">   74</span>&#160;      std::ostringstream oss;</div>
<div class="line"><a name="l00075"></a><span class="lineno">   75</span>&#160;      uint32_t count = 0;</div>
<div class="line"><a name="l00076"></a><span class="lineno">   76</span>&#160;      <span class="keywordflow">for</span> (<span class="keyword">const</span> <span class="keyword">auto</span> &amp;sol : <a class="code" href="classqbpp_1_1exhaustive__solver_1_1Sol.html#a960c32dbfa04542000cbdb7d1ad4df8c">all_solutions_</a>) {</div>
<div class="line"><a name="l00077"></a><span class="lineno">   77</span>&#160;        oss &lt;&lt; count++ &lt;&lt; <span class="stringliteral">&quot;:&quot;</span> &lt;&lt; sol;</div>
<div class="line"><a name="l00078"></a><span class="lineno">   78</span>&#160;        <span class="keywordflow">if</span> (count &lt; <a class="code" href="classqbpp_1_1exhaustive__solver_1_1Sol.html#a960c32dbfa04542000cbdb7d1ad4df8c">all_solutions_</a>.size())</div>
<div class="line"><a name="l00079"></a><span class="lineno">   79</span>&#160;          oss &lt;&lt; std::endl;</div>
<div class="line"><a name="l00080"></a><span class="lineno">   80</span>&#160;      }</div>
<div class="line"><a name="l00081"></a><span class="lineno">   81</span>&#160;      <span class="keywordflow">return</span> oss.str();</div>
<div class="line"><a name="l00082"></a><span class="lineno">   82</span>&#160;    }</div>
<div class="line"><a name="l00083"></a><span class="lineno">   83</span>&#160;    <span class="keywordflow">return</span> <a class="code" href="namespaceqbpp.html#a41f8243940eb837c282aed85287bd3af">qbpp::str</a>(<span class="keyword">static_cast&lt;</span><a class="code" href="classqbpp_1_1Sol.html">qbpp::Sol</a><span class="keyword">&gt;</span>(*<span class="keyword">this</span>));</div>
<div class="line"><a name="l00084"></a><span class="lineno">   84</span>&#160;  }</div>
<div class="line"><a name="l00085"></a><span class="lineno">   85</span>&#160; </div>
<div class="line"><a name="l00086"></a><span class="lineno"><a class="line" href="classqbpp_1_1exhaustive__solver_1_1Sol.html#afb23b4a82d09645fbbf88124c4b3c602">   86</a></span>&#160;  std::vector&lt;qbpp::Sol&gt;::const_iterator <a class="code" href="classqbpp_1_1exhaustive__solver_1_1Sol.html#afb23b4a82d09645fbbf88124c4b3c602">begin</a>()<span class="keyword"> const </span>{</div>
<div class="line"><a name="l00087"></a><span class="lineno">   87</span>&#160;    <span class="keywordflow">return</span> <a class="code" href="classqbpp_1_1exhaustive__solver_1_1Sol.html#a960c32dbfa04542000cbdb7d1ad4df8c">all_solutions_</a>.begin();</div>
<div class="line"><a name="l00088"></a><span class="lineno">   88</span>&#160;  }</div>
<div class="line"><a name="l00089"></a><span class="lineno">   89</span>&#160; </div>
<div class="line"><a name="l00090"></a><span class="lineno"><a class="line" href="classqbpp_1_1exhaustive__solver_1_1Sol.html#a9a79874bd094f53edd0b447f1e5eef62">   90</a></span>&#160;  std::vector&lt;qbpp::Sol&gt;::const_iterator <a class="code" href="classqbpp_1_1exhaustive__solver_1_1Sol.html#a9a79874bd094f53edd0b447f1e5eef62">end</a>()<span class="keyword"> const </span>{</div>
<div class="line"><a name="l00091"></a><span class="lineno">   91</span>&#160;    <span class="keywordflow">return</span> <a class="code" href="classqbpp_1_1exhaustive__solver_1_1Sol.html#a960c32dbfa04542000cbdb7d1ad4df8c">all_solutions_</a>.end();</div>
<div class="line"><a name="l00092"></a><span class="lineno">   92</span>&#160;  }</div>
<div class="line"><a name="l00093"></a><span class="lineno">   93</span>&#160; </div>
<div class="line"><a name="l00094"></a><span class="lineno"><a class="line" href="classqbpp_1_1exhaustive__solver_1_1Sol.html#a14bfd7576baad77821d830e8fb638442">   94</a></span>&#160;  <span class="keywordtype">size_t</span> <a class="code" href="classqbpp_1_1exhaustive__solver_1_1Sol.html#a14bfd7576baad77821d830e8fb638442">size</a>()<span class="keyword"> const </span>{ <span class="keywordflow">return</span> <a class="code" href="classqbpp_1_1exhaustive__solver_1_1Sol.html#a960c32dbfa04542000cbdb7d1ad4df8c">all_solutions_</a>.size(); }</div>
<div class="line"><a name="l00095"></a><span class="lineno">   95</span>&#160; </div>
<div class="line"><a name="l00096"></a><span class="lineno"><a class="line" href="classqbpp_1_1exhaustive__solver_1_1Sol.html#a8c99d944904fbb09a5d32ecc200ff042">   96</a></span>&#160;  <span class="keyword">const</span> <a class="code" href="classqbpp_1_1Sol.html">qbpp::Sol</a> &amp;<a class="code" href="classqbpp_1_1exhaustive__solver_1_1Sol.html#a8c99d944904fbb09a5d32ecc200ff042">operator[]</a>(<span class="keywordtype">size_t</span> i)<span class="keyword"> const </span>{ <span class="keywordflow">return</span> <a class="code" href="classqbpp_1_1exhaustive__solver_1_1Sol.html#a960c32dbfa04542000cbdb7d1ad4df8c">all_solutions_</a>[i]; }</div>
<div class="line"><a name="l00097"></a><span class="lineno">   97</span>&#160; </div>
<div class="line"><a name="l00098"></a><span class="lineno"><a class="line" href="classqbpp_1_1exhaustive__solver_1_1Sol.html#a37a75e15fd103a0930d39696fd863dbc">   98</a></span>&#160;  <a class="code" href="classqbpp_1_1Sol.html">qbpp::Sol</a> &amp;<a class="code" href="classqbpp_1_1exhaustive__solver_1_1Sol.html#a37a75e15fd103a0930d39696fd863dbc">operator[]</a>(<span class="keywordtype">size_t</span> i) { <span class="keywordflow">return</span> <a class="code" href="classqbpp_1_1exhaustive__solver_1_1Sol.html#a960c32dbfa04542000cbdb7d1ad4df8c">all_solutions_</a>[i]; }</div>
<div class="line"><a name="l00099"></a><span class="lineno">   99</span>&#160;};</div>
<div class="line"><a name="l00100"></a><span class="lineno">  100</span>&#160; </div>
<div class="line"><a name="l00101"></a><span class="lineno"><a class="line" href="namespaceqbpp_1_1exhaustive__solver.html#a915ee842570590bc3da41020451535c6">  101</a></span>&#160;<span class="keyword">inline</span> std::ostream &amp;<a class="code" href="namespaceqbpp_1_1exhaustive__solver.html#a915ee842570590bc3da41020451535c6">operator&lt;&lt;</a>(std::ostream &amp;os, <span class="keyword">const</span> <a class="code" href="classqbpp_1_1exhaustive__solver_1_1Sol.html">Sol</a> &amp;sol) {</div>
<div class="line"><a name="l00102"></a><span class="lineno">  102</span>&#160;  os &lt;&lt; sol.<a class="code" href="classqbpp_1_1exhaustive__solver_1_1Sol.html#a86c91d760b8ae016acad899e82e96241">str</a>();</div>
<div class="line"><a name="l00103"></a><span class="lineno">  103</span>&#160;  <span class="keywordflow">return</span> os;</div>
<div class="line"><a name="l00104"></a><span class="lineno">  104</span>&#160;}</div>
<div class="line"><a name="l00105"></a><span class="lineno">  105</span>&#160; </div>
<div class="line"><a name="l00106"></a><span class="lineno"><a class="line" href="classqbpp_1_1exhaustive__solver_1_1ExhaustiveSolver.html">  106</a></span>&#160;<span class="keyword">class </span><a class="code" href="classqbpp_1_1exhaustive__solver_1_1ExhaustiveSolver.html">ExhaustiveSolver</a> {</div>
<div class="line"><a name="l00107"></a><span class="lineno">  107</span>&#160;<span class="keyword">protected</span>:</div>
<div class="line"><a name="l00108"></a><span class="lineno"><a class="line" href="classqbpp_1_1exhaustive__solver_1_1ExhaustiveSolver.html#aa619bcaa7409964046dbe993238d4c18">  108</a></span>&#160;  <span class="keyword">const</span> <a class="code" href="classqbpp_1_1QuadModel.html">QuadModel</a> <a class="code" href="classqbpp_1_1exhaustive__solver_1_1ExhaustiveSolver.html#aa619bcaa7409964046dbe993238d4c18">quad_model_</a>;</div>
<div class="line"><a name="l00109"></a><span class="lineno">  109</span>&#160; </div>
<div class="line"><a name="l00110"></a><span class="lineno"><a class="line" href="classqbpp_1_1exhaustive__solver_1_1ExhaustiveSolver.html#ae431e5a6629f8a5ea7e1ef11166e3595">  110</a></span>&#160;  <span class="keywordtype">bool</span> <a class="code" href="classqbpp_1_1exhaustive__solver_1_1ExhaustiveSolver.html#ae431e5a6629f8a5ea7e1ef11166e3595">enable_default_callback_</a> = <span class="keyword">false</span>;</div>
<div class="line"><a name="l00111"></a><span class="lineno">  111</span>&#160; </div>
<div class="line"><a name="l00112"></a><span class="lineno">  112</span>&#160;<span class="keyword">public</span>:</div>
<div class="line"><a name="l00113"></a><span class="lineno">  113</span>&#160; </div>
<div class="line"><a name="l00114"></a><span class="lineno"><a class="line" href="classqbpp_1_1exhaustive__solver_1_1ExhaustiveSolver.html#a3ed3d408d27799611ac224c432a98f8c">  114</a></span>&#160;  <a class="code" href="classqbpp_1_1exhaustive__solver_1_1ExhaustiveSolver.html#a3ed3d408d27799611ac224c432a98f8c">ExhaustiveSolver</a>(<span class="keyword">const</span> <a class="code" href="classqbpp_1_1QuadModel.html">QuadModel</a> &amp;quad_model) : <a class="code" href="classqbpp_1_1exhaustive__solver_1_1ExhaustiveSolver.html#aa619bcaa7409964046dbe993238d4c18">quad_model_</a>(quad_model) {}</div>
<div class="line"><a name="l00115"></a><span class="lineno">  115</span>&#160; </div>
<div class="line"><a name="l00116"></a><span class="lineno"><a class="line" href="classqbpp_1_1exhaustive__solver_1_1ExhaustiveSolver.html#ab5bd307bb7b5a24b598a46b592df2954">  116</a></span>&#160;  <span class="keyword">mutable</span> std::mutex <a class="code" href="classqbpp_1_1exhaustive__solver_1_1ExhaustiveSolver.html#ab5bd307bb7b5a24b598a46b592df2954">callback_mutex_</a>;</div>
<div class="line"><a name="l00117"></a><span class="lineno">  117</span>&#160; </div>
<div class="line"><a name="l00118"></a><span class="lineno">  118</span>&#160;  <span class="keyword">virtual</span> <a class="code" href="classqbpp_1_1exhaustive__solver_1_1ExhaustiveSolver.html#a8977a8e0cfbd07fe8a4dbb6605f32474">~ExhaustiveSolver</a>() = <span class="keywordflow">default</span>;</div>
<div class="line"><a name="l00119"></a><span class="lineno">  119</span>&#160; </div>
<div class="line"><a name="l00120"></a><span class="lineno"><a class="line" href="classqbpp_1_1exhaustive__solver_1_1ExhaustiveSolver.html#af7aafe9b8c518b36548741f09bb25f90">  120</a></span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">void</span> <a class="code" href="classqbpp_1_1exhaustive__solver_1_1ExhaustiveSolver.html#af7aafe9b8c518b36548741f09bb25f90">callback</a>(<span class="keyword">const</span> <a class="code" href="classqbpp_1_1SolHolderTemplate.html">SolHolder</a> &amp;sol_holder)<span class="keyword"> const </span>{</div>
<div class="line"><a name="l00121"></a><span class="lineno">  121</span>&#160;    <span class="keyword">static</span> std::optional&lt;energy_t&gt; prev_energy = std::nullopt;</div>
<div class="line"><a name="l00122"></a><span class="lineno">  122</span>&#160;    std::lock_guard&lt;std::mutex&gt; lock(<a class="code" href="classqbpp_1_1exhaustive__solver_1_1ExhaustiveSolver.html#ab5bd307bb7b5a24b598a46b592df2954">callback_mutex_</a>);</div>
<div class="line"><a name="l00123"></a><span class="lineno">  123</span>&#160;    <span class="keywordflow">if</span> (<a class="code" href="classqbpp_1_1exhaustive__solver_1_1ExhaustiveSolver.html#ae431e5a6629f8a5ea7e1ef11166e3595">enable_default_callback_</a>) {</div>
<div class="line"><a name="l00124"></a><span class="lineno">  124</span>&#160;      <span class="keywordflow">if</span> (!prev_energy.has_value() ||</div>
<div class="line"><a name="l00125"></a><span class="lineno">  125</span>&#160;          sol_holder.<a class="code" href="classqbpp_1_1SolHolderTemplate.html#a818489f254ed0c02e04f8bfa114e6ef0">energy</a>() &lt; prev_energy.value()) {</div>
<div class="line"><a name="l00126"></a><span class="lineno">  126</span>&#160;        prev_energy = sol_holder.<a class="code" href="classqbpp_1_1SolHolderTemplate.html#a818489f254ed0c02e04f8bfa114e6ef0">energy</a>();</div>
<div class="line"><a name="l00127"></a><span class="lineno">  127</span>&#160;        std::cout &lt;&lt; <span class="stringliteral">&quot;TTS = &quot;</span> &lt;&lt; std::fixed &lt;&lt; std::setprecision(3)</div>
<div class="line"><a name="l00128"></a><span class="lineno">  128</span>&#160;                  &lt;&lt; std::setfill(<span class="charliteral">&#39;0&#39;</span>) &lt;&lt; sol_holder.<a class="code" href="classqbpp_1_1SolHolderTemplate.html#aebffa575db7ebd305230df2f5b85168f">get_tts</a>()</div>
<div class="line"><a name="l00129"></a><span class="lineno">  129</span>&#160;                  &lt;&lt; <span class="stringliteral">&quot;s Energy = &quot;</span> &lt;&lt; sol_holder.<a class="code" href="classqbpp_1_1SolHolderTemplate.html#a818489f254ed0c02e04f8bfa114e6ef0">energy</a>() &lt;&lt; std::endl;</div>
<div class="line"><a name="l00130"></a><span class="lineno">  130</span>&#160;      }</div>
<div class="line"><a name="l00131"></a><span class="lineno">  131</span>&#160;    }</div>
<div class="line"><a name="l00132"></a><span class="lineno">  132</span>&#160;  }</div>
<div class="line"><a name="l00133"></a><span class="lineno">  133</span>&#160; </div>
<div class="line"><a name="l00134"></a><span class="lineno"><a class="line" href="classqbpp_1_1exhaustive__solver_1_1ExhaustiveSolver.html#a156ffd892212e98e1f210fce907a28ec">  134</a></span>&#160;  <a class="code" href="namespaceqbpp.html#a5cf436100ede362797d0c0501ea50a5a">vindex_t</a> <a class="code" href="classqbpp_1_1exhaustive__solver_1_1ExhaustiveSolver.html#a156ffd892212e98e1f210fce907a28ec">var_count</a>()<span class="keyword"> const </span>{ <span class="keywordflow">return</span> <a class="code" href="classqbpp_1_1exhaustive__solver_1_1ExhaustiveSolver.html#aa619bcaa7409964046dbe993238d4c18">quad_model_</a>.<a class="code" href="classqbpp_1_1Model.html#a5cf2bb955c3aa25951ca499d7d8635d2">var_count</a>(); }</div>
<div class="line"><a name="l00135"></a><span class="lineno">  135</span>&#160; </div>
<div class="line"><a name="l00136"></a><span class="lineno">  136</span>&#160;  <a class="code" href="classqbpp_1_1Sol.html">qbpp::Sol</a> <a class="code" href="classqbpp_1_1exhaustive__solver_1_1ExhaustiveSolver.html#a967d19602b7de9fef4198508924c7a25">search</a>();</div>
<div class="line"><a name="l00137"></a><span class="lineno">  137</span>&#160; </div>
<div class="line"><a name="l00138"></a><span class="lineno">  138</span>&#160;  <a class="code" href="classqbpp_1_1exhaustive__solver_1_1Sol.html">Sol</a> <a class="code" href="classqbpp_1_1exhaustive__solver_1_1ExhaustiveSolver.html#a1c26cfdda7d9d8f854170c6430a55e57">search_optimal_solutions</a>();</div>
<div class="line"><a name="l00139"></a><span class="lineno">  139</span>&#160; </div>
<div class="line"><a name="l00140"></a><span class="lineno">  140</span>&#160;  <a class="code" href="classqbpp_1_1exhaustive__solver_1_1Sol.html">Sol</a> <a class="code" href="classqbpp_1_1exhaustive__solver_1_1ExhaustiveSolver.html#a639fd8773458972ddeabd24e66809d70">search_all_solutions</a>();</div>
<div class="line"><a name="l00141"></a><span class="lineno">  141</span>&#160; </div>
<div class="line"><a name="l00142"></a><span class="lineno"><a class="line" href="classqbpp_1_1exhaustive__solver_1_1ExhaustiveSolver.html#a48bf01e66a37bbec332902ecfb2f0d1d">  142</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="classqbpp_1_1exhaustive__solver_1_1ExhaustiveSolver.html#a48bf01e66a37bbec332902ecfb2f0d1d">enable_default_callback</a>(<span class="keywordtype">bool</span> enable = <span class="keyword">true</span>) {</div>
<div class="line"><a name="l00143"></a><span class="lineno">  143</span>&#160;    <a class="code" href="classqbpp_1_1exhaustive__solver_1_1ExhaustiveSolver.html#ae431e5a6629f8a5ea7e1ef11166e3595">enable_default_callback_</a> = enable;</div>
<div class="line"><a name="l00144"></a><span class="lineno">  144</span>&#160;  }</div>
<div class="line"><a name="l00145"></a><span class="lineno">  145</span>&#160; </div>
<div class="line"><a name="l00146"></a><span class="lineno">  146</span>&#160; </div>
<div class="line"><a name="l00147"></a><span class="lineno"><a class="line" href="classqbpp_1_1exhaustive__solver_1_1ExhaustiveSolver.html#a3e90a679f7bc4eb7f63d604a4f65c7f4">  147</a></span>&#160;  <span class="keyword">const</span> <a class="code" href="classqbpp_1_1QuadModel.html">QuadModel</a> &amp;<a class="code" href="classqbpp_1_1exhaustive__solver_1_1ExhaustiveSolver.html#a3e90a679f7bc4eb7f63d604a4f65c7f4">get_quad_model</a>()<span class="keyword"> const </span>{ <span class="keywordflow">return</span> <a class="code" href="classqbpp_1_1exhaustive__solver_1_1ExhaustiveSolver.html#aa619bcaa7409964046dbe993238d4c18">quad_model_</a>; }</div>
<div class="line"><a name="l00148"></a><span class="lineno">  148</span>&#160;};</div>
<div class="line"><a name="l00149"></a><span class="lineno">  149</span>&#160; </div>
<div class="line"><a name="l00150"></a><span class="lineno"><a class="line" href="classqbpp_1_1exhaustive__solver_1_1SearchAlgorithm.html">  150</a></span>&#160;<span class="keyword">class </span><a class="code" href="classqbpp_1_1exhaustive__solver_1_1SearchAlgorithm.html">SearchAlgorithm</a> {</div>
<div class="line"><a name="l00151"></a><span class="lineno"><a class="line" href="classqbpp_1_1exhaustive__solver_1_1SearchAlgorithm.html#af667685bb7ceb4beee261f77621afaf8">  151</a></span>&#160;  <span class="keyword">const</span> <a class="code" href="classqbpp_1_1exhaustive__solver_1_1ExhaustiveSolver.html">ExhaustiveSolver</a> &amp;<a class="code" href="classqbpp_1_1exhaustive__solver_1_1SearchAlgorithm.html#af667685bb7ceb4beee261f77621afaf8">exhaustive_solver_</a>;</div>
<div class="line"><a name="l00152"></a><span class="lineno">  152</span>&#160; </div>
<div class="line"><a name="l00153"></a><span class="lineno"><a class="line" href="classqbpp_1_1exhaustive__solver_1_1SearchAlgorithm.html#a52fca6c665f8bb2492ce340ff84617fa">  153</a></span>&#160;  <span class="keyword">const</span> <a class="code" href="classqbpp_1_1QuadModel.html">QuadModel</a> &amp;<a class="code" href="classqbpp_1_1exhaustive__solver_1_1SearchAlgorithm.html#a52fca6c665f8bb2492ce340ff84617fa">quad_model_</a>;</div>
<div class="line"><a name="l00154"></a><span class="lineno">  154</span>&#160; </div>
<div class="line"><a name="l00155"></a><span class="lineno"><a class="line" href="classqbpp_1_1exhaustive__solver_1_1SearchAlgorithm.html#a52b1739206ba81576f8261fec172db28">  155</a></span>&#160;  <span class="keyword">const</span> std::vector&lt;vindex_t&gt; <a class="code" href="classqbpp_1_1exhaustive__solver_1_1SearchAlgorithm.html#a52b1739206ba81576f8261fec172db28">var_order_</a>;</div>
<div class="line"><a name="l00156"></a><span class="lineno">  156</span>&#160; </div>
<div class="line"><a name="l00157"></a><span class="lineno"><a class="line" href="classqbpp_1_1exhaustive__solver_1_1SearchAlgorithm.html#ac8874b804e5c1d9911df5c2f6cd4998b">  157</a></span>&#160;  <a class="code" href="classqbpp_1_1SolHolderTemplate.html">qbpp::SolHolder</a> <a class="code" href="classqbpp_1_1exhaustive__solver_1_1SearchAlgorithm.html#ac8874b804e5c1d9911df5c2f6cd4998b">sol_holder_</a>;</div>
<div class="line"><a name="l00158"></a><span class="lineno">  158</span>&#160; </div>
<div class="line"><a name="l00159"></a><span class="lineno"><a class="line" href="classqbpp_1_1exhaustive__solver_1_1SearchAlgorithm.html#a777db1cc746c6a25a2f3bc875c51a772">  159</a></span>&#160;  <span class="keywordtype">bool</span> <a class="code" href="classqbpp_1_1exhaustive__solver_1_1SearchAlgorithm.html#a777db1cc746c6a25a2f3bc875c51a772">is_all_solutions_</a> = <span class="keyword">false</span>;</div>
<div class="line"><a name="l00160"></a><span class="lineno">  160</span>&#160; </div>
<div class="line"><a name="l00161"></a><span class="lineno"><a class="line" href="classqbpp_1_1exhaustive__solver_1_1SearchAlgorithm.html#a525b2d527887752022f7354eebb3ea05">  161</a></span>&#160;  <span class="keywordtype">bool</span> <a class="code" href="classqbpp_1_1exhaustive__solver_1_1SearchAlgorithm.html#a525b2d527887752022f7354eebb3ea05">is_optimal_solutions_</a> = <span class="keyword">false</span>;</div>
<div class="line"><a name="l00162"></a><span class="lineno">  162</span>&#160; </div>
<div class="line"><a name="l00163"></a><span class="lineno"><a class="line" href="classqbpp_1_1exhaustive__solver_1_1SearchAlgorithm.html#a905a37715c53d8b96a6e5ef19b894e92">  163</a></span>&#160;  std::vector&lt;qbpp::Sol&gt; <a class="code" href="classqbpp_1_1exhaustive__solver_1_1SearchAlgorithm.html#a905a37715c53d8b96a6e5ef19b894e92">all_solutions_</a>;</div>
<div class="line"><a name="l00164"></a><span class="lineno">  164</span>&#160; </div>
<div class="line"><a name="l00165"></a><span class="lineno"><a class="line" href="classqbpp_1_1exhaustive__solver_1_1SearchAlgorithm.html#a795a13753a1d622588332c862a328216">  165</a></span>&#160;  <span class="keyword">mutable</span> std::mutex <a class="code" href="classqbpp_1_1exhaustive__solver_1_1SearchAlgorithm.html#a795a13753a1d622588332c862a328216">all_solutions_mutex_</a>;</div>
<div class="line"><a name="l00166"></a><span class="lineno">  166</span>&#160; </div>
<div class="line"><a name="l00167"></a><span class="lineno"><a class="line" href="classqbpp_1_1exhaustive__solver_1_1SearchAlgorithm.html#ad9287726921c354099815b6e8ec9bb4d">  167</a></span>&#160;  std::vector&lt;SolDelta&gt; <a class="code" href="classqbpp_1_1exhaustive__solver_1_1SearchAlgorithm.html#ad9287726921c354099815b6e8ec9bb4d">sol_deltas</a>;</div>
<div class="line"><a name="l00168"></a><span class="lineno">  168</span>&#160; </div>
<div class="line"><a name="l00169"></a><span class="lineno"><a class="line" href="classqbpp_1_1exhaustive__solver_1_1SearchAlgorithm.html#a4c70861e652f494ba0659c5bfb1bb3f7">  169</a></span>&#160;  std::vector&lt;vindex_t&gt; <a class="code" href="classqbpp_1_1exhaustive__solver_1_1SearchAlgorithm.html#a4c70861e652f494ba0659c5bfb1bb3f7">init_var_order</a>(<span class="keyword">const</span> <a class="code" href="classqbpp_1_1QuadModel.html">QuadModel</a> &amp;quad_model) {</div>
<div class="line"><a name="l00170"></a><span class="lineno">  170</span>&#160;    std::vector&lt;std::pair&lt;vindex_t, vindex_t&gt;&gt; degree_var;</div>
<div class="line"><a name="l00171"></a><span class="lineno">  171</span>&#160;    degree_var.resize(quad_model.<a class="code" href="classqbpp_1_1Model.html#a5cf2bb955c3aa25951ca499d7d8635d2">var_count</a>());</div>
<div class="line"><a name="l00172"></a><span class="lineno">  172</span>&#160;    <span class="keywordflow">for</span> (<a class="code" href="namespaceqbpp.html#a5cf436100ede362797d0c0501ea50a5a">vindex_t</a> i = 0; i &lt; quad_model.<a class="code" href="classqbpp_1_1Model.html#a5cf2bb955c3aa25951ca499d7d8635d2">var_count</a>(); ++i) {</div>
<div class="line"><a name="l00173"></a><span class="lineno">  173</span>&#160;      degree_var[i] = std::make_pair(quad_model.<a class="code" href="classqbpp_1_1QuadModel.html#ada71ec32906c9d8c73bd25c4267bafc5">degree</a>(i), i);</div>
<div class="line"><a name="l00174"></a><span class="lineno">  174</span>&#160;    }</div>
<div class="line"><a name="l00175"></a><span class="lineno">  175</span>&#160;    std::sort(degree_var.begin(), degree_var.end(), std::greater&lt;&gt;());</div>
<div class="line"><a name="l00176"></a><span class="lineno">  176</span>&#160;    std::vector&lt;vindex_t&gt; var_order;</div>
<div class="line"><a name="l00177"></a><span class="lineno">  177</span>&#160;    var_order.resize(quad_model.<a class="code" href="classqbpp_1_1Model.html#a5cf2bb955c3aa25951ca499d7d8635d2">var_count</a>());</div>
<div class="line"><a name="l00178"></a><span class="lineno">  178</span>&#160;    <span class="keywordflow">for</span> (<a class="code" href="namespaceqbpp.html#a5cf436100ede362797d0c0501ea50a5a">vindex_t</a> i = 0; i &lt; quad_model.<a class="code" href="classqbpp_1_1Model.html#a5cf2bb955c3aa25951ca499d7d8635d2">var_count</a>(); ++i) {</div>
<div class="line"><a name="l00179"></a><span class="lineno">  179</span>&#160;      var_order[i] = degree_var[i].second;</div>
<div class="line"><a name="l00180"></a><span class="lineno">  180</span>&#160;    }</div>
<div class="line"><a name="l00181"></a><span class="lineno">  181</span>&#160;    <span class="keywordflow">return</span> var_order;</div>
<div class="line"><a name="l00182"></a><span class="lineno">  182</span>&#160;  }</div>
<div class="line"><a name="l00183"></a><span class="lineno">  183</span>&#160; </div>
<div class="line"><a name="l00184"></a><span class="lineno">  184</span>&#160;<span class="keyword">public</span>:</div>
<div class="line"><a name="l00185"></a><span class="lineno"><a class="line" href="classqbpp_1_1exhaustive__solver_1_1SearchAlgorithm.html#ab1e6c2357f3c1a1ee4a61d45c73a3b55">  185</a></span>&#160;  <span class="keyword">explicit</span> <a class="code" href="classqbpp_1_1exhaustive__solver_1_1SearchAlgorithm.html#ab1e6c2357f3c1a1ee4a61d45c73a3b55">SearchAlgorithm</a>(<span class="keyword">const</span> <a class="code" href="classqbpp_1_1exhaustive__solver_1_1ExhaustiveSolver.html">ExhaustiveSolver</a> &amp;exhaustive_solver)</div>
<div class="line"><a name="l00186"></a><span class="lineno">  186</span>&#160;      : <a class="code" href="classqbpp_1_1exhaustive__solver_1_1SearchAlgorithm.html#af667685bb7ceb4beee261f77621afaf8">exhaustive_solver_</a>(exhaustive_solver),</div>
<div class="line"><a name="l00187"></a><span class="lineno">  187</span>&#160;        <a class="code" href="classqbpp_1_1exhaustive__solver_1_1SearchAlgorithm.html#a52fca6c665f8bb2492ce340ff84617fa">quad_model_</a>(<a class="code" href="classqbpp_1_1exhaustive__solver_1_1SearchAlgorithm.html#af667685bb7ceb4beee261f77621afaf8">exhaustive_solver_</a>.<a class="code" href="classqbpp_1_1exhaustive__solver_1_1SearchAlgorithm.html#a9159dbff9bdd84bf6302770296804d9d">get_quad_model</a>()),</div>
<div class="line"><a name="l00188"></a><span class="lineno">  188</span>&#160;        <a class="code" href="classqbpp_1_1exhaustive__solver_1_1SearchAlgorithm.html#a52b1739206ba81576f8261fec172db28">var_order_</a>(<a class="code" href="classqbpp_1_1exhaustive__solver_1_1SearchAlgorithm.html#a4c70861e652f494ba0659c5bfb1bb3f7">init_var_order</a>(<a class="code" href="classqbpp_1_1exhaustive__solver_1_1SearchAlgorithm.html#a52fca6c665f8bb2492ce340ff84617fa">quad_model_</a>)), <a class="code" href="classqbpp_1_1exhaustive__solver_1_1SearchAlgorithm.html#ac8874b804e5c1d9911df5c2f6cd4998b">sol_holder_</a>(<a class="code" href="classqbpp_1_1exhaustive__solver_1_1SearchAlgorithm.html#a52fca6c665f8bb2492ce340ff84617fa">quad_model_</a>) {}</div>
<div class="line"><a name="l00189"></a><span class="lineno">  189</span>&#160; </div>
<div class="line"><a name="l00190"></a><span class="lineno"><a class="line" href="classqbpp_1_1exhaustive__solver_1_1SearchAlgorithm.html#a9159dbff9bdd84bf6302770296804d9d">  190</a></span>&#160;  <span class="keyword">const</span> <a class="code" href="classqbpp_1_1QuadModel.html">QuadModel</a> &amp;<a class="code" href="classqbpp_1_1exhaustive__solver_1_1SearchAlgorithm.html#a9159dbff9bdd84bf6302770296804d9d">get_quad_model</a>()<span class="keyword"> const </span>{ <span class="keywordflow">return</span> <a class="code" href="classqbpp_1_1exhaustive__solver_1_1SearchAlgorithm.html#a52fca6c665f8bb2492ce340ff84617fa">quad_model_</a>; }</div>
<div class="line"><a name="l00191"></a><span class="lineno">  191</span>&#160; </div>
<div class="line"><a name="l00192"></a><span class="lineno"><a class="line" href="classqbpp_1_1exhaustive__solver_1_1SearchAlgorithm.html#a33445f7b8c5fd890b3005f62a6e68898">  192</a></span>&#160;  <span class="keyword">const</span> std::vector&lt;vindex_t&gt; &amp;<a class="code" href="classqbpp_1_1exhaustive__solver_1_1SearchAlgorithm.html#a33445f7b8c5fd890b3005f62a6e68898">get_var_order</a>()<span class="keyword"> const </span>{ <span class="keywordflow">return</span> <a class="code" href="classqbpp_1_1exhaustive__solver_1_1SearchAlgorithm.html#a52b1739206ba81576f8261fec172db28">var_order_</a>; }</div>
<div class="line"><a name="l00193"></a><span class="lineno">  193</span>&#160; </div>
<div class="line"><a name="l00194"></a><span class="lineno"><a class="line" href="classqbpp_1_1exhaustive__solver_1_1SearchAlgorithm.html#a4c1877de640b8a605c36f2f32eae246b">  194</a></span>&#160;  <a class="code" href="namespaceqbpp.html#a5cf436100ede362797d0c0501ea50a5a">vindex_t</a> <a class="code" href="classqbpp_1_1exhaustive__solver_1_1SearchAlgorithm.html#a4c1877de640b8a605c36f2f32eae246b">var_count</a>()<span class="keyword"> const </span>{ <span class="keywordflow">return</span> <a class="code" href="classqbpp_1_1exhaustive__solver_1_1SearchAlgorithm.html#a52fca6c665f8bb2492ce340ff84617fa">quad_model_</a>.<a class="code" href="classqbpp_1_1Model.html#a5cf2bb955c3aa25951ca499d7d8635d2">var_count</a>(); }</div>
<div class="line"><a name="l00195"></a><span class="lineno">  195</span>&#160; </div>
<div class="line"><a name="l00196"></a><span class="lineno"><a class="line" href="classqbpp_1_1exhaustive__solver_1_1SearchAlgorithm.html#a0442cfc2f0e2b38329427e8eb23c7c4b">  196</a></span>&#160;  std::vector&lt;qbpp::Sol&gt; &amp;<a class="code" href="classqbpp_1_1exhaustive__solver_1_1SearchAlgorithm.html#a0442cfc2f0e2b38329427e8eb23c7c4b">get_all_solutions</a>() { <span class="keywordflow">return</span> <a class="code" href="classqbpp_1_1exhaustive__solver_1_1SearchAlgorithm.html#a905a37715c53d8b96a6e5ef19b894e92">all_solutions_</a>; }</div>
<div class="line"><a name="l00197"></a><span class="lineno">  197</span>&#160; </div>
<div class="line"><a name="l00198"></a><span class="lineno"><a class="line" href="classqbpp_1_1exhaustive__solver_1_1SearchAlgorithm.html#a0c13def2e6e74113619a0b7c65b50c10">  198</a></span>&#160;  <a class="code" href="classqbpp_1_1SolHolderTemplate.html">qbpp::SolHolder</a> &amp;<a class="code" href="classqbpp_1_1exhaustive__solver_1_1SearchAlgorithm.html#a0c13def2e6e74113619a0b7c65b50c10">get_sol_holder</a>() { <span class="keywordflow">return</span> <a class="code" href="classqbpp_1_1exhaustive__solver_1_1SearchAlgorithm.html#ac8874b804e5c1d9911df5c2f6cd4998b">sol_holder_</a>; }</div>
<div class="line"><a name="l00199"></a><span class="lineno">  199</span>&#160; </div>
<div class="line"><a name="l00200"></a><span class="lineno"><a class="line" href="classqbpp_1_1exhaustive__solver_1_1SearchAlgorithm.html#a73cc42b1cd9e72aa4a56eb25354ca21e">  200</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="classqbpp_1_1exhaustive__solver_1_1SearchAlgorithm.html#a73cc42b1cd9e72aa4a56eb25354ca21e">register_new_sol</a>(<span class="keyword">const</span> <a class="code" href="classqbpp_1_1Sol.html">qbpp::Sol</a> &amp;sol) {</div>
<div class="line"><a name="l00201"></a><span class="lineno">  201</span>&#160;    <span class="keywordflow">if</span> (!<a class="code" href="classqbpp_1_1exhaustive__solver_1_1SearchAlgorithm.html#a777db1cc746c6a25a2f3bc875c51a772">is_all_solutions_</a> &amp;&amp; <a class="code" href="classqbpp_1_1exhaustive__solver_1_1SearchAlgorithm.html#ac8874b804e5c1d9911df5c2f6cd4998b">sol_holder_</a>.<a class="code" href="classqbpp_1_1SolHolderTemplate.html#a818489f254ed0c02e04f8bfa114e6ef0">energy</a>() &lt; sol.<a class="code" href="classqbpp_1_1Sol.html#a3ad455870df155ffed4701ce070cf335">energy</a>())</div>
<div class="line"><a name="l00202"></a><span class="lineno">  202</span>&#160;      <span class="keywordflow">return</span>;</div>
<div class="line"><a name="l00203"></a><span class="lineno">  203</span>&#160;    std::lock_guard&lt;std::mutex&gt; lock(<a class="code" href="classqbpp_1_1exhaustive__solver_1_1SearchAlgorithm.html#a795a13753a1d622588332c862a328216">all_solutions_mutex_</a>);</div>
<div class="line"><a name="l00204"></a><span class="lineno">  204</span>&#160;    <span class="keywordflow">if</span> (<a class="code" href="classqbpp_1_1exhaustive__solver_1_1SearchAlgorithm.html#a777db1cc746c6a25a2f3bc875c51a772">is_all_solutions_</a>) {</div>
<div class="line"><a name="l00205"></a><span class="lineno">  205</span>&#160;      <a class="code" href="classqbpp_1_1exhaustive__solver_1_1SearchAlgorithm.html#a905a37715c53d8b96a6e5ef19b894e92">all_solutions_</a>.push_back(sol);</div>
<div class="line"><a name="l00206"></a><span class="lineno">  206</span>&#160;    } <span class="keywordflow">else</span> <span class="keywordflow">if</span> (<a class="code" href="classqbpp_1_1exhaustive__solver_1_1SearchAlgorithm.html#a525b2d527887752022f7354eebb3ea05">is_optimal_solutions_</a>) {</div>
<div class="line"><a name="l00207"></a><span class="lineno">  207</span>&#160;      <span class="keywordflow">if</span> (<a class="code" href="classqbpp_1_1exhaustive__solver_1_1SearchAlgorithm.html#ac8874b804e5c1d9911df5c2f6cd4998b">sol_holder_</a>.<a class="code" href="classqbpp_1_1SolHolderTemplate.html#a818489f254ed0c02e04f8bfa114e6ef0">energy</a>() == sol.<a class="code" href="classqbpp_1_1Sol.html#a3ad455870df155ffed4701ce070cf335">energy</a>()) {</div>
<div class="line"><a name="l00208"></a><span class="lineno">  208</span>&#160;        <a class="code" href="classqbpp_1_1exhaustive__solver_1_1SearchAlgorithm.html#a905a37715c53d8b96a6e5ef19b894e92">all_solutions_</a>.push_back(sol);</div>
<div class="line"><a name="l00209"></a><span class="lineno">  209</span>&#160;      } <span class="keywordflow">else</span> <span class="keywordflow">if</span> (<a class="code" href="classqbpp_1_1exhaustive__solver_1_1SearchAlgorithm.html#ac8874b804e5c1d9911df5c2f6cd4998b">sol_holder_</a>.<a class="code" href="classqbpp_1_1SolHolderTemplate.html#a818489f254ed0c02e04f8bfa114e6ef0">energy</a>() &gt; sol.<a class="code" href="classqbpp_1_1Sol.html#a3ad455870df155ffed4701ce070cf335">energy</a>()) {</div>
<div class="line"><a name="l00210"></a><span class="lineno">  210</span>&#160;        <a class="code" href="classqbpp_1_1exhaustive__solver_1_1SearchAlgorithm.html#a905a37715c53d8b96a6e5ef19b894e92">all_solutions_</a>.clear();</div>
<div class="line"><a name="l00211"></a><span class="lineno">  211</span>&#160;        <a class="code" href="classqbpp_1_1exhaustive__solver_1_1SearchAlgorithm.html#a905a37715c53d8b96a6e5ef19b894e92">all_solutions_</a>.push_back(sol);</div>
<div class="line"><a name="l00212"></a><span class="lineno">  212</span>&#160;      }</div>
<div class="line"><a name="l00213"></a><span class="lineno">  213</span>&#160;    }</div>
<div class="line"><a name="l00214"></a><span class="lineno">  214</span>&#160;    <span class="keywordflow">if</span> (<a class="code" href="classqbpp_1_1exhaustive__solver_1_1SearchAlgorithm.html#ac8874b804e5c1d9911df5c2f6cd4998b">sol_holder_</a>.<a class="code" href="classqbpp_1_1SolHolderTemplate.html#ac633c4a38cd0088e2d72deda90c349bc">set_if_better</a>(sol)) {</div>
<div class="line"><a name="l00215"></a><span class="lineno">  215</span>&#160;      <a class="code" href="classqbpp_1_1exhaustive__solver_1_1SearchAlgorithm.html#af667685bb7ceb4beee261f77621afaf8">exhaustive_solver_</a>.<a class="code" href="classqbpp_1_1exhaustive__solver_1_1ExhaustiveSolver.html#af7aafe9b8c518b36548741f09bb25f90">callback</a>(<a class="code" href="classqbpp_1_1exhaustive__solver_1_1SearchAlgorithm.html#ac8874b804e5c1d9911df5c2f6cd4998b">sol_holder_</a>);</div>
<div class="line"><a name="l00216"></a><span class="lineno">  216</span>&#160;    }</div>
<div class="line"><a name="l00217"></a><span class="lineno">  217</span>&#160;  }</div>
<div class="line"><a name="l00218"></a><span class="lineno">  218</span>&#160; </div>
<div class="line"><a name="l00219"></a><span class="lineno">  219</span>&#160;  <span class="keywordtype">void</span> <a class="code" href="classqbpp_1_1exhaustive__solver_1_1SearchAlgorithm.html#a3a2b22a2908c11bf374b4f06b2ad1815">search</a>();</div>
<div class="line"><a name="l00220"></a><span class="lineno">  220</span>&#160; </div>
<div class="line"><a name="l00221"></a><span class="lineno">  221</span>&#160;  <span class="keywordtype">void</span> <a class="code" href="classqbpp_1_1exhaustive__solver_1_1SearchAlgorithm.html#ae29feffc0f9bef93caf80bb05b114728">search_optimal_solutions</a>();</div>
<div class="line"><a name="l00222"></a><span class="lineno">  222</span>&#160; </div>
<div class="line"><a name="l00223"></a><span class="lineno">  223</span>&#160;  <span class="keywordtype">void</span> <a class="code" href="classqbpp_1_1exhaustive__solver_1_1SearchAlgorithm.html#a61cba268dd5876ce4c474c219d17410a">search_all_solutions</a>();</div>
<div class="line"><a name="l00224"></a><span class="lineno">  224</span>&#160; </div>
<div class="line"><a name="l00225"></a><span class="lineno">  225</span>&#160;  <span class="keywordtype">void</span> <a class="code" href="classqbpp_1_1exhaustive__solver_1_1SearchAlgorithm.html#ad031941ba26d6f9e9262803068fbfc7e">gen_sol_deltas</a>(<a class="code" href="classqbpp_1_1exhaustive__solver_1_1SolDelta.html">SolDelta</a> &amp;sol_delta, <a class="code" href="namespaceqbpp.html#a5cf436100ede362797d0c0501ea50a5a">vindex_t</a> index);</div>
<div class="line"><a name="l00226"></a><span class="lineno">  226</span>&#160;};</div>
<div class="line"><a name="l00227"></a><span class="lineno">  227</span>&#160; </div>
<div class="line"><a name="l00228"></a><span class="lineno"><a class="line" href="classqbpp_1_1exhaustive__solver_1_1SolDelta.html">  228</a></span>&#160;<span class="keyword">class </span><a class="code" href="classqbpp_1_1exhaustive__solver_1_1SolDelta.html">SolDelta</a> : <span class="keyword">public</span> <a class="code" href="classqbpp_1_1Sol.html">qbpp::Sol</a> {</div>
<div class="line"><a name="l00229"></a><span class="lineno"><a class="line" href="classqbpp_1_1exhaustive__solver_1_1SolDelta.html#a12934fe6cf615d24a00a6d4129a7caeb">  229</a></span>&#160;  <a class="code" href="classqbpp_1_1exhaustive__solver_1_1SearchAlgorithm.html">SearchAlgorithm</a> &amp;<a class="code" href="classqbpp_1_1exhaustive__solver_1_1SolDelta.html#a12934fe6cf615d24a00a6d4129a7caeb">search_algorithm_</a>;</div>
<div class="line"><a name="l00230"></a><span class="lineno">  230</span>&#160; </div>
<div class="line"><a name="l00231"></a><span class="lineno"><a class="line" href="classqbpp_1_1exhaustive__solver_1_1SolDelta.html#a643cfd8c8e7e0047cbdfed82c97fc4c8">  231</a></span>&#160;  <span class="keyword">const</span> std::vector&lt;vindex_t&gt; &amp;<a class="code" href="classqbpp_1_1exhaustive__solver_1_1SolDelta.html#a643cfd8c8e7e0047cbdfed82c97fc4c8">var_order_</a> = <a class="code" href="classqbpp_1_1exhaustive__solver_1_1SolDelta.html#a12934fe6cf615d24a00a6d4129a7caeb">search_algorithm_</a>.<a class="code" href="classqbpp_1_1exhaustive__solver_1_1SearchAlgorithm.html#a33445f7b8c5fd890b3005f62a6e68898">get_var_order</a>();</div>
<div class="line"><a name="l00232"></a><span class="lineno">  232</span>&#160; </div>
<div class="line"><a name="l00233"></a><span class="lineno"><a class="line" href="classqbpp_1_1exhaustive__solver_1_1SolDelta.html#a023aa63e36df19c1cbe0e6ec67c20153">  233</a></span>&#160;  std::vector&lt;energy_t&gt; <a class="code" href="classqbpp_1_1exhaustive__solver_1_1SolDelta.html#a023aa63e36df19c1cbe0e6ec67c20153">delta_</a>;</div>
<div class="line"><a name="l00234"></a><span class="lineno">  234</span>&#160; </div>
<div class="line"><a name="l00235"></a><span class="lineno">  235</span>&#160;<span class="keyword">public</span>:</div>
<div class="line"><a name="l00236"></a><span class="lineno"><a class="line" href="classqbpp_1_1exhaustive__solver_1_1SolDelta.html#a6f368c3a2224a71803d9ee2711effa9b">  236</a></span>&#160;  <span class="keyword">explicit</span> <a class="code" href="classqbpp_1_1exhaustive__solver_1_1SolDelta.html#a6f368c3a2224a71803d9ee2711effa9b">SolDelta</a>(<a class="code" href="classqbpp_1_1exhaustive__solver_1_1SearchAlgorithm.html">SearchAlgorithm</a> &amp;search_algorithm)</div>
<div class="line"><a name="l00237"></a><span class="lineno">  237</span>&#160;      : <a class="code" href="classqbpp_1_1exhaustive__solver_1_1Sol.html">Sol</a>(search_algorithm.get_quad_model()),</div>
<div class="line"><a name="l00238"></a><span class="lineno">  238</span>&#160;        <a class="code" href="classqbpp_1_1exhaustive__solver_1_1SolDelta.html#a12934fe6cf615d24a00a6d4129a7caeb">search_algorithm_</a>(search_algorithm) {</div>
<div class="line"><a name="l00239"></a><span class="lineno">  239</span>&#160;    <a class="code" href="classqbpp_1_1Sol.html#a5dc3ec6002a1d823ad1bd525e9f6fa3e">energy_</a> = <a class="code" href="classqbpp_1_1Sol.html#a11752b0b3742bec8da842db87ba94230">quad_model_</a>.<a class="code" href="classqbpp_1_1Model.html#a3b0641c97573b248f1877c2826277102">constant</a>();</div>
<div class="line"><a name="l00240"></a><span class="lineno">  240</span>&#160;    <a class="code" href="classqbpp_1_1exhaustive__solver_1_1SolDelta.html#a023aa63e36df19c1cbe0e6ec67c20153">delta_</a>.resize(<a class="code" href="classqbpp_1_1Sol.html#a11752b0b3742bec8da842db87ba94230">quad_model_</a>.<a class="code" href="classqbpp_1_1Model.html#a5cf2bb955c3aa25951ca499d7d8635d2">var_count</a>());</div>
<div class="line"><a name="l00241"></a><span class="lineno">  241</span>&#160;    <span class="keywordflow">for</span> (<a class="code" href="namespaceqbpp.html#a5cf436100ede362797d0c0501ea50a5a">vindex_t</a> i = 0; i &lt; <a class="code" href="classqbpp_1_1Sol.html#a11752b0b3742bec8da842db87ba94230">quad_model_</a>.<a class="code" href="classqbpp_1_1Model.html#a5cf2bb955c3aa25951ca499d7d8635d2">var_count</a>(); ++i) {</div>
<div class="line"><a name="l00242"></a><span class="lineno">  242</span>&#160;      <a class="code" href="classqbpp_1_1exhaustive__solver_1_1SolDelta.html#a023aa63e36df19c1cbe0e6ec67c20153">delta_</a>[i] = <a class="code" href="classqbpp_1_1Sol.html#a11752b0b3742bec8da842db87ba94230">quad_model_</a>.<a class="code" href="classqbpp_1_1QuadModel.html#a591e74de688dbff1339c2c9d2352df65">linear</a>(i);</div>
<div class="line"><a name="l00243"></a><span class="lineno">  243</span>&#160;    }</div>
<div class="line"><a name="l00244"></a><span class="lineno">  244</span>&#160;  }</div>
<div class="line"><a name="l00245"></a><span class="lineno">  245</span>&#160; </div>
<div class="line"><a name="l00246"></a><span class="lineno">  246</span>&#160;  <a class="code" href="classqbpp_1_1exhaustive__solver_1_1SolDelta.html#a6f368c3a2224a71803d9ee2711effa9b">SolDelta</a>(<span class="keyword">const</span> <a class="code" href="classqbpp_1_1exhaustive__solver_1_1SolDelta.html">SolDelta</a> &amp;) = <span class="keywordflow">default</span>;</div>
<div class="line"><a name="l00247"></a><span class="lineno">  247</span>&#160; </div>
<div class="line"><a name="l00248"></a><span class="lineno"><a class="line" href="classqbpp_1_1exhaustive__solver_1_1SolDelta.html#a4ca4e09dec03e09553fc50a7d05c0778">  248</a></span>&#160;  <a class="code" href="namespaceqbpp.html#a5cf436100ede362797d0c0501ea50a5a">vindex_t</a> <a class="code" href="classqbpp_1_1exhaustive__solver_1_1SolDelta.html#a4ca4e09dec03e09553fc50a7d05c0778">var_count</a>()<span class="keyword"> const </span>{ <span class="keywordflow">return</span> <a class="code" href="classqbpp_1_1Sol.html#a11752b0b3742bec8da842db87ba94230">quad_model_</a>.<a class="code" href="classqbpp_1_1Model.html#a5cf2bb955c3aa25951ca499d7d8635d2">var_count</a>(); }</div>
<div class="line"><a name="l00249"></a><span class="lineno">  249</span>&#160; </div>
<div class="line"><a name="l00250"></a><span class="lineno"><a class="line" href="classqbpp_1_1exhaustive__solver_1_1SolDelta.html#ae53a1eb415153c3d6e02794123a87b5a">  250</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="classqbpp_1_1exhaustive__solver_1_1SolDelta.html#ae53a1eb415153c3d6e02794123a87b5a">flip</a>(<a class="code" href="namespaceqbpp.html#a5cf436100ede362797d0c0501ea50a5a">vindex_t</a> <a class="code" href="classqbpp_1_1Sol.html#a24f5a53fa68071dd9bcda910533bbad7">index</a>)<span class="keyword"> override </span>{</div>
<div class="line"><a name="l00251"></a><span class="lineno">  251</span>&#160;    <a class="code" href="namespaceqbpp.html#a5cf436100ede362797d0c0501ea50a5a">vindex_t</a> flip_index = <a class="code" href="classqbpp_1_1exhaustive__solver_1_1SolDelta.html#a643cfd8c8e7e0047cbdfed82c97fc4c8">var_order_</a>[<a class="code" href="classqbpp_1_1Sol.html#a24f5a53fa68071dd9bcda910533bbad7">index</a>];</div>
<div class="line"><a name="l00252"></a><span class="lineno">  252</span>&#160;    <span class="keywordflow">if</span> (flip_index &gt;= <a class="code" href="classqbpp_1_1exhaustive__solver_1_1SolDelta.html#a4ca4e09dec03e09553fc50a7d05c0778">var_count</a>()) {</div>
<div class="line"><a name="l00253"></a><span class="lineno">  253</span>&#160;      <span class="keywordflow">throw</span> std::out_of_range(</div>
<div class="line"><a name="l00254"></a><span class="lineno">  254</span>&#160;          <a class="code" href="qbpp_8hpp.html#a265e0828fcea8e75fbc9c0be99f7156e">THROW_MESSAGE</a>(<span class="stringliteral">&quot;Sol: flip_index (&quot;</span>, flip_index, <span class="stringliteral">&quot;) out of range&quot;</span>));</div>
<div class="line"><a name="l00255"></a><span class="lineno">  255</span>&#160;    }</div>
<div class="line"><a name="l00256"></a><span class="lineno">  256</span>&#160;    <a class="code" href="classqbpp_1_1Sol.html#a5dc3ec6002a1d823ad1bd525e9f6fa3e">energy_</a> = <a class="code" href="classqbpp_1_1Sol.html#a3ad455870df155ffed4701ce070cf335">energy</a>() + <a class="code" href="classqbpp_1_1exhaustive__solver_1_1SolDelta.html#a023aa63e36df19c1cbe0e6ec67c20153">delta_</a>[flip_index];</div>
<div class="line"><a name="l00257"></a><span class="lineno">  257</span>&#160;    <span class="keywordflow">for</span> (<a class="code" href="namespaceqbpp.html#a5cf436100ede362797d0c0501ea50a5a">vindex_t</a> j = 0; j &lt; <a class="code" href="classqbpp_1_1Sol.html#a11752b0b3742bec8da842db87ba94230">quad_model_</a>.<a class="code" href="classqbpp_1_1QuadModel.html#ada71ec32906c9d8c73bd25c4267bafc5">degree</a>(flip_index); ++j) {</div>
<div class="line"><a name="l00258"></a><span class="lineno">  258</span>&#160;      <span class="keyword">auto</span> [k, coeff] = <a class="code" href="classqbpp_1_1Sol.html#a11752b0b3742bec8da842db87ba94230">quad_model_</a>.<a class="code" href="classqbpp_1_1QuadModel.html#a6624038d4ff117176c37de2d1260f471">quadratic</a>(flip_index, j);</div>
<div class="line"><a name="l00259"></a><span class="lineno">  259</span>&#160;      <a class="code" href="classqbpp_1_1exhaustive__solver_1_1SolDelta.html#a023aa63e36df19c1cbe0e6ec67c20153">delta_</a>[k] += (2 * <a class="code" href="classqbpp_1_1Sol.html#abdb4f6b779678720715f7845f264440f">get</a>(flip_index) - 1) * (2 * <a class="code" href="classqbpp_1_1Sol.html#abdb4f6b779678720715f7845f264440f">get</a>(k) - 1) * coeff;</div>
<div class="line"><a name="l00260"></a><span class="lineno">  260</span>&#160;    }</div>
<div class="line"><a name="l00261"></a><span class="lineno">  261</span>&#160;    <a class="code" href="classqbpp_1_1exhaustive__solver_1_1SolDelta.html#a023aa63e36df19c1cbe0e6ec67c20153">delta_</a>[flip_index] = -<a class="code" href="classqbpp_1_1exhaustive__solver_1_1SolDelta.html#a023aa63e36df19c1cbe0e6ec67c20153">delta_</a>[flip_index];</div>
<div class="line"><a name="l00262"></a><span class="lineno">  262</span>&#160;    <span class="keywordflow">if</span> (!<a class="code" href="classqbpp_1_1Sol.html#a5dc3ec6002a1d823ad1bd525e9f6fa3e">energy_</a>.has_value()) {</div>
<div class="line"><a name="l00263"></a><span class="lineno">  263</span>&#160;      <a class="code" href="classqbpp_1_1Sol.html#a5dc3ec6002a1d823ad1bd525e9f6fa3e">energy_</a> = <a class="code" href="classqbpp_1_1Sol.html#a90c464b3df40d670646cb6f940462275">comp_energy</a>();</div>
<div class="line"><a name="l00264"></a><span class="lineno">  264</span>&#160;    }</div>
<div class="line"><a name="l00265"></a><span class="lineno">  265</span>&#160; </div>
<div class="line"><a name="l00266"></a><span class="lineno">  266</span>&#160;    <a class="code" href="classqbpp_1_1Sol.html#a97a3c5acd6aff38c15164146e453b2bf">bit_vector_</a>.<a class="code" href="classqbpp_1_1impl_1_1BitVector.html#a1835e45be9c5473b9b1f2d9569be0945">flip</a>(flip_index);</div>
<div class="line"><a name="l00267"></a><span class="lineno">  267</span>&#160;  }</div>
<div class="line"><a name="l00268"></a><span class="lineno">  268</span>&#160; </div>
<div class="line"><a name="l00269"></a><span class="lineno"><a class="line" href="classqbpp_1_1exhaustive__solver_1_1SolDelta.html#a926e9faffb265b7208b75663e76f77da">  269</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="classqbpp_1_1exhaustive__solver_1_1SolDelta.html#a926e9faffb265b7208b75663e76f77da">search</a>(<a class="code" href="namespaceqbpp.html#a5cf436100ede362797d0c0501ea50a5a">vindex_t</a> <a class="code" href="classqbpp_1_1Sol.html#a24f5a53fa68071dd9bcda910533bbad7">index</a>) {</div>
<div class="line"><a name="l00270"></a><span class="lineno">  270</span>&#160;    <span class="keywordflow">if</span> (<a class="code" href="classqbpp_1_1Sol.html#a24f5a53fa68071dd9bcda910533bbad7">index</a> &gt;= 1)</div>
<div class="line"><a name="l00271"></a><span class="lineno">  271</span>&#160;      <a class="code" href="classqbpp_1_1exhaustive__solver_1_1SolDelta.html#a3849dd84e16e534fe2e69b63f0079149">search</a>(<a class="code" href="classqbpp_1_1Sol.html#a24f5a53fa68071dd9bcda910533bbad7">index</a> - 1);</div>
<div class="line"><a name="l00272"></a><span class="lineno">  272</span>&#160;    <a class="code" href="classqbpp_1_1exhaustive__solver_1_1SolDelta.html#ae53a1eb415153c3d6e02794123a87b5a">flip</a>(<a class="code" href="classqbpp_1_1Sol.html#a24f5a53fa68071dd9bcda910533bbad7">index</a>);</div>
<div class="line"><a name="l00273"></a><span class="lineno">  273</span>&#160;    <a class="code" href="classqbpp_1_1exhaustive__solver_1_1SolDelta.html#a12934fe6cf615d24a00a6d4129a7caeb">search_algorithm_</a>.<a class="code" href="classqbpp_1_1exhaustive__solver_1_1SearchAlgorithm.html#a73cc42b1cd9e72aa4a56eb25354ca21e">register_new_sol</a>(*<span class="keyword">this</span>);</div>
<div class="line"><a name="l00274"></a><span class="lineno">  274</span>&#160;    <span class="keywordflow">if</span> (<a class="code" href="classqbpp_1_1Sol.html#a24f5a53fa68071dd9bcda910533bbad7">index</a> &gt;= 1)</div>
<div class="line"><a name="l00275"></a><span class="lineno">  275</span>&#160;      <a class="code" href="classqbpp_1_1exhaustive__solver_1_1SolDelta.html#a3849dd84e16e534fe2e69b63f0079149">search</a>(<a class="code" href="classqbpp_1_1Sol.html#a24f5a53fa68071dd9bcda910533bbad7">index</a> - 1);</div>
<div class="line"><a name="l00276"></a><span class="lineno">  276</span>&#160;  }</div>
<div class="line"><a name="l00277"></a><span class="lineno">  277</span>&#160; </div>
<div class="line"><a name="l00278"></a><span class="lineno"><a class="line" href="classqbpp_1_1exhaustive__solver_1_1SolDelta.html#a3849dd84e16e534fe2e69b63f0079149">  278</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="classqbpp_1_1exhaustive__solver_1_1SolDelta.html#a3849dd84e16e534fe2e69b63f0079149">search</a>() {</div>
<div class="line"><a name="l00279"></a><span class="lineno">  279</span>&#160;    <a class="code" href="classqbpp_1_1exhaustive__solver_1_1SolDelta.html#a12934fe6cf615d24a00a6d4129a7caeb">search_algorithm_</a>.<a class="code" href="classqbpp_1_1exhaustive__solver_1_1SearchAlgorithm.html#a73cc42b1cd9e72aa4a56eb25354ca21e">register_new_sol</a>(*<span class="keyword">this</span>);</div>
<div class="line"><a name="l00280"></a><span class="lineno">  280</span>&#160;    <a class="code" href="classqbpp_1_1exhaustive__solver_1_1SolDelta.html#a3849dd84e16e534fe2e69b63f0079149">search</a>(<a class="code" href="classqbpp_1_1Sol.html#a11752b0b3742bec8da842db87ba94230">quad_model_</a>.<a class="code" href="classqbpp_1_1Model.html#a5cf2bb955c3aa25951ca499d7d8635d2">var_count</a>() - 1);</div>
<div class="line"><a name="l00281"></a><span class="lineno">  281</span>&#160;  }</div>
<div class="line"><a name="l00282"></a><span class="lineno">  282</span>&#160;};</div>
<div class="line"><a name="l00283"></a><span class="lineno">  283</span>&#160; </div>
<div class="line"><a name="l00284"></a><span class="lineno">  284</span>&#160; </div>
<div class="line"><a name="l00285"></a><span class="lineno"><a class="line" href="classqbpp_1_1exhaustive__solver_1_1ExhaustiveSolver.html#a967d19602b7de9fef4198508924c7a25">  285</a></span>&#160;<span class="keyword">inline</span> <a class="code" href="classqbpp_1_1Sol.html">qbpp::Sol</a> <a class="code" href="classqbpp_1_1exhaustive__solver_1_1ExhaustiveSolver.html#a967d19602b7de9fef4198508924c7a25">ExhaustiveSolver::search</a>() {</div>
<div class="line"><a name="l00286"></a><span class="lineno">  286</span>&#160;  <a class="code" href="classqbpp_1_1exhaustive__solver_1_1SearchAlgorithm.html">SearchAlgorithm</a> search_algorithm(*<span class="keyword">this</span>);</div>
<div class="line"><a name="l00287"></a><span class="lineno">  287</span>&#160;  search_algorithm.<a class="code" href="classqbpp_1_1exhaustive__solver_1_1SearchAlgorithm.html#a3a2b22a2908c11bf374b4f06b2ad1815">search</a>();</div>
<div class="line"><a name="l00288"></a><span class="lineno">  288</span>&#160;  <span class="keywordflow">return</span> search_algorithm.<a class="code" href="classqbpp_1_1exhaustive__solver_1_1SearchAlgorithm.html#a0c13def2e6e74113619a0b7c65b50c10">get_sol_holder</a>().<a class="code" href="classqbpp_1_1SolHolderTemplate.html#aa1229c52de2da1dc09c45ff9deade1f7">get_sol</a>();</div>
<div class="line"><a name="l00289"></a><span class="lineno">  289</span>&#160;}</div>
<div class="line"><a name="l00290"></a><span class="lineno">  290</span>&#160; </div>
<div class="line"><a name="l00291"></a><span class="lineno"><a class="line" href="classqbpp_1_1exhaustive__solver_1_1ExhaustiveSolver.html#a1c26cfdda7d9d8f854170c6430a55e57">  291</a></span>&#160;<span class="keyword">inline</span> <a class="code" href="classqbpp_1_1exhaustive__solver_1_1Sol.html">Sol</a> <a class="code" href="classqbpp_1_1exhaustive__solver_1_1ExhaustiveSolver.html#a1c26cfdda7d9d8f854170c6430a55e57">ExhaustiveSolver::search_optimal_solutions</a>() {</div>
<div class="line"><a name="l00292"></a><span class="lineno">  292</span>&#160;  <a class="code" href="classqbpp_1_1exhaustive__solver_1_1SearchAlgorithm.html">SearchAlgorithm</a> search_algorithm(*<span class="keyword">this</span>);</div>
<div class="line"><a name="l00293"></a><span class="lineno">  293</span>&#160;  search_algorithm.<a class="code" href="classqbpp_1_1exhaustive__solver_1_1SearchAlgorithm.html#ae29feffc0f9bef93caf80bb05b114728">search_optimal_solutions</a>();</div>
<div class="line"><a name="l00294"></a><span class="lineno">  294</span>&#160;  <a class="code" href="classqbpp_1_1exhaustive__solver_1_1Sol.html">Sol</a> sol(<a class="code" href="classqbpp_1_1exhaustive__solver_1_1ExhaustiveSolver.html#aa619bcaa7409964046dbe993238d4c18">quad_model_</a>);</div>
<div class="line"><a name="l00295"></a><span class="lineno">  295</span>&#160;  sol.<a class="code" href="classqbpp_1_1exhaustive__solver_1_1Sol.html#aff5d82a2fb9dae3014ada25479452053">optimal_solution_mode</a>();</div>
<div class="line"><a name="l00296"></a><span class="lineno">  296</span>&#160;  sol.<a class="code" href="classqbpp_1_1exhaustive__solver_1_1Sol.html#afba551e637e1ea61ec0c674e6379f436">set_all_solutions</a>(std::move(search_algorithm.<a class="code" href="classqbpp_1_1exhaustive__solver_1_1SearchAlgorithm.html#a0442cfc2f0e2b38329427e8eb23c7c4b">get_all_solutions</a>()));</div>
<div class="line"><a name="l00297"></a><span class="lineno">  297</span>&#160;  <span class="keywordflow">return</span> sol;</div>
<div class="line"><a name="l00298"></a><span class="lineno">  298</span>&#160;}</div>
<div class="line"><a name="l00299"></a><span class="lineno">  299</span>&#160; </div>
<div class="line"><a name="l00300"></a><span class="lineno"><a class="line" href="classqbpp_1_1exhaustive__solver_1_1ExhaustiveSolver.html#a639fd8773458972ddeabd24e66809d70">  300</a></span>&#160;<span class="keyword">inline</span> <a class="code" href="classqbpp_1_1exhaustive__solver_1_1Sol.html">Sol</a> <a class="code" href="classqbpp_1_1exhaustive__solver_1_1ExhaustiveSolver.html#a639fd8773458972ddeabd24e66809d70">ExhaustiveSolver::search_all_solutions</a>() {</div>
<div class="line"><a name="l00301"></a><span class="lineno">  301</span>&#160;  <a class="code" href="classqbpp_1_1exhaustive__solver_1_1SearchAlgorithm.html">SearchAlgorithm</a> search_algorithm(*<span class="keyword">this</span>);</div>
<div class="line"><a name="l00302"></a><span class="lineno">  302</span>&#160;  search_algorithm.<a class="code" href="classqbpp_1_1exhaustive__solver_1_1SearchAlgorithm.html#a61cba268dd5876ce4c474c219d17410a">search_all_solutions</a>();</div>
<div class="line"><a name="l00303"></a><span class="lineno">  303</span>&#160;  <a class="code" href="classqbpp_1_1exhaustive__solver_1_1Sol.html">Sol</a> sol(<a class="code" href="classqbpp_1_1exhaustive__solver_1_1ExhaustiveSolver.html#aa619bcaa7409964046dbe993238d4c18">quad_model_</a>);</div>
<div class="line"><a name="l00304"></a><span class="lineno">  304</span>&#160;  sol.<a class="code" href="classqbpp_1_1exhaustive__solver_1_1Sol.html#a27015c742388744bf513617e78e129f2">all_solution_mode</a>();</div>
<div class="line"><a name="l00305"></a><span class="lineno">  305</span>&#160;  sol.<a class="code" href="classqbpp_1_1exhaustive__solver_1_1Sol.html#afba551e637e1ea61ec0c674e6379f436">set_all_solutions</a>(std::move(search_algorithm.<a class="code" href="classqbpp_1_1exhaustive__solver_1_1SearchAlgorithm.html#a0442cfc2f0e2b38329427e8eb23c7c4b">get_all_solutions</a>()));</div>
<div class="line"><a name="l00306"></a><span class="lineno">  306</span>&#160;  <span class="keywordflow">return</span> sol;</div>
<div class="line"><a name="l00307"></a><span class="lineno">  307</span>&#160;}</div>
<div class="line"><a name="l00308"></a><span class="lineno">  308</span>&#160; </div>
<div class="line"><a name="l00309"></a><span class="lineno">  309</span>&#160; </div>
<div class="line"><a name="l00310"></a><span class="lineno"><a class="line" href="classqbpp_1_1exhaustive__solver_1_1SearchAlgorithm.html#ad031941ba26d6f9e9262803068fbfc7e">  310</a></span>&#160;<span class="keyword">inline</span> <span class="keywordtype">void</span> <a class="code" href="classqbpp_1_1exhaustive__solver_1_1SearchAlgorithm.html#ad031941ba26d6f9e9262803068fbfc7e">SearchAlgorithm::gen_sol_deltas</a>(<a class="code" href="classqbpp_1_1exhaustive__solver_1_1SolDelta.html">SolDelta</a> &amp;sol_delta,</div>
<div class="line"><a name="l00311"></a><span class="lineno">  311</span>&#160;                                            <a class="code" href="namespaceqbpp.html#a5cf436100ede362797d0c0501ea50a5a">vindex_t</a> index) {</div>
<div class="line"><a name="l00312"></a><span class="lineno">  312</span>&#160;  <span class="keywordflow">if</span> (<a class="code" href="classqbpp_1_1exhaustive__solver_1_1SearchAlgorithm.html#a4c1877de640b8a605c36f2f32eae246b">var_count</a>() == index)</div>
<div class="line"><a name="l00313"></a><span class="lineno">  313</span>&#160;    <span class="keywordflow">return</span>;</div>
<div class="line"><a name="l00314"></a><span class="lineno">  314</span>&#160;  <a class="code" href="classqbpp_1_1exhaustive__solver_1_1SearchAlgorithm.html#ad031941ba26d6f9e9262803068fbfc7e">gen_sol_deltas</a>(sol_delta, index + 1);</div>
<div class="line"><a name="l00315"></a><span class="lineno">  315</span>&#160;  sol_delta.<a class="code" href="classqbpp_1_1exhaustive__solver_1_1SolDelta.html#ae53a1eb415153c3d6e02794123a87b5a">flip</a>(index);</div>
<div class="line"><a name="l00316"></a><span class="lineno">  316</span>&#160;  <a class="code" href="classqbpp_1_1exhaustive__solver_1_1SearchAlgorithm.html#ad9287726921c354099815b6e8ec9bb4d">sol_deltas</a>.push_back(sol_delta);</div>
<div class="line"><a name="l00317"></a><span class="lineno">  317</span>&#160;  <a class="code" href="classqbpp_1_1exhaustive__solver_1_1SearchAlgorithm.html#ad031941ba26d6f9e9262803068fbfc7e">gen_sol_deltas</a>(sol_delta, index + 1);</div>
<div class="line"><a name="l00318"></a><span class="lineno">  318</span>&#160;}</div>
<div class="line"><a name="l00319"></a><span class="lineno">  319</span>&#160; </div>
<div class="line"><a name="l00320"></a><span class="lineno"><a class="line" href="classqbpp_1_1exhaustive__solver_1_1SearchAlgorithm.html#a3a2b22a2908c11bf374b4f06b2ad1815">  320</a></span>&#160;<span class="keyword">inline</span> <span class="keywordtype">void</span> <a class="code" href="classqbpp_1_1exhaustive__solver_1_1SearchAlgorithm.html#a3a2b22a2908c11bf374b4f06b2ad1815">SearchAlgorithm::search</a>() {</div>
<div class="line"><a name="l00321"></a><span class="lineno">  321</span>&#160;  <span class="keyword">const</span> <span class="keywordtype">int</span> parallel_param = 8;</div>
<div class="line"><a name="l00322"></a><span class="lineno">  322</span>&#160;  <a class="code" href="classqbpp_1_1exhaustive__solver_1_1SolDelta.html">SolDelta</a> sol_delta(*<span class="keyword">this</span>);</div>
<div class="line"><a name="l00323"></a><span class="lineno">  323</span>&#160;  <span class="keywordflow">if</span> (<a class="code" href="classqbpp_1_1exhaustive__solver_1_1SearchAlgorithm.html#a52fca6c665f8bb2492ce340ff84617fa">quad_model_</a>.<a class="code" href="classqbpp_1_1QuadModel.html#a16af851530b1f63aca9baa8ec67ed01f">term_count</a>() == 0) {</div>
<div class="line"><a name="l00324"></a><span class="lineno">  324</span>&#160;    <a class="code" href="classqbpp_1_1exhaustive__solver_1_1SearchAlgorithm.html#a73cc42b1cd9e72aa4a56eb25354ca21e">register_new_sol</a>(sol_delta);</div>
<div class="line"><a name="l00325"></a><span class="lineno">  325</span>&#160;    <span class="keywordflow">return</span>;</div>
<div class="line"><a name="l00326"></a><span class="lineno">  326</span>&#160;  }</div>
<div class="line"><a name="l00327"></a><span class="lineno">  327</span>&#160;  <span class="keywordflow">if</span> (<a class="code" href="classqbpp_1_1exhaustive__solver_1_1SearchAlgorithm.html#a4c1877de640b8a605c36f2f32eae246b">var_count</a>() &lt;= 16) {</div>
<div class="line"><a name="l00328"></a><span class="lineno">  328</span>&#160;    <a class="code" href="classqbpp_1_1exhaustive__solver_1_1SearchAlgorithm.html#a73cc42b1cd9e72aa4a56eb25354ca21e">register_new_sol</a>(sol_delta);</div>
<div class="line"><a name="l00329"></a><span class="lineno">  329</span>&#160;    sol_delta.<a class="code" href="classqbpp_1_1exhaustive__solver_1_1SolDelta.html#a926e9faffb265b7208b75663e76f77da">search</a>(<a class="code" href="classqbpp_1_1exhaustive__solver_1_1SearchAlgorithm.html#a4c1877de640b8a605c36f2f32eae246b">var_count</a>() - 1);</div>
<div class="line"><a name="l00330"></a><span class="lineno">  330</span>&#160;    <span class="keywordflow">return</span>;</div>
<div class="line"><a name="l00331"></a><span class="lineno">  331</span>&#160;  }</div>
<div class="line"><a name="l00332"></a><span class="lineno">  332</span>&#160;  <a class="code" href="classqbpp_1_1exhaustive__solver_1_1SearchAlgorithm.html#ad9287726921c354099815b6e8ec9bb4d">sol_deltas</a>.push_back(sol_delta);</div>
<div class="line"><a name="l00333"></a><span class="lineno">  333</span>&#160;  <a class="code" href="classqbpp_1_1exhaustive__solver_1_1SearchAlgorithm.html#ad031941ba26d6f9e9262803068fbfc7e">gen_sol_deltas</a>(sol_delta, <a class="code" href="classqbpp_1_1exhaustive__solver_1_1SearchAlgorithm.html#a4c1877de640b8a605c36f2f32eae246b">var_count</a>() - parallel_param);</div>
<div class="line"><a name="l00334"></a><span class="lineno">  334</span>&#160; </div>
<div class="line"><a name="l00335"></a><span class="lineno">  335</span>&#160;  tbb::parallel_for(tbb::blocked_range&lt;size_t&gt;(0, <a class="code" href="classqbpp_1_1exhaustive__solver_1_1SearchAlgorithm.html#ad9287726921c354099815b6e8ec9bb4d">sol_deltas</a>.size()),</div>
<div class="line"><a name="l00336"></a><span class="lineno">  336</span>&#160;                    [&amp;](<span class="keyword">const</span> tbb::blocked_range&lt;size_t&gt; &amp;range) {</div>
<div class="line"><a name="l00337"></a><span class="lineno">  337</span>&#160;                      for (size_t i = range.begin(); i &lt; range.end(); ++i) {</div>
<div class="line"><a name="l00338"></a><span class="lineno">  338</span>&#160;                        register_new_sol(sol_deltas[i]);</div>
<div class="line"><a name="l00339"></a><span class="lineno">  339</span>&#160;                        sol_deltas[i].search(var_count() -</div>
<div class="line"><a name="l00340"></a><span class="lineno">  340</span>&#160;                                             (parallel_param + 1));</div>
<div class="line"><a name="l00341"></a><span class="lineno">  341</span>&#160;                      }</div>
<div class="line"><a name="l00342"></a><span class="lineno">  342</span>&#160;                    });</div>
<div class="line"><a name="l00343"></a><span class="lineno">  343</span>&#160;}</div>
<div class="line"><a name="l00344"></a><span class="lineno">  344</span>&#160; </div>
<div class="line"><a name="l00345"></a><span class="lineno"><a class="line" href="classqbpp_1_1exhaustive__solver_1_1SearchAlgorithm.html#ae29feffc0f9bef93caf80bb05b114728">  345</a></span>&#160;<span class="keyword">inline</span> <span class="keywordtype">void</span> SearchAlgorithm::search_optimal_solutions() {</div>
<div class="line"><a name="l00346"></a><span class="lineno">  346</span>&#160;  is_optimal_solutions_ = <span class="keyword">true</span>;</div>
<div class="line"><a name="l00347"></a><span class="lineno">  347</span>&#160;  search();</div>
<div class="line"><a name="l00348"></a><span class="lineno">  348</span>&#160;  tbb::parallel_sort(all_solutions_.begin(), all_solutions_.end());</div>
<div class="line"><a name="l00349"></a><span class="lineno">  349</span>&#160;}</div>
<div class="line"><a name="l00350"></a><span class="lineno">  350</span>&#160; </div>
<div class="line"><a name="l00351"></a><span class="lineno"><a class="line" href="classqbpp_1_1exhaustive__solver_1_1SearchAlgorithm.html#a61cba268dd5876ce4c474c219d17410a">  351</a></span>&#160;<span class="keyword">inline</span> <span class="keywordtype">void</span> SearchAlgorithm::search_all_solutions() {</div>
<div class="line"><a name="l00352"></a><span class="lineno">  352</span>&#160;  is_all_solutions_ = <span class="keyword">true</span>;</div>
<div class="line"><a name="l00353"></a><span class="lineno">  353</span>&#160;  search();</div>
<div class="line"><a name="l00354"></a><span class="lineno">  354</span>&#160;  tbb::parallel_sort(all_solutions_.begin(), all_solutions_.end());</div>
<div class="line"><a name="l00355"></a><span class="lineno">  355</span>&#160;}</div>
<div class="line"><a name="l00356"></a><span class="lineno">  356</span>&#160; </div>
<div class="line"><a name="l00357"></a><span class="lineno">  357</span>&#160;} </div>
<div class="line"><a name="l00358"></a><span class="lineno">  358</span>&#160;} </div>
<div class="line"><a name="l00359"></a><span class="lineno">  359</span>&#160; </div>
<div class="line"><a name="l00360"></a><span class="lineno">  360</span>&#160;<span class="preprocessor">#endif </span></div>
</div><!-- fragment --></div><!-- contents -->
<div class="ttc" id="aclassqbpp_1_1exhaustive__solver_1_1Sol_html_a8c99d944904fbb09a5d32ecc200ff042"><div class="ttname"><a href="classqbpp_1_1exhaustive__solver_1_1Sol.html#a8c99d944904fbb09a5d32ecc200ff042">qbpp::exhaustive_solver::Sol::operator[]</a></div><div class="ttdeci">const qbpp::Sol &amp; operator[](size_t i) const</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__exhaustive__solver_8hpp_source.html#l00096">qbpp_exhaustive_solver.hpp:96</a></div></div>
<div class="ttc" id="aclassqbpp_1_1exhaustive__solver_1_1SearchAlgorithm_html_a0442cfc2f0e2b38329427e8eb23c7c4b"><div class="ttname"><a href="classqbpp_1_1exhaustive__solver_1_1SearchAlgorithm.html#a0442cfc2f0e2b38329427e8eb23c7c4b">qbpp::exhaustive_solver::SearchAlgorithm::get_all_solutions</a></div><div class="ttdeci">std::vector&lt; qbpp::Sol &gt; &amp; get_all_solutions()</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__exhaustive__solver_8hpp_source.html#l00196">qbpp_exhaustive_solver.hpp:196</a></div></div>
<div class="ttc" id="aclassqbpp_1_1exhaustive__solver_1_1SearchAlgorithm_html_a4c1877de640b8a605c36f2f32eae246b"><div class="ttname"><a href="classqbpp_1_1exhaustive__solver_1_1SearchAlgorithm.html#a4c1877de640b8a605c36f2f32eae246b">qbpp::exhaustive_solver::SearchAlgorithm::var_count</a></div><div class="ttdeci">vindex_t var_count() const</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__exhaustive__solver_8hpp_source.html#l00194">qbpp_exhaustive_solver.hpp:194</a></div></div>
<div class="ttc" id="aclassqbpp_1_1exhaustive__solver_1_1SolDelta_html"><div class="ttname"><a href="classqbpp_1_1exhaustive__solver_1_1SolDelta.html">qbpp::exhaustive_solver::SolDelta</a></div><div class="ttdef"><b>Definition:</b> <a href="qbpp__exhaustive__solver_8hpp_source.html#l00228">qbpp_exhaustive_solver.hpp:228</a></div></div>
<div class="ttc" id="aclassqbpp_1_1exhaustive__solver_1_1SearchAlgorithm_html_a33445f7b8c5fd890b3005f62a6e68898"><div class="ttname"><a href="classqbpp_1_1exhaustive__solver_1_1SearchAlgorithm.html#a33445f7b8c5fd890b3005f62a6e68898">qbpp::exhaustive_solver::SearchAlgorithm::get_var_order</a></div><div class="ttdeci">const std::vector&lt; vindex_t &gt; &amp; get_var_order() const</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__exhaustive__solver_8hpp_source.html#l00192">qbpp_exhaustive_solver.hpp:192</a></div></div>
<div class="ttc" id="anamespaceqbpp_1_1exhaustive__solver_html_a915ee842570590bc3da41020451535c6"><div class="ttname"><a href="namespaceqbpp_1_1exhaustive__solver.html#a915ee842570590bc3da41020451535c6">qbpp::exhaustive_solver::operator&lt;&lt;</a></div><div class="ttdeci">std::ostream &amp; operator&lt;&lt;(std::ostream &amp;os, const Sol &amp;sol)</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__exhaustive__solver_8hpp_source.html#l00101">qbpp_exhaustive_solver.hpp:101</a></div></div>
<div class="ttc" id="aclassqbpp_1_1exhaustive__solver_1_1ExhaustiveSolver_html_a3ed3d408d27799611ac224c432a98f8c"><div class="ttname"><a href="classqbpp_1_1exhaustive__solver_1_1ExhaustiveSolver.html#a3ed3d408d27799611ac224c432a98f8c">qbpp::exhaustive_solver::ExhaustiveSolver::ExhaustiveSolver</a></div><div class="ttdeci">ExhaustiveSolver(const QuadModel &amp;quad_model)</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__exhaustive__solver_8hpp_source.html#l00114">qbpp_exhaustive_solver.hpp:114</a></div></div>
<div class="ttc" id="aclassqbpp_1_1Sol_html_abdb4f6b779678720715f7845f264440f"><div class="ttname"><a href="classqbpp_1_1Sol.html#abdb4f6b779678720715f7845f264440f">qbpp::Sol::get</a></div><div class="ttdeci">var_val_t get(vindex_t index) const</div><div class="ttdef"><b>Definition:</b> <a href="qbpp_8hpp_source.html#l02243">qbpp.hpp:2243</a></div></div>
<div class="ttc" id="aclassqbpp_1_1Sol_html_a97a3c5acd6aff38c15164146e453b2bf"><div class="ttname"><a href="classqbpp_1_1Sol.html#a97a3c5acd6aff38c15164146e453b2bf">qbpp::Sol::bit_vector_</a></div><div class="ttdeci">impl::BitVector bit_vector_</div><div class="ttdef"><b>Definition:</b> <a href="qbpp_8hpp_source.html#l02186">qbpp.hpp:2186</a></div></div>
<div class="ttc" id="aclassqbpp_1_1exhaustive__solver_1_1ExhaustiveSolver_html_a156ffd892212e98e1f210fce907a28ec"><div class="ttname"><a href="classqbpp_1_1exhaustive__solver_1_1ExhaustiveSolver.html#a156ffd892212e98e1f210fce907a28ec">qbpp::exhaustive_solver::ExhaustiveSolver::var_count</a></div><div class="ttdeci">vindex_t var_count() const</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__exhaustive__solver_8hpp_source.html#l00134">qbpp_exhaustive_solver.hpp:134</a></div></div>
<div class="ttc" id="aclassqbpp_1_1exhaustive__solver_1_1Sol_html_a9a79874bd094f53edd0b447f1e5eef62"><div class="ttname"><a href="classqbpp_1_1exhaustive__solver_1_1Sol.html#a9a79874bd094f53edd0b447f1e5eef62">qbpp::exhaustive_solver::Sol::end</a></div><div class="ttdeci">std::vector&lt; qbpp::Sol &gt;::const_iterator end() const</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__exhaustive__solver_8hpp_source.html#l00090">qbpp_exhaustive_solver.hpp:90</a></div></div>
<div class="ttc" id="aclassqbpp_1_1QuadModel_html"><div class="ttname"><a href="classqbpp_1_1QuadModel.html">qbpp::QuadModel</a></div><div class="ttdef"><b>Definition:</b> <a href="qbpp_8hpp_source.html#l01892">qbpp.hpp:1892</a></div></div>
<div class="ttc" id="aclassqbpp_1_1exhaustive__solver_1_1Sol_html_afb23b4a82d09645fbbf88124c4b3c602"><div class="ttname"><a href="classqbpp_1_1exhaustive__solver_1_1Sol.html#afb23b4a82d09645fbbf88124c4b3c602">qbpp::exhaustive_solver::Sol::begin</a></div><div class="ttdeci">std::vector&lt; qbpp::Sol &gt;::const_iterator begin() const</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__exhaustive__solver_8hpp_source.html#l00086">qbpp_exhaustive_solver.hpp:86</a></div></div>
<div class="ttc" id="aclassqbpp_1_1QuadModel_html_a591e74de688dbff1339c2c9d2352df65"><div class="ttname"><a href="classqbpp_1_1QuadModel.html#a591e74de688dbff1339c2c9d2352df65">qbpp::QuadModel::linear</a></div><div class="ttdeci">const std::vector&lt; coeff_t &gt; &amp; linear() const</div><div class="ttdef"><b>Definition:</b> <a href="qbpp_8hpp_source.html#l01967">qbpp.hpp:1967</a></div></div>
<div class="ttc" id="aclassqbpp_1_1exhaustive__solver_1_1SolDelta_html_a12934fe6cf615d24a00a6d4129a7caeb"><div class="ttname"><a href="classqbpp_1_1exhaustive__solver_1_1SolDelta.html#a12934fe6cf615d24a00a6d4129a7caeb">qbpp::exhaustive_solver::SolDelta::search_algorithm_</a></div><div class="ttdeci">SearchAlgorithm &amp; search_algorithm_</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__exhaustive__solver_8hpp_source.html#l00229">qbpp_exhaustive_solver.hpp:229</a></div></div>
<div class="ttc" id="aclassqbpp_1_1SolHolderTemplate_html_aa1229c52de2da1dc09c45ff9deade1f7"><div class="ttname"><a href="classqbpp_1_1SolHolderTemplate.html#aa1229c52de2da1dc09c45ff9deade1f7">qbpp::SolHolderTemplate::get_sol</a></div><div class="ttdeci">const T &amp; get_sol() const</div><div class="ttdef"><b>Definition:</b> <a href="qbpp_8hpp_source.html#l02405">qbpp.hpp:2405</a></div></div>
<div class="ttc" id="aclassqbpp_1_1impl_1_1BitVector_html_a1835e45be9c5473b9b1f2d9569be0945"><div class="ttname"><a href="classqbpp_1_1impl_1_1BitVector.html#a1835e45be9c5473b9b1f2d9569be0945">qbpp::impl::BitVector::flip</a></div><div class="ttdeci">void flip(vindex_t index)</div><div class="ttdef"><b>Definition:</b> <a href="qbpp_8hpp_source.html#l02138">qbpp.hpp:2138</a></div></div>
<div class="ttc" id="aclassqbpp_1_1exhaustive__solver_1_1Sol_html_afba551e637e1ea61ec0c674e6379f436"><div class="ttname"><a href="classqbpp_1_1exhaustive__solver_1_1Sol.html#afba551e637e1ea61ec0c674e6379f436">qbpp::exhaustive_solver::Sol::set_all_solutions</a></div><div class="ttdeci">void set_all_solutions(std::vector&lt; qbpp::Sol &gt; &amp;&amp;all_solutions)</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__exhaustive__solver_8hpp_source.html#l00067">qbpp_exhaustive_solver.hpp:67</a></div></div>
<div class="ttc" id="aclassqbpp_1_1exhaustive__solver_1_1SearchAlgorithm_html_a777db1cc746c6a25a2f3bc875c51a772"><div class="ttname"><a href="classqbpp_1_1exhaustive__solver_1_1SearchAlgorithm.html#a777db1cc746c6a25a2f3bc875c51a772">qbpp::exhaustive_solver::SearchAlgorithm::is_all_solutions_</a></div><div class="ttdeci">bool is_all_solutions_</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__exhaustive__solver_8hpp_source.html#l00159">qbpp_exhaustive_solver.hpp:159</a></div></div>
<div class="ttc" id="aclassqbpp_1_1exhaustive__solver_1_1Sol_html_aff5d82a2fb9dae3014ada25479452053"><div class="ttname"><a href="classqbpp_1_1exhaustive__solver_1_1Sol.html#aff5d82a2fb9dae3014ada25479452053">qbpp::exhaustive_solver::Sol::optimal_solution_mode</a></div><div class="ttdeci">void optimal_solution_mode()</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__exhaustive__solver_8hpp_source.html#l00062">qbpp_exhaustive_solver.hpp:62</a></div></div>
<div class="ttc" id="aclassqbpp_1_1exhaustive__solver_1_1SolDelta_html_a3849dd84e16e534fe2e69b63f0079149"><div class="ttname"><a href="classqbpp_1_1exhaustive__solver_1_1SolDelta.html#a3849dd84e16e534fe2e69b63f0079149">qbpp::exhaustive_solver::SolDelta::search</a></div><div class="ttdeci">void search()</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__exhaustive__solver_8hpp_source.html#l00278">qbpp_exhaustive_solver.hpp:278</a></div></div>
<div class="ttc" id="aclassqbpp_1_1exhaustive__solver_1_1ExhaustiveSolver_html_a967d19602b7de9fef4198508924c7a25"><div class="ttname"><a href="classqbpp_1_1exhaustive__solver_1_1ExhaustiveSolver.html#a967d19602b7de9fef4198508924c7a25">qbpp::exhaustive_solver::ExhaustiveSolver::search</a></div><div class="ttdeci">qbpp::Sol search()</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__exhaustive__solver_8hpp_source.html#l00285">qbpp_exhaustive_solver.hpp:285</a></div></div>
<div class="ttc" id="aclassqbpp_1_1exhaustive__solver_1_1SearchAlgorithm_html_a795a13753a1d622588332c862a328216"><div class="ttname"><a href="classqbpp_1_1exhaustive__solver_1_1SearchAlgorithm.html#a795a13753a1d622588332c862a328216">qbpp::exhaustive_solver::SearchAlgorithm::all_solutions_mutex_</a></div><div class="ttdeci">std::mutex all_solutions_mutex_</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__exhaustive__solver_8hpp_source.html#l00165">qbpp_exhaustive_solver.hpp:165</a></div></div>
<div class="ttc" id="aclassqbpp_1_1exhaustive__solver_1_1SearchAlgorithm_html_a525b2d527887752022f7354eebb3ea05"><div class="ttname"><a href="classqbpp_1_1exhaustive__solver_1_1SearchAlgorithm.html#a525b2d527887752022f7354eebb3ea05">qbpp::exhaustive_solver::SearchAlgorithm::is_optimal_solutions_</a></div><div class="ttdeci">bool is_optimal_solutions_</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__exhaustive__solver_8hpp_source.html#l00161">qbpp_exhaustive_solver.hpp:161</a></div></div>
<div class="ttc" id="aclassqbpp_1_1Sol_html_a90c464b3df40d670646cb6f940462275"><div class="ttname"><a href="classqbpp_1_1Sol.html#a90c464b3df40d670646cb6f940462275">qbpp::Sol::comp_energy</a></div><div class="ttdeci">energy_t comp_energy() const</div><div class="ttdef"><b>Definition:</b> <a href="qbpp_8hpp_source.html#l03492">qbpp.hpp:3492</a></div></div>
<div class="ttc" id="aclassqbpp_1_1exhaustive__solver_1_1SolDelta_html_a023aa63e36df19c1cbe0e6ec67c20153"><div class="ttname"><a href="classqbpp_1_1exhaustive__solver_1_1SolDelta.html#a023aa63e36df19c1cbe0e6ec67c20153">qbpp::exhaustive_solver::SolDelta::delta_</a></div><div class="ttdeci">std::vector&lt; energy_t &gt; delta_</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__exhaustive__solver_8hpp_source.html#l00233">qbpp_exhaustive_solver.hpp:233</a></div></div>
<div class="ttc" id="aclassqbpp_1_1SolHolderTemplate_html_ac633c4a38cd0088e2d72deda90c349bc"><div class="ttname"><a href="classqbpp_1_1SolHolderTemplate.html#ac633c4a38cd0088e2d72deda90c349bc">qbpp::SolHolderTemplate::set_if_better</a></div><div class="ttdeci">virtual std::optional&lt; double &gt; set_if_better(const T &amp;new_sol, const std::string &amp;solver=&quot;&quot;)</div><div class="ttdef"><b>Definition:</b> <a href="qbpp_8hpp_source.html#l02366">qbpp.hpp:2366</a></div></div>
<div class="ttc" id="aclassqbpp_1_1exhaustive__solver_1_1Sol_html"><div class="ttname"><a href="classqbpp_1_1exhaustive__solver_1_1Sol.html">qbpp::exhaustive_solver::Sol</a></div><div class="ttdef"><b>Definition:</b> <a href="qbpp__exhaustive__solver_8hpp_source.html#l00035">qbpp_exhaustive_solver.hpp:35</a></div></div>
<div class="ttc" id="aclassqbpp_1_1exhaustive__solver_1_1SolDelta_html_ae53a1eb415153c3d6e02794123a87b5a"><div class="ttname"><a href="classqbpp_1_1exhaustive__solver_1_1SolDelta.html#ae53a1eb415153c3d6e02794123a87b5a">qbpp::exhaustive_solver::SolDelta::flip</a></div><div class="ttdeci">void flip(vindex_t index) override</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__exhaustive__solver_8hpp_source.html#l00250">qbpp_exhaustive_solver.hpp:250</a></div></div>
<div class="ttc" id="aclassqbpp_1_1QuadModel_html_a6624038d4ff117176c37de2d1260f471"><div class="ttname"><a href="classqbpp_1_1QuadModel.html#a6624038d4ff117176c37de2d1260f471">qbpp::QuadModel::quadratic</a></div><div class="ttdeci">const std::vector&lt; std::vector&lt; std::pair&lt; vindex_t, coeff_t &gt; &gt; &gt; &amp; quadratic() const</div><div class="ttdef"><b>Definition:</b> <a href="qbpp_8hpp_source.html#l01971">qbpp.hpp:1971</a></div></div>
<div class="ttc" id="aclassqbpp_1_1exhaustive__solver_1_1SearchAlgorithm_html_a3a2b22a2908c11bf374b4f06b2ad1815"><div class="ttname"><a href="classqbpp_1_1exhaustive__solver_1_1SearchAlgorithm.html#a3a2b22a2908c11bf374b4f06b2ad1815">qbpp::exhaustive_solver::SearchAlgorithm::search</a></div><div class="ttdeci">void search()</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__exhaustive__solver_8hpp_source.html#l00320">qbpp_exhaustive_solver.hpp:320</a></div></div>
<div class="ttc" id="aclassqbpp_1_1exhaustive__solver_1_1SearchAlgorithm_html_ad031941ba26d6f9e9262803068fbfc7e"><div class="ttname"><a href="classqbpp_1_1exhaustive__solver_1_1SearchAlgorithm.html#ad031941ba26d6f9e9262803068fbfc7e">qbpp::exhaustive_solver::SearchAlgorithm::gen_sol_deltas</a></div><div class="ttdeci">void gen_sol_deltas(SolDelta &amp;sol_delta, vindex_t index)</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__exhaustive__solver_8hpp_source.html#l00310">qbpp_exhaustive_solver.hpp:310</a></div></div>
<div class="ttc" id="aclassqbpp_1_1Sol_html_a5dc3ec6002a1d823ad1bd525e9f6fa3e"><div class="ttname"><a href="classqbpp_1_1Sol.html#a5dc3ec6002a1d823ad1bd525e9f6fa3e">qbpp::Sol::energy_</a></div><div class="ttdeci">std::optional&lt; energy_t &gt; energy_</div><div class="ttdef"><b>Definition:</b> <a href="qbpp_8hpp_source.html#l02188">qbpp.hpp:2188</a></div></div>
<div class="ttc" id="aclassqbpp_1_1exhaustive__solver_1_1Sol_html_a37a75e15fd103a0930d39696fd863dbc"><div class="ttname"><a href="classqbpp_1_1exhaustive__solver_1_1Sol.html#a37a75e15fd103a0930d39696fd863dbc">qbpp::exhaustive_solver::Sol::operator[]</a></div><div class="ttdeci">qbpp::Sol &amp; operator[](size_t i)</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__exhaustive__solver_8hpp_source.html#l00098">qbpp_exhaustive_solver.hpp:98</a></div></div>
<div class="ttc" id="aclassqbpp_1_1exhaustive__solver_1_1Sol_html_a94c59e8bc788aa2916e971e9c8fa1c76"><div class="ttname"><a href="classqbpp_1_1exhaustive__solver_1_1Sol.html#a94c59e8bc788aa2916e971e9c8fa1c76">qbpp::exhaustive_solver::Sol::is_optimal_solutions_</a></div><div class="ttdeci">bool is_optimal_solutions_</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__exhaustive__solver_8hpp_source.html#l00038">qbpp_exhaustive_solver.hpp:38</a></div></div>
<div class="ttc" id="aclassqbpp_1_1exhaustive__solver_1_1Sol_html_a86c91d760b8ae016acad899e82e96241"><div class="ttname"><a href="classqbpp_1_1exhaustive__solver_1_1Sol.html#a86c91d760b8ae016acad899e82e96241">qbpp::exhaustive_solver::Sol::str</a></div><div class="ttdeci">std::string str() const</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__exhaustive__solver_8hpp_source.html#l00072">qbpp_exhaustive_solver.hpp:72</a></div></div>
<div class="ttc" id="aclassqbpp_1_1exhaustive__solver_1_1SolDelta_html_a6f368c3a2224a71803d9ee2711effa9b"><div class="ttname"><a href="classqbpp_1_1exhaustive__solver_1_1SolDelta.html#a6f368c3a2224a71803d9ee2711effa9b">qbpp::exhaustive_solver::SolDelta::SolDelta</a></div><div class="ttdeci">SolDelta(SearchAlgorithm &amp;search_algorithm)</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__exhaustive__solver_8hpp_source.html#l00236">qbpp_exhaustive_solver.hpp:236</a></div></div>
<div class="ttc" id="aclassqbpp_1_1exhaustive__solver_1_1ExhaustiveSolver_html"><div class="ttname"><a href="classqbpp_1_1exhaustive__solver_1_1ExhaustiveSolver.html">qbpp::exhaustive_solver::ExhaustiveSolver</a></div><div class="ttdef"><b>Definition:</b> <a href="qbpp__exhaustive__solver_8hpp_source.html#l00106">qbpp_exhaustive_solver.hpp:106</a></div></div>
<div class="ttc" id="aclassqbpp_1_1exhaustive__solver_1_1ExhaustiveSolver_html_a3e90a679f7bc4eb7f63d604a4f65c7f4"><div class="ttname"><a href="classqbpp_1_1exhaustive__solver_1_1ExhaustiveSolver.html#a3e90a679f7bc4eb7f63d604a4f65c7f4">qbpp::exhaustive_solver::ExhaustiveSolver::get_quad_model</a></div><div class="ttdeci">const QuadModel &amp; get_quad_model() const</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__exhaustive__solver_8hpp_source.html#l00147">qbpp_exhaustive_solver.hpp:147</a></div></div>
<div class="ttc" id="aclassqbpp_1_1SolHolderTemplate_html_aebffa575db7ebd305230df2f5b85168f"><div class="ttname"><a href="classqbpp_1_1SolHolderTemplate.html#aebffa575db7ebd305230df2f5b85168f">qbpp::SolHolderTemplate::get_tts</a></div><div class="ttdeci">double get_tts() const</div><div class="ttdef"><b>Definition:</b> <a href="qbpp_8hpp_source.html#l02425">qbpp.hpp:2425</a></div></div>
<div class="ttc" id="aclassqbpp_1_1exhaustive__solver_1_1Sol_html_af2d151df730232c24068ca0e1b227e3c"><div class="ttname"><a href="classqbpp_1_1exhaustive__solver_1_1Sol.html#af2d151df730232c24068ca0e1b227e3c">qbpp::exhaustive_solver::Sol::is_all_solutions_</a></div><div class="ttdeci">bool is_all_solutions_</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__exhaustive__solver_8hpp_source.html#l00036">qbpp_exhaustive_solver.hpp:36</a></div></div>
<div class="ttc" id="aclassqbpp_1_1QuadModel_html_a16af851530b1f63aca9baa8ec67ed01f"><div class="ttname"><a href="classqbpp_1_1QuadModel.html#a16af851530b1f63aca9baa8ec67ed01f">qbpp::QuadModel::term_count</a></div><div class="ttdeci">size_t term_count(vindex_t deg) const</div><div class="ttdef"><b>Definition:</b> <a href="qbpp_8hpp_source.html#l01992">qbpp.hpp:1992</a></div></div>
<div class="ttc" id="aqbpp_8hpp_html"><div class="ttname"><a href="qbpp_8hpp.html">qbpp.hpp</a></div><div class="ttdoc">QUBO++, a C++ library for generating expressions for binary and spin variables.</div></div>
<div class="ttc" id="aclassqbpp_1_1Model_html_a3b0641c97573b248f1877c2826277102"><div class="ttname"><a href="classqbpp_1_1Model.html#a3b0641c97573b248f1877c2826277102">qbpp::Model::constant</a></div><div class="ttdeci">energy_t constant() const</div><div class="ttdef"><b>Definition:</b> <a href="qbpp_8hpp_source.html#l01873">qbpp.hpp:1873</a></div></div>
<div class="ttc" id="aclassqbpp_1_1exhaustive__solver_1_1SearchAlgorithm_html_a52b1739206ba81576f8261fec172db28"><div class="ttname"><a href="classqbpp_1_1exhaustive__solver_1_1SearchAlgorithm.html#a52b1739206ba81576f8261fec172db28">qbpp::exhaustive_solver::SearchAlgorithm::var_order_</a></div><div class="ttdeci">const std::vector&lt; vindex_t &gt; var_order_</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__exhaustive__solver_8hpp_source.html#l00155">qbpp_exhaustive_solver.hpp:155</a></div></div>
<div class="ttc" id="aclassqbpp_1_1SolHolderTemplate_html_a818489f254ed0c02e04f8bfa114e6ef0"><div class="ttname"><a href="classqbpp_1_1SolHolderTemplate.html#a818489f254ed0c02e04f8bfa114e6ef0">qbpp::SolHolderTemplate::energy</a></div><div class="ttdeci">const U energy() const</div><div class="ttdef"><b>Definition:</b> <a href="qbpp_8hpp_source.html#l02403">qbpp.hpp:2403</a></div></div>
<div class="ttc" id="aclassqbpp_1_1exhaustive__solver_1_1SearchAlgorithm_html_a9159dbff9bdd84bf6302770296804d9d"><div class="ttname"><a href="classqbpp_1_1exhaustive__solver_1_1SearchAlgorithm.html#a9159dbff9bdd84bf6302770296804d9d">qbpp::exhaustive_solver::SearchAlgorithm::get_quad_model</a></div><div class="ttdeci">const QuadModel &amp; get_quad_model() const</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__exhaustive__solver_8hpp_source.html#l00190">qbpp_exhaustive_solver.hpp:190</a></div></div>
<div class="ttc" id="aclassqbpp_1_1exhaustive__solver_1_1SearchAlgorithm_html_a4c70861e652f494ba0659c5bfb1bb3f7"><div class="ttname"><a href="classqbpp_1_1exhaustive__solver_1_1SearchAlgorithm.html#a4c70861e652f494ba0659c5bfb1bb3f7">qbpp::exhaustive_solver::SearchAlgorithm::init_var_order</a></div><div class="ttdeci">std::vector&lt; vindex_t &gt; init_var_order(const QuadModel &amp;quad_model)</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__exhaustive__solver_8hpp_source.html#l00169">qbpp_exhaustive_solver.hpp:169</a></div></div>
<div class="ttc" id="aclassqbpp_1_1exhaustive__solver_1_1SearchAlgorithm_html_a905a37715c53d8b96a6e5ef19b894e92"><div class="ttname"><a href="classqbpp_1_1exhaustive__solver_1_1SearchAlgorithm.html#a905a37715c53d8b96a6e5ef19b894e92">qbpp::exhaustive_solver::SearchAlgorithm::all_solutions_</a></div><div class="ttdeci">std::vector&lt; qbpp::Sol &gt; all_solutions_</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__exhaustive__solver_8hpp_source.html#l00163">qbpp_exhaustive_solver.hpp:163</a></div></div>
<div class="ttc" id="anamespaceqbpp_html_a41f8243940eb837c282aed85287bd3af"><div class="ttname"><a href="namespaceqbpp.html#a41f8243940eb837c282aed85287bd3af">qbpp::str</a></div><div class="ttdeci">std::string str(Var var)</div><div class="ttdef"><b>Definition:</b> <a href="qbpp_8hpp_source.html#l02507">qbpp.hpp:2507</a></div></div>
<div class="ttc" id="aclassqbpp_1_1Sol_html_a11752b0b3742bec8da842db87ba94230"><div class="ttname"><a href="classqbpp_1_1Sol.html#a11752b0b3742bec8da842db87ba94230">qbpp::Sol::quad_model_</a></div><div class="ttdeci">const QuadModel quad_model_</div><div class="ttdef"><b>Definition:</b> <a href="qbpp_8hpp_source.html#l02184">qbpp.hpp:2184</a></div></div>
<div class="ttc" id="aclassqbpp_1_1Sol_html_afb26ebcc595aa1391f18c2968713fdba"><div class="ttname"><a href="classqbpp_1_1Sol.html#afb26ebcc595aa1391f18c2968713fdba">qbpp::Sol::Sol</a></div><div class="ttdeci">Sol()=delete</div></div>
<div class="ttc" id="aclassqbpp_1_1exhaustive__solver_1_1SearchAlgorithm_html_af667685bb7ceb4beee261f77621afaf8"><div class="ttname"><a href="classqbpp_1_1exhaustive__solver_1_1SearchAlgorithm.html#af667685bb7ceb4beee261f77621afaf8">qbpp::exhaustive_solver::SearchAlgorithm::exhaustive_solver_</a></div><div class="ttdeci">const ExhaustiveSolver &amp; exhaustive_solver_</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__exhaustive__solver_8hpp_source.html#l00151">qbpp_exhaustive_solver.hpp:151</a></div></div>
<div class="ttc" id="aclassqbpp_1_1exhaustive__solver_1_1SearchAlgorithm_html_a0c13def2e6e74113619a0b7c65b50c10"><div class="ttname"><a href="classqbpp_1_1exhaustive__solver_1_1SearchAlgorithm.html#a0c13def2e6e74113619a0b7c65b50c10">qbpp::exhaustive_solver::SearchAlgorithm::get_sol_holder</a></div><div class="ttdeci">qbpp::SolHolder &amp; get_sol_holder()</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__exhaustive__solver_8hpp_source.html#l00198">qbpp_exhaustive_solver.hpp:198</a></div></div>
<div class="ttc" id="aclassqbpp_1_1exhaustive__solver_1_1SolDelta_html_a4ca4e09dec03e09553fc50a7d05c0778"><div class="ttname"><a href="classqbpp_1_1exhaustive__solver_1_1SolDelta.html#a4ca4e09dec03e09553fc50a7d05c0778">qbpp::exhaustive_solver::SolDelta::var_count</a></div><div class="ttdeci">vindex_t var_count() const</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__exhaustive__solver_8hpp_source.html#l00248">qbpp_exhaustive_solver.hpp:248</a></div></div>
<div class="ttc" id="aclassqbpp_1_1exhaustive__solver_1_1SolDelta_html_a926e9faffb265b7208b75663e76f77da"><div class="ttname"><a href="classqbpp_1_1exhaustive__solver_1_1SolDelta.html#a926e9faffb265b7208b75663e76f77da">qbpp::exhaustive_solver::SolDelta::search</a></div><div class="ttdeci">void search(vindex_t index)</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__exhaustive__solver_8hpp_source.html#l00269">qbpp_exhaustive_solver.hpp:269</a></div></div>
<div class="ttc" id="aclassqbpp_1_1exhaustive__solver_1_1Sol_html_a960c32dbfa04542000cbdb7d1ad4df8c"><div class="ttname"><a href="classqbpp_1_1exhaustive__solver_1_1Sol.html#a960c32dbfa04542000cbdb7d1ad4df8c">qbpp::exhaustive_solver::Sol::all_solutions_</a></div><div class="ttdeci">std::vector&lt; qbpp::Sol &gt; all_solutions_</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__exhaustive__solver_8hpp_source.html#l00040">qbpp_exhaustive_solver.hpp:40</a></div></div>
<div class="ttc" id="anamespaceqbpp_html"><div class="ttname"><a href="namespaceqbpp.html">qbpp</a></div><div class="ttdoc">Generates a QUBO Expression for the Graph Coloring Problem using QUBO++ library.</div><div class="ttdef"><b>Definition:</b> <a href="qbpp_8hpp_source.html#l00052">qbpp.hpp:52</a></div></div>
<div class="ttc" id="aclassqbpp_1_1exhaustive__solver_1_1ExhaustiveSolver_html_a639fd8773458972ddeabd24e66809d70"><div class="ttname"><a href="classqbpp_1_1exhaustive__solver_1_1ExhaustiveSolver.html#a639fd8773458972ddeabd24e66809d70">qbpp::exhaustive_solver::ExhaustiveSolver::search_all_solutions</a></div><div class="ttdeci">Sol search_all_solutions()</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__exhaustive__solver_8hpp_source.html#l00300">qbpp_exhaustive_solver.hpp:300</a></div></div>
<div class="ttc" id="aclassqbpp_1_1exhaustive__solver_1_1ExhaustiveSolver_html_a8977a8e0cfbd07fe8a4dbb6605f32474"><div class="ttname"><a href="classqbpp_1_1exhaustive__solver_1_1ExhaustiveSolver.html#a8977a8e0cfbd07fe8a4dbb6605f32474">qbpp::exhaustive_solver::ExhaustiveSolver::~ExhaustiveSolver</a></div><div class="ttdeci">virtual ~ExhaustiveSolver()=default</div></div>
<div class="ttc" id="aclassqbpp_1_1exhaustive__solver_1_1ExhaustiveSolver_html_aa619bcaa7409964046dbe993238d4c18"><div class="ttname"><a href="classqbpp_1_1exhaustive__solver_1_1ExhaustiveSolver.html#aa619bcaa7409964046dbe993238d4c18">qbpp::exhaustive_solver::ExhaustiveSolver::quad_model_</a></div><div class="ttdeci">const QuadModel quad_model_</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__exhaustive__solver_8hpp_source.html#l00108">qbpp_exhaustive_solver.hpp:108</a></div></div>
<div class="ttc" id="anamespaceqbpp_html_a5cf436100ede362797d0c0501ea50a5a"><div class="ttname"><a href="namespaceqbpp.html#a5cf436100ede362797d0c0501ea50a5a">qbpp::vindex_t</a></div><div class="ttdeci">uint32_t vindex_t</div><div class="ttdef"><b>Definition:</b> <a href="qbpp_8hpp_source.html#l00129">qbpp.hpp:129</a></div></div>
<div class="ttc" id="aclassqbpp_1_1exhaustive__solver_1_1SearchAlgorithm_html_ab1e6c2357f3c1a1ee4a61d45c73a3b55"><div class="ttname"><a href="classqbpp_1_1exhaustive__solver_1_1SearchAlgorithm.html#ab1e6c2357f3c1a1ee4a61d45c73a3b55">qbpp::exhaustive_solver::SearchAlgorithm::SearchAlgorithm</a></div><div class="ttdeci">SearchAlgorithm(const ExhaustiveSolver &amp;exhaustive_solver)</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__exhaustive__solver_8hpp_source.html#l00185">qbpp_exhaustive_solver.hpp:185</a></div></div>
<div class="ttc" id="aclassqbpp_1_1exhaustive__solver_1_1SearchAlgorithm_html_ac8874b804e5c1d9911df5c2f6cd4998b"><div class="ttname"><a href="classqbpp_1_1exhaustive__solver_1_1SearchAlgorithm.html#ac8874b804e5c1d9911df5c2f6cd4998b">qbpp::exhaustive_solver::SearchAlgorithm::sol_holder_</a></div><div class="ttdeci">qbpp::SolHolder sol_holder_</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__exhaustive__solver_8hpp_source.html#l00157">qbpp_exhaustive_solver.hpp:157</a></div></div>
<div class="ttc" id="aclassqbpp_1_1Sol_html_a3ad455870df155ffed4701ce070cf335"><div class="ttname"><a href="classqbpp_1_1Sol.html#a3ad455870df155ffed4701ce070cf335">qbpp::Sol::energy</a></div><div class="ttdeci">energy_t energy() const</div><div class="ttdef"><b>Definition:</b> <a href="qbpp_8hpp_source.html#l02299">qbpp.hpp:2299</a></div></div>
<div class="ttc" id="aclassqbpp_1_1exhaustive__solver_1_1SearchAlgorithm_html"><div class="ttname"><a href="classqbpp_1_1exhaustive__solver_1_1SearchAlgorithm.html">qbpp::exhaustive_solver::SearchAlgorithm</a></div><div class="ttdef"><b>Definition:</b> <a href="qbpp__exhaustive__solver_8hpp_source.html#l00150">qbpp_exhaustive_solver.hpp:150</a></div></div>
<div class="ttc" id="aclassqbpp_1_1exhaustive__solver_1_1SearchAlgorithm_html_a73cc42b1cd9e72aa4a56eb25354ca21e"><div class="ttname"><a href="classqbpp_1_1exhaustive__solver_1_1SearchAlgorithm.html#a73cc42b1cd9e72aa4a56eb25354ca21e">qbpp::exhaustive_solver::SearchAlgorithm::register_new_sol</a></div><div class="ttdeci">void register_new_sol(const qbpp::Sol &amp;sol)</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__exhaustive__solver_8hpp_source.html#l00200">qbpp_exhaustive_solver.hpp:200</a></div></div>
<div class="ttc" id="aclassqbpp_1_1exhaustive__solver_1_1ExhaustiveSolver_html_ae431e5a6629f8a5ea7e1ef11166e3595"><div class="ttname"><a href="classqbpp_1_1exhaustive__solver_1_1ExhaustiveSolver.html#ae431e5a6629f8a5ea7e1ef11166e3595">qbpp::exhaustive_solver::ExhaustiveSolver::enable_default_callback_</a></div><div class="ttdeci">bool enable_default_callback_</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__exhaustive__solver_8hpp_source.html#l00110">qbpp_exhaustive_solver.hpp:110</a></div></div>
<div class="ttc" id="aclassqbpp_1_1QuadModel_html_ada71ec32906c9d8c73bd25c4267bafc5"><div class="ttname"><a href="classqbpp_1_1QuadModel.html#ada71ec32906c9d8c73bd25c4267bafc5">qbpp::QuadModel::degree</a></div><div class="ttdeci">const std::vector&lt; vindex_t &gt; &amp; degree() const</div><div class="ttdef"><b>Definition:</b> <a href="qbpp_8hpp_source.html#l01969">qbpp.hpp:1969</a></div></div>
<div class="ttc" id="aclassqbpp_1_1exhaustive__solver_1_1SearchAlgorithm_html_a52fca6c665f8bb2492ce340ff84617fa"><div class="ttname"><a href="classqbpp_1_1exhaustive__solver_1_1SearchAlgorithm.html#a52fca6c665f8bb2492ce340ff84617fa">qbpp::exhaustive_solver::SearchAlgorithm::quad_model_</a></div><div class="ttdeci">const QuadModel &amp; quad_model_</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__exhaustive__solver_8hpp_source.html#l00153">qbpp_exhaustive_solver.hpp:153</a></div></div>
<div class="ttc" id="aclassqbpp_1_1exhaustive__solver_1_1Sol_html_a14bfd7576baad77821d830e8fb638442"><div class="ttname"><a href="classqbpp_1_1exhaustive__solver_1_1Sol.html#a14bfd7576baad77821d830e8fb638442">qbpp::exhaustive_solver::Sol::size</a></div><div class="ttdeci">size_t size() const</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__exhaustive__solver_8hpp_source.html#l00094">qbpp_exhaustive_solver.hpp:94</a></div></div>
<div class="ttc" id="aclassqbpp_1_1exhaustive__solver_1_1SearchAlgorithm_html_a61cba268dd5876ce4c474c219d17410a"><div class="ttname"><a href="classqbpp_1_1exhaustive__solver_1_1SearchAlgorithm.html#a61cba268dd5876ce4c474c219d17410a">qbpp::exhaustive_solver::SearchAlgorithm::search_all_solutions</a></div><div class="ttdeci">void search_all_solutions()</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__exhaustive__solver_8hpp_source.html#l00351">qbpp_exhaustive_solver.hpp:351</a></div></div>
<div class="ttc" id="aclassqbpp_1_1exhaustive__solver_1_1SearchAlgorithm_html_ae29feffc0f9bef93caf80bb05b114728"><div class="ttname"><a href="classqbpp_1_1exhaustive__solver_1_1SearchAlgorithm.html#ae29feffc0f9bef93caf80bb05b114728">qbpp::exhaustive_solver::SearchAlgorithm::search_optimal_solutions</a></div><div class="ttdeci">void search_optimal_solutions()</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__exhaustive__solver_8hpp_source.html#l00345">qbpp_exhaustive_solver.hpp:345</a></div></div>
<div class="ttc" id="aclassqbpp_1_1Sol_html_a55ecdaaa939441fb5dbf2f00b4f3d9e0"><div class="ttname"><a href="classqbpp_1_1Sol.html#a55ecdaaa939441fb5dbf2f00b4f3d9e0">qbpp::Sol::operator=</a></div><div class="ttdeci">Sol &amp; operator=(const Sol &amp;sol)</div><div class="ttdef"><b>Definition:</b> <a href="qbpp_8hpp_source.html#l02212">qbpp.hpp:2212</a></div></div>
<div class="ttc" id="aclassqbpp_1_1Sol_html"><div class="ttname"><a href="classqbpp_1_1Sol.html">qbpp::Sol</a></div><div class="ttdef"><b>Definition:</b> <a href="qbpp_8hpp_source.html#l02182">qbpp.hpp:2182</a></div></div>
<div class="ttc" id="aclassqbpp_1_1Sol_html_a24f5a53fa68071dd9bcda910533bbad7"><div class="ttname"><a href="classqbpp_1_1Sol.html#a24f5a53fa68071dd9bcda910533bbad7">qbpp::Sol::index</a></div><div class="ttdeci">vindex_t index(Var var) const</div><div class="ttdef"><b>Definition:</b> <a href="qbpp_8hpp_source.html#l02325">qbpp.hpp:2325</a></div></div>
<div class="ttc" id="aclassqbpp_1_1exhaustive__solver_1_1ExhaustiveSolver_html_af7aafe9b8c518b36548741f09bb25f90"><div class="ttname"><a href="classqbpp_1_1exhaustive__solver_1_1ExhaustiveSolver.html#af7aafe9b8c518b36548741f09bb25f90">qbpp::exhaustive_solver::ExhaustiveSolver::callback</a></div><div class="ttdeci">virtual void callback(const SolHolder &amp;sol_holder) const</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__exhaustive__solver_8hpp_source.html#l00120">qbpp_exhaustive_solver.hpp:120</a></div></div>
<div class="ttc" id="aqbpp_8hpp_html_a265e0828fcea8e75fbc9c0be99f7156e"><div class="ttname"><a href="qbpp_8hpp.html#a265e0828fcea8e75fbc9c0be99f7156e">THROW_MESSAGE</a></div><div class="ttdeci">#define THROW_MESSAGE(...)</div><div class="ttdef"><b>Definition:</b> <a href="qbpp_8hpp_source.html#l00091">qbpp.hpp:91</a></div></div>
<div class="ttc" id="aclassqbpp_1_1SolHolderTemplate_html"><div class="ttname"><a href="classqbpp_1_1SolHolderTemplate.html">qbpp::SolHolderTemplate&lt; Sol, energy_t &gt;</a></div></div>
<div class="ttc" id="aclassqbpp_1_1exhaustive__solver_1_1Sol_html_aa4bb85bf194586a7cbef21510d796a2f"><div class="ttname"><a href="classqbpp_1_1exhaustive__solver_1_1Sol.html#aa4bb85bf194586a7cbef21510d796a2f">qbpp::exhaustive_solver::Sol::operator=</a></div><div class="ttdeci">Sol &amp; operator=(const Sol &amp;)=default</div></div>
<div class="ttc" id="aclassqbpp_1_1exhaustive__solver_1_1ExhaustiveSolver_html_ab5bd307bb7b5a24b598a46b592df2954"><div class="ttname"><a href="classqbpp_1_1exhaustive__solver_1_1ExhaustiveSolver.html#ab5bd307bb7b5a24b598a46b592df2954">qbpp::exhaustive_solver::ExhaustiveSolver::callback_mutex_</a></div><div class="ttdeci">std::mutex callback_mutex_</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__exhaustive__solver_8hpp_source.html#l00116">qbpp_exhaustive_solver.hpp:116</a></div></div>
<div class="ttc" id="aclassqbpp_1_1exhaustive__solver_1_1SolDelta_html_a643cfd8c8e7e0047cbdfed82c97fc4c8"><div class="ttname"><a href="classqbpp_1_1exhaustive__solver_1_1SolDelta.html#a643cfd8c8e7e0047cbdfed82c97fc4c8">qbpp::exhaustive_solver::SolDelta::var_order_</a></div><div class="ttdeci">const std::vector&lt; vindex_t &gt; &amp; var_order_</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__exhaustive__solver_8hpp_source.html#l00231">qbpp_exhaustive_solver.hpp:231</a></div></div>
<div class="ttc" id="aclassqbpp_1_1exhaustive__solver_1_1Sol_html_a27015c742388744bf513617e78e129f2"><div class="ttname"><a href="classqbpp_1_1exhaustive__solver_1_1Sol.html#a27015c742388744bf513617e78e129f2">qbpp::exhaustive_solver::Sol::all_solution_mode</a></div><div class="ttdeci">void all_solution_mode()</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__exhaustive__solver_8hpp_source.html#l00057">qbpp_exhaustive_solver.hpp:57</a></div></div>
<div class="ttc" id="aclassqbpp_1_1exhaustive__solver_1_1Sol_html_aeadf2109631b665db6dfed78bc99cf97"><div class="ttname"><a href="classqbpp_1_1exhaustive__solver_1_1Sol.html#aeadf2109631b665db6dfed78bc99cf97">qbpp::exhaustive_solver::Sol::get_all_solutions</a></div><div class="ttdeci">std::vector&lt; qbpp::Sol &gt; &amp; get_all_solutions()</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__exhaustive__solver_8hpp_source.html#l00053">qbpp_exhaustive_solver.hpp:53</a></div></div>
<div class="ttc" id="aclassqbpp_1_1exhaustive__solver_1_1Sol_html_ab0722463fa799d5892594aada8e2ec4a"><div class="ttname"><a href="classqbpp_1_1exhaustive__solver_1_1Sol.html#ab0722463fa799d5892594aada8e2ec4a">qbpp::exhaustive_solver::Sol::Sol</a></div><div class="ttdeci">Sol(const QuadModel &amp;quad_model)</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__exhaustive__solver_8hpp_source.html#l00043">qbpp_exhaustive_solver.hpp:43</a></div></div>
<div class="ttc" id="aclassqbpp_1_1exhaustive__solver_1_1ExhaustiveSolver_html_a1c26cfdda7d9d8f854170c6430a55e57"><div class="ttname"><a href="classqbpp_1_1exhaustive__solver_1_1ExhaustiveSolver.html#a1c26cfdda7d9d8f854170c6430a55e57">qbpp::exhaustive_solver::ExhaustiveSolver::search_optimal_solutions</a></div><div class="ttdeci">Sol search_optimal_solutions()</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__exhaustive__solver_8hpp_source.html#l00291">qbpp_exhaustive_solver.hpp:291</a></div></div>
<div class="ttc" id="aclassqbpp_1_1Model_html_a5cf2bb955c3aa25951ca499d7d8635d2"><div class="ttname"><a href="classqbpp_1_1Model.html#a5cf2bb955c3aa25951ca499d7d8635d2">qbpp::Model::var_count</a></div><div class="ttdeci">vindex_t var_count() const</div><div class="ttdef"><b>Definition:</b> <a href="qbpp_8hpp_source.html#l01859">qbpp.hpp:1859</a></div></div>
<div class="ttc" id="aclassqbpp_1_1exhaustive__solver_1_1Sol_html_aaef7f68441965cb36da531de6e08e648"><div class="ttname"><a href="classqbpp_1_1exhaustive__solver_1_1Sol.html#aaef7f68441965cb36da531de6e08e648">qbpp::exhaustive_solver::Sol::get_sol</a></div><div class="ttdeci">qbpp::Sol get_sol() const</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__exhaustive__solver_8hpp_source.html#l00055">qbpp_exhaustive_solver.hpp:55</a></div></div>
<div class="ttc" id="aclassqbpp_1_1exhaustive__solver_1_1ExhaustiveSolver_html_a48bf01e66a37bbec332902ecfb2f0d1d"><div class="ttname"><a href="classqbpp_1_1exhaustive__solver_1_1ExhaustiveSolver.html#a48bf01e66a37bbec332902ecfb2f0d1d">qbpp::exhaustive_solver::ExhaustiveSolver::enable_default_callback</a></div><div class="ttdeci">void enable_default_callback(bool enable=true)</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__exhaustive__solver_8hpp_source.html#l00142">qbpp_exhaustive_solver.hpp:142</a></div></div>
<div class="ttc" id="aclassqbpp_1_1exhaustive__solver_1_1SearchAlgorithm_html_ad9287726921c354099815b6e8ec9bb4d"><div class="ttname"><a href="classqbpp_1_1exhaustive__solver_1_1SearchAlgorithm.html#ad9287726921c354099815b6e8ec9bb4d">qbpp::exhaustive_solver::SearchAlgorithm::sol_deltas</a></div><div class="ttdeci">std::vector&lt; SolDelta &gt; sol_deltas</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__exhaustive__solver_8hpp_source.html#l00167">qbpp_exhaustive_solver.hpp:167</a></div></div>
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.17
</small></address>
</body>
</html>
