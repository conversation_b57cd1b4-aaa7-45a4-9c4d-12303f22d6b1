<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.17"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>QUBO++ Library with QUBO Solver APIs: sample/tsp_grb.cpp Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/x-mathjax-config">
  MathJax.Hub.Config({
    extensions: ["tex2jax.js", "TeX/AMSmath.js", "TeX/AMSsymbols.js"],
    jax: ["input/TeX","output/HTML-CSS"],
});
</script>
<script type="text/javascript" async="async" src="https://cdn.jsdelivr.net/npm/mathjax@2/MathJax.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">QUBO++ Library with QUBO Solver APIs
   </div>
   <div id="projectbrief">Author: Koji Nakano, License: Non-commercial research and evaluation purposes without any guarantees.</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.17 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_c29eeb5af533606caea8a09e66794740.html">sample</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">tsp_grb.cpp</div>  </div>
</div><!--header-->
<div class="contents">
<a href="tsp__grb_8cpp.html">Go to the documentation of this file.</a><div class="fragment"><div class="line"><a name="l00001"></a><span class="lineno">    1</span>&#160; </div>
<div class="line"><a name="l00007"></a><span class="lineno">    7</span>&#160;<span class="preprocessor">#include &lt;boost/program_options.hpp&gt;</span></div>
<div class="line"><a name="l00008"></a><span class="lineno">    8</span>&#160; </div>
<div class="line"><a name="l00009"></a><span class="lineno">    9</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="qbpp_8hpp.html">qbpp.hpp</a>&quot;</span></div>
<div class="line"><a name="l00010"></a><span class="lineno">   10</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="qbpp__grb_8hpp.html">qbpp_grb.hpp</a>&quot;</span></div>
<div class="line"><a name="l00011"></a><span class="lineno">   11</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="qbpp__misc_8hpp.html">qbpp_misc.hpp</a>&quot;</span></div>
<div class="line"><a name="l00012"></a><span class="lineno">   12</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="qbpp__tsp_8hpp.html">qbpp_tsp.hpp</a>&quot;</span></div>
<div class="line"><a name="l00013"></a><span class="lineno">   13</span>&#160; </div>
<div class="line"><a name="l00014"></a><span class="lineno">   14</span>&#160;<span class="keyword">namespace </span>po = boost::program_options;</div>
<div class="line"><a name="l00015"></a><span class="lineno">   15</span>&#160; </div>
<div class="line"><a name="l00018"></a><span class="lineno"><a class="line" href="classGRB__Callback.html">   18</a></span>&#160;<span class="keyword">class </span><a class="code" href="classGRB__Callback.html">GRB_Callback</a> : <span class="keyword">public</span> <a class="code" href="classqbpp__grb_1_1Callback.html">qbpp_grb::Callback</a> {</div>
<div class="line"><a name="l00019"></a><span class="lineno"><a class="line" href="classGRB__Callback.html#a12eb524eb639f63bfea8f59c750f9035">   19</a></span>&#160;  <span class="keyword">const</span> <a class="code" href="classqbpp_1_1tsp_1_1TSPQuadModel.html">qbpp::tsp::TSPQuadModel</a> &amp;<a class="code" href="classGRB__Callback.html#a12eb524eb639f63bfea8f59c750f9035">tsp_quad_model</a>;</div>
<div class="line"><a name="l00020"></a><span class="lineno"><a class="line" href="classGRB__Callback.html#a11672ce1bfd6aedb900b3dc581fa9d9d">   20</a></span>&#160;  std::optional&lt;qbpp::energy_t&gt; <a class="code" href="classGRB__Callback.html#a11672ce1bfd6aedb900b3dc581fa9d9d">target_energy</a>;</div>
<div class="line"><a name="l00021"></a><span class="lineno">   21</span>&#160; </div>
<div class="line"><a name="l00022"></a><span class="lineno">   22</span>&#160; <span class="keyword">public</span>:</div>
<div class="line"><a name="l00026"></a><span class="lineno"><a class="line" href="classGRB__Callback.html#a40edec18aefc56b371ba02d33a8713c3">   26</a></span>&#160;  <a class="code" href="classGRB__Callback.html#a40edec18aefc56b371ba02d33a8713c3">GRB_Callback</a>(<span class="keyword">const</span> <a class="code" href="classqbpp_1_1tsp_1_1TSPQuadModel.html">qbpp::tsp::TSPQuadModel</a> &amp;<a class="code" href="classGRB__Callback.html#a12eb524eb639f63bfea8f59c750f9035">tsp_quad_model</a>,</div>
<div class="line"><a name="l00027"></a><span class="lineno">   27</span>&#160;               <a class="code" href="namespaceqbpp.html#a9c324f641779d1a7a3c7723f28d8cff4">qbpp::energy_t</a> <a class="code" href="classGRB__Callback.html#a11672ce1bfd6aedb900b3dc581fa9d9d">target_energy</a>)</div>
<div class="line"><a name="l00028"></a><span class="lineno">   28</span>&#160;      : <a class="code" href="namespaceqbpp__grb.html">qbpp_grb</a>::<a class="code" href="classqbpp__grb_1_1Callback.html#a91592303df972ec49c2279459a681f05">Callback</a>(<a class="code" href="classGRB__Callback.html#a12eb524eb639f63bfea8f59c750f9035">tsp_quad_model</a>),</div>
<div class="line"><a name="l00029"></a><span class="lineno">   29</span>&#160;        <a class="code" href="classGRB__Callback.html#a12eb524eb639f63bfea8f59c750f9035">tsp_quad_model</a>(<a class="code" href="classGRB__Callback.html#a12eb524eb639f63bfea8f59c750f9035">tsp_quad_model</a>),</div>
<div class="line"><a name="l00030"></a><span class="lineno">   30</span>&#160;        <a class="code" href="classGRB__Callback.html#a11672ce1bfd6aedb900b3dc581fa9d9d">target_energy</a>(<a class="code" href="classGRB__Callback.html#a11672ce1bfd6aedb900b3dc581fa9d9d">target_energy</a>) {}</div>
<div class="line"><a name="l00031"></a><span class="lineno">   31</span>&#160; </div>
<div class="line"><a name="l00036"></a><span class="lineno"><a class="line" href="classGRB__Callback.html#ac08cbd22e13d589be7ca35178d3126d0">   36</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="classGRB__Callback.html#ac08cbd22e13d589be7ca35178d3126d0">callback</a>()<span class="keyword"> override </span>{</div>
<div class="line"><a name="l00037"></a><span class="lineno">   37</span>&#160;    <span class="keywordflow">if</span> (where == GRB_CB_MIPSOL) {</div>
<div class="line"><a name="l00038"></a><span class="lineno">   38</span>&#160;      <span class="keyword">auto</span> sol = <a class="code" href="classqbpp__grb_1_1Callback.html#a6d111191d65a98194fec8779fac6dadd">get_sol</a>();</div>
<div class="line"><a name="l00039"></a><span class="lineno">   39</span>&#160;      <a class="code" href="classqbpp_1_1tsp_1_1TSPSol.html">qbpp::tsp::TSPSol</a> tsp_sol(<a class="code" href="classGRB__Callback.html#a12eb524eb639f63bfea8f59c750f9035">tsp_quad_model</a>, sol);</div>
<div class="line"><a name="l00040"></a><span class="lineno">   40</span>&#160;      std::cout &lt;&lt; <span class="stringliteral">&quot;TTS = &quot;</span> &lt;&lt; std::fixed &lt;&lt; std::setprecision(3)</div>
<div class="line"><a name="l00041"></a><span class="lineno">   41</span>&#160;                &lt;&lt; std::setfill(<span class="charliteral">&#39;0&#39;</span>) &lt;&lt; <a class="code" href="namespaceqbpp.html#a4184afa358539dbcf5f35e63b49ce05b">qbpp::get_time</a>() &lt;&lt; <span class="stringliteral">&quot;s &quot;</span>;</div>
<div class="line"><a name="l00042"></a><span class="lineno">   42</span>&#160;      tsp_sol.<a class="code" href="classqbpp_1_1tsp_1_1TSPSol.html#ab2abe5283ba0f9fefad71b1beee340e3">print</a>();</div>
<div class="line"><a name="l00043"></a><span class="lineno">   43</span>&#160;      <span class="keywordflow">if</span> (<a class="code" href="classGRB__Callback.html#a11672ce1bfd6aedb900b3dc581fa9d9d">target_energy</a>.has_value() &amp;&amp; sol.energy() &lt;= <a class="code" href="classGRB__Callback.html#a11672ce1bfd6aedb900b3dc581fa9d9d">target_energy</a>) {</div>
<div class="line"><a name="l00044"></a><span class="lineno">   44</span>&#160;        abort();</div>
<div class="line"><a name="l00045"></a><span class="lineno">   45</span>&#160;      }</div>
<div class="line"><a name="l00046"></a><span class="lineno">   46</span>&#160;    }</div>
<div class="line"><a name="l00047"></a><span class="lineno">   47</span>&#160;  }</div>
<div class="line"><a name="l00048"></a><span class="lineno">   48</span>&#160;};</div>
<div class="line"><a name="l00049"></a><span class="lineno">   49</span>&#160; </div>
<div class="line"><a name="l00056"></a><span class="lineno"><a class="line" href="tsp__grb_8cpp.html#a3c04138a5bfe5d72780bb7e82a18e627">   56</a></span>&#160;<span class="keywordtype">int</span> <a class="code" href="tsp__grb_8cpp.html#a3c04138a5bfe5d72780bb7e82a18e627">main</a>(<span class="keywordtype">int</span> argc, <span class="keywordtype">char</span> **argv) {</div>
<div class="line"><a name="l00057"></a><span class="lineno">   57</span>&#160;  <span class="comment">// clang-format off</span></div>
<div class="line"><a name="l00058"></a><span class="lineno">   58</span>&#160;  po::options_description desc(</div>
<div class="line"><a name="l00059"></a><span class="lineno">   59</span>&#160;      <span class="stringliteral">&quot;Solving the randomly generated TSP using QUBO++ Easy Solver&quot;</span>);</div>
<div class="line"><a name="l00060"></a><span class="lineno">   60</span>&#160;  desc.add_options()(<span class="stringliteral">&quot;help,h&quot;</span>, <span class="stringliteral">&quot;produce help message&quot;</span>)</div>
<div class="line"><a name="l00061"></a><span class="lineno">   61</span>&#160;     (<span class="stringliteral">&quot;nodes,n&quot;</span>, po::value&lt;uint32_t&gt;()-&gt;default_value(10),<span class="stringliteral">&quot;set the number of nodes in the TSP map&quot;</span>)</div>
<div class="line"><a name="l00062"></a><span class="lineno">   62</span>&#160;     (<span class="stringliteral">&quot;time,t&quot;</span>, po::value&lt;uint32_t&gt;()-&gt;default_value(10), <span class="stringliteral">&quot;set time limit in seconds&quot;</span>)</div>
<div class="line"><a name="l00063"></a><span class="lineno">   63</span>&#160;     (<span class="stringliteral">&quot;tsp_seed,s&quot;</span>, po::value&lt;uint32_t&gt;(), <span class="stringliteral">&quot;set the random seed for the TSP map&quot;</span>)</div>
<div class="line"><a name="l00064"></a><span class="lineno">   64</span>&#160;     (<span class="stringliteral">&quot;output,o&quot;</span>, po::value&lt;std::string&gt;(), <span class="stringliteral">&quot;set the output file (png, svg, etc) to save the TSP solution&quot;</span>)</div>
<div class="line"><a name="l00065"></a><span class="lineno">   65</span>&#160;     (<span class="stringliteral">&quot;fix,f&quot;</span>, <span class="stringliteral">&quot;fix node 0 as the starting node&quot;</span>);</div>
<div class="line"><a name="l00066"></a><span class="lineno">   66</span>&#160;  <span class="comment">// clang-format on</span></div>
<div class="line"><a name="l00067"></a><span class="lineno">   67</span>&#160; </div>
<div class="line"><a name="l00068"></a><span class="lineno">   68</span>&#160;  po::variables_map vm;</div>
<div class="line"><a name="l00069"></a><span class="lineno">   69</span>&#160;  <span class="keywordflow">try</span> {</div>
<div class="line"><a name="l00070"></a><span class="lineno">   70</span>&#160;    po::store(po::parse_command_line(argc, argv, desc), vm);</div>
<div class="line"><a name="l00071"></a><span class="lineno">   71</span>&#160;  } <span class="keywordflow">catch</span> (<span class="keyword">const</span> std::exception &amp;e) {</div>
<div class="line"><a name="l00072"></a><span class="lineno">   72</span>&#160;    std::cout &lt;&lt; <span class="stringliteral">&quot;Wrong arguments. Please use -h/--help option to see the &quot;</span></div>
<div class="line"><a name="l00073"></a><span class="lineno">   73</span>&#160;                 <span class="stringliteral">&quot;usage.\n&quot;</span>;</div>
<div class="line"><a name="l00074"></a><span class="lineno">   74</span>&#160;    <span class="keywordflow">return</span> 1;</div>
<div class="line"><a name="l00075"></a><span class="lineno">   75</span>&#160;  }</div>
<div class="line"><a name="l00076"></a><span class="lineno">   76</span>&#160;  po::notify(vm);</div>
<div class="line"><a name="l00077"></a><span class="lineno">   77</span>&#160; </div>
<div class="line"><a name="l00078"></a><span class="lineno">   78</span>&#160;  <span class="keywordflow">if</span> (vm.count(<span class="stringliteral">&quot;help&quot;</span>)) {</div>
<div class="line"><a name="l00079"></a><span class="lineno">   79</span>&#160;    std::cout &lt;&lt; desc &lt;&lt; std::endl;</div>
<div class="line"><a name="l00080"></a><span class="lineno">   80</span>&#160;    <span class="keywordflow">return</span> 0;</div>
<div class="line"><a name="l00081"></a><span class="lineno">   81</span>&#160;  }</div>
<div class="line"><a name="l00082"></a><span class="lineno">   82</span>&#160; </div>
<div class="line"><a name="l00084"></a><span class="lineno">   84</span>&#160;  uint32_t nodes = vm[<span class="stringliteral">&quot;nodes&quot;</span>].as&lt;uint32_t&gt;();</div>
<div class="line"><a name="l00086"></a><span class="lineno">   86</span>&#160;  uint32_t time_limit = vm[<span class="stringliteral">&quot;time&quot;</span>].as&lt;uint32_t&gt;();</div>
<div class="line"><a name="l00088"></a><span class="lineno">   88</span>&#160;  <span class="keywordtype">bool</span> fix_first = vm.count(<span class="stringliteral">&quot;fix&quot;</span>);</div>
<div class="line"><a name="l00089"></a><span class="lineno">   89</span>&#160; </div>
<div class="line"><a name="l00090"></a><span class="lineno">   90</span>&#160;  <span class="comment">// Set the random seed for deterministic behavior if seed is provided.</span></div>
<div class="line"><a name="l00091"></a><span class="lineno">   91</span>&#160;  <span class="keywordflow">if</span> (vm.count(<span class="stringliteral">&quot;tsp_seed&quot;</span>)) {</div>
<div class="line"><a name="l00092"></a><span class="lineno">   92</span>&#160;    <a class="code" href="classqbpp_1_1misc_1_1RandomGenerator.html#a1f4f3d675aee33dce174c8222c3bc22a">qbpp::misc::RandomGenerator::set_seed</a>(vm[<span class="stringliteral">&quot;tsp_seed&quot;</span>].as&lt;uint32_t&gt;());</div>
<div class="line"><a name="l00093"></a><span class="lineno">   93</span>&#160;  } <span class="keywordflow">else</span> {</div>
<div class="line"><a name="l00094"></a><span class="lineno">   94</span>&#160;    <a class="code" href="classqbpp_1_1misc_1_1RandomGenerator.html#a513a93d293d9fa08a7fee2bba9cb73ff">qbpp::misc::RandomGenerator::rd_seed</a>();</div>
<div class="line"><a name="l00095"></a><span class="lineno">   95</span>&#160;  }</div>
<div class="line"><a name="l00096"></a><span class="lineno">   96</span>&#160; </div>
<div class="line"><a name="l00097"></a><span class="lineno">   97</span>&#160;  std::cout &lt;&lt; <span class="stringliteral">&quot;Generating random TSP Map with &quot;</span> &lt;&lt; nodes &lt;&lt; <span class="stringliteral">&quot; nodes&quot;</span></div>
<div class="line"><a name="l00098"></a><span class="lineno">   98</span>&#160;            &lt;&lt; std::endl;</div>
<div class="line"><a name="l00099"></a><span class="lineno">   99</span>&#160;  <a class="code" href="classqbpp_1_1tsp_1_1TSPMap.html">qbpp::tsp::TSPMap</a> tsp_map;</div>
<div class="line"><a name="l00100"></a><span class="lineno">  100</span>&#160;  tsp_map.<a class="code" href="classqbpp_1_1tsp_1_1TSPMap.html#ace1b4039b9447fae50b62c325acefc6e">gen_random_map</a>(nodes);</div>
<div class="line"><a name="l00101"></a><span class="lineno">  101</span>&#160; </div>
<div class="line"><a name="l00102"></a><span class="lineno">  102</span>&#160;  std::cout &lt;&lt; <span class="stringliteral">&quot;Generating a TSP QUBO model&quot;</span> &lt;&lt; std::endl;</div>
<div class="line"><a name="l00103"></a><span class="lineno">  103</span>&#160;  <a class="code" href="classqbpp_1_1tsp_1_1TSPQuadModel.html">qbpp::tsp::TSPQuadModel</a> tsp_quad_model(tsp_map, fix_first);</div>
<div class="line"><a name="l00104"></a><span class="lineno">  104</span>&#160; </div>
<div class="line"><a name="l00105"></a><span class="lineno">  105</span>&#160;  std::cout &lt;&lt; <span class="stringliteral">&quot;Variables = &quot;</span> &lt;&lt; tsp_quad_model.<a class="code" href="classqbpp_1_1Model.html#a5cf2bb955c3aa25951ca499d7d8635d2">var_count</a>()</div>
<div class="line"><a name="l00106"></a><span class="lineno">  106</span>&#160;            &lt;&lt; <span class="stringliteral">&quot; Linear Terms = &quot;</span> &lt;&lt; tsp_quad_model.<a class="code" href="classqbpp_1_1QuadModel.html#a16af851530b1f63aca9baa8ec67ed01f">term_count</a>(1)</div>
<div class="line"><a name="l00107"></a><span class="lineno">  107</span>&#160;            &lt;&lt; <span class="stringliteral">&quot; Quadratic Terms = &quot;</span> &lt;&lt; tsp_quad_model.<a class="code" href="classqbpp_1_1QuadModel.html#a16af851530b1f63aca9baa8ec67ed01f">term_count</a>(2)</div>
<div class="line"><a name="l00108"></a><span class="lineno">  108</span>&#160;            &lt;&lt; std::endl;</div>
<div class="line"><a name="l00109"></a><span class="lineno">  109</span>&#160; </div>
<div class="line"><a name="l00110"></a><span class="lineno">  110</span>&#160;  <a class="code" href="classqbpp__grb_1_1QuadModel.html">qbpp_grb::QuadModel</a> grb_model(tsp_quad_model);</div>
<div class="line"><a name="l00111"></a><span class="lineno">  111</span>&#160;  grb_model.<a class="code" href="group__qbpp__grb.html#ga3e57391fc84e3e58a8fee50b0e511af1">set_time_limit</a>(time_limit);</div>
<div class="line"><a name="l00112"></a><span class="lineno">  112</span>&#160;  <a class="code" href="classGRB__Callback.html">GRB_Callback</a> grb_callback(tsp_quad_model, 0);</div>
<div class="line"><a name="l00113"></a><span class="lineno">  113</span>&#160;  grb_model.<a class="code" href="group__qbpp__grb.html#ga896e8acb781305f1539e390d360a12de">set</a>(grb_callback);</div>
<div class="line"><a name="l00114"></a><span class="lineno">  114</span>&#160; </div>
<div class="line"><a name="l00115"></a><span class="lineno">  115</span>&#160;  std::cout &lt;&lt; <span class="stringliteral">&quot;Solving the TSP&quot;</span> &lt;&lt; std::endl;</div>
<div class="line"><a name="l00116"></a><span class="lineno">  116</span>&#160;  <span class="keyword">auto</span> sol = grb_model.<a class="code" href="classqbpp__grb_1_1QuadModel.html#ad3add2ab2e0d80e5d0d8123deebc6e98">optimize</a>();</div>
<div class="line"><a name="l00117"></a><span class="lineno">  117</span>&#160; </div>
<div class="line"><a name="l00118"></a><span class="lineno">  118</span>&#160;  <a class="code" href="classqbpp_1_1tsp_1_1TSPSol.html">qbpp::tsp::TSPSol</a> tsp_sol(tsp_quad_model, sol);</div>
<div class="line"><a name="l00119"></a><span class="lineno">  119</span>&#160;  tsp_sol.<a class="code" href="classqbpp_1_1tsp_1_1TSPSol.html#ab2abe5283ba0f9fefad71b1beee340e3">print</a>();</div>
<div class="line"><a name="l00120"></a><span class="lineno">  120</span>&#160; </div>
<div class="line"><a name="l00121"></a><span class="lineno">  121</span>&#160;  <span class="keywordflow">if</span> (vm.count(<span class="stringliteral">&quot;output&quot;</span>)) {</div>
<div class="line"><a name="l00122"></a><span class="lineno">  122</span>&#160;    <a class="code" href="classqbpp_1_1tsp_1_1DrawSimpleGraph.html">qbpp::tsp::DrawSimpleGraph</a> graph;</div>
<div class="line"><a name="l00123"></a><span class="lineno">  123</span>&#160;    <span class="keywordflow">for</span> (uint32_t i = 0; i &lt; nodes; ++i) graph.<a class="code" href="classqbpp_1_1tsp_1_1DrawSimpleGraph.html#ae58cb44d06b764de7fae386abf86c0de">add_node</a>(tsp_map[i]);</div>
<div class="line"><a name="l00124"></a><span class="lineno">  124</span>&#160; </div>
<div class="line"><a name="l00125"></a><span class="lineno">  125</span>&#160;    <span class="keywordflow">for</span> (uint32_t i = 0; i &lt; nodes; ++i)</div>
<div class="line"><a name="l00126"></a><span class="lineno">  126</span>&#160;      graph.<a class="code" href="classqbpp_1_1tsp_1_1DrawSimpleGraph.html#a99955897a283a208c4093d4f747491d5">add_edge</a>(tsp_sol[i], tsp_sol[(i + 1) % nodes]);</div>
<div class="line"><a name="l00127"></a><span class="lineno">  127</span>&#160; </div>
<div class="line"><a name="l00128"></a><span class="lineno">  128</span>&#160;    graph.<a class="code" href="classqbpp_1_1tsp_1_1DrawSimpleGraph.html#ae4a6e9be4fa3e068d2dd4a4ace7548b5">draw</a>(vm[<span class="stringliteral">&quot;output&quot;</span>].as&lt;std::string&gt;());</div>
<div class="line"><a name="l00129"></a><span class="lineno">  129</span>&#160;  }</div>
<div class="line"><a name="l00130"></a><span class="lineno">  130</span>&#160;}</div>
</div><!-- fragment --></div><!-- contents -->
<div class="ttc" id="aclassGRB__Callback_html_a11672ce1bfd6aedb900b3dc581fa9d9d"><div class="ttname"><a href="classGRB__Callback.html#a11672ce1bfd6aedb900b3dc581fa9d9d">GRB_Callback::target_energy</a></div><div class="ttdeci">std::optional&lt; qbpp::energy_t &gt; target_energy</div><div class="ttdef"><b>Definition:</b> <a href="tsp__grb_8cpp_source.html#l00020">tsp_grb.cpp:20</a></div></div>
<div class="ttc" id="aclassqbpp_1_1tsp_1_1DrawSimpleGraph_html_a99955897a283a208c4093d4f747491d5"><div class="ttname"><a href="classqbpp_1_1tsp_1_1DrawSimpleGraph.html#a99955897a283a208c4093d4f747491d5">qbpp::tsp::DrawSimpleGraph::add_edge</a></div><div class="ttdeci">void add_edge(unsigned int node1, unsigned int node2)</div><div class="ttdoc">Add an edge to the graph.</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__tsp_8hpp_source.html#l00273">qbpp_tsp.hpp:273</a></div></div>
<div class="ttc" id="aclassqbpp__grb_1_1Callback_html_a6d111191d65a98194fec8779fac6dadd"><div class="ttname"><a href="classqbpp__grb_1_1Callback.html#a6d111191d65a98194fec8779fac6dadd">qbpp_grb::Callback::get_sol</a></div><div class="ttdeci">Sol get_sol()</div><div class="ttdoc">Get the solution obtained by Gurobi Optimizer.</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__grb_8hpp_source.html#l00271">qbpp_grb.hpp:271</a></div></div>
<div class="ttc" id="agroup__qbpp__grb_html_ga896e8acb781305f1539e390d360a12de"><div class="ttname"><a href="group__qbpp__grb.html#ga896e8acb781305f1539e390d360a12de">qbpp_grb::QuadModel::set</a></div><div class="ttdeci">void set(const std::string &amp;key, const std::string &amp;val)</div><div class="ttdoc">Sets a parameter to the Gurobi environment.</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__grb_8hpp_source.html#l00070">qbpp_grb.hpp:70</a></div></div>
<div class="ttc" id="anamespaceqbpp_html_a9c324f641779d1a7a3c7723f28d8cff4"><div class="ttname"><a href="namespaceqbpp.html#a9c324f641779d1a7a3c7723f28d8cff4">qbpp::energy_t</a></div><div class="ttdeci">ENERGY_TYPE energy_t</div><div class="ttdef"><b>Definition:</b> <a href="qbpp_8hpp_source.html#l00137">qbpp.hpp:137</a></div></div>
<div class="ttc" id="aclassqbpp_1_1tsp_1_1TSPSol_html_ab2abe5283ba0f9fefad71b1beee340e3"><div class="ttname"><a href="classqbpp_1_1tsp_1_1TSPSol.html#ab2abe5283ba0f9fefad71b1beee340e3">qbpp::tsp::TSPSol::print</a></div><div class="ttdeci">void print() const</div><div class="ttdoc">Print the tour.</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__tsp_8hpp_source.html#l00176">qbpp_tsp.hpp:176</a></div></div>
<div class="ttc" id="agroup__qbpp__grb_html_ga3e57391fc84e3e58a8fee50b0e511af1"><div class="ttname"><a href="group__qbpp__grb.html#ga3e57391fc84e3e58a8fee50b0e511af1">qbpp_grb::QuadModel::set_time_limit</a></div><div class="ttdeci">void set_time_limit(uint32_t time_limit)</div><div class="ttdoc">Sets time limit to the Gurobi model.</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__grb_8hpp_source.html#l00076">qbpp_grb.hpp:76</a></div></div>
<div class="ttc" id="aclassGRB__Callback_html_a12eb524eb639f63bfea8f59c750f9035"><div class="ttname"><a href="classGRB__Callback.html#a12eb524eb639f63bfea8f59c750f9035">GRB_Callback::tsp_quad_model</a></div><div class="ttdeci">const qbpp::tsp::TSPQuadModel &amp; tsp_quad_model</div><div class="ttdef"><b>Definition:</b> <a href="tsp__grb_8cpp_source.html#l00019">tsp_grb.cpp:19</a></div></div>
<div class="ttc" id="aclassqbpp_1_1misc_1_1RandomGenerator_html_a513a93d293d9fa08a7fee2bba9cb73ff"><div class="ttname"><a href="classqbpp_1_1misc_1_1RandomGenerator.html#a513a93d293d9fa08a7fee2bba9cb73ff">qbpp::misc::RandomGenerator::rd_seed</a></div><div class="ttdeci">static void rd_seed()</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__misc_8hpp_source.html#l00114">qbpp_misc.hpp:114</a></div></div>
<div class="ttc" id="aclassqbpp_1_1tsp_1_1TSPSol_html"><div class="ttname"><a href="classqbpp_1_1tsp_1_1TSPSol.html">qbpp::tsp::TSPSol</a></div><div class="ttdoc">Class to store a Tour of the TSP.</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__tsp_8hpp_source.html#l00143">qbpp_tsp.hpp:143</a></div></div>
<div class="ttc" id="aclassqbpp__grb_1_1QuadModel_html_ad3add2ab2e0d80e5d0d8123deebc6e98"><div class="ttname"><a href="classqbpp__grb_1_1QuadModel.html#ad3add2ab2e0d80e5d0d8123deebc6e98">qbpp_grb::QuadModel::optimize</a></div><div class="ttdeci">Sol optimize()</div><div class="ttdoc">Optimize the QUBO model.</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__grb_8hpp_source.html#l00257">qbpp_grb.hpp:257</a></div></div>
<div class="ttc" id="aclassqbpp__grb_1_1Callback_html_a91592303df972ec49c2279459a681f05"><div class="ttname"><a href="classqbpp__grb_1_1Callback.html#a91592303df972ec49c2279459a681f05">qbpp_grb::Callback::Callback</a></div><div class="ttdeci">Callback(const QuadModel &amp;quad_model)</div><div class="ttdoc">Constructor: a new Callback object.</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__grb_8hpp_source.html#l00178">qbpp_grb.hpp:178</a></div></div>
<div class="ttc" id="aclassGRB__Callback_html"><div class="ttname"><a href="classGRB__Callback.html">GRB_Callback</a></div><div class="ttdoc">Class to define Gurobi callback function for factorization.</div><div class="ttdef"><b>Definition:</b> <a href="tsp__grb_8cpp_source.html#l00018">tsp_grb.cpp:18</a></div></div>
<div class="ttc" id="aqbpp__grb_8hpp_html"><div class="ttname"><a href="qbpp__grb_8hpp.html">qbpp_grb.hpp</a></div><div class="ttdoc">QUBO++ interface to call Gurobi Optimizer.</div></div>
<div class="ttc" id="aqbpp__tsp_8hpp_html"><div class="ttname"><a href="qbpp__tsp_8hpp.html">qbpp_tsp.hpp</a></div><div class="ttdoc">Generates a QUBO Expression for the Traveling Salesman Problem (TSP) using QUBO++ library.</div></div>
<div class="ttc" id="aclassqbpp_1_1QuadModel_html_a16af851530b1f63aca9baa8ec67ed01f"><div class="ttname"><a href="classqbpp_1_1QuadModel.html#a16af851530b1f63aca9baa8ec67ed01f">qbpp::QuadModel::term_count</a></div><div class="ttdeci">size_t term_count(vindex_t deg) const</div><div class="ttdef"><b>Definition:</b> <a href="qbpp_8hpp_source.html#l01992">qbpp.hpp:1992</a></div></div>
<div class="ttc" id="aqbpp_8hpp_html"><div class="ttname"><a href="qbpp_8hpp.html">qbpp.hpp</a></div><div class="ttdoc">QUBO++, a C++ library for generating expressions for binary and spin variables.</div></div>
<div class="ttc" id="aqbpp__misc_8hpp_html"><div class="ttname"><a href="qbpp__misc_8hpp.html">qbpp_misc.hpp</a></div><div class="ttdoc">A miscellaneous library used for sample programs of the QUBO++ library.</div></div>
<div class="ttc" id="anamespaceqbpp_html_a4184afa358539dbcf5f35e63b49ce05b"><div class="ttname"><a href="namespaceqbpp.html#a4184afa358539dbcf5f35e63b49ce05b">qbpp::get_time</a></div><div class="ttdeci">double get_time()</div><div class="ttdef"><b>Definition:</b> <a href="qbpp_8hpp_source.html#l05311">qbpp.hpp:5311</a></div></div>
<div class="ttc" id="aclassGRB__Callback_html_ac08cbd22e13d589be7ca35178d3126d0"><div class="ttname"><a href="classGRB__Callback.html#ac08cbd22e13d589be7ca35178d3126d0">GRB_Callback::callback</a></div><div class="ttdeci">void callback() override</div><div class="ttdoc">callback function for Gurobi optimizer.</div><div class="ttdef"><b>Definition:</b> <a href="tsp__grb_8cpp_source.html#l00036">tsp_grb.cpp:36</a></div></div>
<div class="ttc" id="aclassqbpp_1_1misc_1_1RandomGenerator_html_a1f4f3d675aee33dce174c8222c3bc22a"><div class="ttname"><a href="classqbpp_1_1misc_1_1RandomGenerator.html#a1f4f3d675aee33dce174c8222c3bc22a">qbpp::misc::RandomGenerator::set_seed</a></div><div class="ttdeci">static void set_seed(uint32_t seed=1)</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__misc_8hpp_source.html#l00112">qbpp_misc.hpp:112</a></div></div>
<div class="ttc" id="aclassqbpp_1_1tsp_1_1TSPQuadModel_html"><div class="ttname"><a href="classqbpp_1_1tsp_1_1TSPQuadModel.html">qbpp::tsp::TSPQuadModel</a></div><div class="ttdoc">Class to store the QUBO expression for the Traveling Salesman Problem (TSP).</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__tsp_8hpp_source.html#l00104">qbpp_tsp.hpp:104</a></div></div>
<div class="ttc" id="aclassGRB__Callback_html_a40edec18aefc56b371ba02d33a8713c3"><div class="ttname"><a href="classGRB__Callback.html#a40edec18aefc56b371ba02d33a8713c3">GRB_Callback::GRB_Callback</a></div><div class="ttdeci">GRB_Callback(const qbpp::tsp::TSPQuadModel &amp;tsp_quad_model, qbpp::energy_t target_energy)</div><div class="ttdoc">Construct a new grb callback object.</div><div class="ttdef"><b>Definition:</b> <a href="tsp__grb_8cpp_source.html#l00026">tsp_grb.cpp:26</a></div></div>
<div class="ttc" id="anamespaceqbpp__grb_html"><div class="ttname"><a href="namespaceqbpp__grb.html">qbpp_grb</a></div><div class="ttdoc">Namespace to use Gurobi optimizer from QUBO++ library.</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__grb_8hpp_source.html#l00025">qbpp_grb.hpp:25</a></div></div>
<div class="ttc" id="aclassqbpp_1_1tsp_1_1DrawSimpleGraph_html_ae58cb44d06b764de7fae386abf86c0de"><div class="ttname"><a href="classqbpp_1_1tsp_1_1DrawSimpleGraph.html#ae58cb44d06b764de7fae386abf86c0de">qbpp::tsp::DrawSimpleGraph::add_node</a></div><div class="ttdeci">void add_node(int x, int y, const std::string &amp;label=&quot;&quot;)</div><div class="ttdoc">Add a node to the graph.</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__tsp_8hpp_source.html#l00259">qbpp_tsp.hpp:259</a></div></div>
<div class="ttc" id="aclassqbpp_1_1tsp_1_1TSPMap_html"><div class="ttname"><a href="classqbpp_1_1tsp_1_1TSPMap.html">qbpp::tsp::TSPMap</a></div><div class="ttdoc">Class to generates a random map for the TSP with n nodes.</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__tsp_8hpp_source.html#l00030">qbpp_tsp.hpp:30</a></div></div>
<div class="ttc" id="aclassqbpp_1_1tsp_1_1TSPMap_html_ace1b4039b9447fae50b62c325acefc6e"><div class="ttname"><a href="classqbpp_1_1tsp_1_1TSPMap.html#ace1b4039b9447fae50b62c325acefc6e">qbpp::tsp::TSPMap::gen_random_map</a></div><div class="ttdeci">void gen_random_map(uint32_t n)</div><div class="ttdoc">Generate a random map with n nodes.</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__tsp_8hpp_source.html#l00323">qbpp_tsp.hpp:323</a></div></div>
<div class="ttc" id="aclassqbpp__grb_1_1QuadModel_html"><div class="ttname"><a href="classqbpp__grb_1_1QuadModel.html">qbpp_grb::QuadModel</a></div><div class="ttdoc">Class to store a QUBO model using Gurobi Optimizer through QUBO++ library.</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__grb_8hpp_source.html#l00042">qbpp_grb.hpp:42</a></div></div>
<div class="ttc" id="aclassqbpp_1_1tsp_1_1DrawSimpleGraph_html_ae4a6e9be4fa3e068d2dd4a4ace7548b5"><div class="ttname"><a href="classqbpp_1_1tsp_1_1DrawSimpleGraph.html#ae4a6e9be4fa3e068d2dd4a4ace7548b5">qbpp::tsp::DrawSimpleGraph::draw</a></div><div class="ttdeci">void draw(std::string filename)</div><div class="ttdoc">Draw the graph in a file.</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__tsp_8hpp_source.html#l00286">qbpp_tsp.hpp:286</a></div></div>
<div class="ttc" id="aclassqbpp__grb_1_1Callback_html"><div class="ttname"><a href="classqbpp__grb_1_1Callback.html">qbpp_grb::Callback</a></div><div class="ttdoc">Class to manage a callback function called by Gurobi Optimizer.</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__grb_8hpp_source.html#l00149">qbpp_grb.hpp:149</a></div></div>
<div class="ttc" id="atsp__grb_8cpp_html_a3c04138a5bfe5d72780bb7e82a18e627"><div class="ttname"><a href="tsp__grb_8cpp.html#a3c04138a5bfe5d72780bb7e82a18e627">main</a></div><div class="ttdeci">int main(int argc, char **argv)</div><div class="ttdoc">Main function to generate a random map and solve the Traveling Salesman Problem (TSP) using the Gurob...</div><div class="ttdef"><b>Definition:</b> <a href="tsp__grb_8cpp_source.html#l00056">tsp_grb.cpp:56</a></div></div>
<div class="ttc" id="aclassqbpp_1_1tsp_1_1DrawSimpleGraph_html"><div class="ttname"><a href="classqbpp_1_1tsp_1_1DrawSimpleGraph.html">qbpp::tsp::DrawSimpleGraph</a></div><div class="ttdoc">Class to draw a simple undirected graph.</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__tsp_8hpp_source.html#l00244">qbpp_tsp.hpp:244</a></div></div>
<div class="ttc" id="aclassqbpp_1_1Model_html_a5cf2bb955c3aa25951ca499d7d8635d2"><div class="ttname"><a href="classqbpp_1_1Model.html#a5cf2bb955c3aa25951ca499d7d8635d2">qbpp::Model::var_count</a></div><div class="ttdeci">vindex_t var_count() const</div><div class="ttdef"><b>Definition:</b> <a href="qbpp_8hpp_source.html#l01859">qbpp.hpp:1859</a></div></div>
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.17
</small></address>
</body>
</html>
