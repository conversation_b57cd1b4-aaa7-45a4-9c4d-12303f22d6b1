<map id="include/qbpp.hpp" name="include/qbpp.hpp">
<area shape="rect" id="node1" title="QUBO++, a C++ library for generating expressions for binary and spin variables." alt="" coords="1764,5,1884,32"/>
<area shape="rect" id="node2" href="$qbpp__abs2_8hpp.html" title="QUBO++ interface to call ABS2 GPU QUBO solver." alt="" coords="225,87,380,114"/>
<area shape="rect" id="node3" href="$graph__color__abs2_8cpp.html" title="Solves randomly generated Graph Coloring Problem using ABS2 QUBO Solver." alt="" coords="190,259,324,300"/>
<area shape="rect" id="node4" href="$ilp__abs2_8cpp.html" title="Solves an Integer Linear Programming (ILP) problem using ABS2 solver through QUBO++ library." alt="" coords="46,177,188,203"/>
<area shape="rect" id="node5" href="$tsp__abs2_8cpp.html" title="Solves randomly generated Traveling Salesman Problem (TSP) using the ABS2 QUBO solver." alt="" coords="562,266,709,293"/>
<area shape="rect" id="node6" href="$simple__factorization__abs2_8cpp.html" title="Simple factorization example using ABS2 QUBO Solver." alt="" coords="364,169,548,211"/>
<area shape="rect" id="node7" href="$factorization_8cpp.html" title="Solves factorization and multiplication problems for two prime numbers using multiple QUBO solvers." alt="" coords="750,266,914,293"/>
<area shape="rect" id="node8" href="$qbpp__grb_8hpp.html" title="QUBO++ interface to call Gurobi Optimizer." alt="" coords="998,87,1143,114"/>
<area shape="rect" id="node9" href="$graph__color__grb_8cpp.html" title="Solves randomly generated Graph Coloring Problem using QUBO++ Gurobi Optimizer." alt="" coords="1276,259,1411,300"/>
<area shape="rect" id="node10" href="$ilp__grb_8cpp.html" title="Solves an Integer Linear Programming (ILP) problem using Gurobi Optimizer through QUBO++ library." alt="" coords="673,177,804,203"/>
<area shape="rect" id="node11" href="$tsp__grb_8cpp.html" title="Solves randomly generated Traveling Salesman Problem (TSP) using the QUBO++ Easy solver." alt="" coords="1629,266,1765,293"/>
<area shape="rect" id="node12" href="$simple__factorization__grb_8cpp.html" title="Simple factorization example using Gurobi Optimizer." alt="" coords="828,169,1012,211"/>
<area shape="rect" id="node13" href="$qbpp__misc_8hpp.html" title="A miscellaneous library used for sample programs of the QUBO++ library." alt="" coords="1804,87,1958,114"/>
<area shape="rect" id="node14" href="$qbpp__easy__solver_8hpp.html" title="Easy QUBO Solver for solving QUBO problems." alt="" coords="2236,177,2432,203"/>
<area shape="rect" id="node15" href="$nqueen__easy_8cpp.html" title="Solves the N&#45;Queens problem using ABS2 QUBO solver from QUBO++ library." alt="" coords="3216,266,3384,293"/>
<area shape="rect" id="node16" href="$partition__easy_8cpp.html" title="Solves the Partitioning problem using the QUBO++ Easy solver." alt="" coords="2143,266,2315,293"/>
<area shape="rect" id="node17" href="$graph__color__easy_8cpp.html" title="Solves randomly generated Graph Coloring Problem using QUBO++ Easy Solver." alt="" coords="1815,259,1950,300"/>
<area shape="rect" id="node18" href="$ilp__easy_8cpp.html" title="Solves an Integer Linear Programming (ILP) problem using EasySolver through QUBO++ library." alt="" coords="2340,266,2478,293"/>
<area shape="rect" id="node19" href="$tsp__easy_8cpp.html" title="Solves randomly generated Traveling Salesman Problem (TSP) using the QUBO++ Easy solver." alt="" coords="1974,266,2118,293"/>
<area shape="rect" id="node20" href="$simple__factorization__easy_8cpp.html" title="Simple factorization example using EasySolver." alt="" coords="2502,259,2686,300"/>
<area shape="rect" id="node21" href="$bin__packing__easy_8cpp.html" title="This file contains an example of solving the bin packing problem using the EasySolver." alt="" coords="2724,259,2862,300"/>
<area shape="rect" id="node22" href="$partition__exhaustive_8cpp.html" title="Solves the Partitioning problem using the QUBO++ Exhaustive solver." alt="" coords="2559,177,2768,203"/>
<area shape="rect" id="node23" href="$qbpp__graph__color_8hpp.html" title=" " alt="" coords="1390,177,1585,203"/>
<area shape="rect" id="node24" href="$qbpp__tsp_8hpp.html" title="Generates a QUBO Expression for the Traveling Salesman Problem (TSP) using QUBO++ library." alt="" coords="1710,177,1857,203"/>
<area shape="rect" id="node25" href="$qbpp__exhaustive__solver_8hpp.html" title="Exhaustive QUBO Solver for solving QUBO problems." alt="" coords="2894,80,3060,121"/>
<area shape="rect" id="node26" href="$ilp__exhaustive_8cpp.html" title="Solves an Integer Linear Programming (ILP) problem using ExhaustiveSolver through QUBO++ library." alt="" coords="2844,177,3020,203"/>
<area shape="rect" id="node27" href="$knapsack__exhaustive_8cpp.html" title="This file contains an example of solving the knapsack problem using the exhaustive solver." alt="" coords="3044,177,3262,203"/>
<area shape="rect" id="node28" href="$qbpp__nqueen_8hpp.html" title="Generates QUBO expression for the N&#45;Queens problem using the QUBO++ library." alt="" coords="3337,87,3508,114"/>
<area shape="rect" id="node29" href="$qbpp__multiplier_8hpp.html" title="Generates QUBO expression for binary multipliers using QUBO++ library." alt="" coords="493,87,672,114"/>
</map>
