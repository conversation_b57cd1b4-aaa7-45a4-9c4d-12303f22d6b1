<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.17"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>QUBO++ Library with QUBO Solver APIs: include/qbpp_abs2.hpp Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/x-mathjax-config">
  MathJax.Hub.Config({
    extensions: ["tex2jax.js", "TeX/AMSmath.js", "TeX/AMSsymbols.js"],
    jax: ["input/TeX","output/HTML-CSS"],
});
</script>
<script type="text/javascript" async="async" src="https://cdn.jsdelivr.net/npm/mathjax@2/MathJax.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">QUBO++ Library with QUBO Solver APIs
   </div>
   <div id="projectbrief">Author: Koji Nakano, License: Non-commercial research and evaluation purposes without any guarantees.</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.17 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_d44c64559bbebec7f509842c48db8b23.html">include</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">qbpp_abs2.hpp</div>  </div>
</div><!--header-->
<div class="contents">
<a href="qbpp__abs2_8hpp.html">Go to the documentation of this file.</a><div class="fragment"><div class="line"><a name="l00001"></a><span class="lineno">    1</span>&#160; </div>
<div class="line"><a name="l00007"></a><span class="lineno">    7</span>&#160;<span class="preprocessor">#ifndef QBPP_ABS2_HPP</span></div>
<div class="line"><a name="l00008"></a><span class="lineno">    8</span>&#160;<span class="preprocessor">#define QBPP_ABS2_HPP</span></div>
<div class="line"><a name="l00009"></a><span class="lineno">    9</span>&#160; </div>
<div class="line"><a name="l00010"></a><span class="lineno">   10</span>&#160;<span class="preprocessor">#include &lt;algorithm&gt;</span></div>
<div class="line"><a name="l00011"></a><span class="lineno">   11</span>&#160; </div>
<div class="line"><a name="l00012"></a><span class="lineno">   12</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="abs2_8hpp.html">abs2.hpp</a>&quot;</span></div>
<div class="line"><a name="l00013"></a><span class="lineno">   13</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="qbpp_8hpp.html">qbpp.hpp</a>&quot;</span></div>
<div class="line"><a name="l00014"></a><span class="lineno">   14</span>&#160; </div>
<div class="line"><a name="l00016"></a><span class="lineno"><a class="line" href="namespaceqbpp__abs2.html">   16</a></span>&#160;<span class="keyword">namespace </span><a class="code" href="namespaceqbpp__abs2.html">qbpp_abs2</a> {</div>
<div class="line"><a name="l00017"></a><span class="lineno">   17</span>&#160; </div>
<div class="line"><a name="l00018"></a><span class="lineno">   18</span>&#160;<span class="comment">//===========================</span></div>
<div class="line"><a name="l00019"></a><span class="lineno">   19</span>&#160;<span class="comment">// Class forward declarationsmake</span></div>
<div class="line"><a name="l00020"></a><span class="lineno">   20</span>&#160;<span class="comment">//===========================</span></div>
<div class="line"><a name="l00021"></a><span class="lineno">   21</span>&#160;<span class="keyword">class </span><a class="code" href="classqbpp__abs2_1_1Solver.html">Solver</a>;</div>
<div class="line"><a name="l00022"></a><span class="lineno">   22</span>&#160;<span class="keyword">class </span><a class="code" href="classqbpp__abs2_1_1QuadModel.html">QuadModel</a>;</div>
<div class="line"><a name="l00023"></a><span class="lineno">   23</span>&#160;<span class="keyword">class </span><a class="code" href="classqbpp__abs2_1_1Param.html">Param</a>;</div>
<div class="line"><a name="l00024"></a><span class="lineno">   24</span>&#160;<span class="keyword">class </span><a class="code" href="classqbpp__abs2_1_1Sol.html">Sol</a>;</div>
<div class="line"><a name="l00025"></a><span class="lineno">   25</span>&#160;<span class="keyword">class </span><a class="code" href="classqbpp__abs2_1_1Callback.html">Callback</a>;</div>
<div class="line"><a name="l00026"></a><span class="lineno">   26</span>&#160; </div>
<div class="line"><a name="l00027"></a><span class="lineno">   27</span>&#160;<span class="comment">//===================</span></div>
<div class="line"><a name="l00028"></a><span class="lineno">   28</span>&#160;<span class="comment">// Class declarations</span></div>
<div class="line"><a name="l00029"></a><span class="lineno">   29</span>&#160;<span class="comment">//===================</span></div>
<div class="line"><a name="l00047"></a><span class="lineno">   47</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l00049"></a><span class="lineno">   49</span>&#160; </div>
<div class="line"><a name="l00053"></a><span class="lineno"><a class="line" href="classqbpp__abs2_1_1Solver.html">   53</a></span>&#160;<span class="keyword">class </span><a class="code" href="classqbpp__abs2_1_1Solver.html">Solver</a> : <span class="keyword">public</span> <a class="code" href="classabs2_1_1Solver.html">abs2::Solver</a> {</div>
<div class="line"><a name="l00054"></a><span class="lineno">   54</span>&#160; <span class="keyword">public</span>:</div>
<div class="line"><a name="l00059"></a><span class="lineno">   59</span>&#160;  <a class="code" href="classqbpp__abs2_1_1Sol.html">Sol</a> <a class="code" href="group__qbpp__abs2.html#ga7665b9e646b22156b8680c64cd08c92c">operator()</a>(<span class="keyword">const</span> <a class="code" href="classqbpp__abs2_1_1QuadModel.html">QuadModel</a> &amp;quad_model, <a class="code" href="classqbpp__abs2_1_1Param.html">Param</a> &amp;param) <span class="keyword">const</span>;</div>
<div class="line"><a name="l00060"></a><span class="lineno">   60</span>&#160; </div>
<div class="line"><a name="l00067"></a><span class="lineno">   67</span>&#160;  <a class="code" href="classqbpp__abs2_1_1Sol.html">Sol</a> <a class="code" href="group__qbpp__abs2.html#ga7665b9e646b22156b8680c64cd08c92c">operator()</a>(<span class="keyword">const</span> <a class="code" href="classqbpp__abs2_1_1QuadModel.html">QuadModel</a> &amp;quad_model, <a class="code" href="classqbpp__abs2_1_1Param.html">Param</a> &amp;param,</div>
<div class="line"><a name="l00068"></a><span class="lineno">   68</span>&#160;                 <span class="keyword">const</span> <a class="code" href="classqbpp__abs2_1_1Sol.html">Sol</a> &amp;start) <span class="keyword">const</span>;</div>
<div class="line"><a name="l00069"></a><span class="lineno">   69</span>&#160; </div>
<div class="line"><a name="l00071"></a><span class="lineno">   71</span>&#160;};</div>
<div class="line"><a name="l00072"></a><span class="lineno">   72</span>&#160; </div>
<div class="line"><a name="l00076"></a><span class="lineno"><a class="line" href="classqbpp__abs2_1_1QuadModel.html">   76</a></span>&#160;<span class="keyword">class </span><a class="code" href="classqbpp__abs2_1_1QuadModel.html">QuadModel</a> : <span class="keyword">public</span> <a class="code" href="classqbpp_1_1QuadModel.html">qbpp::QuadModel</a> {</div>
<div class="line"><a name="l00078"></a><span class="lineno"><a class="line" href="classqbpp__abs2_1_1QuadModel.html#a763aa7381304ed9bd82054b6614c929f">   78</a></span>&#160;  std::shared_ptr&lt;abs2::Model&gt; <a class="code" href="classqbpp__abs2_1_1QuadModel.html#a763aa7381304ed9bd82054b6614c929f">abs2model_ptr</a>;</div>
<div class="line"><a name="l00079"></a><span class="lineno">   79</span>&#160; </div>
<div class="line"><a name="l00081"></a><span class="lineno">   81</span>&#160;  <a class="code" href="classqbpp__abs2_1_1QuadModel.html#ae407053ddc1028ef6253ee3f5d23073d">QuadModel</a>() = <span class="keyword">delete</span>;</div>
<div class="line"><a name="l00082"></a><span class="lineno">   82</span>&#160; </div>
<div class="line"><a name="l00083"></a><span class="lineno">   83</span>&#160; <span class="keyword">public</span>:</div>
<div class="line"><a name="l00086"></a><span class="lineno">   86</span>&#160;  <a class="code" href="classqbpp__abs2_1_1QuadModel.html#ae407053ddc1028ef6253ee3f5d23073d">QuadModel</a>(<span class="keyword">const</span> <a class="code" href="classqbpp_1_1QuadModel.html">qbpp::QuadModel</a> &amp;quad_model);</div>
<div class="line"><a name="l00087"></a><span class="lineno">   87</span>&#160; </div>
<div class="line"><a name="l00090"></a><span class="lineno"><a class="line" href="classqbpp__abs2_1_1QuadModel.html#ae79cbaf57fdafd9fd834aba1e0e615f5">   90</a></span>&#160;  <a class="code" href="classqbpp__abs2_1_1QuadModel.html#ae79cbaf57fdafd9fd834aba1e0e615f5">QuadModel</a>(<span class="keyword">const</span> <a class="code" href="classqbpp__abs2_1_1QuadModel.html">QuadModel</a> &amp;quad_model)</div>
<div class="line"><a name="l00091"></a><span class="lineno">   91</span>&#160;      : <a class="code" href="namespaceqbpp.html">qbpp</a>::<a class="code" href="classqbpp__abs2_1_1QuadModel.html">QuadModel</a>(quad_model), <a class="code" href="classqbpp__abs2_1_1QuadModel.html#a763aa7381304ed9bd82054b6614c929f">abs2model_ptr</a>(quad_model.<a class="code" href="classqbpp__abs2_1_1QuadModel.html#a763aa7381304ed9bd82054b6614c929f">abs2model_ptr</a>) {};</div>
<div class="line"><a name="l00092"></a><span class="lineno">   92</span>&#160; </div>
<div class="line"><a name="l00095"></a><span class="lineno"><a class="line" href="classqbpp__abs2_1_1QuadModel.html#a81d36c5c8b3ae978124c217d11a14351">   95</a></span>&#160;  <span class="keyword">const</span> <a class="code" href="classabs2_1_1Model.html">abs2::Model</a> &amp;<a class="code" href="classqbpp__abs2_1_1QuadModel.html#a81d36c5c8b3ae978124c217d11a14351">get_abs2_model</a>()<span class="keyword"> const </span>{ <span class="keywordflow">return</span> *<a class="code" href="classqbpp__abs2_1_1QuadModel.html#a763aa7381304ed9bd82054b6614c929f">abs2model_ptr</a>; }</div>
<div class="line"><a name="l00096"></a><span class="lineno">   96</span>&#160;};</div>
<div class="line"><a name="l00097"></a><span class="lineno">   97</span>&#160; </div>
<div class="line"><a name="l00104"></a><span class="lineno"><a class="line" href="classqbpp__abs2_1_1Param.html">  104</a></span>&#160;<span class="keyword">class </span><a class="code" href="classqbpp__abs2_1_1Param.html">Param</a> : <span class="keyword">public</span> <a class="code" href="classabs2_1_1Param.html">abs2::Param</a> {</div>
<div class="line"><a name="l00108"></a><span class="lineno"><a class="line" href="classqbpp__abs2_1_1Param.html#a39060f29eb41259853f58b5c06348da5">  108</a></span>&#160;  std::optional&lt;qbpp::energy_t&gt; <a class="code" href="classqbpp__abs2_1_1Param.html#a39060f29eb41259853f58b5c06348da5">target_energy</a>;</div>
<div class="line"><a name="l00109"></a><span class="lineno">  109</span>&#160; </div>
<div class="line"><a name="l00110"></a><span class="lineno">  110</span>&#160; <span class="keyword">public</span>:</div>
<div class="line"><a name="l00113"></a><span class="lineno">  113</span>&#160; </div>
<div class="line"><a name="l00116"></a><span class="lineno">  116</span>&#160;  <a class="code" href="group__qbpp__abs2.html#ga03882e256386f69f9d004d28b9e7b53b">Param</a>() = <span class="keywordflow">default</span>;</div>
<div class="line"><a name="l00117"></a><span class="lineno">  117</span>&#160; </div>
<div class="line"><a name="l00120"></a><span class="lineno"><a class="line" href="group__qbpp__abs2.html#gac5b2aaa6db9827d709125edcbe0265a4">  120</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="group__qbpp__abs2.html#gac5b2aaa6db9827d709125edcbe0265a4">set_target_energy</a>(<a class="code" href="namespaceqbpp.html#a9c324f641779d1a7a3c7723f28d8cff4">qbpp::energy_t</a> <a class="code" href="classqbpp__abs2_1_1Param.html#a39060f29eb41259853f58b5c06348da5">target_energy</a>) {</div>
<div class="line"><a name="l00121"></a><span class="lineno">  121</span>&#160;    this-&gt;target_energy = <a class="code" href="classqbpp__abs2_1_1Param.html#a39060f29eb41259853f58b5c06348da5">target_energy</a>;</div>
<div class="line"><a name="l00122"></a><span class="lineno">  122</span>&#160;  }</div>
<div class="line"><a name="l00123"></a><span class="lineno">  123</span>&#160; </div>
<div class="line"><a name="l00126"></a><span class="lineno"><a class="line" href="group__qbpp__abs2.html#ga7436ebabb619505e0f7067f8b812e2a3">  126</a></span>&#160;  std::optional&lt;qbpp::energy_t&gt; <a class="code" href="group__qbpp__abs2.html#ga7436ebabb619505e0f7067f8b812e2a3">get_target_energy</a>()<span class="keyword"> const </span>{</div>
<div class="line"><a name="l00127"></a><span class="lineno">  127</span>&#160;    <span class="keywordflow">return</span> <a class="code" href="classqbpp__abs2_1_1Param.html#a39060f29eb41259853f58b5c06348da5">target_energy</a>;</div>
<div class="line"><a name="l00128"></a><span class="lineno">  128</span>&#160;  }</div>
<div class="line"><a name="l00129"></a><span class="lineno">  129</span>&#160; </div>
<div class="line"><a name="l00132"></a><span class="lineno"><a class="line" href="group__qbpp__abs2.html#ga33c85eefb81407d622de58bd2775598c">  132</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="group__qbpp__abs2.html#ga33c85eefb81407d622de58bd2775598c">set_time_limit</a>(uint32_t time_limit) {</div>
<div class="line"><a name="l00133"></a><span class="lineno">  133</span>&#160;    <a class="code" href="classabs2_1_1Param.html#a7fd8ca3ff207475b8ad4a3d5040ccd01">abs2::Param::set</a>(<span class="stringliteral">&quot;time_limit&quot;</span>, std::to_string(time_limit));</div>
<div class="line"><a name="l00134"></a><span class="lineno">  134</span>&#160;  }</div>
<div class="line"><a name="l00135"></a><span class="lineno">  135</span>&#160; </div>
<div class="line"><a name="l00138"></a><span class="lineno"><a class="line" href="group__qbpp__abs2.html#ga2754c68b51c3d0452731e5da95920fe2">  138</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="group__qbpp__abs2.html#ga2754c68b51c3d0452731e5da95920fe2">set_arithmetic_bits</a>(uint32_t bits) {</div>
<div class="line"><a name="l00139"></a><span class="lineno">  139</span>&#160;    <a class="code" href="classabs2_1_1Param.html#a7fd8ca3ff207475b8ad4a3d5040ccd01">abs2::Param::set</a>(<span class="stringliteral">&quot;arithmetic_bits&quot;</span>, std::to_string(bits));</div>
<div class="line"><a name="l00140"></a><span class="lineno">  140</span>&#160;  }</div>
<div class="line"><a name="l00141"></a><span class="lineno">  141</span>&#160; </div>
<div class="line"><a name="l00143"></a><span class="lineno">  143</span>&#160;};</div>
<div class="line"><a name="l00144"></a><span class="lineno">  144</span>&#160; </div>
<div class="line"><a name="l00148"></a><span class="lineno"><a class="line" href="classqbpp__abs2_1_1Sol.html">  148</a></span>&#160;<span class="keyword">class </span><a class="code" href="classqbpp__abs2_1_1Sol.html">Sol</a> : <span class="keyword">public</span> <a class="code" href="classqbpp_1_1Sol.html">qbpp::Sol</a> {</div>
<div class="line"><a name="l00151"></a><span class="lineno"><a class="line" href="classqbpp__abs2_1_1Sol.html#a2979356a3719cec1699dab0ba5c80ea7">  151</a></span>&#160;  <span class="keyword">const</span> std::shared_ptr&lt;abs2::Sol&gt; <a class="code" href="classqbpp__abs2_1_1Sol.html#a2979356a3719cec1699dab0ba5c80ea7">abs2sol_ptr</a>;</div>
<div class="line"><a name="l00152"></a><span class="lineno">  152</span>&#160; </div>
<div class="line"><a name="l00153"></a><span class="lineno">  153</span>&#160; <span class="keyword">public</span>:</div>
<div class="line"><a name="l00156"></a><span class="lineno">  156</span>&#160; </div>
<div class="line"><a name="l00160"></a><span class="lineno">  160</span>&#160;  <a class="code" href="classqbpp_1_1Sol.html#afb26ebcc595aa1391f18c2968713fdba">Sol</a>(<span class="keyword">const</span> <a class="code" href="classqbpp__abs2_1_1QuadModel.html">QuadModel</a> &amp;quad_model, <span class="keyword">const</span> <a class="code" href="classabs2_1_1Sol.html">abs2::Sol</a> &amp;sol);</div>
<div class="line"><a name="l00161"></a><span class="lineno">  161</span>&#160; </div>
<div class="line"><a name="l00164"></a><span class="lineno">  164</span>&#160;  <a class="code" href="classqbpp_1_1Sol.html#afb26ebcc595aa1391f18c2968713fdba">Sol</a>(<span class="keyword">const</span> <a class="code" href="classqbpp_1_1Sol.html">qbpp::Sol</a> &amp;sol);</div>
<div class="line"><a name="l00165"></a><span class="lineno">  165</span>&#160; </div>
<div class="line"><a name="l00167"></a><span class="lineno">  167</span>&#160; </div>
<div class="line"><a name="l00170"></a><span class="lineno">  170</span>&#160;  <a class="code" href="classqbpp_1_1Sol.html#afb26ebcc595aa1391f18c2968713fdba">Sol</a>(<span class="keyword">const</span> <a class="code" href="classqbpp__abs2_1_1Sol.html">Sol</a> &amp;sol) = <span class="keywordflow">default</span>;</div>
<div class="line"><a name="l00171"></a><span class="lineno">  171</span>&#160; </div>
<div class="line"><a name="l00174"></a><span class="lineno"><a class="line" href="classqbpp__abs2_1_1Sol.html#a32d5e94f5ec2e824cbb8669e5bb1c69a">  174</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="classqbpp__abs2_1_1Sol.html#a32d5e94f5ec2e824cbb8669e5bb1c69a">print</a>(<span class="keyword">const</span> std::string &amp;attrs)<span class="keyword"> const </span>{ <a class="code" href="classqbpp__abs2_1_1Sol.html#a2979356a3719cec1699dab0ba5c80ea7">abs2sol_ptr</a>-&gt;print(attrs); }</div>
<div class="line"><a name="l00175"></a><span class="lineno">  175</span>&#160; </div>
<div class="line"><a name="l00179"></a><span class="lineno"><a class="line" href="classqbpp__abs2_1_1Sol.html#a48d9116f9fcbe00f84640847ee49df23">  179</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="classqbpp__abs2_1_1Sol.html#a48d9116f9fcbe00f84640847ee49df23">set</a>(<span class="keywordtype">int</span> <a class="code" href="classqbpp_1_1Sol.html#a24f5a53fa68071dd9bcda910533bbad7">index</a>, <span class="keywordtype">bool</span> value) {</div>
<div class="line"><a name="l00180"></a><span class="lineno">  180</span>&#160;    <a class="code" href="classqbpp_1_1Sol.html#a2f7494eb4a254dd19b0261e801bacffb">qbpp::Sol::set</a>(<a class="code" href="classqbpp_1_1Sol.html#a24f5a53fa68071dd9bcda910533bbad7">index</a>, value);</div>
<div class="line"><a name="l00181"></a><span class="lineno">  181</span>&#160;    <a class="code" href="classqbpp__abs2_1_1Sol.html#a2979356a3719cec1699dab0ba5c80ea7">abs2sol_ptr</a>-&gt;set(<a class="code" href="classqbpp_1_1Sol.html#a24f5a53fa68071dd9bcda910533bbad7">index</a>, value);</div>
<div class="line"><a name="l00182"></a><span class="lineno">  182</span>&#160;  }</div>
<div class="line"><a name="l00183"></a><span class="lineno">  183</span>&#160; </div>
<div class="line"><a name="l00187"></a><span class="lineno"><a class="line" href="classqbpp__abs2_1_1Sol.html#a04aabe5387a17b86a90acaa607175545">  187</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="classqbpp__abs2_1_1Sol.html#a04aabe5387a17b86a90acaa607175545">set</a>(<a class="code" href="classqbpp_1_1Var.html">qbpp::Var</a> <a class="code" href="namespaceqbpp.html#a1cf3f678bcc7eee089c75d4bc5a0b959">var</a>, <span class="keywordtype">bool</span> value) {</div>
<div class="line"><a name="l00188"></a><span class="lineno">  188</span>&#160;    <a class="code" href="classqbpp_1_1Sol.html#a2f7494eb4a254dd19b0261e801bacffb">qbpp::Sol::set</a>(<a class="code" href="namespaceqbpp.html#a1cf3f678bcc7eee089c75d4bc5a0b959">var</a>, value);</div>
<div class="line"><a name="l00189"></a><span class="lineno">  189</span>&#160;    <a class="code" href="classqbpp__abs2_1_1Sol.html#a2979356a3719cec1699dab0ba5c80ea7">abs2sol_ptr</a>-&gt;set(<a class="code" href="classqbpp_1_1Sol.html#a24f5a53fa68071dd9bcda910533bbad7">index</a>(<a class="code" href="namespaceqbpp.html#a1cf3f678bcc7eee089c75d4bc5a0b959">var</a>), value);</div>
<div class="line"><a name="l00190"></a><span class="lineno">  190</span>&#160;  }</div>
<div class="line"><a name="l00191"></a><span class="lineno">  191</span>&#160; </div>
<div class="line"><a name="l00192"></a><span class="lineno"><a class="line" href="classqbpp__abs2_1_1Sol.html#ae35741170c9c1e91c8c700221fb053e2">  192</a></span>&#160;  <span class="keywordtype">double</span> <a class="code" href="classqbpp__abs2_1_1Sol.html#ae35741170c9c1e91c8c700221fb053e2">get_tts</a>()<span class="keyword"> const </span>{ <span class="keywordflow">return</span> std::stod(<a class="code" href="classqbpp__abs2_1_1Sol.html#a2979356a3719cec1699dab0ba5c80ea7">abs2sol_ptr</a>-&gt;get(<span class="stringliteral">&quot;tts&quot;</span>)); }</div>
<div class="line"><a name="l00193"></a><span class="lineno">  193</span>&#160; </div>
<div class="line"><a name="l00196"></a><span class="lineno"><a class="line" href="classqbpp__abs2_1_1Sol.html#a8de3bebb198a0820db0446f9686e914d">  196</a></span>&#160;  <span class="keyword">const</span> std::shared_ptr&lt;abs2::Sol&gt; <a class="code" href="classqbpp__abs2_1_1Sol.html#a8de3bebb198a0820db0446f9686e914d">get_abs2sol_ptr</a>()<span class="keyword"> const </span>{</div>
<div class="line"><a name="l00197"></a><span class="lineno">  197</span>&#160;    <span class="keywordflow">return</span> <a class="code" href="classqbpp__abs2_1_1Sol.html#a2979356a3719cec1699dab0ba5c80ea7">abs2sol_ptr</a>;</div>
<div class="line"><a name="l00198"></a><span class="lineno">  198</span>&#160;  }</div>
<div class="line"><a name="l00199"></a><span class="lineno">  199</span>&#160;};</div>
<div class="line"><a name="l00200"></a><span class="lineno">  200</span>&#160; </div>
<div class="line"><a name="l00204"></a><span class="lineno"><a class="line" href="classqbpp__abs2_1_1Callback.html">  204</a></span>&#160;<span class="keyword">class </span><a class="code" href="classqbpp__abs2_1_1Callback.html">Callback</a> : <span class="keyword">public</span> <a class="code" href="classabs2_1_1Callback.html">abs2::Callback</a> {</div>
<div class="line"><a name="l00205"></a><span class="lineno">  205</span>&#160; <span class="keyword">protected</span>:</div>
<div class="line"><a name="l00207"></a><span class="lineno"><a class="line" href="classqbpp__abs2_1_1Callback.html#a239479a75950e3b40e2ec5faf32d0b30">  207</a></span>&#160;  <span class="keyword">const</span> <a class="code" href="classqbpp__abs2_1_1QuadModel.html">QuadModel</a> <a class="code" href="classqbpp__abs2_1_1Callback.html#a239479a75950e3b40e2ec5faf32d0b30">quad_model</a>;</div>
<div class="line"><a name="l00208"></a><span class="lineno">  208</span>&#160; </div>
<div class="line"><a name="l00209"></a><span class="lineno">  209</span>&#160; <span class="keyword">public</span>:</div>
<div class="line"><a name="l00212"></a><span class="lineno">  212</span>&#160; </div>
<div class="line"><a name="l00215"></a><span class="lineno"><a class="line" href="group__qbpp__abs2.html#ga6b4dd18ef3cc81619afee888b7bf64d2">  215</a></span>&#160;  <a class="code" href="group__qbpp__abs2.html#ga6b4dd18ef3cc81619afee888b7bf64d2">Callback</a>(<span class="keyword">const</span> <a class="code" href="classqbpp__abs2_1_1QuadModel.html">QuadModel</a> &amp;<a class="code" href="classqbpp__abs2_1_1Callback.html#a239479a75950e3b40e2ec5faf32d0b30">quad_model</a>)</div>
<div class="line"><a name="l00216"></a><span class="lineno">  216</span>&#160;      : <a class="code" href="namespaceabs2.html">abs2</a>::<a class="code" href="classqbpp__abs2_1_1Callback.html">Callback</a>(), <a class="code" href="classqbpp__abs2_1_1Callback.html#a239479a75950e3b40e2ec5faf32d0b30">quad_model</a>(<a class="code" href="classqbpp__abs2_1_1Callback.html#a239479a75950e3b40e2ec5faf32d0b30">quad_model</a>) {};</div>
<div class="line"><a name="l00217"></a><span class="lineno">  217</span>&#160; </div>
<div class="line"><a name="l00220"></a><span class="lineno"><a class="line" href="group__qbpp__abs2.html#ga5946dc74bd5efe6937f6fd3d2fbc4551">  220</a></span>&#160;  <a class="code" href="classqbpp__abs2_1_1Sol.html">Sol</a> <a class="code" href="group__qbpp__abs2.html#ga5946dc74bd5efe6937f6fd3d2fbc4551">get_sol</a>()<span class="keyword"> const </span>{ <span class="keywordflow">return</span> <a class="code" href="classqbpp__abs2_1_1Sol.html">Sol</a>(<a class="code" href="classqbpp__abs2_1_1Callback.html#a239479a75950e3b40e2ec5faf32d0b30">quad_model</a>, <a class="code" href="classabs2_1_1Callback.html#abe677099f6a83a3c588d1609a542420e">abs2::Callback::get</a>()); };</div>
<div class="line"><a name="l00221"></a><span class="lineno">  221</span>&#160; </div>
<div class="line"><a name="l00225"></a><span class="lineno"><a class="line" href="group__qbpp__abs2.html#gacde6f2007700c2ac188f3a88c5bba1d0">  225</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="group__qbpp__abs2.html#gacde6f2007700c2ac188f3a88c5bba1d0">set_hint</a>(<span class="keyword">const</span> <a class="code" href="classqbpp__abs2_1_1Sol.html">Sol</a> &amp;hint) {</div>
<div class="line"><a name="l00226"></a><span class="lineno">  226</span>&#160;    <a class="code" href="classabs2_1_1Callback.html#a365f96a6e4f72d37ae897841d07e55ab">abs2::Callback::set</a>(*hint.<a class="code" href="classqbpp__abs2_1_1Sol.html#a8de3bebb198a0820db0446f9686e914d">get_abs2sol_ptr</a>());</div>
<div class="line"><a name="l00227"></a><span class="lineno">  227</span>&#160;  }</div>
<div class="line"><a name="l00234"></a><span class="lineno"><a class="line" href="group__qbpp__abs2.html#ga692214095508b4187ec18035e1c275e8">  234</a></span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">void</span> <a class="code" href="group__qbpp__abs2.html#ga692214095508b4187ec18035e1c275e8">callback</a>(<span class="keyword">const</span> std::string &amp;event) {</div>
<div class="line"><a name="l00235"></a><span class="lineno">  235</span>&#160;    <span class="keywordflow">if</span> (event == <span class="stringliteral">&quot;init&quot;</span>) {</div>
<div class="line"><a name="l00236"></a><span class="lineno">  236</span>&#160;      <a class="code" href="classabs2_1_1Callback.html#a365f96a6e4f72d37ae897841d07e55ab">qbpp_abs2::Callback::set</a>(<span class="stringliteral">&quot;new&quot;</span>);</div>
<div class="line"><a name="l00237"></a><span class="lineno">  237</span>&#160;    } <span class="keywordflow">else</span> <span class="keywordflow">if</span> (event == <span class="stringliteral">&quot;new&quot;</span>) {</div>
<div class="line"><a name="l00238"></a><span class="lineno">  238</span>&#160;      <span class="keyword">auto</span> sol = <a class="code" href="group__qbpp__abs2.html#ga5946dc74bd5efe6937f6fd3d2fbc4551">get_sol</a>();</div>
<div class="line"><a name="l00239"></a><span class="lineno">  239</span>&#160;      std::cout &lt;&lt; <span class="stringliteral">&quot;TTS = &quot;</span> &lt;&lt; std::fixed &lt;&lt; std::setprecision(3)</div>
<div class="line"><a name="l00240"></a><span class="lineno">  240</span>&#160;                &lt;&lt; sol.get_tts() &lt;&lt; <span class="stringliteral">&quot;s Energy=&quot;</span> &lt;&lt; sol.energy() &lt;&lt; std::endl;</div>
<div class="line"><a name="l00241"></a><span class="lineno">  241</span>&#160;    }</div>
<div class="line"><a name="l00242"></a><span class="lineno">  242</span>&#160;  }</div>
<div class="line"><a name="l00243"></a><span class="lineno">  243</span>&#160; </div>
<div class="line"><a name="l00245"></a><span class="lineno">  245</span>&#160; </div>
<div class="line"><a name="l00246"></a><span class="lineno">  246</span>&#160;};  <span class="comment">// namespace qbpp_abs2</span></div>
<div class="line"><a name="l00247"></a><span class="lineno">  247</span>&#160; </div>
<div class="line"><a name="l00248"></a><span class="lineno">  248</span>&#160;<span class="comment">//============================</span></div>
<div class="line"><a name="l00249"></a><span class="lineno">  249</span>&#160;<span class="comment">// Class QuadModel member function</span></div>
<div class="line"><a name="l00250"></a><span class="lineno">  250</span>&#160;<span class="comment">//============================</span></div>
<div class="line"><a name="l00251"></a><span class="lineno"><a class="line" href="group__qbpp__abs2.html#gab125241142be7f65e2a913158e00394a">  251</a></span>&#160;<span class="keyword">inline</span> <a class="code" href="classqbpp__abs2_1_1QuadModel.html#ae407053ddc1028ef6253ee3f5d23073d">QuadModel::QuadModel</a>(<span class="keyword">const</span> <a class="code" href="classqbpp_1_1QuadModel.html">qbpp::QuadModel</a> &amp;quad_model)</div>
<div class="line"><a name="l00252"></a><span class="lineno">  252</span>&#160;    : <a class="code" href="namespaceqbpp.html">qbpp</a>::<a class="code" href="classqbpp__abs2_1_1QuadModel.html">QuadModel</a>(quad_model) {</div>
<div class="line"><a name="l00253"></a><span class="lineno">  253</span>&#160;  <span class="comment">// Create an empty ABS2 model</span></div>
<div class="line"><a name="l00254"></a><span class="lineno">  254</span>&#160;  <a class="code" href="classqbpp__abs2_1_1QuadModel.html#a763aa7381304ed9bd82054b6614c929f">abs2model_ptr</a> = std::make_unique&lt;abs2::Model&gt;(</div>
<div class="line"><a name="l00255"></a><span class="lineno">  255</span>&#160;      <a class="code" href="classqbpp_1_1Model.html#a5cf2bb955c3aa25951ca499d7d8635d2">var_count</a>(), quad_model.<a class="code" href="classqbpp_1_1QuadModel.html#a06b84580a41556201ab2f4bb3d838403">min_coeff</a>(), quad_model.<a class="code" href="classqbpp_1_1QuadModel.html#ab6ac73dcc23030f0f7e303e94d3c2bd3">max_coeff</a>());</div>
<div class="line"><a name="l00256"></a><span class="lineno">  256</span>&#160; </div>
<div class="line"><a name="l00257"></a><span class="lineno">  257</span>&#160;  <span class="comment">// Set the linear and quadratic terms</span></div>
<div class="line"><a name="l00258"></a><span class="lineno">  258</span>&#160;  <span class="keywordflow">for</span> (<a class="code" href="namespaceqbpp.html#a5cf436100ede362797d0c0501ea50a5a">qbpp::vindex_t</a> i = 0; i &lt; <a class="code" href="classqbpp_1_1Model.html#a5cf2bb955c3aa25951ca499d7d8635d2">var_count</a>(); i++) {</div>
<div class="line"><a name="l00259"></a><span class="lineno">  259</span>&#160;    <span class="keywordflow">if</span> (quad_model.<a class="code" href="classqbpp_1_1QuadModel.html#a591e74de688dbff1339c2c9d2352df65">linear</a>(i) != 0)</div>
<div class="line"><a name="l00260"></a><span class="lineno">  260</span>&#160;      <a class="code" href="classqbpp__abs2_1_1QuadModel.html#a763aa7381304ed9bd82054b6614c929f">abs2model_ptr</a>-&gt;set(i, i, quad_model.<a class="code" href="classqbpp_1_1QuadModel.html#a591e74de688dbff1339c2c9d2352df65">linear</a>(i));</div>
<div class="line"><a name="l00261"></a><span class="lineno">  261</span>&#160;  }</div>
<div class="line"><a name="l00262"></a><span class="lineno">  262</span>&#160;  <span class="keywordflow">for</span> (<a class="code" href="namespaceqbpp.html#a5cf436100ede362797d0c0501ea50a5a">qbpp::vindex_t</a> i = 0; i &lt; <a class="code" href="classqbpp_1_1Model.html#a5cf2bb955c3aa25951ca499d7d8635d2">var_count</a>(); i++) {</div>
<div class="line"><a name="l00263"></a><span class="lineno">  263</span>&#160;    <span class="keywordflow">for</span> (<a class="code" href="namespaceqbpp.html#a5cf436100ede362797d0c0501ea50a5a">qbpp::vindex_t</a> j = 0; j &lt; quad_model.<a class="code" href="classqbpp_1_1QuadModel.html#ada71ec32906c9d8c73bd25c4267bafc5">degree</a>(i); j++) {</div>
<div class="line"><a name="l00264"></a><span class="lineno">  264</span>&#160;      <span class="keyword">auto</span> [k, coeff] = quad_model.<a class="code" href="classqbpp_1_1QuadModel.html#a6624038d4ff117176c37de2d1260f471">quadratic</a>(i, j);</div>
<div class="line"><a name="l00265"></a><span class="lineno">  265</span>&#160;      <a class="code" href="classqbpp__abs2_1_1QuadModel.html#a763aa7381304ed9bd82054b6614c929f">abs2model_ptr</a>-&gt;set(i, k, coeff);</div>
<div class="line"><a name="l00266"></a><span class="lineno">  266</span>&#160;    }</div>
<div class="line"><a name="l00267"></a><span class="lineno">  267</span>&#160;  }</div>
<div class="line"><a name="l00268"></a><span class="lineno">  268</span>&#160;}</div>
<div class="line"><a name="l00269"></a><span class="lineno">  269</span>&#160; </div>
<div class="line"><a name="l00270"></a><span class="lineno">  270</span>&#160;<span class="comment">//===============================</span></div>
<div class="line"><a name="l00271"></a><span class="lineno">  271</span>&#160;<span class="comment">// Class Solver member functions</span></div>
<div class="line"><a name="l00272"></a><span class="lineno">  272</span>&#160;<span class="comment">//===============================</span></div>
<div class="line"><a name="l00273"></a><span class="lineno">  273</span>&#160; </div>
<div class="line"><a name="l00274"></a><span class="lineno">  274</span>&#160;<span class="comment">// Execute ABS2 for &quot;quad_model&quot; with &quot;param&quot; and returns &quot;sol&quot;.</span></div>
<div class="line"><a name="l00275"></a><span class="lineno"><a class="line" href="group__qbpp__abs2.html#ga7665b9e646b22156b8680c64cd08c92c">  275</a></span>&#160;<span class="keyword">inline</span> <a class="code" href="classqbpp__abs2_1_1Sol.html">Sol</a> <a class="code" href="group__qbpp__abs2.html#ga7665b9e646b22156b8680c64cd08c92c">Solver::operator()</a>(<span class="keyword">const</span> <a class="code" href="classqbpp__abs2_1_1QuadModel.html">QuadModel</a> &amp;quad_model, <a class="code" href="classqbpp__abs2_1_1Param.html">Param</a> &amp;param)<span class="keyword"> const </span>{</div>
<div class="line"><a name="l00276"></a><span class="lineno">  276</span>&#160;  <span class="comment">// target_energy is adjusted by the constant term of the QUBO model because</span></div>
<div class="line"><a name="l00277"></a><span class="lineno">  277</span>&#160;  <span class="comment">// ABS2 Solver does not handle constant terms.</span></div>
<div class="line"><a name="l00278"></a><span class="lineno">  278</span>&#160;  <span class="keywordflow">if</span> (param.<a class="code" href="group__qbpp__abs2.html#ga7436ebabb619505e0f7067f8b812e2a3">get_target_energy</a>().has_value()) {</div>
<div class="line"><a name="l00279"></a><span class="lineno">  279</span>&#160;    param.<a class="code" href="classabs2_1_1Param.html#a7fd8ca3ff207475b8ad4a3d5040ccd01">set</a>(<span class="stringliteral">&quot;target_energy&quot;</span>,</div>
<div class="line"><a name="l00280"></a><span class="lineno">  280</span>&#160;              std::to_string(param.<a class="code" href="group__qbpp__abs2.html#ga7436ebabb619505e0f7067f8b812e2a3">get_target_energy</a>().value() -</div>
<div class="line"><a name="l00281"></a><span class="lineno">  281</span>&#160;                             quad_model.<a class="code" href="classqbpp_1_1Model.html#a3b0641c97573b248f1877c2826277102">constant</a>()));</div>
<div class="line"><a name="l00282"></a><span class="lineno">  282</span>&#160;  }</div>
<div class="line"><a name="l00283"></a><span class="lineno">  283</span>&#160;  <span class="keywordflow">return</span> <a class="code" href="classqbpp__abs2_1_1Sol.html">Sol</a>(quad_model,</div>
<div class="line"><a name="l00284"></a><span class="lineno">  284</span>&#160;             abs2::Solver::operator()(quad_model.<a class="code" href="classqbpp__abs2_1_1QuadModel.html#a81d36c5c8b3ae978124c217d11a14351">get_abs2_model</a>(), param));</div>
<div class="line"><a name="l00285"></a><span class="lineno">  285</span>&#160;}</div>
<div class="line"><a name="l00286"></a><span class="lineno">  286</span>&#160; </div>
<div class="line"><a name="l00287"></a><span class="lineno">  287</span>&#160;<span class="comment">// Execute ABS2 for model with &quot;quad_param&quot; and &quot;start&quot;, and returns &quot;sol&quot;.</span></div>
<div class="line"><a name="l00288"></a><span class="lineno"><a class="line" href="group__qbpp__abs2.html#ga515435f4a02b9b0daba94b59e01d5556">  288</a></span>&#160;<span class="keyword">inline</span> <a class="code" href="classqbpp__abs2_1_1Sol.html">Sol</a> <a class="code" href="group__qbpp__abs2.html#ga7665b9e646b22156b8680c64cd08c92c">Solver::operator()</a>(<span class="keyword">const</span> <a class="code" href="classqbpp__abs2_1_1QuadModel.html">QuadModel</a> &amp;quad_model, <a class="code" href="classqbpp__abs2_1_1Param.html">Param</a> &amp;param,</div>
<div class="line"><a name="l00289"></a><span class="lineno">  289</span>&#160;                              <span class="keyword">const</span> <a class="code" href="classqbpp__abs2_1_1Sol.html">Sol</a> &amp;start)<span class="keyword"> const </span>{</div>
<div class="line"><a name="l00290"></a><span class="lineno">  290</span>&#160;  <span class="comment">// target_energy is adjusted by the constant term of the QUBO model because</span></div>
<div class="line"><a name="l00291"></a><span class="lineno">  291</span>&#160;  <span class="comment">// ABS2 Solver does not handle constant terms.</span></div>
<div class="line"><a name="l00292"></a><span class="lineno">  292</span>&#160;  <span class="keywordflow">if</span> (param.<a class="code" href="group__qbpp__abs2.html#ga7436ebabb619505e0f7067f8b812e2a3">get_target_energy</a>().has_value()) {</div>
<div class="line"><a name="l00293"></a><span class="lineno">  293</span>&#160;    param.<a class="code" href="classabs2_1_1Param.html#a7fd8ca3ff207475b8ad4a3d5040ccd01">set</a>(<span class="stringliteral">&quot;target_energy&quot;</span>,</div>
<div class="line"><a name="l00294"></a><span class="lineno">  294</span>&#160;              std::to_string(param.<a class="code" href="group__qbpp__abs2.html#ga7436ebabb619505e0f7067f8b812e2a3">get_target_energy</a>().value() -</div>
<div class="line"><a name="l00295"></a><span class="lineno">  295</span>&#160;                             quad_model.<a class="code" href="classqbpp_1_1Model.html#a3b0641c97573b248f1877c2826277102">constant</a>()));</div>
<div class="line"><a name="l00296"></a><span class="lineno">  296</span>&#160;  }</div>
<div class="line"><a name="l00297"></a><span class="lineno">  297</span>&#160;  <span class="keywordflow">return</span> <a class="code" href="classqbpp__abs2_1_1Sol.html">Sol</a>(quad_model,</div>
<div class="line"><a name="l00298"></a><span class="lineno">  298</span>&#160;             abs2::Solver::operator()(quad_model.<a class="code" href="classqbpp__abs2_1_1QuadModel.html#a81d36c5c8b3ae978124c217d11a14351">get_abs2_model</a>(), param,</div>
<div class="line"><a name="l00299"></a><span class="lineno">  299</span>&#160;                                      *start.<a class="code" href="classqbpp__abs2_1_1Sol.html#a8de3bebb198a0820db0446f9686e914d">get_abs2sol_ptr</a>()));</div>
<div class="line"><a name="l00300"></a><span class="lineno">  300</span>&#160;}</div>
<div class="line"><a name="l00301"></a><span class="lineno">  301</span>&#160; </div>
<div class="line"><a name="l00302"></a><span class="lineno">  302</span>&#160;<span class="comment">//=============================</span></div>
<div class="line"><a name="l00303"></a><span class="lineno">  303</span>&#160;<span class="comment">// Class Sol member functions</span></div>
<div class="line"><a name="l00304"></a><span class="lineno">  304</span>&#160;<span class="comment">//=============================</span></div>
<div class="line"><a name="l00305"></a><span class="lineno"><a class="line" href="group__qbpp__abs2.html#gaba8e35f84b2f822cd2633b61358370db">  305</a></span>&#160;<span class="keyword">inline</span> <a class="code" href="classqbpp_1_1Sol.html#afb26ebcc595aa1391f18c2968713fdba">Sol::Sol</a>(<span class="keyword">const</span> <a class="code" href="classqbpp__abs2_1_1QuadModel.html">QuadModel</a> &amp;quad_model, <span class="keyword">const</span> <a class="code" href="classabs2_1_1Sol.html">abs2::Sol</a> &amp;sol)</div>
<div class="line"><a name="l00306"></a><span class="lineno">  306</span>&#160;    : <a class="code" href="namespaceqbpp.html">qbpp</a>::<a class="code" href="classqbpp__abs2_1_1Sol.html">Sol</a>(quad_model), abs2sol_ptr(std::make_shared&lt;<a class="code" href="namespaceabs2.html">abs2</a>::<a class="code" href="classqbpp__abs2_1_1Sol.html">Sol</a>&gt;(sol)) {</div>
<div class="line"><a name="l00307"></a><span class="lineno">  307</span>&#160;  <span class="keywordflow">for</span> (<a class="code" href="namespaceqbpp.html#a5cf436100ede362797d0c0501ea50a5a">qbpp::vindex_t</a> i = 0; i &lt; quad_model.<a class="code" href="classqbpp_1_1Model.html#a5cf2bb955c3aa25951ca499d7d8635d2">var_count</a>(); i++) {</div>
<div class="line"><a name="l00308"></a><span class="lineno">  308</span>&#160;    <a class="code" href="classqbpp_1_1Sol.html#a2f7494eb4a254dd19b0261e801bacffb">qbpp::Sol::set</a>(i, <a class="code" href="classqbpp__abs2_1_1Sol.html#a2979356a3719cec1699dab0ba5c80ea7">abs2sol_ptr</a>-&gt;get(i));</div>
<div class="line"><a name="l00309"></a><span class="lineno">  309</span>&#160;  }</div>
<div class="line"><a name="l00310"></a><span class="lineno">  310</span>&#160;  <a class="code" href="classqbpp_1_1Sol.html#a5dc3ec6002a1d823ad1bd525e9f6fa3e">energy_</a> = std::stod(<a class="code" href="classqbpp__abs2_1_1Sol.html#a2979356a3719cec1699dab0ba5c80ea7">abs2sol_ptr</a>-&gt;get(<span class="stringliteral">&quot;energy&quot;</span>)) + quad_model.<a class="code" href="classqbpp_1_1Model.html#a3b0641c97573b248f1877c2826277102">constant</a>();</div>
<div class="line"><a name="l00311"></a><span class="lineno">  311</span>&#160;}</div>
<div class="line"><a name="l00312"></a><span class="lineno">  312</span>&#160; </div>
<div class="line"><a name="l00313"></a><span class="lineno"><a class="line" href="group__qbpp__abs2.html#gac27ae911e72fdeb424a950d41fbef95c">  313</a></span>&#160;<span class="keyword">inline</span> <a class="code" href="classqbpp_1_1Sol.html#afb26ebcc595aa1391f18c2968713fdba">Sol::Sol</a>(<span class="keyword">const</span> <a class="code" href="classqbpp_1_1Sol.html">qbpp::Sol</a> &amp;sol)</div>
<div class="line"><a name="l00314"></a><span class="lineno">  314</span>&#160;    : <a class="code" href="namespaceqbpp.html">qbpp</a>::<a class="code" href="classqbpp__abs2_1_1Sol.html">Sol</a>(sol), abs2sol_ptr(std::make_shared&lt;<a class="code" href="namespaceabs2.html">abs2</a>::<a class="code" href="classqbpp__abs2_1_1Sol.html">Sol</a>&gt;(var_count())) {</div>
<div class="line"><a name="l00315"></a><span class="lineno">  315</span>&#160;  <span class="keywordflow">for</span> (<a class="code" href="namespaceqbpp.html#a5cf436100ede362797d0c0501ea50a5a">qbpp::vindex_t</a> i = 0; i &lt; <a class="code" href="classqbpp_1_1Sol.html#a06cbfd519670efbc74363592d5e826b2">var_count</a>(); i++) {</div>
<div class="line"><a name="l00316"></a><span class="lineno">  316</span>&#160;    <a class="code" href="classqbpp__abs2_1_1Sol.html#a2979356a3719cec1699dab0ba5c80ea7">abs2sol_ptr</a>-&gt;set(i, sol.<a class="code" href="classqbpp_1_1Sol.html#abdb4f6b779678720715f7845f264440f">get</a>(i));</div>
<div class="line"><a name="l00317"></a><span class="lineno">  317</span>&#160;  }</div>
<div class="line"><a name="l00318"></a><span class="lineno">  318</span>&#160;}</div>
<div class="line"><a name="l00319"></a><span class="lineno">  319</span>&#160; </div>
<div class="line"><a name="l00320"></a><span class="lineno">  320</span>&#160;}  <span class="comment">// namespace qbpp_abs2</span></div>
<div class="line"><a name="l00321"></a><span class="lineno">  321</span>&#160;<span class="preprocessor">#endif  // QBPP_ABS2_HPP</span></div>
</div><!-- fragment --></div><!-- contents -->
<div class="ttc" id="aclassabs2_1_1Model_html"><div class="ttname"><a href="classabs2_1_1Model.html">abs2::Model</a></div><div class="ttdoc">Class to store and manipulate a QUBO model.</div><div class="ttdef"><b>Definition:</b> <a href="abs2_8hpp_source.html#l00091">abs2.hpp:91</a></div></div>
<div class="ttc" id="aclassqbpp__abs2_1_1QuadModel_html_a81d36c5c8b3ae978124c217d11a14351"><div class="ttname"><a href="classqbpp__abs2_1_1QuadModel.html#a81d36c5c8b3ae978124c217d11a14351">qbpp_abs2::QuadModel::get_abs2_model</a></div><div class="ttdeci">const abs2::Model &amp; get_abs2_model() const</div><div class="ttdoc">Get the ABS2 model.</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__abs2_8hpp_source.html#l00095">qbpp_abs2.hpp:95</a></div></div>
<div class="ttc" id="aclassqbpp__abs2_1_1Sol_html_a32d5e94f5ec2e824cbb8669e5bb1c69a"><div class="ttname"><a href="classqbpp__abs2_1_1Sol.html#a32d5e94f5ec2e824cbb8669e5bb1c69a">qbpp_abs2::Sol::print</a></div><div class="ttdeci">void print(const std::string &amp;attrs) const</div><div class="ttdoc">print the solution</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__abs2_8hpp_source.html#l00174">qbpp_abs2.hpp:174</a></div></div>
<div class="ttc" id="aclassqbpp__abs2_1_1QuadModel_html"><div class="ttname"><a href="classqbpp__abs2_1_1QuadModel.html">qbpp_abs2::QuadModel</a></div><div class="ttdoc">A class for storing both the qbpp::QuadModel and abs2::Model.</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__abs2_8hpp_source.html#l00076">qbpp_abs2.hpp:76</a></div></div>
<div class="ttc" id="agroup__qbpp__abs2_html_ga692214095508b4187ec18035e1c275e8"><div class="ttname"><a href="group__qbpp__abs2.html#ga692214095508b4187ec18035e1c275e8">qbpp_abs2::Callback::callback</a></div><div class="ttdeci">virtual void callback(const std::string &amp;event)</div><div class="ttdoc">The default callback function for ABS2 QUBO solver.</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__abs2_8hpp_source.html#l00234">qbpp_abs2.hpp:234</a></div></div>
<div class="ttc" id="aclassqbpp_1_1Sol_html_abdb4f6b779678720715f7845f264440f"><div class="ttname"><a href="classqbpp_1_1Sol.html#abdb4f6b779678720715f7845f264440f">qbpp::Sol::get</a></div><div class="ttdeci">var_val_t get(vindex_t index) const</div><div class="ttdef"><b>Definition:</b> <a href="qbpp_8hpp_source.html#l02243">qbpp.hpp:2243</a></div></div>
<div class="ttc" id="aclassqbpp_1_1QuadModel_html_a06b84580a41556201ab2f4bb3d838403"><div class="ttname"><a href="classqbpp_1_1QuadModel.html#a06b84580a41556201ab2f4bb3d838403">qbpp::QuadModel::min_coeff</a></div><div class="ttdeci">coeff_t min_coeff() const</div><div class="ttdef"><b>Definition:</b> <a href="qbpp_8hpp_source.html#l02004">qbpp.hpp:2004</a></div></div>
<div class="ttc" id="aclassabs2_1_1Callback_html_abe677099f6a83a3c588d1609a542420e"><div class="ttname"><a href="classabs2_1_1Callback.html#abe677099f6a83a3c588d1609a542420e">abs2::Callback::get</a></div><div class="ttdeci">const Sol &amp; get() const</div><div class="ttdoc">Get the current solution.</div></div>
<div class="ttc" id="aclassabs2_1_1Sol_html"><div class="ttname"><a href="classabs2_1_1Sol.html">abs2::Sol</a></div><div class="ttdoc">Class to store a solution computed by ABS2 QUBO solver.</div><div class="ttdef"><b>Definition:</b> <a href="abs2_8hpp_source.html#l00205">abs2.hpp:205</a></div></div>
<div class="ttc" id="agroup__qbpp__abs2_html_ga5946dc74bd5efe6937f6fd3d2fbc4551"><div class="ttname"><a href="group__qbpp__abs2.html#ga5946dc74bd5efe6937f6fd3d2fbc4551">qbpp_abs2::Callback::get_sol</a></div><div class="ttdeci">Sol get_sol() const</div><div class="ttdoc">Get the solution from the ABS2 solver.</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__abs2_8hpp_source.html#l00220">qbpp_abs2.hpp:220</a></div></div>
<div class="ttc" id="aclassqbpp__abs2_1_1QuadModel_html_ae407053ddc1028ef6253ee3f5d23073d"><div class="ttname"><a href="classqbpp__abs2_1_1QuadModel.html#ae407053ddc1028ef6253ee3f5d23073d">qbpp_abs2::QuadModel::QuadModel</a></div><div class="ttdeci">QuadModel()=delete</div><div class="ttdoc">Default constructor is deleted to avoid creating an empty model.</div></div>
<div class="ttc" id="anamespaceqbpp_html_a1cf3f678bcc7eee089c75d4bc5a0b959"><div class="ttname"><a href="namespaceqbpp.html#a1cf3f678bcc7eee089c75d4bc5a0b959">qbpp::var</a></div><div class="ttdeci">Var var(const std::string &amp;var_str)</div><div class="ttdef"><b>Definition:</b> <a href="qbpp_8hpp_source.html#l02887">qbpp.hpp:2887</a></div></div>
<div class="ttc" id="anamespaceqbpp_html_a9c324f641779d1a7a3c7723f28d8cff4"><div class="ttname"><a href="namespaceqbpp.html#a9c324f641779d1a7a3c7723f28d8cff4">qbpp::energy_t</a></div><div class="ttdeci">ENERGY_TYPE energy_t</div><div class="ttdef"><b>Definition:</b> <a href="qbpp_8hpp_source.html#l00137">qbpp.hpp:137</a></div></div>
<div class="ttc" id="aclassqbpp_1_1QuadModel_html"><div class="ttname"><a href="classqbpp_1_1QuadModel.html">qbpp::QuadModel</a></div><div class="ttdef"><b>Definition:</b> <a href="qbpp_8hpp_source.html#l01892">qbpp.hpp:1892</a></div></div>
<div class="ttc" id="aclassabs2_1_1Solver_html"><div class="ttname"><a href="classabs2_1_1Solver.html">abs2::Solver</a></div><div class="ttdoc">Class to configure the ABS2 QUBO solver.</div><div class="ttdef"><b>Definition:</b> <a href="abs2_8hpp_source.html#l00043">abs2.hpp:43</a></div></div>
<div class="ttc" id="aclassqbpp_1_1QuadModel_html_a591e74de688dbff1339c2c9d2352df65"><div class="ttname"><a href="classqbpp_1_1QuadModel.html#a591e74de688dbff1339c2c9d2352df65">qbpp::QuadModel::linear</a></div><div class="ttdeci">const std::vector&lt; coeff_t &gt; &amp; linear() const</div><div class="ttdef"><b>Definition:</b> <a href="qbpp_8hpp_source.html#l01967">qbpp.hpp:1967</a></div></div>
<div class="ttc" id="agroup__qbpp__abs2_html_ga7436ebabb619505e0f7067f8b812e2a3"><div class="ttname"><a href="group__qbpp__abs2.html#ga7436ebabb619505e0f7067f8b812e2a3">qbpp_abs2::Param::get_target_energy</a></div><div class="ttdeci">std::optional&lt; qbpp::energy_t &gt; get_target_energy() const</div><div class="ttdoc">Get the target energy for ABS2 QUBO solver.</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__abs2_8hpp_source.html#l00126">qbpp_abs2.hpp:126</a></div></div>
<div class="ttc" id="aclassqbpp__abs2_1_1Sol_html_a48d9116f9fcbe00f84640847ee49df23"><div class="ttname"><a href="classqbpp__abs2_1_1Sol.html#a48d9116f9fcbe00f84640847ee49df23">qbpp_abs2::Sol::set</a></div><div class="ttdeci">void set(int index, bool value)</div><div class="ttdoc">Set the value of the variable with &quot;index&quot; to &quot;value&quot;.</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__abs2_8hpp_source.html#l00179">qbpp_abs2.hpp:179</a></div></div>
<div class="ttc" id="aclassqbpp__abs2_1_1Sol_html_a2979356a3719cec1699dab0ba5c80ea7"><div class="ttname"><a href="classqbpp__abs2_1_1Sol.html#a2979356a3719cec1699dab0ba5c80ea7">qbpp_abs2::Sol::abs2sol_ptr</a></div><div class="ttdeci">const std::shared_ptr&lt; abs2::Sol &gt; abs2sol_ptr</div><div class="ttdoc">Sol object created by ABS2 QUBO solver.</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__abs2_8hpp_source.html#l00151">qbpp_abs2.hpp:151</a></div></div>
<div class="ttc" id="agroup__qbpp__abs2_html_ga2754c68b51c3d0452731e5da95920fe2"><div class="ttname"><a href="group__qbpp__abs2.html#ga2754c68b51c3d0452731e5da95920fe2">qbpp_abs2::Param::set_arithmetic_bits</a></div><div class="ttdeci">void set_arithmetic_bits(uint32_t bits)</div><div class="ttdoc">Set the arithmetic bits.</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__abs2_8hpp_source.html#l00138">qbpp_abs2.hpp:138</a></div></div>
<div class="ttc" id="aclassabs2_1_1Param_html_a7fd8ca3ff207475b8ad4a3d5040ccd01"><div class="ttname"><a href="classabs2_1_1Param.html#a7fd8ca3ff207475b8ad4a3d5040ccd01">abs2::Param::set</a></div><div class="ttdeci">void set(const std::string &amp;key, const std::string &amp;val)</div><div class="ttdoc">Set &quot;val&quot; to &quot;key&quot;.</div></div>
<div class="ttc" id="agroup__qbpp__abs2_html_gac5b2aaa6db9827d709125edcbe0265a4"><div class="ttname"><a href="group__qbpp__abs2.html#gac5b2aaa6db9827d709125edcbe0265a4">qbpp_abs2::Param::set_target_energy</a></div><div class="ttdeci">void set_target_energy(qbpp::energy_t target_energy)</div><div class="ttdoc">Set the target energy for ABS2 QUBO solver.</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__abs2_8hpp_source.html#l00120">qbpp_abs2.hpp:120</a></div></div>
<div class="ttc" id="aclassqbpp__abs2_1_1Param_html_a39060f29eb41259853f58b5c06348da5"><div class="ttname"><a href="classqbpp__abs2_1_1Param.html#a39060f29eb41259853f58b5c06348da5">qbpp_abs2::Param::target_energy</a></div><div class="ttdeci">std::optional&lt; qbpp::energy_t &gt; target_energy</div><div class="ttdoc">The target energy given by C++ library.</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__abs2_8hpp_source.html#l00108">qbpp_abs2.hpp:108</a></div></div>
<div class="ttc" id="agroup__qbpp__abs2_html_ga33c85eefb81407d622de58bd2775598c"><div class="ttname"><a href="group__qbpp__abs2.html#ga33c85eefb81407d622de58bd2775598c">qbpp_abs2::Param::set_time_limit</a></div><div class="ttdeci">void set_time_limit(uint32_t time_limit)</div><div class="ttdoc">Set the time limit for ABS2 QUBO solver.</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__abs2_8hpp_source.html#l00132">qbpp_abs2.hpp:132</a></div></div>
<div class="ttc" id="aclassqbpp_1_1Var_html"><div class="ttname"><a href="classqbpp_1_1Var.html">qbpp::Var</a></div><div class="ttdef"><b>Definition:</b> <a href="qbpp_8hpp_source.html#l00729">qbpp.hpp:729</a></div></div>
<div class="ttc" id="aclassqbpp__abs2_1_1Sol_html_a04aabe5387a17b86a90acaa607175545"><div class="ttname"><a href="classqbpp__abs2_1_1Sol.html#a04aabe5387a17b86a90acaa607175545">qbpp_abs2::Sol::set</a></div><div class="ttdeci">void set(qbpp::Var var, bool value)</div><div class="ttdoc">Set the value of the variable &quot;var&quot; to &quot;value&quot;.</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__abs2_8hpp_source.html#l00187">qbpp_abs2.hpp:187</a></div></div>
<div class="ttc" id="aclassqbpp_1_1QuadModel_html_a6624038d4ff117176c37de2d1260f471"><div class="ttname"><a href="classqbpp_1_1QuadModel.html#a6624038d4ff117176c37de2d1260f471">qbpp::QuadModel::quadratic</a></div><div class="ttdeci">const std::vector&lt; std::vector&lt; std::pair&lt; vindex_t, coeff_t &gt; &gt; &gt; &amp; quadratic() const</div><div class="ttdef"><b>Definition:</b> <a href="qbpp_8hpp_source.html#l01971">qbpp.hpp:1971</a></div></div>
<div class="ttc" id="agroup__qbpp__abs2_html_gacde6f2007700c2ac188f3a88c5bba1d0"><div class="ttname"><a href="group__qbpp__abs2.html#gacde6f2007700c2ac188f3a88c5bba1d0">qbpp_abs2::Callback::set_hint</a></div><div class="ttdeci">void set_hint(const Sol &amp;hint)</div><div class="ttdoc">Provide a hint solution to the ABS2 solver.</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__abs2_8hpp_source.html#l00225">qbpp_abs2.hpp:225</a></div></div>
<div class="ttc" id="anamespaceabs2_html"><div class="ttname"><a href="namespaceabs2.html">abs2</a></div><div class="ttdoc">Namespace to call ABS2 GPU QUBO solver.</div><div class="ttdef"><b>Definition:</b> <a href="abs2_8hpp_source.html#l00026">abs2.hpp:26</a></div></div>
<div class="ttc" id="aclassqbpp_1_1QuadModel_html_ab6ac73dcc23030f0f7e303e94d3c2bd3"><div class="ttname"><a href="classqbpp_1_1QuadModel.html#ab6ac73dcc23030f0f7e303e94d3c2bd3">qbpp::QuadModel::max_coeff</a></div><div class="ttdeci">coeff_t max_coeff() const</div><div class="ttdef"><b>Definition:</b> <a href="qbpp_8hpp_source.html#l02006">qbpp.hpp:2006</a></div></div>
<div class="ttc" id="aclassqbpp_1_1Sol_html_a5dc3ec6002a1d823ad1bd525e9f6fa3e"><div class="ttname"><a href="classqbpp_1_1Sol.html#a5dc3ec6002a1d823ad1bd525e9f6fa3e">qbpp::Sol::energy_</a></div><div class="ttdeci">std::optional&lt; energy_t &gt; energy_</div><div class="ttdef"><b>Definition:</b> <a href="qbpp_8hpp_source.html#l02188">qbpp.hpp:2188</a></div></div>
<div class="ttc" id="aclassabs2_1_1Param_html"><div class="ttname"><a href="classabs2_1_1Param.html">abs2::Param</a></div><div class="ttdoc">Class to store parameters for ABS2 QUBO solver.</div><div class="ttdef"><b>Definition:</b> <a href="abs2_8hpp_source.html#l00162">abs2.hpp:162</a></div></div>
<div class="ttc" id="aclassqbpp_1_1Sol_html_a06cbfd519670efbc74363592d5e826b2"><div class="ttname"><a href="classqbpp_1_1Sol.html#a06cbfd519670efbc74363592d5e826b2">qbpp::Sol::var_count</a></div><div class="ttdeci">vindex_t var_count() const</div><div class="ttdef"><b>Definition:</b> <a href="qbpp_8hpp_source.html#l02319">qbpp.hpp:2319</a></div></div>
<div class="ttc" id="aqbpp_8hpp_html"><div class="ttname"><a href="qbpp_8hpp.html">qbpp.hpp</a></div><div class="ttdoc">QUBO++, a C++ library for generating expressions for binary and spin variables.</div></div>
<div class="ttc" id="agroup__qbpp__abs2_html_ga6b4dd18ef3cc81619afee888b7bf64d2"><div class="ttname"><a href="group__qbpp__abs2.html#ga6b4dd18ef3cc81619afee888b7bf64d2">qbpp_abs2::Callback::Callback</a></div><div class="ttdeci">Callback(const QuadModel &amp;quad_model)</div><div class="ttdoc">Construct a new Callback object.</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__abs2_8hpp_source.html#l00215">qbpp_abs2.hpp:215</a></div></div>
<div class="ttc" id="aclassqbpp__abs2_1_1QuadModel_html_ae79cbaf57fdafd9fd834aba1e0e615f5"><div class="ttname"><a href="classqbpp__abs2_1_1QuadModel.html#ae79cbaf57fdafd9fd834aba1e0e615f5">qbpp_abs2::QuadModel::QuadModel</a></div><div class="ttdeci">QuadModel(const QuadModel &amp;quad_model)</div><div class="ttdoc">Copy constructor.</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__abs2_8hpp_source.html#l00090">qbpp_abs2.hpp:90</a></div></div>
<div class="ttc" id="aclassqbpp_1_1Model_html_a3b0641c97573b248f1877c2826277102"><div class="ttname"><a href="classqbpp_1_1Model.html#a3b0641c97573b248f1877c2826277102">qbpp::Model::constant</a></div><div class="ttdeci">energy_t constant() const</div><div class="ttdef"><b>Definition:</b> <a href="qbpp_8hpp_source.html#l01873">qbpp.hpp:1873</a></div></div>
<div class="ttc" id="aabs2_8hpp_html"><div class="ttname"><a href="abs2_8hpp.html">abs2.hpp</a></div><div class="ttdoc">API for the ABS2 GPU QUBO Solver.</div></div>
<div class="ttc" id="aclassqbpp__abs2_1_1Sol_html"><div class="ttname"><a href="classqbpp__abs2_1_1Sol.html">qbpp_abs2::Sol</a></div><div class="ttdoc">A class for storing the ABS2 QUBO solution.</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__abs2_8hpp_source.html#l00148">qbpp_abs2.hpp:148</a></div></div>
<div class="ttc" id="aclassqbpp__abs2_1_1Sol_html_ae35741170c9c1e91c8c700221fb053e2"><div class="ttname"><a href="classqbpp__abs2_1_1Sol.html#ae35741170c9c1e91c8c700221fb053e2">qbpp_abs2::Sol::get_tts</a></div><div class="ttdeci">double get_tts() const</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__abs2_8hpp_source.html#l00192">qbpp_abs2.hpp:192</a></div></div>
<div class="ttc" id="aclassqbpp_1_1Sol_html_afb26ebcc595aa1391f18c2968713fdba"><div class="ttname"><a href="classqbpp_1_1Sol.html#afb26ebcc595aa1391f18c2968713fdba">qbpp::Sol::Sol</a></div><div class="ttdeci">Sol()=delete</div></div>
<div class="ttc" id="aclassqbpp__abs2_1_1Solver_html"><div class="ttname"><a href="classqbpp__abs2_1_1Solver.html">qbpp_abs2::Solver</a></div><div class="ttdoc">A class for calling the ABS2 QUBO solver.</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__abs2_8hpp_source.html#l00053">qbpp_abs2.hpp:53</a></div></div>
<div class="ttc" id="aclassqbpp__abs2_1_1Callback_html_a239479a75950e3b40e2ec5faf32d0b30"><div class="ttname"><a href="classqbpp__abs2_1_1Callback.html#a239479a75950e3b40e2ec5faf32d0b30">qbpp_abs2::Callback::quad_model</a></div><div class="ttdeci">const QuadModel quad_model</div><div class="ttdoc">QuadModel object created by QUBO++ library.</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__abs2_8hpp_source.html#l00207">qbpp_abs2.hpp:207</a></div></div>
<div class="ttc" id="anamespaceqbpp_html"><div class="ttname"><a href="namespaceqbpp.html">qbpp</a></div><div class="ttdoc">Generates a QUBO Expression for the Graph Coloring Problem using QUBO++ library.</div><div class="ttdef"><b>Definition:</b> <a href="qbpp_8hpp_source.html#l00052">qbpp.hpp:52</a></div></div>
<div class="ttc" id="aclassabs2_1_1Callback_html_a365f96a6e4f72d37ae897841d07e55ab"><div class="ttname"><a href="classabs2_1_1Callback.html#a365f96a6e4f72d37ae897841d07e55ab">abs2::Callback::set</a></div><div class="ttdeci">void set(const std::string &amp;operation)</div><div class="ttdoc">Set the operation to the ABS2 Callback.</div></div>
<div class="ttc" id="anamespaceqbpp_html_a5cf436100ede362797d0c0501ea50a5a"><div class="ttname"><a href="namespaceqbpp.html#a5cf436100ede362797d0c0501ea50a5a">qbpp::vindex_t</a></div><div class="ttdeci">uint32_t vindex_t</div><div class="ttdef"><b>Definition:</b> <a href="qbpp_8hpp_source.html#l00129">qbpp.hpp:129</a></div></div>
<div class="ttc" id="aclassqbpp_1_1Sol_html_a2f7494eb4a254dd19b0261e801bacffb"><div class="ttname"><a href="classqbpp_1_1Sol.html#a2f7494eb4a254dd19b0261e801bacffb">qbpp::Sol::set</a></div><div class="ttdeci">void set(vindex_t index, bool value)</div><div class="ttdef"><b>Definition:</b> <a href="qbpp_8hpp_source.html#l02270">qbpp.hpp:2270</a></div></div>
<div class="ttc" id="aclassqbpp__abs2_1_1QuadModel_html_a763aa7381304ed9bd82054b6614c929f"><div class="ttname"><a href="classqbpp__abs2_1_1QuadModel.html#a763aa7381304ed9bd82054b6614c929f">qbpp_abs2::QuadModel::abs2model_ptr</a></div><div class="ttdeci">std::shared_ptr&lt; abs2::Model &gt; abs2model_ptr</div><div class="ttdoc">ABS2 model associated with the QUBO model.</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__abs2_8hpp_source.html#l00078">qbpp_abs2.hpp:78</a></div></div>
<div class="ttc" id="aclassqbpp_1_1QuadModel_html_ada71ec32906c9d8c73bd25c4267bafc5"><div class="ttname"><a href="classqbpp_1_1QuadModel.html#ada71ec32906c9d8c73bd25c4267bafc5">qbpp::QuadModel::degree</a></div><div class="ttdeci">const std::vector&lt; vindex_t &gt; &amp; degree() const</div><div class="ttdef"><b>Definition:</b> <a href="qbpp_8hpp_source.html#l01969">qbpp.hpp:1969</a></div></div>
<div class="ttc" id="aclassqbpp_1_1Sol_html"><div class="ttname"><a href="classqbpp_1_1Sol.html">qbpp::Sol</a></div><div class="ttdef"><b>Definition:</b> <a href="qbpp_8hpp_source.html#l02182">qbpp.hpp:2182</a></div></div>
<div class="ttc" id="aclassqbpp_1_1Sol_html_a24f5a53fa68071dd9bcda910533bbad7"><div class="ttname"><a href="classqbpp_1_1Sol.html#a24f5a53fa68071dd9bcda910533bbad7">qbpp::Sol::index</a></div><div class="ttdeci">vindex_t index(Var var) const</div><div class="ttdef"><b>Definition:</b> <a href="qbpp_8hpp_source.html#l02325">qbpp.hpp:2325</a></div></div>
<div class="ttc" id="aclassqbpp__abs2_1_1Sol_html_a8de3bebb198a0820db0446f9686e914d"><div class="ttname"><a href="classqbpp__abs2_1_1Sol.html#a8de3bebb198a0820db0446f9686e914d">qbpp_abs2::Sol::get_abs2sol_ptr</a></div><div class="ttdeci">const std::shared_ptr&lt; abs2::Sol &gt; get_abs2sol_ptr() const</div><div class="ttdoc">Returns the reference to the ABS2 solution.</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__abs2_8hpp_source.html#l00196">qbpp_abs2.hpp:196</a></div></div>
<div class="ttc" id="aclassqbpp__abs2_1_1Callback_html"><div class="ttname"><a href="classqbpp__abs2_1_1Callback.html">qbpp_abs2::Callback</a></div><div class="ttdoc">A class for defining the ABS2 callback function.</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__abs2_8hpp_source.html#l00204">qbpp_abs2.hpp:204</a></div></div>
<div class="ttc" id="anamespaceqbpp__abs2_html"><div class="ttname"><a href="namespaceqbpp__abs2.html">qbpp_abs2</a></div><div class="ttdoc">Namespace to use ABS2 QUBO solver from QUBO++ library.</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__abs2_8hpp_source.html#l00016">qbpp_abs2.hpp:16</a></div></div>
<div class="ttc" id="agroup__qbpp__abs2_html_ga7665b9e646b22156b8680c64cd08c92c"><div class="ttname"><a href="group__qbpp__abs2.html#ga7665b9e646b22156b8680c64cd08c92c">qbpp_abs2::Solver::operator()</a></div><div class="ttdeci">Sol operator()(const QuadModel &amp;quad_model, Param &amp;param) const</div><div class="ttdoc">Executes ABS2 for &quot;quad_model&quot; with &quot;param&quot; and returns &quot;sol&quot;.</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__abs2_8hpp_source.html#l00275">qbpp_abs2.hpp:275</a></div></div>
<div class="ttc" id="aclassabs2_1_1Callback_html"><div class="ttname"><a href="classabs2_1_1Callback.html">abs2::Callback</a></div><div class="ttdoc">Class to manage callback function for ABS2 QUBO solver.</div><div class="ttdef"><b>Definition:</b> <a href="abs2_8hpp_source.html#l00258">abs2.hpp:258</a></div></div>
<div class="ttc" id="aclassqbpp__abs2_1_1Param_html"><div class="ttname"><a href="classqbpp__abs2_1_1Param.html">qbpp_abs2::Param</a></div><div class="ttdoc">A class for setting parameters for the ABS2 QUBO solver.</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__abs2_8hpp_source.html#l00104">qbpp_abs2.hpp:104</a></div></div>
<div class="ttc" id="agroup__qbpp__abs2_html_ga03882e256386f69f9d004d28b9e7b53b"><div class="ttname"><a href="group__qbpp__abs2.html#ga03882e256386f69f9d004d28b9e7b53b">qbpp_abs2::Param::Param</a></div><div class="ttdeci">Param()=default</div><div class="ttdoc">Construct a new Param object from a QuadModel object.</div></div>
<div class="ttc" id="aclassqbpp_1_1Model_html_a5cf2bb955c3aa25951ca499d7d8635d2"><div class="ttname"><a href="classqbpp_1_1Model.html#a5cf2bb955c3aa25951ca499d7d8635d2">qbpp::Model::var_count</a></div><div class="ttdeci">vindex_t var_count() const</div><div class="ttdef"><b>Definition:</b> <a href="qbpp_8hpp_source.html#l01859">qbpp.hpp:1859</a></div></div>
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.17
</small></address>
</body>
</html>
