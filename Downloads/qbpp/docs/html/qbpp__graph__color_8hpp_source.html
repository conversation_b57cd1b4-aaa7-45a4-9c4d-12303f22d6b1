<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.17"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>QUBO++ Library with QUBO Solver APIs: sample/qbpp_graph_color.hpp Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/x-mathjax-config">
  MathJax.Hub.Config({
    extensions: ["tex2jax.js", "TeX/AMSmath.js", "TeX/AMSsymbols.js"],
    jax: ["input/TeX","output/HTML-CSS"],
});
</script>
<script type="text/javascript" async="async" src="https://cdn.jsdelivr.net/npm/mathjax@2/MathJax.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">QUBO++ Library with QUBO Solver APIs
   </div>
   <div id="projectbrief">Author: Koji Nakano, License: Non-commercial research and evaluation purposes without any guarantees.</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.17 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_c29eeb5af533606caea8a09e66794740.html">sample</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">qbpp_graph_color.hpp</div>  </div>
</div><!--header-->
<div class="contents">
<a href="qbpp__graph__color_8hpp.html">Go to the documentation of this file.</a><div class="fragment"><div class="line"><a name="l00001"></a><span class="lineno">    1</span>&#160; </div>
<div class="line"><a name="l00006"></a><span class="lineno">    6</span>&#160;<span class="preprocessor">#include &lt;boost/polygon/voronoi.hpp&gt;</span></div>
<div class="line"><a name="l00007"></a><span class="lineno">    7</span>&#160;<span class="preprocessor">#include &lt;iostream&gt;</span></div>
<div class="line"><a name="l00008"></a><span class="lineno">    8</span>&#160;<span class="preprocessor">#include &lt;limits&gt;</span></div>
<div class="line"><a name="l00009"></a><span class="lineno">    9</span>&#160;<span class="preprocessor">#include &lt;memory&gt;</span></div>
<div class="line"><a name="l00010"></a><span class="lineno">   10</span>&#160;<span class="preprocessor">#include &lt;queue&gt;</span></div>
<div class="line"><a name="l00011"></a><span class="lineno">   11</span>&#160;<span class="preprocessor">#include &lt;random&gt;</span></div>
<div class="line"><a name="l00012"></a><span class="lineno">   12</span>&#160;<span class="preprocessor">#include &lt;sstream&gt;</span></div>
<div class="line"><a name="l00013"></a><span class="lineno">   13</span>&#160;<span class="preprocessor">#include &lt;string&gt;</span></div>
<div class="line"><a name="l00014"></a><span class="lineno">   14</span>&#160;<span class="preprocessor">#include &lt;utility&gt;</span></div>
<div class="line"><a name="l00015"></a><span class="lineno">   15</span>&#160;<span class="preprocessor">#include &lt;vector&gt;</span></div>
<div class="line"><a name="l00016"></a><span class="lineno">   16</span>&#160; </div>
<div class="line"><a name="l00017"></a><span class="lineno">   17</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="qbpp_8hpp.html">qbpp.hpp</a>&quot;</span></div>
<div class="line"><a name="l00018"></a><span class="lineno">   18</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="qbpp__misc_8hpp.html">qbpp_misc.hpp</a>&quot;</span></div>
<div class="line"><a name="l00019"></a><span class="lineno">   19</span>&#160; </div>
<div class="line"><a name="l00020"></a><span class="lineno">   20</span>&#160;<span class="keyword">namespace </span><a class="code" href="namespaceqbpp.html">qbpp</a> {</div>
<div class="line"><a name="l00021"></a><span class="lineno">   21</span>&#160; </div>
<div class="line"><a name="l00023"></a><span class="lineno"><a class="line" href="namespaceqbpp_1_1graph__color.html">   23</a></span>&#160;<span class="keyword">namespace </span>graph_color {</div>
<div class="line"><a name="l00024"></a><span class="lineno">   24</span>&#160; </div>
<div class="line"><a name="l00025"></a><span class="lineno">   25</span>&#160;<span class="keyword">class </span><a class="code" href="classqbpp_1_1graph__color_1_1GraphColorMap.html">GraphColorMap</a>;</div>
<div class="line"><a name="l00026"></a><span class="lineno">   26</span>&#160;<span class="keyword">class </span><a class="code" href="classqbpp_1_1graph__color_1_1GraphColorQuadModel.html">GraphColorQuadModel</a>;</div>
<div class="line"><a name="l00027"></a><span class="lineno">   27</span>&#160; </div>
<div class="line"><a name="l00031"></a><span class="lineno"><a class="line" href="classqbpp_1_1graph__color_1_1GraphColorMap.html">   31</a></span>&#160;<span class="keyword">class </span><a class="code" href="classqbpp_1_1graph__color_1_1GraphColorMap.html">GraphColorMap</a> {</div>
<div class="line"><a name="l00034"></a><span class="lineno"><a class="line" href="classqbpp_1_1graph__color_1_1GraphColorMap.html#a5579bbb4b7154c6ff380ab941345698b">   34</a></span>&#160;  <span class="keyword">const</span> uint32_t <a class="code" href="classqbpp_1_1graph__color_1_1GraphColorMap.html#a5579bbb4b7154c6ff380ab941345698b">grid_size_</a>;</div>
<div class="line"><a name="l00035"></a><span class="lineno">   35</span>&#160; </div>
<div class="line"><a name="l00038"></a><span class="lineno"><a class="line" href="classqbpp_1_1graph__color_1_1GraphColorMap.html#ad1dda5ce351f070a0eb1e2f3e328a9a4">   38</a></span>&#160;  std::vector&lt;std::pair&lt;int32_t, int32_t&gt;&gt; <a class="code" href="classqbpp_1_1graph__color_1_1GraphColorMap.html#ad1dda5ce351f070a0eb1e2f3e328a9a4">nodes_</a>;</div>
<div class="line"><a name="l00039"></a><span class="lineno">   39</span>&#160; </div>
<div class="line"><a name="l00044"></a><span class="lineno"><a class="line" href="classqbpp_1_1graph__color_1_1GraphColorMap.html#a50c783bf212bef619d408d379df17a36">   44</a></span>&#160;  std::vector&lt;std::pair&lt;uint32_t, uint32_t&gt;&gt; <a class="code" href="classqbpp_1_1graph__color_1_1GraphColorMap.html#a50c783bf212bef619d408d379df17a36">edges_</a>;</div>
<div class="line"><a name="l00045"></a><span class="lineno">   45</span>&#160; </div>
<div class="line"><a name="l00048"></a><span class="lineno">   48</span>&#160;  <span class="comment">//  @note -1 means that the color is not determined.</span></div>
<div class="line"><a name="l00049"></a><span class="lineno"><a class="line" href="classqbpp_1_1graph__color_1_1GraphColorMap.html#a2964d48ac547f74049b38bba057ed337">   49</a></span>&#160;  <a class="code" href="classqbpp_1_1Vector.html">qbpp::Vector&lt;int32_t&gt;</a> <a class="code" href="classqbpp_1_1graph__color_1_1GraphColorMap.html#a2964d48ac547f74049b38bba057ed337">color_</a>;</div>
<div class="line"><a name="l00050"></a><span class="lineno">   50</span>&#160; </div>
<div class="line"><a name="l00053"></a><span class="lineno"><a class="line" href="classqbpp_1_1graph__color_1_1GraphColorMap.html#aa33498f6607b683532bbc874dd1553f8">   53</a></span>&#160;  std::vector&lt;uint32_t&gt; <a class="code" href="classqbpp_1_1graph__color_1_1GraphColorMap.html#aa33498f6607b683532bbc874dd1553f8">color_hist_</a>;</div>
<div class="line"><a name="l00054"></a><span class="lineno">   54</span>&#160; </div>
<div class="line"><a name="l00058"></a><span class="lineno"><a class="line" href="classqbpp_1_1graph__color_1_1GraphColorMap.html#aa5ff2e4ea686651e6f96d4c133c53333">   58</a></span>&#160;  uint32_t <a class="code" href="classqbpp_1_1graph__color_1_1GraphColorMap.html#aa5ff2e4ea686651e6f96d4c133c53333">failure_</a> = 0;</div>
<div class="line"><a name="l00059"></a><span class="lineno">   59</span>&#160; </div>
<div class="line"><a name="l00063"></a><span class="lineno"><a class="line" href="classqbpp_1_1graph__color_1_1GraphColorMap.html#af229a75b21e41767048e169b0b1ae895">   63</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="classqbpp_1_1graph__color_1_1GraphColorMap.html#af229a75b21e41767048e169b0b1ae895">add_node</a>(uint32_t x, uint32_t y) { <a class="code" href="classqbpp_1_1graph__color_1_1GraphColorMap.html#ad1dda5ce351f070a0eb1e2f3e328a9a4">nodes_</a>.push_back({x, y}); }</div>
<div class="line"><a name="l00064"></a><span class="lineno">   64</span>&#160; </div>
<div class="line"><a name="l00070"></a><span class="lineno"><a class="line" href="classqbpp_1_1graph__color_1_1GraphColorMap.html#a71b8ad384f9a9de9c62390d5e3cf27ad">   70</a></span>&#160;  uint32_t <a class="code" href="classqbpp_1_1graph__color_1_1GraphColorMap.html#a71b8ad384f9a9de9c62390d5e3cf27ad">dist</a>(<span class="keyword">const</span> std::pair&lt;int32_t, int32_t&gt; &amp;p1,</div>
<div class="line"><a name="l00071"></a><span class="lineno">   71</span>&#160;                <span class="keyword">const</span> std::pair&lt;int32_t, int32_t&gt; &amp;p2)<span class="keyword"> const </span>{</div>
<div class="line"><a name="l00072"></a><span class="lineno">   72</span>&#160;    <span class="keywordflow">return</span> <span class="keyword">static_cast&lt;</span>uint32_t<span class="keyword">&gt;</span>(std::round(</div>
<div class="line"><a name="l00073"></a><span class="lineno">   73</span>&#160;        std::sqrt((p1.first - p2.first) * (p1.first - p2.first) +</div>
<div class="line"><a name="l00074"></a><span class="lineno">   74</span>&#160;                  (p1.second - p2.second) * (p1.second - p2.second))));</div>
<div class="line"><a name="l00075"></a><span class="lineno">   75</span>&#160;  }</div>
<div class="line"><a name="l00076"></a><span class="lineno">   76</span>&#160; </div>
<div class="line"><a name="l00081"></a><span class="lineno"><a class="line" href="classqbpp_1_1graph__color_1_1GraphColorMap.html#a6c893ea2db3651474f84553e804aa95a">   81</a></span>&#160;  uint32_t <a class="code" href="classqbpp_1_1graph__color_1_1GraphColorMap.html#a6c893ea2db3651474f84553e804aa95a">dist</a>(<span class="keywordtype">size_t</span> i, <span class="keywordtype">size_t</span> j)<span class="keyword"> const </span>{ <span class="keywordflow">return</span> <a class="code" href="classqbpp_1_1graph__color_1_1GraphColorMap.html#a6c893ea2db3651474f84553e804aa95a">dist</a>(<a class="code" href="classqbpp_1_1graph__color_1_1GraphColorMap.html#ad1dda5ce351f070a0eb1e2f3e328a9a4">nodes_</a>[i], <a class="code" href="classqbpp_1_1graph__color_1_1GraphColorMap.html#ad1dda5ce351f070a0eb1e2f3e328a9a4">nodes_</a>[j]); }</div>
<div class="line"><a name="l00082"></a><span class="lineno">   82</span>&#160; </div>
<div class="line"><a name="l00088"></a><span class="lineno"><a class="line" href="classqbpp_1_1graph__color_1_1GraphColorMap.html#a209786c3963fd87b7275b87c06d54cf3">   88</a></span>&#160;  uint32_t <a class="code" href="classqbpp_1_1graph__color_1_1GraphColorMap.html#a209786c3963fd87b7275b87c06d54cf3">min_dist</a>(uint32_t x, uint32_t y)<span class="keyword"> const </span>{</div>
<div class="line"><a name="l00089"></a><span class="lineno">   89</span>&#160;    uint32_t <a class="code" href="classqbpp_1_1graph__color_1_1GraphColorMap.html#a209786c3963fd87b7275b87c06d54cf3">min_dist</a> = <a class="code" href="classqbpp_1_1graph__color_1_1GraphColorMap.html#a5579bbb4b7154c6ff380ab941345698b">grid_size_</a> * 2;</div>
<div class="line"><a name="l00090"></a><span class="lineno">   90</span>&#160;    <span class="keywordflow">for</span> (<span class="keyword">const</span> <span class="keyword">auto</span> &amp;[px, py] : <a class="code" href="classqbpp_1_1graph__color_1_1GraphColorMap.html#ad1dda5ce351f070a0eb1e2f3e328a9a4">nodes_</a>) {</div>
<div class="line"><a name="l00091"></a><span class="lineno">   91</span>&#160;      <span class="keywordflow">if</span> (<a class="code" href="classqbpp_1_1graph__color_1_1GraphColorMap.html#a71b8ad384f9a9de9c62390d5e3cf27ad">dist</a>({x, y}, {px, py}) &lt; <a class="code" href="classqbpp_1_1graph__color_1_1GraphColorMap.html#a209786c3963fd87b7275b87c06d54cf3">min_dist</a>) <a class="code" href="classqbpp_1_1graph__color_1_1GraphColorMap.html#a209786c3963fd87b7275b87c06d54cf3">min_dist</a> = <a class="code" href="classqbpp_1_1graph__color_1_1GraphColorMap.html#a71b8ad384f9a9de9c62390d5e3cf27ad">dist</a>({x, y}, {px, py});</div>
<div class="line"><a name="l00092"></a><span class="lineno">   92</span>&#160;    }</div>
<div class="line"><a name="l00093"></a><span class="lineno">   93</span>&#160;    <span class="keywordflow">return</span> <a class="code" href="classqbpp_1_1graph__color_1_1GraphColorMap.html#a209786c3963fd87b7275b87c06d54cf3">min_dist</a>;</div>
<div class="line"><a name="l00094"></a><span class="lineno">   94</span>&#160;  }</div>
<div class="line"><a name="l00095"></a><span class="lineno">   95</span>&#160; </div>
<div class="line"><a name="l00099"></a><span class="lineno"><a class="line" href="classqbpp_1_1graph__color_1_1GraphColorMap.html#aeb565d9a9de7e19c8e073397c5b64aa8">   99</a></span>&#160;  std::pair&lt;int32_t, int32_t&gt; &amp;<a class="code" href="classqbpp_1_1graph__color_1_1GraphColorMap.html#aeb565d9a9de7e19c8e073397c5b64aa8">operator[]</a>(uint32_t index) {</div>
<div class="line"><a name="l00100"></a><span class="lineno">  100</span>&#160;    <span class="keywordflow">return</span> <a class="code" href="classqbpp_1_1graph__color_1_1GraphColorMap.html#ad1dda5ce351f070a0eb1e2f3e328a9a4">nodes_</a>[index];</div>
<div class="line"><a name="l00101"></a><span class="lineno">  101</span>&#160;  }</div>
<div class="line"><a name="l00102"></a><span class="lineno">  102</span>&#160; </div>
<div class="line"><a name="l00103"></a><span class="lineno">  103</span>&#160; <span class="keyword">public</span>:</div>
<div class="line"><a name="l00107"></a><span class="lineno"><a class="line" href="classqbpp_1_1graph__color_1_1GraphColorMap.html#a9f987a3d14219ed4ed4b477f24e3dde8">  107</a></span>&#160;  <a class="code" href="classqbpp_1_1graph__color_1_1GraphColorMap.html#a9f987a3d14219ed4ed4b477f24e3dde8">GraphColorMap</a>(uint32_t grid_size = 100) : <a class="code" href="classqbpp_1_1graph__color_1_1GraphColorMap.html#a5579bbb4b7154c6ff380ab941345698b">grid_size_</a>(grid_size) {};</div>
<div class="line"><a name="l00108"></a><span class="lineno">  108</span>&#160; </div>
<div class="line"><a name="l00112"></a><span class="lineno">  112</span>&#160;  <span class="keywordtype">void</span> <a class="code" href="classqbpp_1_1graph__color_1_1GraphColorMap.html#aaac00a29df1d2986a05b246ec3f61ea9">gen_random_map</a>(uint32_t n, <span class="keywordtype">bool</span> is_circle = <span class="keyword">false</span>);</div>
<div class="line"><a name="l00113"></a><span class="lineno">  113</span>&#160; </div>
<div class="line"><a name="l00116"></a><span class="lineno">  116</span>&#160;  <span class="keywordtype">void</span> <a class="code" href="classqbpp_1_1graph__color_1_1GraphColorMap.html#a21bd722871bfdf7f2e8f93cbcf0ad639">gen_proximity_edges</a>(uint32_t proximity);</div>
<div class="line"><a name="l00117"></a><span class="lineno">  117</span>&#160; </div>
<div class="line"><a name="l00119"></a><span class="lineno">  119</span>&#160;  <span class="keywordtype">void</span> <a class="code" href="classqbpp_1_1graph__color_1_1GraphColorMap.html#a2998475fcfdad0901e3a20bd1032867b">gen_delaunay_edges</a>();</div>
<div class="line"><a name="l00120"></a><span class="lineno">  120</span>&#160; </div>
<div class="line"><a name="l00126"></a><span class="lineno">  126</span>&#160;  <span class="keywordtype">void</span> <a class="code" href="classqbpp_1_1graph__color_1_1GraphColorMap.html#ad90af91eeca9f99c1a611d144cae1934">set_color_histogram</a>(<span class="keyword">const</span> <a class="code" href="classqbpp_1_1graph__color_1_1GraphColorQuadModel.html">GraphColorQuadModel</a> &amp;model,</div>
<div class="line"><a name="l00127"></a><span class="lineno">  127</span>&#160;                           <span class="keyword">const</span> <a class="code" href="classqbpp_1_1Sol.html">qbpp::Sol</a> &amp;sol);</div>
<div class="line"><a name="l00128"></a><span class="lineno">  128</span>&#160; </div>
<div class="line"><a name="l00131"></a><span class="lineno"><a class="line" href="classqbpp_1_1graph__color_1_1GraphColorMap.html#af28b8561c505cf32403aa8368ff0decf">  131</a></span>&#160;  uint32_t <a class="code" href="classqbpp_1_1graph__color_1_1GraphColorMap.html#af28b8561c505cf32403aa8368ff0decf">node_count</a>()<span class="keyword"> const </span>{ <span class="keywordflow">return</span> <span class="keyword">static_cast&lt;</span>uint32_t<span class="keyword">&gt;</span>(<a class="code" href="classqbpp_1_1graph__color_1_1GraphColorMap.html#ad1dda5ce351f070a0eb1e2f3e328a9a4">nodes_</a>.size()); }</div>
<div class="line"><a name="l00132"></a><span class="lineno">  132</span>&#160; </div>
<div class="line"><a name="l00135"></a><span class="lineno"><a class="line" href="classqbpp_1_1graph__color_1_1GraphColorMap.html#a1b5655bcb05bdad4816e2f28b71df5ea">  135</a></span>&#160;  uint32_t <a class="code" href="classqbpp_1_1graph__color_1_1GraphColorMap.html#a1b5655bcb05bdad4816e2f28b71df5ea">get_grid_size</a>()<span class="keyword"> const </span>{ <span class="keywordflow">return</span> <a class="code" href="classqbpp_1_1graph__color_1_1GraphColorMap.html#a5579bbb4b7154c6ff380ab941345698b">grid_size_</a>; }</div>
<div class="line"><a name="l00136"></a><span class="lineno">  136</span>&#160; </div>
<div class="line"><a name="l00137"></a><span class="lineno"><a class="line" href="classqbpp_1_1graph__color_1_1GraphColorMap.html#a57e69d0643a44dd69336f1cbe41e74de">  137</a></span>&#160;  <span class="keyword">const</span> std::vector&lt;std::pair&lt;uint32_t, uint32_t&gt;&gt; <a class="code" href="classqbpp_1_1graph__color_1_1GraphColorMap.html#a57e69d0643a44dd69336f1cbe41e74de">get_edges</a>()<span class="keyword"> const </span>{</div>
<div class="line"><a name="l00138"></a><span class="lineno">  138</span>&#160;    <span class="keywordflow">return</span> <a class="code" href="classqbpp_1_1graph__color_1_1GraphColorMap.html#a50c783bf212bef619d408d379df17a36">edges_</a>;</div>
<div class="line"><a name="l00139"></a><span class="lineno">  139</span>&#160;  }</div>
<div class="line"><a name="l00140"></a><span class="lineno">  140</span>&#160; </div>
<div class="line"><a name="l00146"></a><span class="lineno">  146</span>&#160;  <span class="keywordtype">void</span> <a class="code" href="classqbpp_1_1graph__color_1_1GraphColorMap.html#a2bd9f172d531644b2fe1e85f83a66e16">draw</a>(<span class="keyword">const</span> std::string &amp;filename, <span class="keywordtype">bool</span> is_blank = <span class="keyword">false</span>);</div>
<div class="line"><a name="l00147"></a><span class="lineno">  147</span>&#160; </div>
<div class="line"><a name="l00150"></a><span class="lineno"><a class="line" href="classqbpp_1_1graph__color_1_1GraphColorMap.html#ac6f6a20d8a0ddd93cc34a49ac07ffa9f">  150</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="classqbpp_1_1graph__color_1_1GraphColorMap.html#ac6f6a20d8a0ddd93cc34a49ac07ffa9f">print</a>() {</div>
<div class="line"><a name="l00151"></a><span class="lineno">  151</span>&#160;    <span class="keywordflow">for</span> (<span class="keywordtype">size_t</span> i = 0; i &lt; <a class="code" href="classqbpp_1_1graph__color_1_1GraphColorMap.html#aa33498f6607b683532bbc874dd1553f8">color_hist_</a>.size(); i++) {</div>
<div class="line"><a name="l00152"></a><span class="lineno">  152</span>&#160;      std::cout &lt;&lt; <span class="stringliteral">&quot;Color &quot;</span> &lt;&lt; i &lt;&lt; <span class="stringliteral">&quot; : &quot;</span> &lt;&lt; <a class="code" href="classqbpp_1_1graph__color_1_1GraphColorMap.html#aa33498f6607b683532bbc874dd1553f8">color_hist_</a>[i] &lt;&lt; std::endl;</div>
<div class="line"><a name="l00153"></a><span class="lineno">  153</span>&#160;    }</div>
<div class="line"><a name="l00154"></a><span class="lineno">  154</span>&#160;    std::cout &lt;&lt; <span class="stringliteral">&quot;Failure : &quot;</span> &lt;&lt; <a class="code" href="classqbpp_1_1graph__color_1_1GraphColorMap.html#aa5ff2e4ea686651e6f96d4c133c53333">failure_</a> &lt;&lt; std::endl;</div>
<div class="line"><a name="l00155"></a><span class="lineno">  155</span>&#160;  }</div>
<div class="line"><a name="l00156"></a><span class="lineno">  156</span>&#160;};</div>
<div class="line"><a name="l00157"></a><span class="lineno">  157</span>&#160; </div>
<div class="line"><a name="l00162"></a><span class="lineno"><a class="line" href="classqbpp_1_1graph__color_1_1GraphColorQuadModel.html">  162</a></span>&#160;<span class="keyword">class </span><a class="code" href="classqbpp_1_1graph__color_1_1GraphColorQuadModel.html">GraphColorQuadModel</a> : <span class="keyword">public</span> <a class="code" href="classqbpp_1_1QuadModel.html">qbpp::QuadModel</a> {</div>
<div class="line"><a name="l00164"></a><span class="lineno"><a class="line" href="classqbpp_1_1graph__color_1_1GraphColorQuadModel.html#ae37a9ca9de5c6ba22b0379e6683ea2f0">  164</a></span>&#160;  <span class="keyword">const</span> <a class="code" href="classqbpp_1_1Vector.html">qbpp::Vector&lt;qbpp::Vector&lt;qbpp::Var&gt;</a>&gt; <a class="code" href="classqbpp_1_1graph__color_1_1GraphColorQuadModel.html#ae37a9ca9de5c6ba22b0379e6683ea2f0">x_</a>;</div>
<div class="line"><a name="l00165"></a><span class="lineno">  165</span>&#160; </div>
<div class="line"><a name="l00168"></a><span class="lineno">  168</span>&#160;  std::pair&lt;qbpp::Model, qbpp::Vector&lt;qbpp::Vector&lt;qbpp::Var&gt;&gt;&gt; <a class="code" href="classqbpp_1_1graph__color_1_1GraphColorQuadModel.html#a5137f108589790091a08b0c8f1bfc049">helper_func</a>(</div>
<div class="line"><a name="l00169"></a><span class="lineno">  169</span>&#160;      <span class="keyword">const</span> <a class="code" href="classqbpp_1_1graph__color_1_1GraphColorMap.html">GraphColorMap</a> &amp;map, uint32_t color_count);</div>
<div class="line"><a name="l00170"></a><span class="lineno">  170</span>&#160; </div>
<div class="line"><a name="l00175"></a><span class="lineno"><a class="line" href="classqbpp_1_1graph__color_1_1GraphColorQuadModel.html#aedcc6d7da75d07ac65c40950d40f7706">  175</a></span>&#160;  <a class="code" href="classqbpp_1_1graph__color_1_1GraphColorQuadModel.html#aedcc6d7da75d07ac65c40950d40f7706">GraphColorQuadModel</a>(</div>
<div class="line"><a name="l00176"></a><span class="lineno">  176</span>&#160;      std::pair&lt;<a class="code" href="classqbpp_1_1QuadModel.html">qbpp::QuadModel</a>, <a class="code" href="classqbpp_1_1Vector.html">qbpp::Vector</a>&lt;<a class="code" href="classqbpp_1_1Vector.html">qbpp::Vector&lt;qbpp::Var&gt;</a>&gt;&gt; pair)</div>
<div class="line"><a name="l00177"></a><span class="lineno">  177</span>&#160;      : <a class="code" href="namespaceqbpp.html">qbpp</a>::<a class="code" href="classqbpp_1_1QuadModel.html">QuadModel</a>(pair.first), <a class="code" href="classqbpp_1_1graph__color_1_1GraphColorQuadModel.html#ae37a9ca9de5c6ba22b0379e6683ea2f0">x_</a>(pair.second) {}</div>
<div class="line"><a name="l00178"></a><span class="lineno">  178</span>&#160; </div>
<div class="line"><a name="l00179"></a><span class="lineno">  179</span>&#160; <span class="keyword">public</span>:</div>
<div class="line"><a name="l00183"></a><span class="lineno"><a class="line" href="classqbpp_1_1graph__color_1_1GraphColorQuadModel.html#a693134505a13381ab5602f24e4928529">  183</a></span>&#160;  <a class="code" href="classqbpp_1_1graph__color_1_1GraphColorQuadModel.html#a693134505a13381ab5602f24e4928529">GraphColorQuadModel</a>(<span class="keyword">const</span> <a class="code" href="classqbpp_1_1graph__color_1_1GraphColorMap.html">GraphColorMap</a> &amp;map, uint32_t color_count)</div>
<div class="line"><a name="l00184"></a><span class="lineno">  184</span>&#160;      : <a class="code" href="classqbpp_1_1graph__color_1_1GraphColorQuadModel.html">GraphColorQuadModel</a>(<a class="code" href="classqbpp_1_1graph__color_1_1GraphColorQuadModel.html#a5137f108589790091a08b0c8f1bfc049">helper_func</a>(map, color_count)) {}</div>
<div class="line"><a name="l00185"></a><span class="lineno">  185</span>&#160; </div>
<div class="line"><a name="l00188"></a><span class="lineno"><a class="line" href="classqbpp_1_1graph__color_1_1GraphColorQuadModel.html#a64a8634684f7ec725b804b76b383e093">  188</a></span>&#160;  uint32_t <a class="code" href="classqbpp_1_1graph__color_1_1GraphColorQuadModel.html#a64a8634684f7ec725b804b76b383e093">node_count</a>()<span class="keyword"> const </span>{ <span class="keywordflow">return</span> <span class="keyword">static_cast&lt;</span>uint32_t<span class="keyword">&gt;</span>(<a class="code" href="classqbpp_1_1graph__color_1_1GraphColorQuadModel.html#ae37a9ca9de5c6ba22b0379e6683ea2f0">x_</a>.<a class="code" href="classqbpp_1_1Vector.html#af719287ecdb5f44bbd0aa5d9ba3aaea1">size</a>()); }</div>
<div class="line"><a name="l00189"></a><span class="lineno">  189</span>&#160; </div>
<div class="line"><a name="l00192"></a><span class="lineno"><a class="line" href="classqbpp_1_1graph__color_1_1GraphColorQuadModel.html#ab60868e452baf1ad7d34f0d78a8d6195">  192</a></span>&#160;  <span class="keyword">const</span> <a class="code" href="classqbpp_1_1Vector.html">qbpp::Vector&lt;qbpp::Vector&lt;qbpp::Var&gt;</a>&gt; &amp;<a class="code" href="classqbpp_1_1graph__color_1_1GraphColorQuadModel.html#ab60868e452baf1ad7d34f0d78a8d6195">get_x</a>()<span class="keyword"> const </span>{ <span class="keywordflow">return</span> <a class="code" href="classqbpp_1_1graph__color_1_1GraphColorQuadModel.html#ae37a9ca9de5c6ba22b0379e6683ea2f0">x_</a>; }</div>
<div class="line"><a name="l00193"></a><span class="lineno">  193</span>&#160;};</div>
<div class="line"><a name="l00194"></a><span class="lineno">  194</span>&#160; </div>
<div class="line"><a name="l00195"></a><span class="lineno">  195</span>&#160;<span class="comment">//==============================</span></div>
<div class="line"><a name="l00196"></a><span class="lineno">  196</span>&#160;<span class="comment">// GraphColorMap member functions</span></div>
<div class="line"><a name="l00197"></a><span class="lineno">  197</span>&#160;<span class="comment">//==============================</span></div>
<div class="line"><a name="l00198"></a><span class="lineno">  198</span>&#160; </div>
<div class="line"><a name="l00199"></a><span class="lineno"><a class="line" href="classqbpp_1_1graph__color_1_1GraphColorMap.html#aaac00a29df1d2986a05b246ec3f61ea9">  199</a></span>&#160;<span class="keyword">inline</span> <span class="keywordtype">void</span> <a class="code" href="classqbpp_1_1graph__color_1_1GraphColorMap.html#aaac00a29df1d2986a05b246ec3f61ea9">GraphColorMap::gen_random_map</a>(uint32_t n, <span class="keywordtype">bool</span> is_circle) {</div>
<div class="line"><a name="l00200"></a><span class="lineno">  200</span>&#160;  <a class="code" href="classqbpp_1_1graph__color_1_1GraphColorMap.html#ad1dda5ce351f070a0eb1e2f3e328a9a4">nodes_</a>.reserve(n);</div>
<div class="line"><a name="l00201"></a><span class="lineno">  201</span>&#160;  uint32_t x, y;</div>
<div class="line"><a name="l00202"></a><span class="lineno">  202</span>&#160;  <span class="keywordflow">for</span> (uint32_t i = 0; i &lt; n; i++) {</div>
<div class="line"><a name="l00203"></a><span class="lineno">  203</span>&#160;    uint32_t counter = 0;</div>
<div class="line"><a name="l00204"></a><span class="lineno">  204</span>&#160;    uint32_t max_dist = 1;</div>
<div class="line"><a name="l00205"></a><span class="lineno">  205</span>&#160;    <span class="comment">// Terminate if dist&gt;=max_dist is satisfied 10 times.</span></div>
<div class="line"><a name="l00206"></a><span class="lineno">  206</span>&#160;    <span class="keywordflow">while</span> (counter &lt; 10) {</div>
<div class="line"><a name="l00207"></a><span class="lineno">  207</span>&#160;      <span class="keywordflow">if</span> (is_circle) {</div>
<div class="line"><a name="l00208"></a><span class="lineno">  208</span>&#160;        <span class="keywordflow">do</span> {</div>
<div class="line"><a name="l00209"></a><span class="lineno">  209</span>&#160;          x = <a class="code" href="classqbpp_1_1misc_1_1RandomGenerator.html#a95308c2300aeef9a881ba97786367977">qbpp::misc::RandomGenerator::gen</a>(<a class="code" href="classqbpp_1_1graph__color_1_1GraphColorMap.html#a5579bbb4b7154c6ff380ab941345698b">grid_size_</a>);</div>
<div class="line"><a name="l00210"></a><span class="lineno">  210</span>&#160;          y = <a class="code" href="classqbpp_1_1misc_1_1RandomGenerator.html#a95308c2300aeef9a881ba97786367977">qbpp::misc::RandomGenerator::gen</a>(<a class="code" href="classqbpp_1_1graph__color_1_1GraphColorMap.html#a5579bbb4b7154c6ff380ab941345698b">grid_size_</a>);</div>
<div class="line"><a name="l00211"></a><span class="lineno">  211</span>&#160;        } <span class="keywordflow">while</span> ((x - <a class="code" href="classqbpp_1_1graph__color_1_1GraphColorMap.html#a5579bbb4b7154c6ff380ab941345698b">grid_size_</a> / 2) * (x - <a class="code" href="classqbpp_1_1graph__color_1_1GraphColorMap.html#a5579bbb4b7154c6ff380ab941345698b">grid_size_</a> / 2) +</div>
<div class="line"><a name="l00212"></a><span class="lineno">  212</span>&#160;                     (y - <a class="code" href="classqbpp_1_1graph__color_1_1GraphColorMap.html#a5579bbb4b7154c6ff380ab941345698b">grid_size_</a> / 2) * (y - <a class="code" href="classqbpp_1_1graph__color_1_1GraphColorMap.html#a5579bbb4b7154c6ff380ab941345698b">grid_size_</a> / 2) &gt;</div>
<div class="line"><a name="l00213"></a><span class="lineno">  213</span>&#160;                 <a class="code" href="classqbpp_1_1graph__color_1_1GraphColorMap.html#a5579bbb4b7154c6ff380ab941345698b">grid_size_</a> * <a class="code" href="classqbpp_1_1graph__color_1_1GraphColorMap.html#a5579bbb4b7154c6ff380ab941345698b">grid_size_</a> / 4);</div>
<div class="line"><a name="l00214"></a><span class="lineno">  214</span>&#160;      } <span class="keywordflow">else</span> {</div>
<div class="line"><a name="l00215"></a><span class="lineno">  215</span>&#160;        x = <a class="code" href="classqbpp_1_1misc_1_1RandomGenerator.html#a95308c2300aeef9a881ba97786367977">qbpp::misc::RandomGenerator::gen</a>(<a class="code" href="classqbpp_1_1graph__color_1_1GraphColorMap.html#a5579bbb4b7154c6ff380ab941345698b">grid_size_</a>);</div>
<div class="line"><a name="l00216"></a><span class="lineno">  216</span>&#160;        y = <a class="code" href="classqbpp_1_1misc_1_1RandomGenerator.html#a95308c2300aeef9a881ba97786367977">qbpp::misc::RandomGenerator::gen</a>(<a class="code" href="classqbpp_1_1graph__color_1_1GraphColorMap.html#a5579bbb4b7154c6ff380ab941345698b">grid_size_</a>);</div>
<div class="line"><a name="l00217"></a><span class="lineno">  217</span>&#160;      }</div>
<div class="line"><a name="l00218"></a><span class="lineno">  218</span>&#160;      uint32_t <a class="code" href="classqbpp_1_1graph__color_1_1GraphColorMap.html#a71b8ad384f9a9de9c62390d5e3cf27ad">dist</a> = <a class="code" href="classqbpp_1_1graph__color_1_1GraphColorMap.html#a209786c3963fd87b7275b87c06d54cf3">min_dist</a>(x, y);</div>
<div class="line"><a name="l00219"></a><span class="lineno">  219</span>&#160;      <span class="keywordflow">if</span> (<a class="code" href="classqbpp_1_1graph__color_1_1GraphColorMap.html#a71b8ad384f9a9de9c62390d5e3cf27ad">dist</a> &gt;= max_dist) {</div>
<div class="line"><a name="l00220"></a><span class="lineno">  220</span>&#160;        max_dist = <a class="code" href="classqbpp_1_1graph__color_1_1GraphColorMap.html#a71b8ad384f9a9de9c62390d5e3cf27ad">dist</a>;</div>
<div class="line"><a name="l00221"></a><span class="lineno">  221</span>&#160;        counter++;</div>
<div class="line"><a name="l00222"></a><span class="lineno">  222</span>&#160;      }</div>
<div class="line"><a name="l00223"></a><span class="lineno">  223</span>&#160;    }</div>
<div class="line"><a name="l00224"></a><span class="lineno">  224</span>&#160;    <a class="code" href="classqbpp_1_1graph__color_1_1GraphColorMap.html#af229a75b21e41767048e169b0b1ae895">add_node</a>(x, y);</div>
<div class="line"><a name="l00225"></a><span class="lineno">  225</span>&#160;  }</div>
<div class="line"><a name="l00226"></a><span class="lineno">  226</span>&#160;}</div>
<div class="line"><a name="l00227"></a><span class="lineno">  227</span>&#160; </div>
<div class="line"><a name="l00228"></a><span class="lineno"><a class="line" href="classqbpp_1_1graph__color_1_1GraphColorMap.html#a21bd722871bfdf7f2e8f93cbcf0ad639">  228</a></span>&#160;<span class="keyword">inline</span> <span class="keywordtype">void</span> <a class="code" href="classqbpp_1_1graph__color_1_1GraphColorMap.html#a21bd722871bfdf7f2e8f93cbcf0ad639">GraphColorMap::gen_proximity_edges</a>(uint32_t proximity) {</div>
<div class="line"><a name="l00229"></a><span class="lineno">  229</span>&#160;  <span class="keywordflow">for</span> (<span class="keywordtype">size_t</span> i = 0; i &lt; <a class="code" href="classqbpp_1_1graph__color_1_1GraphColorMap.html#ad1dda5ce351f070a0eb1e2f3e328a9a4">nodes_</a>.size(); i++) {</div>
<div class="line"><a name="l00230"></a><span class="lineno">  230</span>&#160;    <span class="keywordflow">for</span> (<span class="keywordtype">size_t</span> j = i + 1; j &lt; <a class="code" href="classqbpp_1_1graph__color_1_1GraphColorMap.html#ad1dda5ce351f070a0eb1e2f3e328a9a4">nodes_</a>.size(); j++) {</div>
<div class="line"><a name="l00231"></a><span class="lineno">  231</span>&#160;      <span class="keywordflow">if</span> (<a class="code" href="classqbpp_1_1graph__color_1_1GraphColorMap.html#a71b8ad384f9a9de9c62390d5e3cf27ad">dist</a>(i, j) &lt; proximity) {</div>
<div class="line"><a name="l00232"></a><span class="lineno">  232</span>&#160;        <a class="code" href="classqbpp_1_1graph__color_1_1GraphColorMap.html#a50c783bf212bef619d408d379df17a36">edges_</a>.push_back({i, j});</div>
<div class="line"><a name="l00233"></a><span class="lineno">  233</span>&#160;      }</div>
<div class="line"><a name="l00234"></a><span class="lineno">  234</span>&#160;    }</div>
<div class="line"><a name="l00235"></a><span class="lineno">  235</span>&#160;  }</div>
<div class="line"><a name="l00236"></a><span class="lineno">  236</span>&#160;}</div>
<div class="line"><a name="l00237"></a><span class="lineno">  237</span>&#160; </div>
<div class="line"><a name="l00238"></a><span class="lineno"><a class="line" href="classqbpp_1_1graph__color_1_1GraphColorMap.html#a2998475fcfdad0901e3a20bd1032867b">  238</a></span>&#160;<span class="keyword">inline</span> <span class="keywordtype">void</span> <a class="code" href="classqbpp_1_1graph__color_1_1GraphColorMap.html#a2998475fcfdad0901e3a20bd1032867b">GraphColorMap::gen_delaunay_edges</a>() {</div>
<div class="line"><a name="l00239"></a><span class="lineno">  239</span>&#160;  <span class="keyword">typedef</span> boost::polygon::point_data&lt;int&gt; Point;</div>
<div class="line"><a name="l00240"></a><span class="lineno">  240</span>&#160;  std::vector&lt;Point&gt; points;</div>
<div class="line"><a name="l00241"></a><span class="lineno">  241</span>&#160;  <span class="keywordflow">for</span> (<span class="keyword">const</span> <span class="keyword">auto</span> &amp;[x, y] : <a class="code" href="classqbpp_1_1graph__color_1_1GraphColorMap.html#ad1dda5ce351f070a0eb1e2f3e328a9a4">nodes_</a>) {</div>
<div class="line"><a name="l00242"></a><span class="lineno">  242</span>&#160;    points.push_back(Point(x, y));</div>
<div class="line"><a name="l00243"></a><span class="lineno">  243</span>&#160;  }</div>
<div class="line"><a name="l00244"></a><span class="lineno">  244</span>&#160;  boost::polygon::voronoi_diagram&lt;double&gt; vd;</div>
<div class="line"><a name="l00245"></a><span class="lineno">  245</span>&#160;  boost::polygon::construct_voronoi(points.begin(), points.end(), &amp;vd);</div>
<div class="line"><a name="l00246"></a><span class="lineno">  246</span>&#160;  <span class="keywordflow">for</span> (<span class="keyword">const</span> <span class="keyword">auto</span> &amp;cell : vd.cells()) {</div>
<div class="line"><a name="l00247"></a><span class="lineno">  247</span>&#160;    <span class="keywordtype">size_t</span> source_index = cell.source_index();</div>
<div class="line"><a name="l00248"></a><span class="lineno">  248</span>&#160;    <span class="keyword">const</span> <span class="keyword">auto</span> *edge = cell.incident_edge();</div>
<div class="line"><a name="l00249"></a><span class="lineno">  249</span>&#160;    <span class="keywordflow">do</span> {</div>
<div class="line"><a name="l00250"></a><span class="lineno">  250</span>&#160;      <span class="keywordtype">size_t</span> twin_source_index = edge-&gt;twin()-&gt;cell()-&gt;source_index();</div>
<div class="line"><a name="l00251"></a><span class="lineno">  251</span>&#160;      <span class="keywordflow">if</span> (source_index &lt; twin_source_index)</div>
<div class="line"><a name="l00252"></a><span class="lineno">  252</span>&#160;        <a class="code" href="classqbpp_1_1graph__color_1_1GraphColorMap.html#a50c783bf212bef619d408d379df17a36">edges_</a>.push_back({<span class="keyword">static_cast&lt;</span>uint32_t<span class="keyword">&gt;</span>(source_index),</div>
<div class="line"><a name="l00253"></a><span class="lineno">  253</span>&#160;                          <span class="keyword">static_cast&lt;</span>uint32_t<span class="keyword">&gt;</span>(twin_source_index)});</div>
<div class="line"><a name="l00254"></a><span class="lineno">  254</span>&#160;      edge = edge-&gt;next();</div>
<div class="line"><a name="l00255"></a><span class="lineno">  255</span>&#160;    } <span class="keywordflow">while</span> (edge != cell.incident_edge());</div>
<div class="line"><a name="l00256"></a><span class="lineno">  256</span>&#160;  }</div>
<div class="line"><a name="l00257"></a><span class="lineno">  257</span>&#160;}</div>
<div class="line"><a name="l00258"></a><span class="lineno">  258</span>&#160; </div>
<div class="line"><a name="l00259"></a><span class="lineno"><a class="line" href="classqbpp_1_1graph__color_1_1GraphColorMap.html#ad90af91eeca9f99c1a611d144cae1934">  259</a></span>&#160;<span class="keyword">inline</span> <span class="keywordtype">void</span> <a class="code" href="classqbpp_1_1graph__color_1_1GraphColorMap.html#ad90af91eeca9f99c1a611d144cae1934">GraphColorMap::set_color_histogram</a>(<span class="keyword">const</span> <a class="code" href="classqbpp_1_1graph__color_1_1GraphColorQuadModel.html">GraphColorQuadModel</a> &amp;model,</div>
<div class="line"><a name="l00260"></a><span class="lineno">  260</span>&#160;                                               <span class="keyword">const</span> <a class="code" href="classqbpp_1_1Sol.html">qbpp::Sol</a> &amp;sol) {</div>
<div class="line"><a name="l00261"></a><span class="lineno">  261</span>&#160;  <a class="code" href="classqbpp_1_1graph__color_1_1GraphColorMap.html#a2964d48ac547f74049b38bba057ed337">color_</a> = <a class="code" href="namespaceqbpp.html#afc344ea88cf7416a6827b5cec17838df">qbpp::onehot_to_int</a>(sol.<a class="code" href="classqbpp_1_1Sol.html#abdb4f6b779678720715f7845f264440f">get</a>(model.<a class="code" href="classqbpp_1_1graph__color_1_1GraphColorQuadModel.html#ab60868e452baf1ad7d34f0d78a8d6195">get_x</a>()));</div>
<div class="line"><a name="l00262"></a><span class="lineno">  262</span>&#160;  <span class="keywordflow">for</span> (<span class="keyword">auto</span> c : <a class="code" href="classqbpp_1_1graph__color_1_1GraphColorMap.html#a2964d48ac547f74049b38bba057ed337">color_</a>) {</div>
<div class="line"><a name="l00263"></a><span class="lineno">  263</span>&#160;    <span class="keywordflow">if</span> (c &lt; 0) {</div>
<div class="line"><a name="l00264"></a><span class="lineno">  264</span>&#160;      ++<a class="code" href="classqbpp_1_1graph__color_1_1GraphColorMap.html#aa5ff2e4ea686651e6f96d4c133c53333">failure_</a>;</div>
<div class="line"><a name="l00265"></a><span class="lineno">  265</span>&#160;    } <span class="keywordflow">else</span> {</div>
<div class="line"><a name="l00266"></a><span class="lineno">  266</span>&#160;      <span class="keywordflow">if</span> (c &gt;= <span class="keyword">static_cast&lt;</span>decltype(c)<span class="keyword">&gt;</span>(<a class="code" href="classqbpp_1_1graph__color_1_1GraphColorMap.html#aa33498f6607b683532bbc874dd1553f8">color_hist_</a>.size())) {</div>
<div class="line"><a name="l00267"></a><span class="lineno">  267</span>&#160;        <a class="code" href="classqbpp_1_1graph__color_1_1GraphColorMap.html#aa33498f6607b683532bbc874dd1553f8">color_hist_</a>.resize(<span class="keyword">static_cast&lt;</span><span class="keywordtype">size_t</span><span class="keyword">&gt;</span>(c) + 1, 0);</div>
<div class="line"><a name="l00268"></a><span class="lineno">  268</span>&#160;      }</div>
<div class="line"><a name="l00269"></a><span class="lineno">  269</span>&#160;      <a class="code" href="classqbpp_1_1graph__color_1_1GraphColorMap.html#aa33498f6607b683532bbc874dd1553f8">color_hist_</a>[<span class="keyword">static_cast&lt;</span><span class="keywordtype">size_t</span><span class="keyword">&gt;</span>(c)]++;</div>
<div class="line"><a name="l00270"></a><span class="lineno">  270</span>&#160;    }</div>
<div class="line"><a name="l00271"></a><span class="lineno">  271</span>&#160;  }</div>
<div class="line"><a name="l00272"></a><span class="lineno">  272</span>&#160;}</div>
<div class="line"><a name="l00273"></a><span class="lineno">  273</span>&#160; </div>
<div class="line"><a name="l00274"></a><span class="lineno"><a class="line" href="classqbpp_1_1graph__color_1_1GraphColorMap.html#a2bd9f172d531644b2fe1e85f83a66e16">  274</a></span>&#160;<span class="keyword">inline</span> <span class="keywordtype">void</span> <a class="code" href="classqbpp_1_1graph__color_1_1GraphColorMap.html#a2bd9f172d531644b2fe1e85f83a66e16">GraphColorMap::draw</a>(<span class="keyword">const</span> std::string &amp;filename, <span class="keywordtype">bool</span> is_blank) {</div>
<div class="line"><a name="l00275"></a><span class="lineno">  275</span>&#160;  <span class="keyword">const</span> std::vector&lt;std::string&gt; color_palette = {</div>
<div class="line"><a name="l00276"></a><span class="lineno">  276</span>&#160;      <span class="stringliteral">&quot;#FFFFFF&quot;</span>,  <span class="comment">// 白 エラーを意味．</span></div>
<div class="line"><a name="l00277"></a><span class="lineno">  277</span>&#160;      <span class="stringliteral">&quot;#FF0000&quot;</span>,  <span class="comment">// 赤</span></div>
<div class="line"><a name="l00278"></a><span class="lineno">  278</span>&#160;      <span class="stringliteral">&quot;#00FF00&quot;</span>,  <span class="comment">// 緑</span></div>
<div class="line"><a name="l00279"></a><span class="lineno">  279</span>&#160;      <span class="stringliteral">&quot;#FFFF00&quot;</span>,  <span class="comment">// 黄色</span></div>
<div class="line"><a name="l00280"></a><span class="lineno">  280</span>&#160;      <span class="stringliteral">&quot;#00FFFF&quot;</span>,  <span class="comment">// シアン</span></div>
<div class="line"><a name="l00281"></a><span class="lineno">  281</span>&#160;      <span class="stringliteral">&quot;#FF00FF&quot;</span>,  <span class="comment">// マゼンタ</span></div>
<div class="line"><a name="l00282"></a><span class="lineno">  282</span>&#160;      <span class="stringliteral">&quot;#FFA500&quot;</span>,  <span class="comment">// オレンジ</span></div>
<div class="line"><a name="l00283"></a><span class="lineno">  283</span>&#160;      <span class="stringliteral">&quot;#800080&quot;</span>,  <span class="comment">// 紫</span></div>
<div class="line"><a name="l00284"></a><span class="lineno">  284</span>&#160;      <span class="stringliteral">&quot;#A52A2A&quot;</span>,  <span class="comment">// 茶色</span></div>
<div class="line"><a name="l00285"></a><span class="lineno">  285</span>&#160;      <span class="stringliteral">&quot;#87CEEB&quot;</span>,  <span class="comment">// ライトブルー</span></div>
<div class="line"><a name="l00286"></a><span class="lineno">  286</span>&#160;      <span class="stringliteral">&quot;#FFD700&quot;</span>,  <span class="comment">// ゴールド</span></div>
<div class="line"><a name="l00287"></a><span class="lineno">  287</span>&#160;      <span class="stringliteral">&quot;#808080&quot;</span>,  <span class="comment">// グレー</span></div>
<div class="line"><a name="l00288"></a><span class="lineno">  288</span>&#160;      <span class="stringliteral">&quot;#FF1493&quot;</span>,  <span class="comment">// ディープピンク</span></div>
<div class="line"><a name="l00289"></a><span class="lineno">  289</span>&#160;      <span class="stringliteral">&quot;#00CED1&quot;</span>,  <span class="comment">// ダークターコイズ</span></div>
<div class="line"><a name="l00290"></a><span class="lineno">  290</span>&#160;      <span class="stringliteral">&quot;#ADFF2F&quot;</span>,  <span class="comment">// イエローグリーン</span></div>
<div class="line"><a name="l00291"></a><span class="lineno">  291</span>&#160;      <span class="stringliteral">&quot;#ADD8E6&quot;</span>,  <span class="comment">// ライトスカイブルー</span></div>
<div class="line"><a name="l00292"></a><span class="lineno">  292</span>&#160;      <span class="stringliteral">&quot;#008000&quot;</span>,  <span class="comment">// ダークグリーン</span></div>
<div class="line"><a name="l00293"></a><span class="lineno">  293</span>&#160;      <span class="stringliteral">&quot;#F0E68C&quot;</span>,  <span class="comment">// カーキ</span></div>
<div class="line"><a name="l00294"></a><span class="lineno">  294</span>&#160;      <span class="stringliteral">&quot;#7FFF00&quot;</span>,  <span class="comment">// チャートリューズ</span></div>
<div class="line"><a name="l00295"></a><span class="lineno">  295</span>&#160;      <span class="stringliteral">&quot;#40E0D0&quot;</span>,  <span class="comment">// ターコイズ</span></div>
<div class="line"><a name="l00296"></a><span class="lineno">  296</span>&#160;      <span class="stringliteral">&quot;#DDA0DD&quot;</span>,  <span class="comment">// プラム</span></div>
<div class="line"><a name="l00297"></a><span class="lineno">  297</span>&#160;      <span class="stringliteral">&quot;#FF4500&quot;</span>,  <span class="comment">// オレンジレッド</span></div>
<div class="line"><a name="l00298"></a><span class="lineno">  298</span>&#160;      <span class="stringliteral">&quot;#DA70D6&quot;</span>,  <span class="comment">// オーキッド</span></div>
<div class="line"><a name="l00299"></a><span class="lineno">  299</span>&#160;      <span class="stringliteral">&quot;#F08080&quot;</span>,  <span class="comment">// ライトコーラル</span></div>
<div class="line"><a name="l00300"></a><span class="lineno">  300</span>&#160;      <span class="stringliteral">&quot;#87CEFA&quot;</span>,  <span class="comment">// スカイブルー（もう一つの明るい青）</span></div>
<div class="line"><a name="l00301"></a><span class="lineno">  301</span>&#160;      <span class="stringliteral">&quot;#FF6347&quot;</span>,  <span class="comment">// トマト</span></div>
<div class="line"><a name="l00302"></a><span class="lineno">  302</span>&#160;      <span class="stringliteral">&quot;#FFE4B5&quot;</span>,  <span class="comment">// モカ</span></div>
<div class="line"><a name="l00303"></a><span class="lineno">  303</span>&#160;      <span class="stringliteral">&quot;#BA55D3&quot;</span>,  <span class="comment">// ミディアムオーキッド</span></div>
<div class="line"><a name="l00304"></a><span class="lineno">  304</span>&#160;      <span class="stringliteral">&quot;#3CB371&quot;</span>,  <span class="comment">// ミディアムシーグリーン</span></div>
<div class="line"><a name="l00305"></a><span class="lineno">  305</span>&#160;      <span class="stringliteral">&quot;#4682B4&quot;</span>,  <span class="comment">// スチールブルー</span></div>
<div class="line"><a name="l00306"></a><span class="lineno">  306</span>&#160;      <span class="stringliteral">&quot;#B0E0E6&quot;</span>,  <span class="comment">// パウダーブルー</span></div>
<div class="line"><a name="l00307"></a><span class="lineno">  307</span>&#160;      <span class="stringliteral">&quot;#7B68EE&quot;</span>   <span class="comment">// ミディアムスレートブルー</span></div>
<div class="line"><a name="l00308"></a><span class="lineno">  308</span>&#160;  };</div>
<div class="line"><a name="l00309"></a><span class="lineno">  309</span>&#160; </div>
<div class="line"><a name="l00310"></a><span class="lineno">  310</span>&#160;  std::ostringstream dot_stream;</div>
<div class="line"><a name="l00311"></a><span class="lineno">  311</span>&#160;  dot_stream &lt;&lt; <span class="stringliteral">&quot;graph G {\n&quot;</span></div>
<div class="line"><a name="l00312"></a><span class="lineno">  312</span>&#160;             &lt;&lt; <span class="stringliteral">&quot;node [shape=circle, fixedsize=true, width=3, fontsize=100, &quot;</span></div>
<div class="line"><a name="l00313"></a><span class="lineno">  313</span>&#160;                <span class="stringliteral">&quot;penwidth=8];\n&quot;</span></div>
<div class="line"><a name="l00314"></a><span class="lineno">  314</span>&#160;             &lt;&lt; <span class="stringliteral">&quot;edge [penwidth=8];\n&quot;</span>;</div>
<div class="line"><a name="l00315"></a><span class="lineno">  315</span>&#160; </div>
<div class="line"><a name="l00316"></a><span class="lineno">  316</span>&#160;  <span class="keywordflow">for</span> (<span class="keywordtype">size_t</span> i = 0; i &lt; <a class="code" href="classqbpp_1_1graph__color_1_1GraphColorMap.html#ad1dda5ce351f070a0eb1e2f3e328a9a4">nodes_</a>.size(); ++i) {</div>
<div class="line"><a name="l00317"></a><span class="lineno">  317</span>&#160;    dot_stream &lt;&lt; i &lt;&lt; <span class="stringliteral">&quot; [label=\&quot;&quot;</span> &lt;&lt; i &lt;&lt; <span class="stringliteral">&quot;\&quot;, pos=\&quot;&quot;</span> &lt;&lt; <a class="code" href="classqbpp_1_1graph__color_1_1GraphColorMap.html#ad1dda5ce351f070a0eb1e2f3e328a9a4">nodes_</a>[i].first</div>
<div class="line"><a name="l00318"></a><span class="lineno">  318</span>&#160;               &lt;&lt; <span class="stringliteral">&quot;,&quot;</span> &lt;&lt; <a class="code" href="classqbpp_1_1graph__color_1_1GraphColorMap.html#ad1dda5ce351f070a0eb1e2f3e328a9a4">nodes_</a>[i].second &lt;&lt; <span class="stringliteral">&quot;!\&quot;&quot;</span> &lt;&lt; <span class="stringliteral">&quot;, fillcolor=\&quot;&quot;</span>;</div>
<div class="line"><a name="l00319"></a><span class="lineno">  319</span>&#160;    <span class="keywordflow">if</span> (is_blank) {</div>
<div class="line"><a name="l00320"></a><span class="lineno">  320</span>&#160;      dot_stream &lt;&lt; <span class="stringliteral">&quot;#FFFFFF&quot;</span>;</div>
<div class="line"><a name="l00321"></a><span class="lineno">  321</span>&#160;    } <span class="keywordflow">else</span> <span class="keywordflow">if</span> (<span class="keyword">static_cast&lt;</span><span class="keywordtype">size_t</span><span class="keyword">&gt;</span>(<a class="code" href="classqbpp_1_1graph__color_1_1GraphColorMap.html#a2964d48ac547f74049b38bba057ed337">color_</a>[i] + 1) &lt; color_palette.size()) {</div>
<div class="line"><a name="l00322"></a><span class="lineno">  322</span>&#160;      dot_stream &lt;&lt; color_palette[static_cast&lt;size_t&gt;(<a class="code" href="classqbpp_1_1graph__color_1_1GraphColorMap.html#a2964d48ac547f74049b38bba057ed337">color_</a>[i]) + 1];</div>
<div class="line"><a name="l00323"></a><span class="lineno">  323</span>&#160;    } <span class="keywordflow">else</span> {</div>
<div class="line"><a name="l00324"></a><span class="lineno">  324</span>&#160;      dot_stream &lt;&lt; color_palette[0];</div>
<div class="line"><a name="l00325"></a><span class="lineno">  325</span>&#160;    }</div>
<div class="line"><a name="l00326"></a><span class="lineno">  326</span>&#160;    dot_stream &lt;&lt; <span class="stringliteral">&quot;\&quot;, style=filled];\n&quot;</span>;</div>
<div class="line"><a name="l00327"></a><span class="lineno">  327</span>&#160;  }</div>
<div class="line"><a name="l00328"></a><span class="lineno">  328</span>&#160; </div>
<div class="line"><a name="l00329"></a><span class="lineno">  329</span>&#160;  <span class="keywordflow">for</span> (<span class="keyword">const</span> <span class="keyword">auto</span> &amp;[node1, node2] : <a class="code" href="classqbpp_1_1graph__color_1_1GraphColorMap.html#a50c783bf212bef619d408d379df17a36">edges_</a>) {</div>
<div class="line"><a name="l00330"></a><span class="lineno">  330</span>&#160;    <span class="keywordflow">if</span> (!is_blank &amp;&amp; (<a class="code" href="classqbpp_1_1graph__color_1_1GraphColorMap.html#a2964d48ac547f74049b38bba057ed337">color_</a>[node1] == <a class="code" href="classqbpp_1_1graph__color_1_1GraphColorMap.html#a2964d48ac547f74049b38bba057ed337">color_</a>[node2] || <a class="code" href="classqbpp_1_1graph__color_1_1GraphColorMap.html#a2964d48ac547f74049b38bba057ed337">color_</a>[node1] &lt; 0 ||</div>
<div class="line"><a name="l00331"></a><span class="lineno">  331</span>&#160;                      <a class="code" href="classqbpp_1_1graph__color_1_1GraphColorMap.html#a2964d48ac547f74049b38bba057ed337">color_</a>[node2] &lt; 0)) {</div>
<div class="line"><a name="l00332"></a><span class="lineno">  332</span>&#160;      dot_stream &lt;&lt; node1 &lt;&lt; <span class="stringliteral">&quot; -- &quot;</span> &lt;&lt; node2</div>
<div class="line"><a name="l00333"></a><span class="lineno">  333</span>&#160;                 &lt;&lt; <span class="stringliteral">&quot; [style=dashed,penwidth=16,color=\&quot;red\&quot;];\n&quot;</span>;</div>
<div class="line"><a name="l00334"></a><span class="lineno">  334</span>&#160;    } <span class="keywordflow">else</span> {</div>
<div class="line"><a name="l00335"></a><span class="lineno">  335</span>&#160;      dot_stream &lt;&lt; node1 &lt;&lt; <span class="stringliteral">&quot; -- &quot;</span> &lt;&lt; node2 &lt;&lt; <span class="stringliteral">&quot;;\n&quot;</span>;</div>
<div class="line"><a name="l00336"></a><span class="lineno">  336</span>&#160;    }</div>
<div class="line"><a name="l00337"></a><span class="lineno">  337</span>&#160;  }</div>
<div class="line"><a name="l00338"></a><span class="lineno">  338</span>&#160; </div>
<div class="line"><a name="l00339"></a><span class="lineno">  339</span>&#160;  dot_stream &lt;&lt; <span class="stringliteral">&quot;}\n&quot;</span>;</div>
<div class="line"><a name="l00340"></a><span class="lineno">  340</span>&#160; </div>
<div class="line"><a name="l00341"></a><span class="lineno">  341</span>&#160;  std::string command =</div>
<div class="line"><a name="l00342"></a><span class="lineno">  342</span>&#160;      <span class="stringliteral">&quot;neato -T&quot;</span> + filename.substr(filename.rfind(<span class="charliteral">&#39;.&#39;</span>) + 1) + <span class="stringliteral">&quot; -o &quot;</span> + filename;</div>
<div class="line"><a name="l00343"></a><span class="lineno">  343</span>&#160; </div>
<div class="line"><a name="l00344"></a><span class="lineno">  344</span>&#160;  std::unique_ptr&lt;FILE, qbpp::misc::PcloseDeleter&gt; pipe(</div>
<div class="line"><a name="l00345"></a><span class="lineno">  345</span>&#160;      popen(command.c_str(), <span class="stringliteral">&quot;w&quot;</span>));</div>
<div class="line"><a name="l00346"></a><span class="lineno">  346</span>&#160; </div>
<div class="line"><a name="l00347"></a><span class="lineno">  347</span>&#160;  <span class="keywordflow">if</span> (!pipe) {</div>
<div class="line"><a name="l00348"></a><span class="lineno">  348</span>&#160;    <span class="keywordflow">throw</span> std::runtime_error(<span class="stringliteral">&quot;popen() failed!&quot;</span>);</div>
<div class="line"><a name="l00349"></a><span class="lineno">  349</span>&#160;  }</div>
<div class="line"><a name="l00350"></a><span class="lineno">  350</span>&#160; </div>
<div class="line"><a name="l00351"></a><span class="lineno">  351</span>&#160;  std::string dot_content = dot_stream.str();</div>
<div class="line"><a name="l00352"></a><span class="lineno">  352</span>&#160;  fwrite(dot_content.c_str(), <span class="keyword">sizeof</span>(<span class="keywordtype">char</span>), dot_content.size(), pipe.get());</div>
<div class="line"><a name="l00353"></a><span class="lineno">  353</span>&#160;}</div>
<div class="line"><a name="l00354"></a><span class="lineno">  354</span>&#160; </div>
<div class="line"><a name="l00355"></a><span class="lineno">  355</span>&#160;<span class="comment">//======================================</span></div>
<div class="line"><a name="l00356"></a><span class="lineno">  356</span>&#160;<span class="comment">// GraphColorQuadModel member functions</span></div>
<div class="line"><a name="l00357"></a><span class="lineno">  357</span>&#160;<span class="comment">//======================================</span></div>
<div class="line"><a name="l00358"></a><span class="lineno">  358</span>&#160; </div>
<div class="line"><a name="l00359"></a><span class="lineno">  359</span>&#160;<span class="keyword">inline</span> std::pair&lt;qbpp::Model, qbpp::Vector&lt;qbpp::Vector&lt;qbpp::Var&gt;&gt;&gt;</div>
<div class="line"><a name="l00360"></a><span class="lineno"><a class="line" href="classqbpp_1_1graph__color_1_1GraphColorQuadModel.html#a5137f108589790091a08b0c8f1bfc049">  360</a></span>&#160;<a class="code" href="classqbpp_1_1graph__color_1_1GraphColorQuadModel.html#a5137f108589790091a08b0c8f1bfc049">GraphColorQuadModel::helper_func</a>(<span class="keyword">const</span> <a class="code" href="classqbpp_1_1graph__color_1_1GraphColorMap.html">GraphColorMap</a> &amp;graph_map,</div>
<div class="line"><a name="l00361"></a><span class="lineno">  361</span>&#160;                                 uint32_t color_count) {</div>
<div class="line"><a name="l00362"></a><span class="lineno">  362</span>&#160;  <span class="keyword">auto</span> x = <a class="code" href="namespaceqbpp.html#a1cf3f678bcc7eee089c75d4bc5a0b959">qbpp::var</a>(<span class="stringliteral">&quot;x&quot;</span>, graph_map.<a class="code" href="classqbpp_1_1graph__color_1_1GraphColorMap.html#af28b8561c505cf32403aa8368ff0decf">node_count</a>(), color_count);</div>
<div class="line"><a name="l00363"></a><span class="lineno">  363</span>&#160; </div>
<div class="line"><a name="l00364"></a><span class="lineno">  364</span>&#160;  <span class="keyword">auto</span> f = <a class="code" href="namespaceqbpp.html#a9041f8ee7d3f4a7c04a0d385babae285">qbpp::sum</a>(<a class="code" href="namespaceqbpp.html#a008af6dfb07e3e463ee6d83c547c3ff0">qbpp::vector_sum</a>(x) == 1);</div>
<div class="line"><a name="l00365"></a><span class="lineno">  365</span>&#160; </div>
<div class="line"><a name="l00366"></a><span class="lineno">  366</span>&#160;  <span class="keywordflow">for</span> (<span class="keyword">auto</span> [i, j] : graph_map.<a class="code" href="classqbpp_1_1graph__color_1_1GraphColorMap.html#a57e69d0643a44dd69336f1cbe41e74de">get_edges</a>()) {</div>
<div class="line"><a name="l00367"></a><span class="lineno">  367</span>&#160;    f += <a class="code" href="namespaceqbpp.html#a9041f8ee7d3f4a7c04a0d385babae285">qbpp::sum</a>(x[i] * x[j]);</div>
<div class="line"><a name="l00368"></a><span class="lineno">  368</span>&#160;  }</div>
<div class="line"><a name="l00369"></a><span class="lineno">  369</span>&#160;  <span class="keywordflow">return</span> {<a class="code" href="namespaceqbpp.html#aa3ce65464a38ba36e0b6e99b97234245">simplify_as_binary</a>(f), x};</div>
<div class="line"><a name="l00370"></a><span class="lineno">  370</span>&#160;}</div>
<div class="line"><a name="l00371"></a><span class="lineno">  371</span>&#160; </div>
<div class="line"><a name="l00372"></a><span class="lineno">  372</span>&#160;}  <span class="comment">// namespace graph_color</span></div>
<div class="line"><a name="l00373"></a><span class="lineno">  373</span>&#160;}  <span class="comment">// namespace qbpp</span></div>
</div><!-- fragment --></div><!-- contents -->
<div class="ttc" id="aclassqbpp_1_1Vector_html_af719287ecdb5f44bbd0aa5d9ba3aaea1"><div class="ttname"><a href="classqbpp_1_1Vector.html#af719287ecdb5f44bbd0aa5d9ba3aaea1">qbpp::Vector::size</a></div><div class="ttdeci">size_t size() const</div><div class="ttdef"><b>Definition:</b> <a href="qbpp_8hpp_source.html#l00591">qbpp.hpp:591</a></div></div>
<div class="ttc" id="aclassqbpp_1_1graph__color_1_1GraphColorMap_html_aa5ff2e4ea686651e6f96d4c133c53333"><div class="ttname"><a href="classqbpp_1_1graph__color_1_1GraphColorMap.html#aa5ff2e4ea686651e6f96d4c133c53333">qbpp::graph_color::GraphColorMap::failure_</a></div><div class="ttdeci">uint32_t failure_</div><div class="ttdoc">Number of failure nodes.</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__graph__color_8hpp_source.html#l00058">qbpp_graph_color.hpp:58</a></div></div>
<div class="ttc" id="aclassqbpp_1_1Sol_html_abdb4f6b779678720715f7845f264440f"><div class="ttname"><a href="classqbpp_1_1Sol.html#abdb4f6b779678720715f7845f264440f">qbpp::Sol::get</a></div><div class="ttdeci">var_val_t get(vindex_t index) const</div><div class="ttdef"><b>Definition:</b> <a href="qbpp_8hpp_source.html#l02243">qbpp.hpp:2243</a></div></div>
<div class="ttc" id="aclassqbpp_1_1graph__color_1_1GraphColorQuadModel_html_ab60868e452baf1ad7d34f0d78a8d6195"><div class="ttname"><a href="classqbpp_1_1graph__color_1_1GraphColorQuadModel.html#ab60868e452baf1ad7d34f0d78a8d6195">qbpp::graph_color::GraphColorQuadModel::get_x</a></div><div class="ttdeci">const qbpp::Vector&lt; qbpp::Vector&lt; qbpp::Var &gt; &gt; &amp; get_x() const</div><div class="ttdoc">Returns the reference of the variables for the Graph Coloring Problem.</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__graph__color_8hpp_source.html#l00192">qbpp_graph_color.hpp:192</a></div></div>
<div class="ttc" id="aclassqbpp_1_1graph__color_1_1GraphColorQuadModel_html_ae37a9ca9de5c6ba22b0379e6683ea2f0"><div class="ttname"><a href="classqbpp_1_1graph__color_1_1GraphColorQuadModel.html#ae37a9ca9de5c6ba22b0379e6683ea2f0">qbpp::graph_color::GraphColorQuadModel::x_</a></div><div class="ttdeci">const qbpp::Vector&lt; qbpp::Vector&lt; qbpp::Var &gt; &gt; x_</div><div class="ttdoc">Variables for the Graph Coloring Problem.</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__graph__color_8hpp_source.html#l00164">qbpp_graph_color.hpp:164</a></div></div>
<div class="ttc" id="anamespaceqbpp_html_a1cf3f678bcc7eee089c75d4bc5a0b959"><div class="ttname"><a href="namespaceqbpp.html#a1cf3f678bcc7eee089c75d4bc5a0b959">qbpp::var</a></div><div class="ttdeci">Var var(const std::string &amp;var_str)</div><div class="ttdef"><b>Definition:</b> <a href="qbpp_8hpp_source.html#l02887">qbpp.hpp:2887</a></div></div>
<div class="ttc" id="aclassqbpp_1_1QuadModel_html"><div class="ttname"><a href="classqbpp_1_1QuadModel.html">qbpp::QuadModel</a></div><div class="ttdef"><b>Definition:</b> <a href="qbpp_8hpp_source.html#l01892">qbpp.hpp:1892</a></div></div>
<div class="ttc" id="aclassqbpp_1_1graph__color_1_1GraphColorMap_html_a57e69d0643a44dd69336f1cbe41e74de"><div class="ttname"><a href="classqbpp_1_1graph__color_1_1GraphColorMap.html#a57e69d0643a44dd69336f1cbe41e74de">qbpp::graph_color::GraphColorMap::get_edges</a></div><div class="ttdeci">const std::vector&lt; std::pair&lt; uint32_t, uint32_t &gt; &gt; get_edges() const</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__graph__color_8hpp_source.html#l00137">qbpp_graph_color.hpp:137</a></div></div>
<div class="ttc" id="aclassqbpp_1_1graph__color_1_1GraphColorMap_html_af229a75b21e41767048e169b0b1ae895"><div class="ttname"><a href="classqbpp_1_1graph__color_1_1GraphColorMap.html#af229a75b21e41767048e169b0b1ae895">qbpp::graph_color::GraphColorMap::add_node</a></div><div class="ttdeci">void add_node(uint32_t x, uint32_t y)</div><div class="ttdoc">Add a node to the map.</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__graph__color_8hpp_source.html#l00063">qbpp_graph_color.hpp:63</a></div></div>
<div class="ttc" id="aclassqbpp_1_1graph__color_1_1GraphColorMap_html_a71b8ad384f9a9de9c62390d5e3cf27ad"><div class="ttname"><a href="classqbpp_1_1graph__color_1_1GraphColorMap.html#a71b8ad384f9a9de9c62390d5e3cf27ad">qbpp::graph_color::GraphColorMap::dist</a></div><div class="ttdeci">uint32_t dist(const std::pair&lt; int32_t, int32_t &gt; &amp;p1, const std::pair&lt; int32_t, int32_t &gt; &amp;p2) const</div><div class="ttdoc">Compute the Euclidean distance between two nodes.</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__graph__color_8hpp_source.html#l00070">qbpp_graph_color.hpp:70</a></div></div>
<div class="ttc" id="anamespaceqbpp_html_afc344ea88cf7416a6827b5cec17838df"><div class="ttname"><a href="namespaceqbpp.html#afc344ea88cf7416a6827b5cec17838df">qbpp::onehot_to_int</a></div><div class="ttdeci">int32_t onehot_to_int(const Vector&lt; var_val_t &gt; &amp;vec)</div><div class="ttdef"><b>Definition:</b> <a href="qbpp_8hpp_source.html#l04389">qbpp.hpp:4389</a></div></div>
<div class="ttc" id="aclassqbpp_1_1graph__color_1_1GraphColorMap_html_a2998475fcfdad0901e3a20bd1032867b"><div class="ttname"><a href="classqbpp_1_1graph__color_1_1GraphColorMap.html#a2998475fcfdad0901e3a20bd1032867b">qbpp::graph_color::GraphColorMap::gen_delaunay_edges</a></div><div class="ttdeci">void gen_delaunay_edges()</div><div class="ttdoc">Create edges between nodes using the Delaunay triangulation.</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__graph__color_8hpp_source.html#l00238">qbpp_graph_color.hpp:238</a></div></div>
<div class="ttc" id="aclassqbpp_1_1graph__color_1_1GraphColorMap_html_a50c783bf212bef619d408d379df17a36"><div class="ttname"><a href="classqbpp_1_1graph__color_1_1GraphColorMap.html#a50c783bf212bef619d408d379df17a36">qbpp::graph_color::GraphColorMap::edges_</a></div><div class="ttdeci">std::vector&lt; std::pair&lt; uint32_t, uint32_t &gt; &gt; edges_</div><div class="ttdoc">List of edges with the distance between the nodes.</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__graph__color_8hpp_source.html#l00044">qbpp_graph_color.hpp:44</a></div></div>
<div class="ttc" id="aclassqbpp_1_1graph__color_1_1GraphColorQuadModel_html_a693134505a13381ab5602f24e4928529"><div class="ttname"><a href="classqbpp_1_1graph__color_1_1GraphColorQuadModel.html#a693134505a13381ab5602f24e4928529">qbpp::graph_color::GraphColorQuadModel::GraphColorQuadModel</a></div><div class="ttdeci">GraphColorQuadModel(const GraphColorMap &amp;map, uint32_t color_count)</div><div class="ttdoc">Generate a QUBO expression for the Graph Coloring Problem.</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__graph__color_8hpp_source.html#l00183">qbpp_graph_color.hpp:183</a></div></div>
<div class="ttc" id="aclassqbpp_1_1graph__color_1_1GraphColorQuadModel_html_aedcc6d7da75d07ac65c40950d40f7706"><div class="ttname"><a href="classqbpp_1_1graph__color_1_1GraphColorQuadModel.html#aedcc6d7da75d07ac65c40950d40f7706">qbpp::graph_color::GraphColorQuadModel::GraphColorQuadModel</a></div><div class="ttdeci">GraphColorQuadModel(std::pair&lt; qbpp::QuadModel, qbpp::Vector&lt; qbpp::Vector&lt; qbpp::Var &gt;&gt;&gt; pair)</div><div class="ttdoc">Delegated constructor for the GraphColorQuadModel.</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__graph__color_8hpp_source.html#l00175">qbpp_graph_color.hpp:175</a></div></div>
<div class="ttc" id="aclassqbpp_1_1misc_1_1RandomGenerator_html_a95308c2300aeef9a881ba97786367977"><div class="ttname"><a href="classqbpp_1_1misc_1_1RandomGenerator.html#a95308c2300aeef9a881ba97786367977">qbpp::misc::RandomGenerator::gen</a></div><div class="ttdeci">static uint64_t gen()</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__misc_8hpp_source.html#l00116">qbpp_misc.hpp:116</a></div></div>
<div class="ttc" id="aclassqbpp_1_1graph__color_1_1GraphColorMap_html_a21bd722871bfdf7f2e8f93cbcf0ad639"><div class="ttname"><a href="classqbpp_1_1graph__color_1_1GraphColorMap.html#a21bd722871bfdf7f2e8f93cbcf0ad639">qbpp::graph_color::GraphColorMap::gen_proximity_edges</a></div><div class="ttdeci">void gen_proximity_edges(uint32_t proximity)</div><div class="ttdoc">Create an edges between nodes that are close to each other.</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__graph__color_8hpp_source.html#l00228">qbpp_graph_color.hpp:228</a></div></div>
<div class="ttc" id="aclassqbpp_1_1Vector_html"><div class="ttname"><a href="classqbpp_1_1Vector.html">qbpp::Vector&lt; int32_t &gt;</a></div></div>
<div class="ttc" id="aclassqbpp_1_1graph__color_1_1GraphColorMap_html_a2964d48ac547f74049b38bba057ed337"><div class="ttname"><a href="classqbpp_1_1graph__color_1_1GraphColorMap.html#a2964d48ac547f74049b38bba057ed337">qbpp::graph_color::GraphColorMap::color_</a></div><div class="ttdeci">qbpp::Vector&lt; int32_t &gt; color_</div><div class="ttdoc">List of colors of the nodes.</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__graph__color_8hpp_source.html#l00049">qbpp_graph_color.hpp:49</a></div></div>
<div class="ttc" id="aclassqbpp_1_1graph__color_1_1GraphColorQuadModel_html_a5137f108589790091a08b0c8f1bfc049"><div class="ttname"><a href="classqbpp_1_1graph__color_1_1GraphColorQuadModel.html#a5137f108589790091a08b0c8f1bfc049">qbpp::graph_color::GraphColorQuadModel::helper_func</a></div><div class="ttdeci">std::pair&lt; qbpp::Model, qbpp::Vector&lt; qbpp::Vector&lt; qbpp::Var &gt; &gt; &gt; helper_func(const GraphColorMap &amp;map, uint32_t color_count)</div><div class="ttdoc">Helper function to generate the QUBO expression for the GraphColorMap object and the number of colors...</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__graph__color_8hpp_source.html#l00360">qbpp_graph_color.hpp:360</a></div></div>
<div class="ttc" id="aqbpp_8hpp_html"><div class="ttname"><a href="qbpp_8hpp.html">qbpp.hpp</a></div><div class="ttdoc">QUBO++, a C++ library for generating expressions for binary and spin variables.</div></div>
<div class="ttc" id="aclassqbpp_1_1graph__color_1_1GraphColorMap_html_a9f987a3d14219ed4ed4b477f24e3dde8"><div class="ttname"><a href="classqbpp_1_1graph__color_1_1GraphColorMap.html#a9f987a3d14219ed4ed4b477f24e3dde8">qbpp::graph_color::GraphColorMap::GraphColorMap</a></div><div class="ttdeci">GraphColorMap(uint32_t grid_size=100)</div><div class="ttdoc">Constructor to Create an empty map.</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__graph__color_8hpp_source.html#l00107">qbpp_graph_color.hpp:107</a></div></div>
<div class="ttc" id="aclassqbpp_1_1graph__color_1_1GraphColorMap_html_a2bd9f172d531644b2fe1e85f83a66e16"><div class="ttname"><a href="classqbpp_1_1graph__color_1_1GraphColorMap.html#a2bd9f172d531644b2fe1e85f83a66e16">qbpp::graph_color::GraphColorMap::draw</a></div><div class="ttdeci">void draw(const std::string &amp;filename, bool is_blank=false)</div><div class="ttdoc">Draw the graph in a file.</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__graph__color_8hpp_source.html#l00274">qbpp_graph_color.hpp:274</a></div></div>
<div class="ttc" id="aqbpp__misc_8hpp_html"><div class="ttname"><a href="qbpp__misc_8hpp.html">qbpp_misc.hpp</a></div><div class="ttdoc">A miscellaneous library used for sample programs of the QUBO++ library.</div></div>
<div class="ttc" id="aclassqbpp_1_1graph__color_1_1GraphColorMap_html_aeb565d9a9de7e19c8e073397c5b64aa8"><div class="ttname"><a href="classqbpp_1_1graph__color_1_1GraphColorMap.html#aeb565d9a9de7e19c8e073397c5b64aa8">qbpp::graph_color::GraphColorMap::operator[]</a></div><div class="ttdeci">std::pair&lt; int32_t, int32_t &gt; &amp; operator[](uint32_t index)</div><div class="ttdoc">Get the position of the node at index i.</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__graph__color_8hpp_source.html#l00099">qbpp_graph_color.hpp:99</a></div></div>
<div class="ttc" id="aclassqbpp_1_1graph__color_1_1GraphColorMap_html_ad90af91eeca9f99c1a611d144cae1934"><div class="ttname"><a href="classqbpp_1_1graph__color_1_1GraphColorMap.html#ad90af91eeca9f99c1a611d144cae1934">qbpp::graph_color::GraphColorMap::set_color_histogram</a></div><div class="ttdeci">void set_color_histogram(const GraphColorQuadModel &amp;model, const qbpp::Sol &amp;sol)</div><div class="ttdoc">Set the color histogram of the nodes.</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__graph__color_8hpp_source.html#l00259">qbpp_graph_color.hpp:259</a></div></div>
<div class="ttc" id="aclassqbpp_1_1graph__color_1_1GraphColorMap_html_a1b5655bcb05bdad4816e2f28b71df5ea"><div class="ttname"><a href="classqbpp_1_1graph__color_1_1GraphColorMap.html#a1b5655bcb05bdad4816e2f28b71df5ea">qbpp::graph_color::GraphColorMap::get_grid_size</a></div><div class="ttdeci">uint32_t get_grid_size() const</div><div class="ttdoc">Gets the size of the grid.</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__graph__color_8hpp_source.html#l00135">qbpp_graph_color.hpp:135</a></div></div>
<div class="ttc" id="anamespaceqbpp_html_a008af6dfb07e3e463ee6d83c547c3ff0"><div class="ttname"><a href="namespaceqbpp.html#a008af6dfb07e3e463ee6d83c547c3ff0">qbpp::vector_sum</a></div><div class="ttdeci">Expr vector_sum(const T &amp;items[[maybe_unused]])</div><div class="ttdef"><b>Definition:</b> <a href="qbpp_8hpp_source.html#l03998">qbpp.hpp:3998</a></div></div>
<div class="ttc" id="anamespaceqbpp_html"><div class="ttname"><a href="namespaceqbpp.html">qbpp</a></div><div class="ttdoc">Generates a QUBO Expression for the Graph Coloring Problem using QUBO++ library.</div><div class="ttdef"><b>Definition:</b> <a href="qbpp_8hpp_source.html#l00052">qbpp.hpp:52</a></div></div>
<div class="ttc" id="aclassqbpp_1_1graph__color_1_1GraphColorMap_html_a209786c3963fd87b7275b87c06d54cf3"><div class="ttname"><a href="classqbpp_1_1graph__color_1_1GraphColorMap.html#a209786c3963fd87b7275b87c06d54cf3">qbpp::graph_color::GraphColorMap::min_dist</a></div><div class="ttdeci">uint32_t min_dist(uint32_t x, uint32_t y) const</div><div class="ttdoc">Compute the minimum distance between a new node and all other nodes.</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__graph__color_8hpp_source.html#l00088">qbpp_graph_color.hpp:88</a></div></div>
<div class="ttc" id="aclassqbpp_1_1graph__color_1_1GraphColorQuadModel_html_a64a8634684f7ec725b804b76b383e093"><div class="ttname"><a href="classqbpp_1_1graph__color_1_1GraphColorQuadModel.html#a64a8634684f7ec725b804b76b383e093">qbpp::graph_color::GraphColorQuadModel::node_count</a></div><div class="ttdeci">uint32_t node_count() const</div><div class="ttdoc">Returns the number of nodes in the TSP.</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__graph__color_8hpp_source.html#l00188">qbpp_graph_color.hpp:188</a></div></div>
<div class="ttc" id="aclassqbpp_1_1graph__color_1_1GraphColorMap_html_a6c893ea2db3651474f84553e804aa95a"><div class="ttname"><a href="classqbpp_1_1graph__color_1_1GraphColorMap.html#a6c893ea2db3651474f84553e804aa95a">qbpp::graph_color::GraphColorMap::dist</a></div><div class="ttdeci">uint32_t dist(size_t i, size_t j) const</div><div class="ttdoc">Compute the Euclidean distance between two nodes.</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__graph__color_8hpp_source.html#l00081">qbpp_graph_color.hpp:81</a></div></div>
<div class="ttc" id="aclassqbpp_1_1graph__color_1_1GraphColorMap_html_ac6f6a20d8a0ddd93cc34a49ac07ffa9f"><div class="ttname"><a href="classqbpp_1_1graph__color_1_1GraphColorMap.html#ac6f6a20d8a0ddd93cc34a49ac07ffa9f">qbpp::graph_color::GraphColorMap::print</a></div><div class="ttdeci">void print()</div><div class="ttdoc">Displays the histogram of the colors.</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__graph__color_8hpp_source.html#l00150">qbpp_graph_color.hpp:150</a></div></div>
<div class="ttc" id="aclassqbpp_1_1graph__color_1_1GraphColorQuadModel_html"><div class="ttname"><a href="classqbpp_1_1graph__color_1_1GraphColorQuadModel.html">qbpp::graph_color::GraphColorQuadModel</a></div><div class="ttdoc">Class to store the QUBO expression with variables for the Graph Coloring Problem.</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__graph__color_8hpp_source.html#l00162">qbpp_graph_color.hpp:162</a></div></div>
<div class="ttc" id="anamespaceqbpp_html_aa3ce65464a38ba36e0b6e99b97234245"><div class="ttname"><a href="namespaceqbpp.html#aa3ce65464a38ba36e0b6e99b97234245">qbpp::simplify_as_binary</a></div><div class="ttdeci">Expr simplify_as_binary(const Expr &amp;expr)</div><div class="ttdef"><b>Definition:</b> <a href="qbpp_8hpp_source.html#l03442">qbpp.hpp:3442</a></div></div>
<div class="ttc" id="aclassqbpp_1_1graph__color_1_1GraphColorMap_html_a5579bbb4b7154c6ff380ab941345698b"><div class="ttname"><a href="classqbpp_1_1graph__color_1_1GraphColorMap.html#a5579bbb4b7154c6ff380ab941345698b">qbpp::graph_color::GraphColorMap::grid_size_</a></div><div class="ttdeci">const uint32_t grid_size_</div><div class="ttdoc">Size of the grid. grid_size x grid_size coordinates are used for the map.</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__graph__color_8hpp_source.html#l00034">qbpp_graph_color.hpp:34</a></div></div>
<div class="ttc" id="aclassqbpp_1_1graph__color_1_1GraphColorMap_html_aa33498f6607b683532bbc874dd1553f8"><div class="ttname"><a href="classqbpp_1_1graph__color_1_1GraphColorMap.html#aa33498f6607b683532bbc874dd1553f8">qbpp::graph_color::GraphColorMap::color_hist_</a></div><div class="ttdeci">std::vector&lt; uint32_t &gt; color_hist_</div><div class="ttdoc">Histogram of the colors.</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__graph__color_8hpp_source.html#l00053">qbpp_graph_color.hpp:53</a></div></div>
<div class="ttc" id="aclassqbpp_1_1Sol_html"><div class="ttname"><a href="classqbpp_1_1Sol.html">qbpp::Sol</a></div><div class="ttdef"><b>Definition:</b> <a href="qbpp_8hpp_source.html#l02182">qbpp.hpp:2182</a></div></div>
<div class="ttc" id="aclassqbpp_1_1graph__color_1_1GraphColorMap_html_af28b8561c505cf32403aa8368ff0decf"><div class="ttname"><a href="classqbpp_1_1graph__color_1_1GraphColorMap.html#af28b8561c505cf32403aa8368ff0decf">qbpp::graph_color::GraphColorMap::node_count</a></div><div class="ttdeci">uint32_t node_count() const</div><div class="ttdoc">Gets the number of nodes in the map.</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__graph__color_8hpp_source.html#l00131">qbpp_graph_color.hpp:131</a></div></div>
<div class="ttc" id="aclassqbpp_1_1graph__color_1_1GraphColorMap_html_ad1dda5ce351f070a0eb1e2f3e328a9a4"><div class="ttname"><a href="classqbpp_1_1graph__color_1_1GraphColorMap.html#ad1dda5ce351f070a0eb1e2f3e328a9a4">qbpp::graph_color::GraphColorMap::nodes_</a></div><div class="ttdeci">std::vector&lt; std::pair&lt; int32_t, int32_t &gt; &gt; nodes_</div><div class="ttdoc">List of nodes with coordinate (x, y)</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__graph__color_8hpp_source.html#l00038">qbpp_graph_color.hpp:38</a></div></div>
<div class="ttc" id="aclassqbpp_1_1graph__color_1_1GraphColorMap_html"><div class="ttname"><a href="classqbpp_1_1graph__color_1_1GraphColorMap.html">qbpp::graph_color::GraphColorMap</a></div><div class="ttdoc">Class to store a graph with node coloring and related information.</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__graph__color_8hpp_source.html#l00031">qbpp_graph_color.hpp:31</a></div></div>
<div class="ttc" id="anamespaceqbpp_html_a9041f8ee7d3f4a7c04a0d385babae285"><div class="ttname"><a href="namespaceqbpp.html#a9041f8ee7d3f4a7c04a0d385babae285">qbpp::sum</a></div><div class="ttdeci">Expr sum(const Vector&lt; T &gt; &amp;items)</div><div class="ttdef"><b>Definition:</b> <a href="qbpp_8hpp_source.html#l03925">qbpp.hpp:3925</a></div></div>
<div class="ttc" id="aclassqbpp_1_1graph__color_1_1GraphColorMap_html_aaac00a29df1d2986a05b246ec3f61ea9"><div class="ttname"><a href="classqbpp_1_1graph__color_1_1GraphColorMap.html#aaac00a29df1d2986a05b246ec3f61ea9">qbpp::graph_color::GraphColorMap::gen_random_map</a></div><div class="ttdeci">void gen_random_map(uint32_t n, bool is_circle=false)</div><div class="ttdoc">Generate a random map with n nodes.</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__graph__color_8hpp_source.html#l00199">qbpp_graph_color.hpp:199</a></div></div>
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.17
</small></address>
</body>
</html>
