<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.17"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>QUBO++ Library with QUBO Solver APIs: sample/partition_easy.cpp Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/x-mathjax-config">
  MathJax.Hub.Config({
    extensions: ["tex2jax.js", "TeX/AMSmath.js", "TeX/AMSsymbols.js"],
    jax: ["input/TeX","output/HTML-CSS"],
});
</script>
<script type="text/javascript" async="async" src="https://cdn.jsdelivr.net/npm/mathjax@2/MathJax.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">QUBO++ Library with QUBO Solver APIs
   </div>
   <div id="projectbrief">Author: Koji Nakano, License: Non-commercial research and evaluation purposes without any guarantees.</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.17 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_c29eeb5af533606caea8a09e66794740.html">sample</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">partition_easy.cpp</div>  </div>
</div><!--header-->
<div class="contents">
<a href="partition__easy_8cpp.html">Go to the documentation of this file.</a><div class="fragment"><div class="line"><a name="l00001"></a><span class="lineno">    1</span>&#160; </div>
<div class="line"><a name="l00006"></a><span class="lineno">    6</span>&#160;<span class="preprocessor">#include &lt;boost/program_options.hpp&gt;</span></div>
<div class="line"><a name="l00007"></a><span class="lineno">    7</span>&#160; </div>
<div class="line"><a name="l00008"></a><span class="lineno">    8</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="qbpp_8hpp.html">qbpp.hpp</a>&quot;</span></div>
<div class="line"><a name="l00009"></a><span class="lineno">    9</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="qbpp__easy__solver_8hpp.html">qbpp_easy_solver.hpp</a>&quot;</span></div>
<div class="line"><a name="l00010"></a><span class="lineno">   10</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="qbpp__misc_8hpp.html">qbpp_misc.hpp</a>&quot;</span></div>
<div class="line"><a name="l00011"></a><span class="lineno">   11</span>&#160; </div>
<div class="line"><a name="l00012"></a><span class="lineno">   12</span>&#160;<span class="keyword">namespace </span>po = boost::program_options;</div>
<div class="line"><a name="l00013"></a><span class="lineno">   13</span>&#160; </div>
<div class="line"><a name="l00018"></a><span class="lineno"><a class="line" href="partition__easy_8cpp.html#a3c04138a5bfe5d72780bb7e82a18e627">   18</a></span>&#160;<span class="keywordtype">int</span> <a class="code" href="partition__easy_8cpp.html#a3c04138a5bfe5d72780bb7e82a18e627">main</a>(<span class="keywordtype">int</span> argc, <span class="keywordtype">char</span> **argv) {</div>
<div class="line"><a name="l00019"></a><span class="lineno">   19</span>&#160;  <span class="comment">// clang-format off</span></div>
<div class="line"><a name="l00020"></a><span class="lineno">   20</span>&#160;  po::options_description desc(</div>
<div class="line"><a name="l00021"></a><span class="lineno">   21</span>&#160;      <span class="stringliteral">&quot;Solving the random partitioning problem using QUBO++ Easy Solver.&quot;</span>);</div>
<div class="line"><a name="l00022"></a><span class="lineno">   22</span>&#160;  desc.add_options()(<span class="stringliteral">&quot;help,h&quot;</span>, <span class="stringliteral">&quot;produce help message.&quot;</span>)</div>
<div class="line"><a name="l00023"></a><span class="lineno">   23</span>&#160;    (<span class="stringliteral">&quot;size,s&quot;</span>, po::value&lt;size_t&gt;()-&gt;default_value(10), <span class="stringliteral">&quot;Set the size of the input set.&quot;</span>)</div>
<div class="line"><a name="l00024"></a><span class="lineno">   24</span>&#160;    (<span class="stringliteral">&quot;random,r&quot;</span>, po::value&lt;uint32_t&gt;(), <span class="stringliteral">&quot;Set the base seed for the random number generator for deterministic behavior.&quot;</span>)</div>
<div class="line"><a name="l00025"></a><span class="lineno">   25</span>&#160;    (<span class="stringliteral">&quot;max,m&quot;</span>, po::value&lt;qbpp::energy_t&gt;()-&gt;default_value(1000), <span class="stringliteral">&quot;Set the maximum value of the input set.&quot;</span>)</div>
<div class="line"><a name="l00026"></a><span class="lineno">   26</span>&#160;    (<span class="stringliteral">&quot;time,t&quot;</span>, po::value&lt;uint32_t&gt;()-&gt;default_value(10), <span class="stringliteral">&quot;Set the time limit in seconds.&quot;</span>);</div>
<div class="line"><a name="l00027"></a><span class="lineno">   27</span>&#160;  <span class="comment">// clang-format on</span></div>
<div class="line"><a name="l00028"></a><span class="lineno">   28</span>&#160; </div>
<div class="line"><a name="l00029"></a><span class="lineno">   29</span>&#160;  po::variables_map vm;</div>
<div class="line"><a name="l00030"></a><span class="lineno">   30</span>&#160;  <span class="keywordflow">try</span> {</div>
<div class="line"><a name="l00031"></a><span class="lineno">   31</span>&#160;    po::store(po::parse_command_line(argc, argv, desc), vm);</div>
<div class="line"><a name="l00032"></a><span class="lineno">   32</span>&#160;  } <span class="keywordflow">catch</span> (<span class="keyword">const</span> std::exception &amp;e) {</div>
<div class="line"><a name="l00033"></a><span class="lineno">   33</span>&#160;    std::cout &lt;&lt; <span class="stringliteral">&quot;Wrong arguments. Please use -h/--help option to see the &quot;</span></div>
<div class="line"><a name="l00034"></a><span class="lineno">   34</span>&#160;                 <span class="stringliteral">&quot;usage.\n&quot;</span>;</div>
<div class="line"><a name="l00035"></a><span class="lineno">   35</span>&#160;    <span class="keywordflow">return</span> 1;</div>
<div class="line"><a name="l00036"></a><span class="lineno">   36</span>&#160;  }</div>
<div class="line"><a name="l00037"></a><span class="lineno">   37</span>&#160;  po::notify(vm);</div>
<div class="line"><a name="l00038"></a><span class="lineno">   38</span>&#160; </div>
<div class="line"><a name="l00039"></a><span class="lineno">   39</span>&#160;  <span class="keywordflow">if</span> (vm.count(<span class="stringliteral">&quot;help&quot;</span>)) {</div>
<div class="line"><a name="l00040"></a><span class="lineno">   40</span>&#160;    std::cout &lt;&lt; desc &lt;&lt; std::endl;</div>
<div class="line"><a name="l00041"></a><span class="lineno">   41</span>&#160;    <span class="keywordflow">return</span> 0;</div>
<div class="line"><a name="l00042"></a><span class="lineno">   42</span>&#160;  }</div>
<div class="line"><a name="l00043"></a><span class="lineno">   43</span>&#160; </div>
<div class="line"><a name="l00044"></a><span class="lineno">   44</span>&#160;  <span class="comment">// Set the random seed for deterministic behavior if seed is provided.</span></div>
<div class="line"><a name="l00045"></a><span class="lineno">   45</span>&#160;  <span class="keywordflow">if</span> (vm.count(<span class="stringliteral">&quot;random&quot;</span>)) {</div>
<div class="line"><a name="l00046"></a><span class="lineno">   46</span>&#160;    <a class="code" href="classqbpp_1_1misc_1_1RandomGenerator.html#a1f4f3d675aee33dce174c8222c3bc22a">qbpp::misc::RandomGenerator::set_seed</a>(vm[<span class="stringliteral">&quot;random&quot;</span>].as&lt;uint32_t&gt;());</div>
<div class="line"><a name="l00047"></a><span class="lineno">   47</span>&#160;  }</div>
<div class="line"><a name="l00048"></a><span class="lineno">   48</span>&#160; </div>
<div class="line"><a name="l00050"></a><span class="lineno">   50</span>&#160;  <span class="keywordtype">size_t</span> size = vm[<span class="stringliteral">&quot;size&quot;</span>].as&lt;<span class="keywordtype">size_t</span>&gt;();</div>
<div class="line"><a name="l00052"></a><span class="lineno">   52</span>&#160;  <a class="code" href="namespaceqbpp.html#a9c324f641779d1a7a3c7723f28d8cff4">qbpp::energy_t</a> max_val = vm[<span class="stringliteral">&quot;max&quot;</span>].as&lt;<a class="code" href="namespaceqbpp.html#a9c324f641779d1a7a3c7723f28d8cff4">qbpp::energy_t</a>&gt;();</div>
<div class="line"><a name="l00053"></a><span class="lineno">   53</span>&#160; </div>
<div class="line"><a name="l00055"></a><span class="lineno">   55</span>&#160;  uint32_t time_limit = vm[<span class="stringliteral">&quot;time&quot;</span>].as&lt;uint32_t&gt;();</div>
<div class="line"><a name="l00056"></a><span class="lineno">   56</span>&#160; </div>
<div class="line"><a name="l00058"></a><span class="lineno">   58</span>&#160;  <a class="code" href="classqbpp_1_1Vector.html">qbpp::Vector&lt;qbpp::energy_t&gt;</a> w;</div>
<div class="line"><a name="l00059"></a><span class="lineno">   59</span>&#160; </div>
<div class="line"><a name="l00061"></a><span class="lineno">   61</span>&#160;  <span class="keywordflow">for</span> (<span class="keywordtype">size_t</span> i = 0; i &lt; size; i++) {</div>
<div class="line"><a name="l00062"></a><span class="lineno">   62</span>&#160;    w.<a class="code" href="classqbpp_1_1Vector.html#a05545411bd760deb7206b0079f4f5833">emplace_back</a>(<a class="code" href="classqbpp_1_1misc_1_1RandomGenerator.html#a95308c2300aeef9a881ba97786367977">qbpp::misc::RandomGenerator::gen</a>(max_val));</div>
<div class="line"><a name="l00063"></a><span class="lineno">   63</span>&#160;  }</div>
<div class="line"><a name="l00064"></a><span class="lineno">   64</span>&#160; </div>
<div class="line"><a name="l00066"></a><span class="lineno">   66</span>&#160;  std::cout &lt;&lt; <a class="code" href="namespaceqbpp.html#a41f8243940eb837c282aed85287bd3af">qbpp::str</a>(w, <span class="stringliteral">&quot;w&quot;</span>) &lt;&lt; std::endl;</div>
<div class="line"><a name="l00067"></a><span class="lineno">   67</span>&#160; </div>
<div class="line"><a name="l00068"></a><span class="lineno">   68</span>&#160;  <span class="keyword">auto</span> x = <a class="code" href="namespaceqbpp.html#a1cf3f678bcc7eee089c75d4bc5a0b959">qbpp::var</a>(<span class="stringliteral">&quot;x&quot;</span>, w.size());</div>
<div class="line"><a name="l00069"></a><span class="lineno">   69</span>&#160;  <span class="keyword">auto</span> f =</div>
<div class="line"><a name="l00070"></a><span class="lineno">   70</span>&#160;      <a class="code" href="namespaceqbpp.html#abd03cc17ab61f6ab2be95751c7b92626">qbpp::sqr</a>(<a class="code" href="namespaceqbpp.html#a9041f8ee7d3f4a7c04a0d385babae285">qbpp::sum</a>(w * (1 - x)) - <a class="code" href="namespaceqbpp.html#a9041f8ee7d3f4a7c04a0d385babae285">qbpp::sum</a>(w * x)).simplify_as_binary();</div>
<div class="line"><a name="l00071"></a><span class="lineno">   71</span>&#160; </div>
<div class="line"><a name="l00072"></a><span class="lineno">   72</span>&#160;  <span class="comment">// Generates a QUBO++ easy solver object from the QUBO model</span></div>
<div class="line"><a name="l00073"></a><span class="lineno">   73</span>&#160;  <span class="keyword">auto</span> solver = <a class="code" href="classqbpp_1_1easy__solver_1_1EasySolver.html">qbpp::easy_solver::EasySolver</a>(f);</div>
<div class="line"><a name="l00074"></a><span class="lineno">   74</span>&#160; </div>
<div class="line"><a name="l00075"></a><span class="lineno">   75</span>&#160;  <span class="comment">// Sets the time limit</span></div>
<div class="line"><a name="l00076"></a><span class="lineno">   76</span>&#160;  solver.set_time_limit(time_limit);</div>
<div class="line"><a name="l00077"></a><span class="lineno">   77</span>&#160; </div>
<div class="line"><a name="l00078"></a><span class="lineno">   78</span>&#160;  <span class="comment">// Sets the target energy to 1</span></div>
<div class="line"><a name="l00079"></a><span class="lineno">   79</span>&#160;  solver.set_target_energy(1);</div>
<div class="line"><a name="l00080"></a><span class="lineno">   80</span>&#160; </div>
<div class="line"><a name="l00081"></a><span class="lineno">   81</span>&#160;  <span class="comment">// Enables the default callback</span></div>
<div class="line"><a name="l00082"></a><span class="lineno">   82</span>&#160;  solver.enable_default_callback();</div>
<div class="line"><a name="l00083"></a><span class="lineno">   83</span>&#160; </div>
<div class="line"><a name="l00084"></a><span class="lineno">   84</span>&#160;  <span class="comment">// Executes the QUBO++ easy solver</span></div>
<div class="line"><a name="l00085"></a><span class="lineno">   85</span>&#160;  <span class="keyword">auto</span> sol = solver.search();</div>
<div class="line"><a name="l00086"></a><span class="lineno">   86</span>&#160; </div>
<div class="line"><a name="l00087"></a><span class="lineno">   87</span>&#160;  <span class="comment">// Prints the QUBO solution</span></div>
<div class="line"><a name="l00088"></a><span class="lineno">   88</span>&#160;  std::cout &lt;&lt; <span class="stringliteral">&quot;Solution = &quot;</span> &lt;&lt; sol &lt;&lt; std::endl;</div>
<div class="line"><a name="l00089"></a><span class="lineno">   89</span>&#160; </div>
<div class="line"><a name="l00090"></a><span class="lineno">   90</span>&#160;  <span class="comment">// Prints the sum of the input set and the sum of the elements in the set</span></div>
<div class="line"><a name="l00091"></a><span class="lineno">   91</span>&#160;  std::cout &lt;&lt; <span class="stringliteral">&quot;sum0 = &quot;</span> &lt;&lt; <a class="code" href="namespaceqbpp.html#ab5da6d0960920098998bb7347082ee6b">qbpp::eval</a>(<a class="code" href="namespaceqbpp.html#a9041f8ee7d3f4a7c04a0d385babae285">qbpp::sum</a>(w * (1 - x)), sol) &lt;&lt; <span class="stringliteral">&quot; :&quot;</span>;</div>
<div class="line"><a name="l00092"></a><span class="lineno">   92</span>&#160;  <span class="keywordflow">for</span> (<span class="keywordtype">size_t</span> i = 0; i &lt; size; i++) {</div>
<div class="line"><a name="l00093"></a><span class="lineno">   93</span>&#160;    <span class="keywordflow">if</span> (sol.get(x[i]) == 0) {</div>
<div class="line"><a name="l00094"></a><span class="lineno">   94</span>&#160;      std::cout &lt;&lt; <span class="stringliteral">&quot; &quot;</span> &lt;&lt; w[i];</div>
<div class="line"><a name="l00095"></a><span class="lineno">   95</span>&#160;    }</div>
<div class="line"><a name="l00096"></a><span class="lineno">   96</span>&#160;  }</div>
<div class="line"><a name="l00097"></a><span class="lineno">   97</span>&#160;  std::cout &lt;&lt; std::endl;</div>
<div class="line"><a name="l00098"></a><span class="lineno">   98</span>&#160;  std::cout &lt;&lt; <span class="stringliteral">&quot;sum1 = &quot;</span> &lt;&lt; <a class="code" href="namespaceqbpp.html#ab5da6d0960920098998bb7347082ee6b">qbpp::eval</a>(<a class="code" href="namespaceqbpp.html#a9041f8ee7d3f4a7c04a0d385babae285">qbpp::sum</a>(w * x), sol) &lt;&lt; <span class="stringliteral">&quot; :&quot;</span>;</div>
<div class="line"><a name="l00099"></a><span class="lineno">   99</span>&#160;  <span class="keywordflow">for</span> (<span class="keywordtype">size_t</span> i = 0; i &lt; size; i++) {</div>
<div class="line"><a name="l00100"></a><span class="lineno">  100</span>&#160;    <span class="keywordflow">if</span> (sol.get(x[i]) == 1) {</div>
<div class="line"><a name="l00101"></a><span class="lineno">  101</span>&#160;      std::cout &lt;&lt; <span class="stringliteral">&quot; &quot;</span> &lt;&lt; w[i];</div>
<div class="line"><a name="l00102"></a><span class="lineno">  102</span>&#160;    }</div>
<div class="line"><a name="l00103"></a><span class="lineno">  103</span>&#160;  }</div>
<div class="line"><a name="l00104"></a><span class="lineno">  104</span>&#160;  std::cout &lt;&lt; std::endl;</div>
<div class="line"><a name="l00105"></a><span class="lineno">  105</span>&#160;}</div>
</div><!-- fragment --></div><!-- contents -->
<div class="ttc" id="anamespaceqbpp_html_abd03cc17ab61f6ab2be95751c7b92626"><div class="ttname"><a href="namespaceqbpp.html#abd03cc17ab61f6ab2be95751c7b92626">qbpp::sqr</a></div><div class="ttdeci">Vector&lt; Expr &gt; sqr(const Vector&lt; T &gt; &amp;arg)</div><div class="ttdef"><b>Definition:</b> <a href="qbpp_8hpp_source.html#l04294">qbpp.hpp:4294</a></div></div>
<div class="ttc" id="anamespaceqbpp_html_a1cf3f678bcc7eee089c75d4bc5a0b959"><div class="ttname"><a href="namespaceqbpp.html#a1cf3f678bcc7eee089c75d4bc5a0b959">qbpp::var</a></div><div class="ttdeci">Var var(const std::string &amp;var_str)</div><div class="ttdef"><b>Definition:</b> <a href="qbpp_8hpp_source.html#l02887">qbpp.hpp:2887</a></div></div>
<div class="ttc" id="anamespaceqbpp_html_a9c324f641779d1a7a3c7723f28d8cff4"><div class="ttname"><a href="namespaceqbpp.html#a9c324f641779d1a7a3c7723f28d8cff4">qbpp::energy_t</a></div><div class="ttdeci">ENERGY_TYPE energy_t</div><div class="ttdef"><b>Definition:</b> <a href="qbpp_8hpp_source.html#l00137">qbpp.hpp:137</a></div></div>
<div class="ttc" id="aclassqbpp_1_1misc_1_1RandomGenerator_html_a95308c2300aeef9a881ba97786367977"><div class="ttname"><a href="classqbpp_1_1misc_1_1RandomGenerator.html#a95308c2300aeef9a881ba97786367977">qbpp::misc::RandomGenerator::gen</a></div><div class="ttdeci">static uint64_t gen()</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__misc_8hpp_source.html#l00116">qbpp_misc.hpp:116</a></div></div>
<div class="ttc" id="aclassqbpp_1_1Vector_html"><div class="ttname"><a href="classqbpp_1_1Vector.html">qbpp::Vector</a></div><div class="ttdef"><b>Definition:</b> <a href="qbpp_8hpp_source.html#l00056">qbpp.hpp:56</a></div></div>
<div class="ttc" id="aqbpp_8hpp_html"><div class="ttname"><a href="qbpp_8hpp.html">qbpp.hpp</a></div><div class="ttdoc">QUBO++, a C++ library for generating expressions for binary and spin variables.</div></div>
<div class="ttc" id="aqbpp__misc_8hpp_html"><div class="ttname"><a href="qbpp__misc_8hpp.html">qbpp_misc.hpp</a></div><div class="ttdoc">A miscellaneous library used for sample programs of the QUBO++ library.</div></div>
<div class="ttc" id="anamespaceqbpp_html_a41f8243940eb837c282aed85287bd3af"><div class="ttname"><a href="namespaceqbpp.html#a41f8243940eb837c282aed85287bd3af">qbpp::str</a></div><div class="ttdeci">std::string str(Var var)</div><div class="ttdef"><b>Definition:</b> <a href="qbpp_8hpp_source.html#l02507">qbpp.hpp:2507</a></div></div>
<div class="ttc" id="aqbpp__easy__solver_8hpp_html"><div class="ttname"><a href="qbpp__easy__solver_8hpp.html">qbpp_easy_solver.hpp</a></div><div class="ttdoc">Easy QUBO Solver for solving QUBO problems.</div></div>
<div class="ttc" id="aclassqbpp_1_1Vector_html_a05545411bd760deb7206b0079f4f5833"><div class="ttname"><a href="classqbpp_1_1Vector.html#a05545411bd760deb7206b0079f4f5833">qbpp::Vector::emplace_back</a></div><div class="ttdeci">void emplace_back(T &amp;&amp;t)</div><div class="ttdef"><b>Definition:</b> <a href="qbpp_8hpp_source.html#l00583">qbpp.hpp:583</a></div></div>
<div class="ttc" id="aclassqbpp_1_1misc_1_1RandomGenerator_html_a1f4f3d675aee33dce174c8222c3bc22a"><div class="ttname"><a href="classqbpp_1_1misc_1_1RandomGenerator.html#a1f4f3d675aee33dce174c8222c3bc22a">qbpp::misc::RandomGenerator::set_seed</a></div><div class="ttdeci">static void set_seed(uint32_t seed=1)</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__misc_8hpp_source.html#l00112">qbpp_misc.hpp:112</a></div></div>
<div class="ttc" id="aclassqbpp_1_1easy__solver_1_1EasySolver_html"><div class="ttname"><a href="classqbpp_1_1easy__solver_1_1EasySolver.html">qbpp::easy_solver::EasySolver</a></div><div class="ttdef"><b>Definition:</b> <a href="qbpp__easy__solver_8hpp_source.html#l00225">qbpp_easy_solver.hpp:225</a></div></div>
<div class="ttc" id="apartition__easy_8cpp_html_a3c04138a5bfe5d72780bb7e82a18e627"><div class="ttname"><a href="partition__easy_8cpp.html#a3c04138a5bfe5d72780bb7e82a18e627">main</a></div><div class="ttdeci">int main(int argc, char **argv)</div><div class="ttdoc">Solves the Partitioning problem using the QUBO++ Easy Solver.</div><div class="ttdef"><b>Definition:</b> <a href="partition__easy_8cpp_source.html#l00018">partition_easy.cpp:18</a></div></div>
<div class="ttc" id="anamespaceqbpp_html_a9041f8ee7d3f4a7c04a0d385babae285"><div class="ttname"><a href="namespaceqbpp.html#a9041f8ee7d3f4a7c04a0d385babae285">qbpp::sum</a></div><div class="ttdeci">Expr sum(const Vector&lt; T &gt; &amp;items)</div><div class="ttdef"><b>Definition:</b> <a href="qbpp_8hpp_source.html#l03925">qbpp.hpp:3925</a></div></div>
<div class="ttc" id="anamespaceqbpp_html_ab5da6d0960920098998bb7347082ee6b"><div class="ttname"><a href="namespaceqbpp.html#ab5da6d0960920098998bb7347082ee6b">qbpp::eval</a></div><div class="ttdeci">energy_t eval(const Expr &amp;expr, const Sol &amp;sol)</div><div class="ttdef"><b>Definition:</b> <a href="qbpp_8hpp_source.html#l02868">qbpp.hpp:2868</a></div></div>
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.17
</small></address>
</body>
</html>
