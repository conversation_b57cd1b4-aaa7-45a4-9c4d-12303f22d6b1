<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.17"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>QUBO++ Library with QUBO Solver APIs: include/qbpp_exhaustive_solver.hpp File Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/x-mathjax-config">
  MathJax.Hub.Config({
    extensions: ["tex2jax.js", "TeX/AMSmath.js", "TeX/AMSsymbols.js"],
    jax: ["input/TeX","output/HTML-CSS"],
});
</script>
<script type="text/javascript" async="async" src="https://cdn.jsdelivr.net/npm/mathjax@2/MathJax.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">QUBO++ Library with QUBO Solver APIs
   </div>
   <div id="projectbrief">Author: Koji Nakano, License: Non-commercial research and evaluation purposes without any guarantees.</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.17 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_d44c64559bbebec7f509842c48db8b23.html">include</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#nested-classes">Classes</a> &#124;
<a href="#namespaces">Namespaces</a> &#124;
<a href="#func-members">Functions</a>  </div>
  <div class="headertitle">
<div class="title">qbpp_exhaustive_solver.hpp File Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p>Exhaustive QUBO Solver for solving QUBO problems.  
<a href="#details">More...</a></p>
<div class="textblock"><code>#include &lt;tbb/blocked_range.h&gt;</code><br />
<code>#include &lt;tbb/parallel_for.h&gt;</code><br />
<code>#include &lt;boost/circular_buffer.hpp&gt;</code><br />
<code>#include &lt;random&gt;</code><br />
<code>#include &lt;set&gt;</code><br />
<code>#include &quot;<a class="el" href="qbpp_8hpp_source.html">qbpp.hpp</a>&quot;</code><br />
</div><div class="textblock"><div class="dynheader">
Include dependency graph for qbpp_exhaustive_solver.hpp:</div>
<div class="dyncontent">
<div class="center"><img src="qbpp__exhaustive__solver_8hpp__incl.png" border="0" usemap="#include_2qbpp__exhaustive__solver_8hpp" alt=""/></div>
<map name="include_2qbpp__exhaustive__solver_8hpp" id="include_2qbpp__exhaustive__solver_8hpp">
<area shape="rect" title="Exhaustive QUBO Solver for solving QUBO problems." alt="" coords="1460,5,1625,47"/>
<area shape="rect" title=" " alt="" coords="5,177,144,203"/>
<area shape="rect" title=" " alt="" coords="168,177,285,203"/>
<area shape="rect" title=" " alt="" coords="1533,95,1697,121"/>
<area shape="rect" title=" " alt="" coords="1721,95,1786,121"/>
<area shape="rect" title=" " alt="" coords="1811,95,1851,121"/>
<area shape="rect" href="qbpp_8hpp.html" title="QUBO++, a C++ library for generating expressions for binary and spin variables." alt="" coords="1433,95,1508,121"/>
<area shape="rect" title=" " alt="" coords="2401,177,2543,203"/>
<area shape="rect" title=" " alt="" coords="2568,177,2693,203"/>
<area shape="rect" title=" " alt="" coords="2717,177,2792,203"/>
<area shape="rect" title=" " alt="" coords="2816,169,2963,211"/>
<area shape="rect" title=" " alt="" coords="2987,169,3133,211"/>
<area shape="rect" title=" " alt="" coords="3158,177,3346,203"/>
<area shape="rect" title=" " alt="" coords="310,177,455,203"/>
<area shape="rect" title=" " alt="" coords="480,169,619,211"/>
<area shape="rect" title=" " alt="" coords="643,177,704,203"/>
<area shape="rect" title=" " alt="" coords="728,177,787,203"/>
<area shape="rect" title=" " alt="" coords="811,177,909,203"/>
<area shape="rect" title=" " alt="" coords="933,177,1000,203"/>
<area shape="rect" title=" " alt="" coords="1024,177,1096,203"/>
<area shape="rect" title=" " alt="" coords="1120,177,1173,203"/>
<area shape="rect" title=" " alt="" coords="1198,177,1237,203"/>
<area shape="rect" title=" " alt="" coords="1261,177,1331,203"/>
<area shape="rect" title=" " alt="" coords="1355,177,1413,203"/>
<area shape="rect" title=" " alt="" coords="1437,177,1504,203"/>
<area shape="rect" title=" " alt="" coords="1528,177,1597,203"/>
<area shape="rect" title=" " alt="" coords="1622,177,1677,203"/>
<area shape="rect" title=" " alt="" coords="1701,177,1760,203"/>
<area shape="rect" title=" " alt="" coords="1784,177,1867,203"/>
<area shape="rect" title=" " alt="" coords="1891,177,2002,203"/>
<area shape="rect" title=" " alt="" coords="2027,177,2131,203"/>
<area shape="rect" title=" " alt="" coords="2155,177,2208,203"/>
<area shape="rect" title=" " alt="" coords="2232,177,2293,203"/>
<area shape="rect" title=" " alt="" coords="2317,177,2376,203"/>
</map>
</div>
</div><div class="textblock"><div class="dynheader">
This graph shows which files directly or indirectly include this file:</div>
<div class="dyncontent">
<div class="center"><img src="qbpp__exhaustive__solver_8hpp__dep__incl.png" border="0" usemap="#include_2qbpp__exhaustive__solver_8hppdep" alt=""/></div>
<map name="include_2qbpp__exhaustive__solver_8hppdep" id="include_2qbpp__exhaustive__solver_8hppdep">
<area shape="rect" title="Exhaustive QUBO Solver for solving QUBO problems." alt="" coords="245,5,410,47"/>
<area shape="rect" href="partition__exhaustive_8cpp.html" title="Solves the Partitioning problem using the QUBO++ Exhaustive solver." alt="" coords="5,95,215,121"/>
<area shape="rect" href="ilp__exhaustive_8cpp.html" title="Solves an Integer Linear Programming (ILP) problem using ExhaustiveSolver through QUBO++ library." alt="" coords="239,95,415,121"/>
<area shape="rect" href="knapsack__exhaustive_8cpp.html" title="This file contains an example of solving the knapsack problem using the exhaustive solver." alt="" coords="439,95,658,121"/>
</map>
</div>
</div>
<p><a href="qbpp__exhaustive__solver_8hpp_source.html">Go to the source code of this file.</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="nested-classes"></a>
Classes</h2></td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">class &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classqbpp_1_1exhaustive__solver_1_1Sol.html">qbpp::exhaustive_solver::Sol</a></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">class &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classqbpp_1_1exhaustive__solver_1_1ExhaustiveSolver.html">qbpp::exhaustive_solver::ExhaustiveSolver</a></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">class &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classqbpp_1_1exhaustive__solver_1_1SearchAlgorithm.html">qbpp::exhaustive_solver::SearchAlgorithm</a></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">class &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classqbpp_1_1exhaustive__solver_1_1SolDelta.html">qbpp::exhaustive_solver::SolDelta</a></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="namespaces"></a>
Namespaces</h2></td></tr>
<tr class="memitem:namespaceqbpp"><td class="memItemLeft" align="right" valign="top"> &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html">qbpp</a></td></tr>
<tr class="memdesc:namespaceqbpp"><td class="mdescLeft">&#160;</td><td class="mdescRight">Generates a QUBO Expression for the Graph Coloring Problem using QUBO++ library. <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:namespaceqbpp_1_1exhaustive__solver"><td class="memItemLeft" align="right" valign="top"> &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp_1_1exhaustive__solver.html">qbpp::exhaustive_solver</a></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:a915ee842570590bc3da41020451535c6"><td class="memItemLeft" align="right" valign="top">std::ostream &amp;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp_1_1exhaustive__solver.html#a915ee842570590bc3da41020451535c6">qbpp::exhaustive_solver::operator&lt;&lt;</a> (std::ostream &amp;os, const Sol &amp;sol)</td></tr>
<tr class="separator:a915ee842570590bc3da41020451535c6"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p>Exhaustive QUBO Solver for solving QUBO problems. </p>
<dl class="section author"><dt>Author</dt><dd>Koji Nakano</dd></dl>
<p>The ExhaustiveSolver class provides a straightforward QUBO solver that evaluates all possible solutions. This solver is primarily intended for testing the correctness of QUBO expressions. For more details on the algorithm, please refer to the following paper: Masaki Tao et al., "A Work-Time Optimal Parallel Exhaustive Search
Algorithm for the QUBO and the Ising Model, with GPU Implementation," IPDPS Workshops 2020: 557-566. <a href="https://doi.org/10.1109/IPDPSW50202.2020.00098">https://doi.org/10.1109/IPDPSW50202.2020.00098</a> </p><dl class="section version"><dt>Version</dt><dd>2025-01-19 </dd></dl>

<p class="definition">Definition in file <a class="el" href="qbpp__exhaustive__solver_8hpp_source.html">qbpp_exhaustive_solver.hpp</a>.</p>
</div></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.17
</small></address>
</body>
</html>
