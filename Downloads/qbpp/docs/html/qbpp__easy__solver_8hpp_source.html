<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.17"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>QUBO++ Library with QUBO Solver APIs: include/qbpp_easy_solver.hpp Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/x-mathjax-config">
  MathJax.Hub.Config({
    extensions: ["tex2jax.js", "TeX/AMSmath.js", "TeX/AMSsymbols.js"],
    jax: ["input/TeX","output/HTML-CSS"],
});
</script>
<script type="text/javascript" async="async" src="https://cdn.jsdelivr.net/npm/mathjax@2/MathJax.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">QUBO++ Library with QUBO Solver APIs
   </div>
   <div id="projectbrief">Author: Koji Nakano, License: Non-commercial research and evaluation purposes without any guarantees.</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.17 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_d44c64559bbebec7f509842c48db8b23.html">include</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">qbpp_easy_solver.hpp</div>  </div>
</div><!--header-->
<div class="contents">
<a href="qbpp__easy__solver_8hpp.html">Go to the documentation of this file.</a><div class="fragment"><div class="line"><a name="l00001"></a><span class="lineno">    1</span>&#160; </div>
<div class="line"><a name="l00018"></a><span class="lineno">   18</span>&#160;<span class="preprocessor">#ifndef QBPP_EASY_SOLVER_HPP</span></div>
<div class="line"><a name="l00019"></a><span class="lineno">   19</span>&#160;<span class="preprocessor">#define QBPP_EASY_SOLVER_HPP</span></div>
<div class="line"><a name="l00020"></a><span class="lineno">   20</span>&#160; </div>
<div class="line"><a name="l00021"></a><span class="lineno">   21</span>&#160;<span class="preprocessor">#include &lt;tbb/blocked_range.h&gt;</span></div>
<div class="line"><a name="l00022"></a><span class="lineno">   22</span>&#160;<span class="preprocessor">#include &lt;tbb/parallel_for.h&gt;</span></div>
<div class="line"><a name="l00023"></a><span class="lineno">   23</span>&#160; </div>
<div class="line"><a name="l00024"></a><span class="lineno">   24</span>&#160;<span class="preprocessor">#include &lt;boost/circular_buffer.hpp&gt;</span></div>
<div class="line"><a name="l00025"></a><span class="lineno">   25</span>&#160;<span class="preprocessor">#include &lt;limits&gt;</span></div>
<div class="line"><a name="l00026"></a><span class="lineno">   26</span>&#160;<span class="preprocessor">#include &lt;random&gt;</span></div>
<div class="line"><a name="l00027"></a><span class="lineno">   27</span>&#160;<span class="preprocessor">#include &lt;set&gt;</span></div>
<div class="line"><a name="l00028"></a><span class="lineno">   28</span>&#160;<span class="preprocessor">#include &lt;thread&gt;</span></div>
<div class="line"><a name="l00029"></a><span class="lineno">   29</span>&#160;<span class="preprocessor">#include &lt;vector&gt;</span></div>
<div class="line"><a name="l00030"></a><span class="lineno">   30</span>&#160; </div>
<div class="line"><a name="l00031"></a><span class="lineno">   31</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="qbpp_8hpp.html">qbpp.hpp</a>&quot;</span></div>
<div class="line"><a name="l00032"></a><span class="lineno">   32</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="qbpp__misc_8hpp.html">qbpp_misc.hpp</a>&quot;</span></div>
<div class="line"><a name="l00033"></a><span class="lineno">   33</span>&#160; </div>
<div class="line"><a name="l00034"></a><span class="lineno">   34</span>&#160;<span class="keyword">extern</span> <span class="stringliteral">&quot;C&quot;</span> {</div>
<div class="line"><a name="l00035"></a><span class="lineno">   35</span>&#160;<span class="preprocessor">#include &quot;xxhash.h&quot;</span></div>
<div class="line"><a name="l00036"></a><span class="lineno">   36</span>&#160;}</div>
<div class="line"><a name="l00037"></a><span class="lineno">   37</span>&#160; </div>
<div class="line"><a name="l00038"></a><span class="lineno">   38</span>&#160;<span class="keyword">namespace </span><a class="code" href="namespaceqbpp.html">qbpp</a> {</div>
<div class="line"><a name="l00039"></a><span class="lineno">   39</span>&#160; </div>
<div class="line"><a name="l00040"></a><span class="lineno">   40</span>&#160; </div>
<div class="line"><a name="l00041"></a><span class="lineno"><a class="line" href="namespaceqbpp_1_1easy__solver.html">   41</a></span>&#160;<span class="keyword">namespace </span>easy_solver {</div>
<div class="line"><a name="l00042"></a><span class="lineno">   42</span>&#160; </div>
<div class="line"><a name="l00043"></a><span class="lineno">   43</span>&#160;<span class="keyword">class </span><a class="code" href="classqbpp_1_1easy__solver_1_1BestSols.html">BestSols</a>;</div>
<div class="line"><a name="l00044"></a><span class="lineno">   44</span>&#160;<span class="keyword">class </span><a class="code" href="classqbpp_1_1easy__solver_1_1SolDelta.html">SolDelta</a>;</div>
<div class="line"><a name="l00045"></a><span class="lineno">   45</span>&#160;<span class="keyword">class </span><a class="code" href="classqbpp_1_1easy__solver_1_1TabuSolDelta.html">TabuSolDelta</a>;</div>
<div class="line"><a name="l00046"></a><span class="lineno">   46</span>&#160;<span class="keyword">class </span><a class="code" href="classqbpp_1_1easy__solver_1_1PosMinSolDelta.html">PosMinSolDelta</a>;</div>
<div class="line"><a name="l00047"></a><span class="lineno">   47</span>&#160;<span class="keyword">class </span><a class="code" href="classqbpp_1_1easy__solver_1_1EasySolver.html">EasySolver</a>;</div>
<div class="line"><a name="l00048"></a><span class="lineno">   48</span>&#160; </div>
<div class="line"><a name="l00049"></a><span class="lineno">   49</span>&#160;<span class="preprocessor">#ifdef USE_128BIT_HASH</span></div>
<div class="line"><a name="l00050"></a><span class="lineno">   50</span>&#160;<span class="keyword">using</span> <a class="code" href="namespaceqbpp_1_1easy__solver.html#ae5027899639f91d80883ae1ca2cf1ddd">HashType</a> = <a class="code" href="namespaceqbpp.html#a35435559d3a02f18b80ae5554dc65c75">boost::multiprecision::uint128_t</a>;</div>
<div class="line"><a name="l00051"></a><span class="lineno">   51</span>&#160;<span class="preprocessor">#else</span></div>
<div class="line"><a name="l00052"></a><span class="lineno"><a class="line" href="namespaceqbpp_1_1easy__solver.html#ae5027899639f91d80883ae1ca2cf1ddd">   52</a></span>&#160;<span class="keyword">using</span> <a class="code" href="namespaceqbpp_1_1easy__solver.html#ae5027899639f91d80883ae1ca2cf1ddd">HashType</a> = uint64_t;</div>
<div class="line"><a name="l00053"></a><span class="lineno">   53</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00054"></a><span class="lineno">   54</span>&#160; </div>
<div class="line"><a name="l00055"></a><span class="lineno"><a class="line" href="namespaceqbpp_1_1easy__solver.html#a99d432f46cb5703bcf15a858bc58c742">   55</a></span>&#160;<a class="code" href="namespaceqbpp_1_1easy__solver.html#ae5027899639f91d80883ae1ca2cf1ddd">HashType</a> <a class="code" href="namespaceqbpp_1_1easy__solver.html#a99d432f46cb5703bcf15a858bc58c742">bit_vector_hash</a>(<span class="keyword">const</span> <a class="code" href="classqbpp_1_1impl_1_1BitVector.html">qbpp::impl::BitVector</a> &amp;bit_vector) {</div>
<div class="line"><a name="l00056"></a><span class="lineno">   56</span>&#160;<span class="preprocessor">#ifdef USE_128BIT_HASH</span></div>
<div class="line"><a name="l00057"></a><span class="lineno">   57</span>&#160;  <span class="keywordflow">return</span> XXH3_128bits(bit_vector.<a class="code" href="classqbpp_1_1impl_1_1BitVector.html#ae5bea2dd45ef269fee44932908717e1e">get_bits_ptr</a>(), bit_vector.<a class="code" href="classqbpp_1_1impl_1_1BitVector.html#a6326d5d93d29fad3e96035a56abbee98">size64</a>() * 8);</div>
<div class="line"><a name="l00058"></a><span class="lineno">   58</span>&#160;<span class="preprocessor">#else</span></div>
<div class="line"><a name="l00059"></a><span class="lineno">   59</span>&#160;  <span class="keywordflow">return</span> XXH3_64bits(bit_vector.<a class="code" href="classqbpp_1_1impl_1_1BitVector.html#ae5bea2dd45ef269fee44932908717e1e">get_bits_ptr</a>(), bit_vector.<a class="code" href="classqbpp_1_1impl_1_1BitVector.html#a6326d5d93d29fad3e96035a56abbee98">size64</a>() * 8);</div>
<div class="line"><a name="l00060"></a><span class="lineno">   60</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00061"></a><span class="lineno">   61</span>&#160;}</div>
<div class="line"><a name="l00062"></a><span class="lineno">   62</span>&#160; </div>
<div class="line"><a name="l00063"></a><span class="lineno"><a class="line" href="structqbpp_1_1easy__solver_1_1SolHash.html">   63</a></span>&#160;<span class="keyword">struct </span><a class="code" href="structqbpp_1_1easy__solver_1_1SolHash.html">SolHash</a> {</div>
<div class="line"><a name="l00064"></a><span class="lineno"><a class="line" href="structqbpp_1_1easy__solver_1_1SolHash.html#af8635a88d82c853e1521797eefe88969">   64</a></span>&#160;  <a class="code" href="namespaceqbpp_1_1easy__solver.html#ae5027899639f91d80883ae1ca2cf1ddd">HashType</a> <a class="code" href="structqbpp_1_1easy__solver_1_1SolHash.html#af8635a88d82c853e1521797eefe88969">operator()</a>(<span class="keyword">const</span> <a class="code" href="classqbpp_1_1Sol.html">qbpp::Sol</a> &amp;sol) <span class="keyword">const</span> noexcept {</div>
<div class="line"><a name="l00065"></a><span class="lineno">   65</span>&#160;    <span class="keywordflow">return</span> <span class="keyword">static_cast&lt;</span><span class="keywordtype">size_t</span><span class="keyword">&gt;</span>(<a class="code" href="namespaceqbpp_1_1easy__solver.html#a99d432f46cb5703bcf15a858bc58c742">bit_vector_hash</a>(sol.bit_vector()));</div>
<div class="line"><a name="l00066"></a><span class="lineno">   66</span>&#160;  }</div>
<div class="line"><a name="l00067"></a><span class="lineno">   67</span>&#160;};</div>
<div class="line"><a name="l00068"></a><span class="lineno">   68</span>&#160; </div>
<div class="line"><a name="l00069"></a><span class="lineno"><a class="line" href="classqbpp_1_1easy__solver_1_1BestSols.html">   69</a></span>&#160;<span class="keyword">class </span><a class="code" href="classqbpp_1_1easy__solver_1_1BestSols.html">BestSols</a> {</div>
<div class="line"><a name="l00070"></a><span class="lineno"><a class="line" href="classqbpp_1_1easy__solver_1_1BestSols.html#aff80d3f4bfebc38e4799d6e0fd78f95d">   70</a></span>&#160;  <span class="keyword">const</span> <span class="keywordtype">size_t</span> <a class="code" href="classqbpp_1_1easy__solver_1_1BestSols.html#aff80d3f4bfebc38e4799d6e0fd78f95d">max_best_sol_count_</a>;</div>
<div class="line"><a name="l00071"></a><span class="lineno"><a class="line" href="classqbpp_1_1easy__solver_1_1BestSols.html#aea0db6e737aa21fc745af8a781cdc9a4">   71</a></span>&#160;  std::vector&lt;qbpp::Sol&gt; <a class="code" href="classqbpp_1_1easy__solver_1_1BestSols.html#aea0db6e737aa21fc745af8a781cdc9a4">sol_vector_</a>;</div>
<div class="line"><a name="l00072"></a><span class="lineno"><a class="line" href="classqbpp_1_1easy__solver_1_1BestSols.html#a9b53d76ce7a7edbbf9b17229f51ec0b9">   72</a></span>&#160;  std::unordered_set&lt;qbpp::Sol, SolHash&gt; <a class="code" href="classqbpp_1_1easy__solver_1_1BestSols.html#a9b53d76ce7a7edbbf9b17229f51ec0b9">sol_set_</a>;</div>
<div class="line"><a name="l00073"></a><span class="lineno"><a class="line" href="classqbpp_1_1easy__solver_1_1BestSols.html#a13dff8120e443f0faeb0fae3218f9a00">   73</a></span>&#160;  <a class="code" href="namespaceqbpp.html#a9c324f641779d1a7a3c7723f28d8cff4">energy_t</a> <a class="code" href="classqbpp_1_1easy__solver_1_1BestSols.html#a13dff8120e443f0faeb0fae3218f9a00">worst_energy_</a> = std::numeric_limits&lt;energy_t&gt;::max();</div>
<div class="line"><a name="l00074"></a><span class="lineno">   74</span>&#160; </div>
<div class="line"><a name="l00075"></a><span class="lineno">   75</span>&#160;<span class="keyword">public</span>:</div>
<div class="line"><a name="l00076"></a><span class="lineno"><a class="line" href="classqbpp_1_1easy__solver_1_1BestSols.html#a26f8926bea0b2a5d383e2a3d0ac197d0">   76</a></span>&#160;  <span class="keyword">explicit</span> <a class="code" href="classqbpp_1_1easy__solver_1_1BestSols.html#a26f8926bea0b2a5d383e2a3d0ac197d0">BestSols</a>(<span class="keywordtype">size_t</span> max_best_sol_count = 0)</div>
<div class="line"><a name="l00077"></a><span class="lineno">   77</span>&#160;      : <a class="code" href="classqbpp_1_1easy__solver_1_1BestSols.html#aff80d3f4bfebc38e4799d6e0fd78f95d">max_best_sol_count_</a>(max_best_sol_count) {}</div>
<div class="line"><a name="l00078"></a><span class="lineno">   78</span>&#160; </div>
<div class="line"><a name="l00079"></a><span class="lineno"><a class="line" href="classqbpp_1_1easy__solver_1_1BestSols.html#a50e4d669aace640e64c43cebac64dc78">   79</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="classqbpp_1_1easy__solver_1_1BestSols.html#a50e4d669aace640e64c43cebac64dc78">insert_if_better</a>(<span class="keyword">const</span> <a class="code" href="classqbpp_1_1Sol.html">qbpp::Sol</a> &amp;sol) {</div>
<div class="line"><a name="l00080"></a><span class="lineno">   80</span>&#160;    <span class="keyword">static</span> std::mutex mutex_;</div>
<div class="line"><a name="l00081"></a><span class="lineno">   81</span>&#160;    <span class="keywordflow">if</span> (<a class="code" href="classqbpp_1_1easy__solver_1_1BestSols.html#aff80d3f4bfebc38e4799d6e0fd78f95d">max_best_sol_count_</a> == 0)</div>
<div class="line"><a name="l00082"></a><span class="lineno">   82</span>&#160;      <span class="keywordflow">return</span>;</div>
<div class="line"><a name="l00083"></a><span class="lineno">   83</span>&#160;    <span class="keywordflow">if</span> (<a class="code" href="classqbpp_1_1easy__solver_1_1BestSols.html#a13dff8120e443f0faeb0fae3218f9a00">worst_energy_</a> &lt; sol.<a class="code" href="classqbpp_1_1Sol.html#a3ad455870df155ffed4701ce070cf335">energy</a>())</div>
<div class="line"><a name="l00084"></a><span class="lineno">   84</span>&#160;      <span class="keywordflow">return</span>;</div>
<div class="line"><a name="l00085"></a><span class="lineno">   85</span>&#160;    std::lock_guard&lt;std::mutex&gt; lock(mutex_);</div>
<div class="line"><a name="l00086"></a><span class="lineno">   86</span>&#160;    <span class="keywordflow">if</span> (<a class="code" href="classqbpp_1_1easy__solver_1_1BestSols.html#a9b53d76ce7a7edbbf9b17229f51ec0b9">sol_set_</a>.count(sol))</div>
<div class="line"><a name="l00087"></a><span class="lineno">   87</span>&#160;      <span class="keywordflow">return</span>;</div>
<div class="line"><a name="l00088"></a><span class="lineno">   88</span>&#160;    <span class="keywordflow">if</span> (<a class="code" href="classqbpp_1_1easy__solver_1_1BestSols.html#aea0db6e737aa21fc745af8a781cdc9a4">sol_vector_</a>.size() &lt; <a class="code" href="classqbpp_1_1easy__solver_1_1BestSols.html#aff80d3f4bfebc38e4799d6e0fd78f95d">max_best_sol_count_</a>) {</div>
<div class="line"><a name="l00089"></a><span class="lineno">   89</span>&#160;      <a class="code" href="classqbpp_1_1easy__solver_1_1BestSols.html#aea0db6e737aa21fc745af8a781cdc9a4">sol_vector_</a>.push_back(sol);</div>
<div class="line"><a name="l00090"></a><span class="lineno">   90</span>&#160;      <a class="code" href="classqbpp_1_1easy__solver_1_1BestSols.html#a9b53d76ce7a7edbbf9b17229f51ec0b9">sol_set_</a>.insert(sol);</div>
<div class="line"><a name="l00091"></a><span class="lineno">   91</span>&#160;    } <span class="keywordflow">else</span> {</div>
<div class="line"><a name="l00092"></a><span class="lineno">   92</span>&#160;      <span class="keywordflow">if</span> (sol.<a class="code" href="classqbpp_1_1Sol.html#a3ad455870df155ffed4701ce070cf335">energy</a>() &gt;= <a class="code" href="classqbpp_1_1easy__solver_1_1BestSols.html#aea0db6e737aa21fc745af8a781cdc9a4">sol_vector_</a>.back().energy())</div>
<div class="line"><a name="l00093"></a><span class="lineno">   93</span>&#160;        <span class="keywordflow">return</span>;</div>
<div class="line"><a name="l00094"></a><span class="lineno">   94</span>&#160;      <a class="code" href="classqbpp_1_1easy__solver_1_1BestSols.html#a9b53d76ce7a7edbbf9b17229f51ec0b9">sol_set_</a>.erase(<a class="code" href="classqbpp_1_1easy__solver_1_1BestSols.html#aea0db6e737aa21fc745af8a781cdc9a4">sol_vector_</a>.back());</div>
<div class="line"><a name="l00095"></a><span class="lineno">   95</span>&#160;      <a class="code" href="classqbpp_1_1easy__solver_1_1BestSols.html#aea0db6e737aa21fc745af8a781cdc9a4">sol_vector_</a>.back() = sol;</div>
<div class="line"><a name="l00096"></a><span class="lineno">   96</span>&#160;      <a class="code" href="classqbpp_1_1easy__solver_1_1BestSols.html#a9b53d76ce7a7edbbf9b17229f51ec0b9">sol_set_</a>.insert(sol);</div>
<div class="line"><a name="l00097"></a><span class="lineno">   97</span>&#160;    }</div>
<div class="line"><a name="l00098"></a><span class="lineno">   98</span>&#160; </div>
<div class="line"><a name="l00099"></a><span class="lineno">   99</span>&#160;    <span class="keywordflow">for</span> (<span class="keywordtype">size_t</span> i = <a class="code" href="classqbpp_1_1easy__solver_1_1BestSols.html#aea0db6e737aa21fc745af8a781cdc9a4">sol_vector_</a>.size() - 1; i &gt; 0; --i) {</div>
<div class="line"><a name="l00100"></a><span class="lineno">  100</span>&#160;      <span class="keywordflow">if</span> (<a class="code" href="classqbpp_1_1easy__solver_1_1BestSols.html#aea0db6e737aa21fc745af8a781cdc9a4">sol_vector_</a>[i].energy() &lt; <a class="code" href="classqbpp_1_1easy__solver_1_1BestSols.html#aea0db6e737aa21fc745af8a781cdc9a4">sol_vector_</a>[i - 1].energy()) {</div>
<div class="line"><a name="l00101"></a><span class="lineno">  101</span>&#160;        std::swap(<a class="code" href="classqbpp_1_1easy__solver_1_1BestSols.html#aea0db6e737aa21fc745af8a781cdc9a4">sol_vector_</a>[i], <a class="code" href="classqbpp_1_1easy__solver_1_1BestSols.html#aea0db6e737aa21fc745af8a781cdc9a4">sol_vector_</a>[i - 1]);</div>
<div class="line"><a name="l00102"></a><span class="lineno">  102</span>&#160;      } <span class="keywordflow">else</span> {</div>
<div class="line"><a name="l00103"></a><span class="lineno">  103</span>&#160;        <span class="keywordflow">break</span>;</div>
<div class="line"><a name="l00104"></a><span class="lineno">  104</span>&#160;      }</div>
<div class="line"><a name="l00105"></a><span class="lineno">  105</span>&#160;    }</div>
<div class="line"><a name="l00106"></a><span class="lineno">  106</span>&#160;    <a class="code" href="classqbpp_1_1easy__solver_1_1BestSols.html#a13dff8120e443f0faeb0fae3218f9a00">worst_energy_</a> = <a class="code" href="classqbpp_1_1easy__solver_1_1BestSols.html#aea0db6e737aa21fc745af8a781cdc9a4">sol_vector_</a>.back().energy();</div>
<div class="line"><a name="l00107"></a><span class="lineno">  107</span>&#160;  }</div>
<div class="line"><a name="l00108"></a><span class="lineno">  108</span>&#160; </div>
<div class="line"><a name="l00109"></a><span class="lineno"><a class="line" href="classqbpp_1_1easy__solver_1_1BestSols.html#a2acd14bf69fb55561c2c859d8c2ed771">  109</a></span>&#160;  <span class="keywordtype">size_t</span> <a class="code" href="classqbpp_1_1easy__solver_1_1BestSols.html#a2acd14bf69fb55561c2c859d8c2ed771">size</a>()<span class="keyword"> const </span>{ <span class="keywordflow">return</span> <a class="code" href="classqbpp_1_1easy__solver_1_1BestSols.html#aea0db6e737aa21fc745af8a781cdc9a4">sol_vector_</a>.size(); }</div>
<div class="line"><a name="l00110"></a><span class="lineno"><a class="line" href="classqbpp_1_1easy__solver_1_1BestSols.html#a281472c9f50678102a7820e4368a3b58">  110</a></span>&#160;  <span class="keyword">const</span> std::vector&lt;qbpp::Sol&gt; &amp;<a class="code" href="classqbpp_1_1easy__solver_1_1BestSols.html#a281472c9f50678102a7820e4368a3b58">get</a>()<span class="keyword"> const </span>{ <span class="keywordflow">return</span> <a class="code" href="classqbpp_1_1easy__solver_1_1BestSols.html#aea0db6e737aa21fc745af8a781cdc9a4">sol_vector_</a>; }</div>
<div class="line"><a name="l00111"></a><span class="lineno"><a class="line" href="classqbpp_1_1easy__solver_1_1BestSols.html#ad7b7e97daf1860cafd68fe9a743f25cf">  111</a></span>&#160;  <span class="keyword">const</span> <a class="code" href="classqbpp_1_1Sol.html">qbpp::Sol</a> &amp;<a class="code" href="classqbpp_1_1easy__solver_1_1BestSols.html#ad7b7e97daf1860cafd68fe9a743f25cf">get</a>(<span class="keywordtype">size_t</span> i)<span class="keyword"> const </span>{ <span class="keywordflow">return</span> <a class="code" href="classqbpp_1_1easy__solver_1_1BestSols.html#aea0db6e737aa21fc745af8a781cdc9a4">sol_vector_</a>[i]; }</div>
<div class="line"><a name="l00112"></a><span class="lineno">  112</span>&#160;};</div>
<div class="line"><a name="l00113"></a><span class="lineno">  113</span>&#160; </div>
<div class="line"><a name="l00114"></a><span class="lineno"><a class="line" href="classqbpp_1_1easy__solver_1_1SolDelta.html">  114</a></span>&#160;<span class="keyword">class </span><a class="code" href="classqbpp_1_1easy__solver_1_1SolDelta.html">SolDelta</a> : <span class="keyword">public</span> <a class="code" href="classqbpp_1_1Sol.html">Sol</a> {</div>
<div class="line"><a name="l00115"></a><span class="lineno">  115</span>&#160;<span class="keyword">protected</span>:</div>
<div class="line"><a name="l00116"></a><span class="lineno"><a class="line" href="classqbpp_1_1easy__solver_1_1SolDelta.html#a688778b8eb570de31703bf26d38cf01d">  116</a></span>&#160;  <span class="keyword">const</span> <a class="code" href="classqbpp_1_1easy__solver_1_1EasySolver.html">EasySolver</a> &amp;<a class="code" href="classqbpp_1_1easy__solver_1_1SolDelta.html#a688778b8eb570de31703bf26d38cf01d">easy_solver_</a>;</div>
<div class="line"><a name="l00117"></a><span class="lineno">  117</span>&#160; </div>
<div class="line"><a name="l00118"></a><span class="lineno"><a class="line" href="classqbpp_1_1easy__solver_1_1SolDelta.html#aaa3700bdd76001664905d47719a3e9da">  118</a></span>&#160;  std::vector&lt;energy_t&gt; <a class="code" href="classqbpp_1_1easy__solver_1_1SolDelta.html#aaa3700bdd76001664905d47719a3e9da">delta_</a>;</div>
<div class="line"><a name="l00119"></a><span class="lineno">  119</span>&#160; </div>
<div class="line"><a name="l00120"></a><span class="lineno"><a class="line" href="classqbpp_1_1easy__solver_1_1SolDelta.html#a9a87ab5a497acf8597110c7b54dd4278">  120</a></span>&#160;  <a class="code" href="classqbpp_1_1misc_1_1MinHeap.html">misc::MinHeap&lt;energy_t&gt;</a> <a class="code" href="classqbpp_1_1easy__solver_1_1SolDelta.html#a9a87ab5a497acf8597110c7b54dd4278">neg_set_</a>;</div>
<div class="line"><a name="l00121"></a><span class="lineno">  121</span>&#160; </div>
<div class="line"><a name="l00122"></a><span class="lineno"><a class="line" href="classqbpp_1_1easy__solver_1_1SolDelta.html#a9f9ac387600b8004d161c6c4a17e6446">  122</a></span>&#160;  <span class="keyword">const</span> std::shared_ptr&lt;qbpp::SolHolder&gt; <a class="code" href="classqbpp_1_1easy__solver_1_1SolDelta.html#a9f9ac387600b8004d161c6c4a17e6446">sol_holder_ptr_</a>;</div>
<div class="line"><a name="l00123"></a><span class="lineno">  123</span>&#160; </div>
<div class="line"><a name="l00124"></a><span class="lineno"><a class="line" href="classqbpp_1_1easy__solver_1_1SolDelta.html#a5532efad1e99357b8930a7d0634647cd">  124</a></span>&#160;  <span class="keyword">const</span> std::shared_ptr&lt;BestSols&gt; <a class="code" href="classqbpp_1_1easy__solver_1_1SolDelta.html#a5532efad1e99357b8930a7d0634647cd">best_sols_ptr_</a>;</div>
<div class="line"><a name="l00125"></a><span class="lineno">  125</span>&#160; </div>
<div class="line"><a name="l00126"></a><span class="lineno"><a class="line" href="classqbpp_1_1easy__solver_1_1SolDelta.html#a80634501337c3b39960cf323dd075705">  126</a></span>&#160;  uint64_t <a class="code" href="classqbpp_1_1easy__solver_1_1SolDelta.html#a80634501337c3b39960cf323dd075705">flip_count_</a> = 0;</div>
<div class="line"><a name="l00127"></a><span class="lineno">  127</span>&#160; </div>
<div class="line"><a name="l00128"></a><span class="lineno">  128</span>&#160;<span class="keyword">public</span>:</div>
<div class="line"><a name="l00129"></a><span class="lineno">  129</span>&#160;  <span class="keyword">explicit</span> <a class="code" href="classqbpp_1_1easy__solver_1_1SolDelta.html#a2a6c5b08bc697bd6f633b68fcdd550e1">SolDelta</a>(<span class="keyword">const</span> <a class="code" href="classqbpp_1_1easy__solver_1_1EasySolver.html">EasySolver</a> &amp;easy_solver);</div>
<div class="line"><a name="l00130"></a><span class="lineno">  130</span>&#160; </div>
<div class="line"><a name="l00131"></a><span class="lineno">  131</span>&#160;  <span class="keyword">virtual</span> <a class="code" href="classqbpp_1_1easy__solver_1_1SolDelta.html#aa6f858dece742eeface9a4353b0677e2">~SolDelta</a>() = <span class="keywordflow">default</span>;</div>
<div class="line"><a name="l00132"></a><span class="lineno">  132</span>&#160; </div>
<div class="line"><a name="l00133"></a><span class="lineno"><a class="line" href="classqbpp_1_1easy__solver_1_1SolDelta.html#a4c83ce5d5273ec31abf361612cc7d0bb">  133</a></span>&#160;  <a class="code" href="namespaceqbpp.html#a9c324f641779d1a7a3c7723f28d8cff4">energy_t</a> <a class="code" href="classqbpp_1_1easy__solver_1_1SolDelta.html#a4c83ce5d5273ec31abf361612cc7d0bb">get_delta</a>(<a class="code" href="namespaceqbpp.html#a5cf436100ede362797d0c0501ea50a5a">vindex_t</a> i)<span class="keyword"> const </span>{ <span class="keywordflow">return</span> <a class="code" href="classqbpp_1_1easy__solver_1_1SolDelta.html#aaa3700bdd76001664905d47719a3e9da">delta_</a>[i]; }</div>
<div class="line"><a name="l00134"></a><span class="lineno">  134</span>&#160; </div>
<div class="line"><a name="l00135"></a><span class="lineno">  135</span>&#160;  <a class="code" href="namespaceqbpp.html#a5cf436100ede362797d0c0501ea50a5a">vindex_t</a> <a class="code" href="classqbpp_1_1easy__solver_1_1SolDelta.html#a98373d99b30ddd261919266c2ac59f57">var_count</a>() <span class="keyword">const</span>;</div>
<div class="line"><a name="l00136"></a><span class="lineno">  136</span>&#160; </div>
<div class="line"><a name="l00137"></a><span class="lineno">  137</span>&#160;  <span class="keywordtype">void</span> <a class="code" href="classqbpp_1_1easy__solver_1_1SolDelta.html#a1a107d8974386a07d201e88fb2c9aed9">flip</a>(<a class="code" href="namespaceqbpp.html#a5cf436100ede362797d0c0501ea50a5a">vindex_t</a> <a class="code" href="classqbpp_1_1Sol.html#a24f5a53fa68071dd9bcda910533bbad7">index</a>) <span class="keyword">override</span>;</div>
<div class="line"><a name="l00138"></a><span class="lineno">  138</span>&#160; </div>
<div class="line"><a name="l00139"></a><span class="lineno">  139</span>&#160;  std::optional&lt;double&gt; <a class="code" href="classqbpp_1_1easy__solver_1_1SolDelta.html#ac0f902682f6255272a79333a947e1a24">set_if_better</a>(<span class="keyword">const</span> std::string &amp;solver_name);</div>
<div class="line"><a name="l00140"></a><span class="lineno">  140</span>&#160; </div>
<div class="line"><a name="l00141"></a><span class="lineno"><a class="line" href="classqbpp_1_1easy__solver_1_1SolDelta.html#a9b594e0f5962872772bad177ec3a41be">  141</a></span>&#160;  <a class="code" href="namespaceqbpp.html#a5cf436100ede362797d0c0501ea50a5a">vindex_t</a> <a class="code" href="classqbpp_1_1easy__solver_1_1SolDelta.html#a9b594e0f5962872772bad177ec3a41be">neg_count</a>()<span class="keyword"> const </span>{ <span class="keywordflow">return</span> <a class="code" href="classqbpp_1_1easy__solver_1_1SolDelta.html#a9a87ab5a497acf8597110c7b54dd4278">neg_set_</a>.<a class="code" href="classqbpp_1_1misc_1_1MinHeap.html#ae61d02fae1382293fb465d2c52477dd0">heap_size</a>(); }</div>
<div class="line"><a name="l00142"></a><span class="lineno">  142</span>&#160; </div>
<div class="line"><a name="l00143"></a><span class="lineno"><a class="line" href="classqbpp_1_1easy__solver_1_1SolDelta.html#a03de2250fb7d272df91c43dcf9a937c2">  143</a></span>&#160;  <a class="code" href="namespaceqbpp.html#a5cf436100ede362797d0c0501ea50a5a">vindex_t</a> <a class="code" href="classqbpp_1_1easy__solver_1_1SolDelta.html#a03de2250fb7d272df91c43dcf9a937c2">neg_random</a>() { <span class="keywordflow">return</span> <a class="code" href="classqbpp_1_1easy__solver_1_1SolDelta.html#a9a87ab5a497acf8597110c7b54dd4278">neg_set_</a>.<a class="code" href="classqbpp_1_1misc_1_1MinHeap.html#acba907f46558aefc996cc8aafa381249">select_at_random</a>(); }</div>
<div class="line"><a name="l00144"></a><span class="lineno">  144</span>&#160; </div>
<div class="line"><a name="l00145"></a><span class="lineno"><a class="line" href="classqbpp_1_1easy__solver_1_1SolDelta.html#a5675af40ee9d8ec2f39a61ff5d1ac322">  145</a></span>&#160;  <a class="code" href="namespaceqbpp.html#a5cf436100ede362797d0c0501ea50a5a">vindex_t</a> <a class="code" href="classqbpp_1_1easy__solver_1_1SolDelta.html#a5675af40ee9d8ec2f39a61ff5d1ac322">neg_min</a>()<span class="keyword"> const </span>{ <span class="keywordflow">return</span> <a class="code" href="classqbpp_1_1easy__solver_1_1SolDelta.html#a9a87ab5a497acf8597110c7b54dd4278">neg_set_</a>.<a class="code" href="classqbpp_1_1misc_1_1MinHeap.html#ad5e42b4bd3f622c159a7863a1b7c011f">get_first</a>(); }</div>
<div class="line"><a name="l00146"></a><span class="lineno">  146</span>&#160; </div>
<div class="line"><a name="l00147"></a><span class="lineno">  147</span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">void</span></div>
<div class="line"><a name="l00148"></a><span class="lineno"><a class="line" href="classqbpp_1_1easy__solver_1_1SolDelta.html#a948cb17c3b50104d7dbd333a88909757">  148</a></span>&#160;  <a class="code" href="classqbpp_1_1easy__solver_1_1SolDelta.html#a948cb17c3b50104d7dbd333a88909757">before_delta_updated</a>([[maybe_unused]] <a class="code" href="namespaceqbpp.html#a5cf436100ede362797d0c0501ea50a5a">vindex_t</a> update_delta_index) {}</div>
<div class="line"><a name="l00149"></a><span class="lineno">  149</span>&#160; </div>
<div class="line"><a name="l00150"></a><span class="lineno">  150</span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">void</span></div>
<div class="line"><a name="l00151"></a><span class="lineno"><a class="line" href="classqbpp_1_1easy__solver_1_1SolDelta.html#ac82df38362383a0c302a979c0b394844">  151</a></span>&#160;  <a class="code" href="classqbpp_1_1easy__solver_1_1SolDelta.html#ac82df38362383a0c302a979c0b394844">after_delta_updated</a>([[maybe_unused]] <a class="code" href="namespaceqbpp.html#a5cf436100ede362797d0c0501ea50a5a">vindex_t</a> update_delta_index) {}</div>
<div class="line"><a name="l00152"></a><span class="lineno">  152</span>&#160; </div>
<div class="line"><a name="l00153"></a><span class="lineno">  153</span>&#160;  <span class="keywordtype">void</span> <a class="code" href="classqbpp_1_1easy__solver_1_1SolDelta.html#acf11d3f5b86a20077d4c20dc9c18e044">greedy</a>();</div>
<div class="line"><a name="l00154"></a><span class="lineno">  154</span>&#160; </div>
<div class="line"><a name="l00155"></a><span class="lineno">  155</span>&#160;  <span class="keywordtype">void</span> <a class="code" href="classqbpp_1_1easy__solver_1_1SolDelta.html#ad0505230c61ae317b5414f06327daeb4">random_flip</a>(<span class="keywordtype">size_t</span> iteration);</div>
<div class="line"><a name="l00156"></a><span class="lineno">  156</span>&#160; </div>
<div class="line"><a name="l00157"></a><span class="lineno">  157</span>&#160;  <span class="keywordtype">void</span> <a class="code" href="classqbpp_1_1easy__solver_1_1SolDelta.html#ae5e7dbc16325f4f3b26895fe8686afc5">move_to</a>(<a class="code" href="classqbpp_1_1Sol.html">qbpp::Sol</a> destination);</div>
<div class="line"><a name="l00158"></a><span class="lineno">  158</span>&#160; </div>
<div class="line"><a name="l00159"></a><span class="lineno"><a class="line" href="classqbpp_1_1easy__solver_1_1SolDelta.html#a4288d75e854df11c4023461d03c319e3">  159</a></span>&#160;  uint64_t <a class="code" href="classqbpp_1_1easy__solver_1_1SolDelta.html#a4288d75e854df11c4023461d03c319e3">get_flip_count</a>()<span class="keyword"> const </span>{ <span class="keywordflow">return</span> <a class="code" href="classqbpp_1_1easy__solver_1_1SolDelta.html#a80634501337c3b39960cf323dd075705">flip_count_</a>; }</div>
<div class="line"><a name="l00160"></a><span class="lineno">  160</span>&#160; </div>
<div class="line"><a name="l00161"></a><span class="lineno"><a class="line" href="classqbpp_1_1easy__solver_1_1SolDelta.html#a28965e9da467b6c28f46caad2b0d6a37">  161</a></span>&#160;  <span class="keyword">const</span> std::shared_ptr&lt;BestSols&gt; &amp;<a class="code" href="classqbpp_1_1easy__solver_1_1SolDelta.html#a28965e9da467b6c28f46caad2b0d6a37">best_sols_ptr</a>()<span class="keyword"> const </span>{</div>
<div class="line"><a name="l00162"></a><span class="lineno">  162</span>&#160;    <span class="keywordflow">return</span> <a class="code" href="classqbpp_1_1easy__solver_1_1SolDelta.html#a5532efad1e99357b8930a7d0634647cd">best_sols_ptr_</a>;</div>
<div class="line"><a name="l00163"></a><span class="lineno">  163</span>&#160;  }</div>
<div class="line"><a name="l00164"></a><span class="lineno">  164</span>&#160;};</div>
<div class="line"><a name="l00165"></a><span class="lineno">  165</span>&#160; </div>
<div class="line"><a name="l00166"></a><span class="lineno"><a class="line" href="classqbpp_1_1easy__solver_1_1TabuSolDelta.html">  166</a></span>&#160;<span class="keyword">class </span><a class="code" href="classqbpp_1_1easy__solver_1_1TabuSolDelta.html">TabuSolDelta</a> : <span class="keyword">public</span> <a class="code" href="classqbpp_1_1easy__solver_1_1SolDelta.html">SolDelta</a> {</div>
<div class="line"><a name="l00167"></a><span class="lineno">  167</span>&#160;<span class="keyword">protected</span>:</div>
<div class="line"><a name="l00168"></a><span class="lineno"><a class="line" href="classqbpp_1_1easy__solver_1_1TabuSolDelta.html#a1381fbf8ed64faf60c3ad0556a37b400">  168</a></span>&#160;  <span class="keyword">const</span> <a class="code" href="namespaceqbpp.html#a5cf436100ede362797d0c0501ea50a5a">vindex_t</a> <a class="code" href="classqbpp_1_1easy__solver_1_1TabuSolDelta.html#a1381fbf8ed64faf60c3ad0556a37b400">tabu_size_</a>;</div>
<div class="line"><a name="l00169"></a><span class="lineno">  169</span>&#160; </div>
<div class="line"><a name="l00170"></a><span class="lineno"><a class="line" href="classqbpp_1_1easy__solver_1_1TabuSolDelta.html#ac17653919bfa66975c06ed71e41d5b68">  170</a></span>&#160;  <a class="code" href="classqbpp_1_1misc_1_1Tabu.html">misc::Tabu</a> <a class="code" href="classqbpp_1_1easy__solver_1_1TabuSolDelta.html#ac17653919bfa66975c06ed71e41d5b68">tabu_</a>;</div>
<div class="line"><a name="l00171"></a><span class="lineno">  171</span>&#160; </div>
<div class="line"><a name="l00172"></a><span class="lineno">  172</span>&#160;<span class="keyword">public</span>:</div>
<div class="line"><a name="l00173"></a><span class="lineno"><a class="line" href="classqbpp_1_1easy__solver_1_1TabuSolDelta.html#af665d76e9e237ecc6e0280f3b5db2679">  173</a></span>&#160;  <a class="code" href="classqbpp_1_1easy__solver_1_1TabuSolDelta.html#af665d76e9e237ecc6e0280f3b5db2679">TabuSolDelta</a>(<span class="keyword">const</span> <a class="code" href="classqbpp_1_1easy__solver_1_1EasySolver.html">EasySolver</a> &amp;easy_solver, <a class="code" href="namespaceqbpp.html#a5cf436100ede362797d0c0501ea50a5a">vindex_t</a> tabu_size)</div>
<div class="line"><a name="l00174"></a><span class="lineno">  174</span>&#160;      : <a class="code" href="classqbpp_1_1easy__solver_1_1SolDelta.html">SolDelta</a>(easy_solver), <a class="code" href="classqbpp_1_1easy__solver_1_1TabuSolDelta.html#a1381fbf8ed64faf60c3ad0556a37b400">tabu_size_</a>(tabu_size),</div>
<div class="line"><a name="l00175"></a><span class="lineno">  175</span>&#160;        <a class="code" href="classqbpp_1_1easy__solver_1_1TabuSolDelta.html#ac17653919bfa66975c06ed71e41d5b68">tabu_</a>(<a class="code" href="classqbpp_1_1easy__solver_1_1SolDelta.html#a98373d99b30ddd261919266c2ac59f57">var_count</a>(), tabu_size) {}</div>
<div class="line"><a name="l00176"></a><span class="lineno">  176</span>&#160; </div>
<div class="line"><a name="l00177"></a><span class="lineno">  177</span>&#160;  <span class="keyword">virtual</span> <a class="code" href="classqbpp_1_1easy__solver_1_1TabuSolDelta.html#a26d698066983b574a1a8d64fe879f5ee">~TabuSolDelta</a>() = <span class="keywordflow">default</span>;</div>
<div class="line"><a name="l00178"></a><span class="lineno">  178</span>&#160; </div>
<div class="line"><a name="l00179"></a><span class="lineno"><a class="line" href="classqbpp_1_1easy__solver_1_1TabuSolDelta.html#abddd3367c047935e82e517c421cb5a9a">  179</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="classqbpp_1_1easy__solver_1_1TabuSolDelta.html#abddd3367c047935e82e517c421cb5a9a">flip</a>(<a class="code" href="namespaceqbpp.html#a5cf436100ede362797d0c0501ea50a5a">vindex_t</a> <a class="code" href="classqbpp_1_1Sol.html#a24f5a53fa68071dd9bcda910533bbad7">index</a>)<span class="keyword"> override </span>{</div>
<div class="line"><a name="l00180"></a><span class="lineno">  180</span>&#160;    <a class="code" href="classqbpp_1_1easy__solver_1_1SolDelta.html#a1a107d8974386a07d201e88fb2c9aed9">SolDelta::flip</a>(<a class="code" href="classqbpp_1_1Sol.html#a24f5a53fa68071dd9bcda910533bbad7">index</a>);</div>
<div class="line"><a name="l00181"></a><span class="lineno">  181</span>&#160;    <a class="code" href="classqbpp_1_1easy__solver_1_1TabuSolDelta.html#ac17653919bfa66975c06ed71e41d5b68">tabu_</a>.<a class="code" href="classqbpp_1_1misc_1_1Tabu.html#a549866fee09f51bb17a90e3a1831bf77">insert</a>(<a class="code" href="classqbpp_1_1Sol.html#a24f5a53fa68071dd9bcda910533bbad7">index</a>);</div>
<div class="line"><a name="l00182"></a><span class="lineno">  182</span>&#160;  }</div>
<div class="line"><a name="l00183"></a><span class="lineno">  183</span>&#160; </div>
<div class="line"><a name="l00184"></a><span class="lineno"><a class="line" href="classqbpp_1_1easy__solver_1_1TabuSolDelta.html#a514b9fd7511de63ea113064e40c50862">  184</a></span>&#160;  <span class="keywordtype">bool</span> <a class="code" href="classqbpp_1_1easy__solver_1_1TabuSolDelta.html#a514b9fd7511de63ea113064e40c50862">tabu_has</a>(<a class="code" href="namespaceqbpp.html#a5cf436100ede362797d0c0501ea50a5a">vindex_t</a> <a class="code" href="classqbpp_1_1Sol.html#a24f5a53fa68071dd9bcda910533bbad7">index</a>)<span class="keyword"> const </span>{ <span class="keywordflow">return</span> <a class="code" href="classqbpp_1_1easy__solver_1_1TabuSolDelta.html#ac17653919bfa66975c06ed71e41d5b68">tabu_</a>.<a class="code" href="classqbpp_1_1misc_1_1Tabu.html#a44a9ecb55678821cc1021e33b4faba72">has</a>(<a class="code" href="classqbpp_1_1Sol.html#a24f5a53fa68071dd9bcda910533bbad7">index</a>); }</div>
<div class="line"><a name="l00185"></a><span class="lineno">  185</span>&#160; </div>
<div class="line"><a name="l00186"></a><span class="lineno"><a class="line" href="classqbpp_1_1easy__solver_1_1TabuSolDelta.html#a57c97c62983526b68ab764846a7f3060">  186</a></span>&#160;  <a class="code" href="namespaceqbpp.html#a5cf436100ede362797d0c0501ea50a5a">vindex_t</a> <a class="code" href="classqbpp_1_1easy__solver_1_1TabuSolDelta.html#a57c97c62983526b68ab764846a7f3060">non_tabu_random</a>() { <span class="keywordflow">return</span> <a class="code" href="classqbpp_1_1easy__solver_1_1TabuSolDelta.html#ac17653919bfa66975c06ed71e41d5b68">tabu_</a>.<a class="code" href="classqbpp_1_1misc_1_1Tabu.html#ac79f02e24591065f8224dc1aa5355e20">non_tabu_random</a>(); }</div>
<div class="line"><a name="l00187"></a><span class="lineno">  187</span>&#160;};</div>
<div class="line"><a name="l00188"></a><span class="lineno">  188</span>&#160; </div>
<div class="line"><a name="l00189"></a><span class="lineno"><a class="line" href="classqbpp_1_1easy__solver_1_1PosMinSolDelta.html">  189</a></span>&#160;<span class="keyword">class </span><a class="code" href="classqbpp_1_1easy__solver_1_1PosMinSolDelta.html">PosMinSolDelta</a> : <span class="keyword">public</span> <a class="code" href="classqbpp_1_1easy__solver_1_1TabuSolDelta.html">TabuSolDelta</a> {</div>
<div class="line"><a name="l00190"></a><span class="lineno"><a class="line" href="classqbpp_1_1easy__solver_1_1PosMinSolDelta.html#a1b2b9ca3a340f92871de75954d335d40">  190</a></span>&#160;  <a class="code" href="classqbpp_1_1misc_1_1MinHeap.html">misc::MinHeap&lt;energy_t&gt;</a> <a class="code" href="classqbpp_1_1easy__solver_1_1PosMinSolDelta.html#a1b2b9ca3a340f92871de75954d335d40">pos_set_</a>;</div>
<div class="line"><a name="l00191"></a><span class="lineno">  191</span>&#160; </div>
<div class="line"><a name="l00192"></a><span class="lineno">  192</span>&#160;<span class="keyword">public</span>:</div>
<div class="line"><a name="l00193"></a><span class="lineno">  193</span>&#160;  <span class="keyword">explicit</span> <a class="code" href="classqbpp_1_1easy__solver_1_1PosMinSolDelta.html#af512f524573c98e13ee2e236cc147c17">PosMinSolDelta</a>(<span class="keyword">const</span> <a class="code" href="classqbpp_1_1easy__solver_1_1EasySolver.html">EasySolver</a> &amp;easy_solver);</div>
<div class="line"><a name="l00194"></a><span class="lineno">  194</span>&#160; </div>
<div class="line"><a name="l00195"></a><span class="lineno">  195</span>&#160;  <span class="keyword">virtual</span> <a class="code" href="classqbpp_1_1easy__solver_1_1PosMinSolDelta.html#af803882846692d994043dfc1fa9c4206">~PosMinSolDelta</a>() = <span class="keywordflow">default</span>;</div>
<div class="line"><a name="l00196"></a><span class="lineno">  196</span>&#160; </div>
<div class="line"><a name="l00197"></a><span class="lineno"><a class="line" href="classqbpp_1_1easy__solver_1_1PosMinSolDelta.html#ad7fb2fffb0e77d69983865304fa11008">  197</a></span>&#160;  <a class="code" href="namespaceqbpp.html#a5cf436100ede362797d0c0501ea50a5a">vindex_t</a> <a class="code" href="classqbpp_1_1easy__solver_1_1PosMinSolDelta.html#ad7fb2fffb0e77d69983865304fa11008">pos_count</a>()<span class="keyword"> const </span>{ <span class="keywordflow">return</span> <a class="code" href="classqbpp_1_1easy__solver_1_1PosMinSolDelta.html#a1b2b9ca3a340f92871de75954d335d40">pos_set_</a>.<a class="code" href="classqbpp_1_1misc_1_1MinHeap.html#ae61d02fae1382293fb465d2c52477dd0">heap_size</a>(); }</div>
<div class="line"><a name="l00198"></a><span class="lineno">  198</span>&#160; </div>
<div class="line"><a name="l00199"></a><span class="lineno"><a class="line" href="classqbpp_1_1easy__solver_1_1PosMinSolDelta.html#a66bae180e06d9fb7ae8461ba81111223">  199</a></span>&#160;  <a class="code" href="namespaceqbpp.html#a5cf436100ede362797d0c0501ea50a5a">vindex_t</a> <a class="code" href="classqbpp_1_1easy__solver_1_1PosMinSolDelta.html#a66bae180e06d9fb7ae8461ba81111223">pos_min</a>()<span class="keyword"> const </span>{ <span class="keywordflow">return</span> <a class="code" href="classqbpp_1_1easy__solver_1_1PosMinSolDelta.html#a1b2b9ca3a340f92871de75954d335d40">pos_set_</a>.<a class="code" href="classqbpp_1_1misc_1_1MinHeap.html#ad5e42b4bd3f622c159a7863a1b7c011f">get_first</a>(); }</div>
<div class="line"><a name="l00200"></a><span class="lineno">  200</span>&#160; </div>
<div class="line"><a name="l00201"></a><span class="lineno"><a class="line" href="classqbpp_1_1easy__solver_1_1PosMinSolDelta.html#adf4ed5f4493c8ee2daae7b3f5b0a5443">  201</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="classqbpp_1_1easy__solver_1_1PosMinSolDelta.html#adf4ed5f4493c8ee2daae7b3f5b0a5443">before_delta_updated</a>(<a class="code" href="namespaceqbpp.html#a5cf436100ede362797d0c0501ea50a5a">vindex_t</a> k)<span class="keyword"> override </span>{</div>
<div class="line"><a name="l00202"></a><span class="lineno">  202</span>&#160;    <span class="keywordflow">if</span> (<a class="code" href="classqbpp_1_1easy__solver_1_1SolDelta.html#aaa3700bdd76001664905d47719a3e9da">delta_</a>[k] &gt; 0) {</div>
<div class="line"><a name="l00203"></a><span class="lineno">  203</span>&#160;      <a class="code" href="classqbpp_1_1easy__solver_1_1PosMinSolDelta.html#a1b2b9ca3a340f92871de75954d335d40">pos_set_</a>.<a class="code" href="classqbpp_1_1misc_1_1MinHeap.html#aa209280cf2cfd0dd5ee04131bc96a582">erase</a>(k);</div>
<div class="line"><a name="l00204"></a><span class="lineno">  204</span>&#160;    }</div>
<div class="line"><a name="l00205"></a><span class="lineno">  205</span>&#160;  }</div>
<div class="line"><a name="l00206"></a><span class="lineno">  206</span>&#160; </div>
<div class="line"><a name="l00207"></a><span class="lineno"><a class="line" href="classqbpp_1_1easy__solver_1_1PosMinSolDelta.html#a7d159351964937d75adac8acad67a4d4">  207</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="classqbpp_1_1easy__solver_1_1PosMinSolDelta.html#a7d159351964937d75adac8acad67a4d4">after_delta_updated</a>(<a class="code" href="namespaceqbpp.html#a5cf436100ede362797d0c0501ea50a5a">vindex_t</a> k)<span class="keyword"> override </span>{</div>
<div class="line"><a name="l00208"></a><span class="lineno">  208</span>&#160;    <span class="keywordflow">if</span> (<a class="code" href="classqbpp_1_1easy__solver_1_1SolDelta.html#aaa3700bdd76001664905d47719a3e9da">delta_</a>[k] &gt; 0) {</div>
<div class="line"><a name="l00209"></a><span class="lineno">  209</span>&#160;      <a class="code" href="classqbpp_1_1easy__solver_1_1PosMinSolDelta.html#a1b2b9ca3a340f92871de75954d335d40">pos_set_</a>.<a class="code" href="classqbpp_1_1misc_1_1MinHeap.html#a4ffbfe7ef3c611efd3de67cd523c94bd">insert</a>(k, <a class="code" href="classqbpp_1_1easy__solver_1_1SolDelta.html#aaa3700bdd76001664905d47719a3e9da">delta_</a>[k]);</div>
<div class="line"><a name="l00210"></a><span class="lineno">  210</span>&#160;    }</div>
<div class="line"><a name="l00211"></a><span class="lineno">  211</span>&#160;  }</div>
<div class="line"><a name="l00212"></a><span class="lineno">  212</span>&#160; </div>
<div class="line"><a name="l00213"></a><span class="lineno">  213</span>&#160;  <span class="keywordtype">void</span> <a class="code" href="classqbpp_1_1easy__solver_1_1PosMinSolDelta.html#aff102bc17a0d06a78a285f1ab2c9632e">search</a>(<span class="keywordtype">size_t</span> iteration);</div>
<div class="line"><a name="l00214"></a><span class="lineno">  214</span>&#160; </div>
<div class="line"><a name="l00215"></a><span class="lineno"><a class="line" href="classqbpp_1_1easy__solver_1_1PosMinSolDelta.html#ad8811137073388d35654ad8d6f8db0f2">  215</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="classqbpp_1_1easy__solver_1_1PosMinSolDelta.html#ad8811137073388d35654ad8d6f8db0f2">print</a>()<span class="keyword"> const </span>{</div>
<div class="line"><a name="l00216"></a><span class="lineno">  216</span>&#160;    <span class="keywordflow">for</span> (<a class="code" href="namespaceqbpp.html#a5cf436100ede362797d0c0501ea50a5a">vindex_t</a> i = 0; i &lt; <a class="code" href="classqbpp_1_1easy__solver_1_1SolDelta.html#a98373d99b30ddd261919266c2ac59f57">var_count</a>(); ++i) {</div>
<div class="line"><a name="l00217"></a><span class="lineno">  217</span>&#160;      std::cout &lt;&lt; <span class="stringliteral">&quot;(&quot;</span> &lt;&lt; i &lt;&lt; <span class="stringliteral">&quot;,&quot;</span> &lt;&lt; <a class="code" href="classqbpp_1_1easy__solver_1_1SolDelta.html#aaa3700bdd76001664905d47719a3e9da">delta_</a>[i] &lt;&lt; <span class="stringliteral">&quot;)&quot;</span>;</div>
<div class="line"><a name="l00218"></a><span class="lineno">  218</span>&#160;    }</div>
<div class="line"><a name="l00219"></a><span class="lineno">  219</span>&#160;    std::cout &lt;&lt; std::endl;</div>
<div class="line"><a name="l00220"></a><span class="lineno">  220</span>&#160;    <a class="code" href="classqbpp_1_1easy__solver_1_1SolDelta.html#a9a87ab5a497acf8597110c7b54dd4278">neg_set_</a>.<a class="code" href="classqbpp_1_1misc_1_1MinHeap.html#ae0d409ca2b14bae78db373f1eecbd99c">print</a>(<span class="stringliteral">&quot;NEG:&quot;</span>);</div>
<div class="line"><a name="l00221"></a><span class="lineno">  221</span>&#160;    <a class="code" href="classqbpp_1_1easy__solver_1_1PosMinSolDelta.html#a1b2b9ca3a340f92871de75954d335d40">pos_set_</a>.<a class="code" href="classqbpp_1_1misc_1_1MinHeap.html#ae0d409ca2b14bae78db373f1eecbd99c">print</a>(<span class="stringliteral">&quot;POS:&quot;</span>);</div>
<div class="line"><a name="l00222"></a><span class="lineno">  222</span>&#160;  }</div>
<div class="line"><a name="l00223"></a><span class="lineno">  223</span>&#160;};</div>
<div class="line"><a name="l00224"></a><span class="lineno">  224</span>&#160; </div>
<div class="line"><a name="l00225"></a><span class="lineno"><a class="line" href="classqbpp_1_1easy__solver_1_1EasySolver.html">  225</a></span>&#160;<span class="keyword">class </span><a class="code" href="classqbpp_1_1easy__solver_1_1EasySolver.html">EasySolver</a> {</div>
<div class="line"><a name="l00226"></a><span class="lineno">  226</span>&#160;<span class="keyword">protected</span>:</div>
<div class="line"><a name="l00227"></a><span class="lineno"><a class="line" href="classqbpp_1_1easy__solver_1_1EasySolver.html#a1d55e04ffc11f6c8460f184efc6d2ea3">  227</a></span>&#160;  <span class="keyword">const</span> <a class="code" href="classqbpp_1_1QuadModel.html">qbpp::QuadModel</a> <a class="code" href="classqbpp_1_1easy__solver_1_1EasySolver.html#a1d55e04ffc11f6c8460f184efc6d2ea3">quad_model_</a>;</div>
<div class="line"><a name="l00228"></a><span class="lineno">  228</span>&#160; </div>
<div class="line"><a name="l00229"></a><span class="lineno"><a class="line" href="classqbpp_1_1easy__solver_1_1EasySolver.html#ab99254a861b99ba22b5132ffa67416c2">  229</a></span>&#160;  std::optional&lt;uint32_t&gt; <a class="code" href="classqbpp_1_1easy__solver_1_1EasySolver.html#ab99254a861b99ba22b5132ffa67416c2">time_limit_</a>;</div>
<div class="line"><a name="l00230"></a><span class="lineno">  230</span>&#160; </div>
<div class="line"><a name="l00231"></a><span class="lineno"><a class="line" href="classqbpp_1_1easy__solver_1_1EasySolver.html#aced832de618720dce416415ba5f58c5c">  231</a></span>&#160;  <span class="keywordtype">size_t</span> <a class="code" href="classqbpp_1_1easy__solver_1_1EasySolver.html#aced832de618720dce416415ba5f58c5c">thread_count_</a>{std::thread::hardware_concurrency() &lt; 4</div>
<div class="line"><a name="l00232"></a><span class="lineno">  232</span>&#160;                           ? 4</div>
<div class="line"><a name="l00233"></a><span class="lineno">  233</span>&#160;                           : std::thread::hardware_concurrency()};</div>
<div class="line"><a name="l00234"></a><span class="lineno">  234</span>&#160; </div>
<div class="line"><a name="l00235"></a><span class="lineno"><a class="line" href="classqbpp_1_1easy__solver_1_1EasySolver.html#a5114362571468d84f507f9e3d17e0995">  235</a></span>&#160;  std::optional&lt;energy_t&gt; <a class="code" href="classqbpp_1_1easy__solver_1_1EasySolver.html#a5114362571468d84f507f9e3d17e0995">target_energy_</a>;</div>
<div class="line"><a name="l00236"></a><span class="lineno">  236</span>&#160; </div>
<div class="line"><a name="l00237"></a><span class="lineno"><a class="line" href="classqbpp_1_1easy__solver_1_1EasySolver.html#a41a71df4e0f7029335c0bcf9f407881e">  237</a></span>&#160;  <span class="keyword">const</span> <span class="keywordtype">bool</span> <a class="code" href="classqbpp_1_1easy__solver_1_1EasySolver.html#a41a71df4e0f7029335c0bcf9f407881e">is_internal_sol_holder_</a>;</div>
<div class="line"><a name="l00238"></a><span class="lineno">  238</span>&#160; </div>
<div class="line"><a name="l00239"></a><span class="lineno"><a class="line" href="classqbpp_1_1easy__solver_1_1EasySolver.html#ae6e3c634ea34e47131d8e9a407906f4a">  239</a></span>&#160;  std::shared_ptr&lt;qbpp::SolHolder&gt; <a class="code" href="classqbpp_1_1easy__solver_1_1EasySolver.html#ae6e3c634ea34e47131d8e9a407906f4a">sol_holder_ptr_</a>;</div>
<div class="line"><a name="l00240"></a><span class="lineno">  240</span>&#160; </div>
<div class="line"><a name="l00241"></a><span class="lineno"><a class="line" href="classqbpp_1_1easy__solver_1_1EasySolver.html#a01d3912bcf0c4741103f85106fc72037">  241</a></span>&#160;  std::shared_ptr&lt;BestSols&gt; <a class="code" href="classqbpp_1_1easy__solver_1_1EasySolver.html#a01d3912bcf0c4741103f85106fc72037">best_sols_ptr_</a>;</div>
<div class="line"><a name="l00242"></a><span class="lineno">  242</span>&#160; </div>
<div class="line"><a name="l00243"></a><span class="lineno"><a class="line" href="classqbpp_1_1easy__solver_1_1EasySolver.html#a737bb24b042e6d038ffd2b2a80aaab7f">  243</a></span>&#160;  <span class="keyword">mutable</span> std::mutex <a class="code" href="classqbpp_1_1easy__solver_1_1EasySolver.html#a737bb24b042e6d038ffd2b2a80aaab7f">callback_mutex_</a>;</div>
<div class="line"><a name="l00244"></a><span class="lineno">  244</span>&#160; </div>
<div class="line"><a name="l00245"></a><span class="lineno"><a class="line" href="classqbpp_1_1easy__solver_1_1EasySolver.html#aeb2ca474df5de0ea608d461a486b1d11">  245</a></span>&#160;  <span class="keywordtype">bool</span> <a class="code" href="classqbpp_1_1easy__solver_1_1EasySolver.html#aeb2ca474df5de0ea608d461a486b1d11">enable_default_callback_</a> = <span class="keyword">false</span>;</div>
<div class="line"><a name="l00246"></a><span class="lineno">  246</span>&#160; </div>
<div class="line"><a name="l00247"></a><span class="lineno">  247</span>&#160;  <span class="keywordtype">void</span> <a class="code" href="classqbpp_1_1easy__solver_1_1EasySolver.html#a329a03e4e5c401ec471e0f7f4c42cb51">single_search</a>(<span class="keywordtype">size_t</span> thread_id);</div>
<div class="line"><a name="l00248"></a><span class="lineno">  248</span>&#160; </div>
<div class="line"><a name="l00249"></a><span class="lineno"><a class="line" href="classqbpp_1_1easy__solver_1_1EasySolver.html#a9a4b07b571358cf4627c0c5d2b138222">  249</a></span>&#160;  <span class="keywordtype">double</span> <a class="code" href="classqbpp_1_1easy__solver_1_1EasySolver.html#a9a4b07b571358cf4627c0c5d2b138222">start_time_</a>;</div>
<div class="line"><a name="l00250"></a><span class="lineno">  250</span>&#160; </div>
<div class="line"><a name="l00251"></a><span class="lineno"><a class="line" href="classqbpp_1_1easy__solver_1_1EasySolver.html#ac66675c187e2add239134fd04df4e5ce">  251</a></span>&#160;  std::mutex <a class="code" href="classqbpp_1_1easy__solver_1_1EasySolver.html#ac66675c187e2add239134fd04df4e5ce">flip_count_mutex_</a>;</div>
<div class="line"><a name="l00252"></a><span class="lineno"><a class="line" href="classqbpp_1_1easy__solver_1_1EasySolver.html#a41b03b454ad9acb22e785df8bba2d717">  252</a></span>&#160;  uint64_t <a class="code" href="classqbpp_1_1easy__solver_1_1EasySolver.html#a41b03b454ad9acb22e785df8bba2d717">flip_count_</a> = 0;</div>
<div class="line"><a name="l00253"></a><span class="lineno">  253</span>&#160; </div>
<div class="line"><a name="l00254"></a><span class="lineno"><a class="line" href="classqbpp_1_1easy__solver_1_1EasySolver.html#a801ffcc65ab06fa8f6bd0cf1cface28a">  254</a></span>&#160;  <span class="keyword">mutable</span> std::optional&lt;energy_t&gt; <a class="code" href="classqbpp_1_1easy__solver_1_1EasySolver.html#a801ffcc65ab06fa8f6bd0cf1cface28a">prev_energy_</a> = std::nullopt;</div>
<div class="line"><a name="l00255"></a><span class="lineno">  255</span>&#160; </div>
<div class="line"><a name="l00256"></a><span class="lineno">  256</span>&#160;<span class="keyword">public</span>:</div>
<div class="line"><a name="l00257"></a><span class="lineno">  257</span>&#160; </div>
<div class="line"><a name="l00258"></a><span class="lineno"><a class="line" href="classqbpp_1_1easy__solver_1_1EasySolver.html#a2511d160e47784e4543aa7a8efb4194a">  258</a></span>&#160;  <span class="keyword">explicit</span> <a class="code" href="classqbpp_1_1easy__solver_1_1EasySolver.html#a2511d160e47784e4543aa7a8efb4194a">EasySolver</a>(<span class="keyword">const</span> <a class="code" href="classqbpp_1_1QuadModel.html">qbpp::QuadModel</a> &amp;quad_model,</div>
<div class="line"><a name="l00259"></a><span class="lineno">  259</span>&#160;                      std::shared_ptr&lt;qbpp::SolHolder&gt; <a class="code" href="classqbpp_1_1easy__solver_1_1EasySolver.html#ada3fbb50b6993a825681a4a712fd61b7">sol_holder_ptr</a> = <span class="keyword">nullptr</span>)</div>
<div class="line"><a name="l00260"></a><span class="lineno">  260</span>&#160;      : <a class="code" href="classqbpp_1_1easy__solver_1_1EasySolver.html#a1d55e04ffc11f6c8460f184efc6d2ea3">quad_model_</a>(quad_model),</div>
<div class="line"><a name="l00261"></a><span class="lineno">  261</span>&#160;        <a class="code" href="classqbpp_1_1easy__solver_1_1EasySolver.html#a41a71df4e0f7029335c0bcf9f407881e">is_internal_sol_holder_</a>(<a class="code" href="classqbpp_1_1easy__solver_1_1EasySolver.html#ada3fbb50b6993a825681a4a712fd61b7">sol_holder_ptr</a> == nullptr),</div>
<div class="line"><a name="l00262"></a><span class="lineno">  262</span>&#160;        <a class="code" href="classqbpp_1_1easy__solver_1_1EasySolver.html#ae6e3c634ea34e47131d8e9a407906f4a">sol_holder_ptr_</a>(<a class="code" href="classqbpp_1_1easy__solver_1_1EasySolver.html#ada3fbb50b6993a825681a4a712fd61b7">sol_holder_ptr</a>) {}</div>
<div class="line"><a name="l00263"></a><span class="lineno">  263</span>&#160; </div>
<div class="line"><a name="l00264"></a><span class="lineno">  264</span>&#160; </div>
<div class="line"><a name="l00265"></a><span class="lineno">  265</span>&#160;  <span class="keyword">virtual</span> <a class="code" href="classqbpp_1_1easy__solver_1_1EasySolver.html#a6e57baef4356ac67e7b25e7a31d2a8f6">~EasySolver</a>() = <span class="keywordflow">default</span>;</div>
<div class="line"><a name="l00266"></a><span class="lineno">  266</span>&#160; </div>
<div class="line"><a name="l00267"></a><span class="lineno"><a class="line" href="classqbpp_1_1easy__solver_1_1EasySolver.html#a90e3ce85062b4e25bab775948e1e56f1">  267</a></span>&#160;  <span class="keyword">const</span> <a class="code" href="classqbpp_1_1QuadModel.html">qbpp::QuadModel</a> &amp;<a class="code" href="classqbpp_1_1easy__solver_1_1EasySolver.html#a90e3ce85062b4e25bab775948e1e56f1">get_quad_model</a>()<span class="keyword"> const </span>{ <span class="keywordflow">return</span> <a class="code" href="classqbpp_1_1easy__solver_1_1EasySolver.html#a1d55e04ffc11f6c8460f184efc6d2ea3">quad_model_</a>; }</div>
<div class="line"><a name="l00268"></a><span class="lineno">  268</span>&#160; </div>
<div class="line"><a name="l00269"></a><span class="lineno"><a class="line" href="classqbpp_1_1easy__solver_1_1EasySolver.html#ad8b81b7d0fe63ed7b0f00d5ac9fad2e1">  269</a></span>&#160;  <span class="keyword">const</span> std::optional&lt;uint32_t&gt; <a class="code" href="classqbpp_1_1easy__solver_1_1EasySolver.html#ad8b81b7d0fe63ed7b0f00d5ac9fad2e1">get_time_limit</a>()<span class="keyword"> const </span>{ <span class="keywordflow">return</span> <a class="code" href="classqbpp_1_1easy__solver_1_1EasySolver.html#ab99254a861b99ba22b5132ffa67416c2">time_limit_</a>; }</div>
<div class="line"><a name="l00270"></a><span class="lineno">  270</span>&#160; </div>
<div class="line"><a name="l00271"></a><span class="lineno"><a class="line" href="classqbpp_1_1easy__solver_1_1EasySolver.html#a011d395b4907d86b49efdcd43fbf6d3b">  271</a></span>&#160;  <span class="keyword">const</span> std::optional&lt;energy_t&gt; <a class="code" href="classqbpp_1_1easy__solver_1_1EasySolver.html#a011d395b4907d86b49efdcd43fbf6d3b">get_target_energy</a>()<span class="keyword"> const </span>{</div>
<div class="line"><a name="l00272"></a><span class="lineno">  272</span>&#160;    <span class="keywordflow">return</span> <a class="code" href="classqbpp_1_1easy__solver_1_1EasySolver.html#a5114362571468d84f507f9e3d17e0995">target_energy_</a>;</div>
<div class="line"><a name="l00273"></a><span class="lineno">  273</span>&#160;  }</div>
<div class="line"><a name="l00274"></a><span class="lineno">  274</span>&#160; </div>
<div class="line"><a name="l00275"></a><span class="lineno"><a class="line" href="classqbpp_1_1easy__solver_1_1EasySolver.html#ada3fbb50b6993a825681a4a712fd61b7">  275</a></span>&#160;  <span class="keyword">const</span> std::shared_ptr&lt;qbpp::SolHolder&gt; <a class="code" href="classqbpp_1_1easy__solver_1_1EasySolver.html#ada3fbb50b6993a825681a4a712fd61b7">sol_holder_ptr</a>()<span class="keyword"> const </span>{</div>
<div class="line"><a name="l00276"></a><span class="lineno">  276</span>&#160;    <span class="keywordflow">return</span> <a class="code" href="classqbpp_1_1easy__solver_1_1EasySolver.html#ae6e3c634ea34e47131d8e9a407906f4a">sol_holder_ptr_</a>;</div>
<div class="line"><a name="l00277"></a><span class="lineno">  277</span>&#160;  }</div>
<div class="line"><a name="l00278"></a><span class="lineno">  278</span>&#160; </div>
<div class="line"><a name="l00279"></a><span class="lineno"><a class="line" href="classqbpp_1_1easy__solver_1_1EasySolver.html#a5aca8737256d008df76e3f53cce1f53a">  279</a></span>&#160;  <a class="code" href="namespaceqbpp.html#a5cf436100ede362797d0c0501ea50a5a">vindex_t</a> <a class="code" href="classqbpp_1_1easy__solver_1_1EasySolver.html#a5aca8737256d008df76e3f53cce1f53a">var_count</a>()<span class="keyword"> const </span>{ <span class="keywordflow">return</span> <a class="code" href="classqbpp_1_1easy__solver_1_1EasySolver.html#a1d55e04ffc11f6c8460f184efc6d2ea3">quad_model_</a>.<a class="code" href="classqbpp_1_1Model.html#a5cf2bb955c3aa25951ca499d7d8635d2">var_count</a>(); }</div>
<div class="line"><a name="l00280"></a><span class="lineno">  280</span>&#160; </div>
<div class="line"><a name="l00281"></a><span class="lineno"><a class="line" href="classqbpp_1_1easy__solver_1_1EasySolver.html#ae8fcef1929f94e0a05dd1b039a151d57">  281</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="classqbpp_1_1easy__solver_1_1EasySolver.html#ae8fcef1929f94e0a05dd1b039a151d57">set_time_limit</a>(uint32_t limit) { <a class="code" href="classqbpp_1_1easy__solver_1_1EasySolver.html#ab99254a861b99ba22b5132ffa67416c2">time_limit_</a> = limit; }</div>
<div class="line"><a name="l00282"></a><span class="lineno">  282</span>&#160; </div>
<div class="line"><a name="l00283"></a><span class="lineno"><a class="line" href="classqbpp_1_1easy__solver_1_1EasySolver.html#aceec9cd9048576610b0cc1fba0b792c5">  283</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="classqbpp_1_1easy__solver_1_1EasySolver.html#aceec9cd9048576610b0cc1fba0b792c5">set_target_energy</a>(<a class="code" href="namespaceqbpp.html#a9c324f641779d1a7a3c7723f28d8cff4">energy_t</a> energy) { <a class="code" href="classqbpp_1_1easy__solver_1_1EasySolver.html#a5114362571468d84f507f9e3d17e0995">target_energy_</a> = energy; }</div>
<div class="line"><a name="l00284"></a><span class="lineno">  284</span>&#160; </div>
<div class="line"><a name="l00285"></a><span class="lineno"><a class="line" href="classqbpp_1_1easy__solver_1_1EasySolver.html#a963bc1625873dc0e7351ec72bf7e6fe5">  285</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="classqbpp_1_1easy__solver_1_1EasySolver.html#a963bc1625873dc0e7351ec72bf7e6fe5">set_thread_count</a>(<span class="keywordtype">unsigned</span> <span class="keywordtype">int</span> count) { <a class="code" href="classqbpp_1_1easy__solver_1_1EasySolver.html#aced832de618720dce416415ba5f58c5c">thread_count_</a> = count; }</div>
<div class="line"><a name="l00286"></a><span class="lineno">  286</span>&#160; </div>
<div class="line"><a name="l00287"></a><span class="lineno"><a class="line" href="classqbpp_1_1easy__solver_1_1EasySolver.html#ae347d0227c297c799f5a5b6168c09add">  287</a></span>&#160;  <span class="keywordtype">size_t</span> <a class="code" href="classqbpp_1_1easy__solver_1_1EasySolver.html#ae347d0227c297c799f5a5b6168c09add">get_thread_count</a>() { <span class="keywordflow">return</span> <a class="code" href="classqbpp_1_1easy__solver_1_1EasySolver.html#aced832de618720dce416415ba5f58c5c">thread_count_</a>; }</div>
<div class="line"><a name="l00288"></a><span class="lineno">  288</span>&#160; </div>
<div class="line"><a name="l00289"></a><span class="lineno"><a class="line" href="classqbpp_1_1easy__solver_1_1EasySolver.html#af79a776cac94fa618d27cd403ac9b2cf">  289</a></span>&#160;  <a class="code" href="classqbpp_1_1Sol.html">qbpp::Sol</a> <a class="code" href="classqbpp_1_1easy__solver_1_1EasySolver.html#af79a776cac94fa618d27cd403ac9b2cf">get_sol</a>()<span class="keyword"> const </span>{ <span class="keywordflow">return</span> <a class="code" href="classqbpp_1_1easy__solver_1_1EasySolver.html#ae6e3c634ea34e47131d8e9a407906f4a">sol_holder_ptr_</a>-&gt;get_sol(); }</div>
<div class="line"><a name="l00290"></a><span class="lineno">  290</span>&#160; </div>
<div class="line"><a name="l00291"></a><span class="lineno"><a class="line" href="classqbpp_1_1easy__solver_1_1EasySolver.html#a959d4f5bf764f394c9a99cb1cb6194f7">  291</a></span>&#160;  <span class="keyword">const</span> <a class="code" href="classqbpp_1_1Sol.html">qbpp::Sol</a> &amp;<a class="code" href="classqbpp_1_1easy__solver_1_1EasySolver.html#a959d4f5bf764f394c9a99cb1cb6194f7">set_sol</a>(<span class="keyword">const</span> <a class="code" href="classqbpp_1_1Sol.html">qbpp::Sol</a> &amp;sol) {</div>
<div class="line"><a name="l00292"></a><span class="lineno">  292</span>&#160;    <span class="keywordflow">return</span> <a class="code" href="classqbpp_1_1easy__solver_1_1EasySolver.html#ae6e3c634ea34e47131d8e9a407906f4a">sol_holder_ptr_</a>-&gt;set_sol(sol);</div>
<div class="line"><a name="l00293"></a><span class="lineno">  293</span>&#160;  }</div>
<div class="line"><a name="l00294"></a><span class="lineno">  294</span>&#160; </div>
<div class="line"><a name="l00295"></a><span class="lineno"><a class="line" href="classqbpp_1_1easy__solver_1_1EasySolver.html#adae92e04d52216b23f89a0e8e22f238e">  295</a></span>&#160;  <span class="keywordtype">double</span> <a class="code" href="classqbpp_1_1easy__solver_1_1EasySolver.html#adae92e04d52216b23f89a0e8e22f238e">get_tts</a>()<span class="keyword"> const </span>{ <span class="keywordflow">return</span> <a class="code" href="classqbpp_1_1easy__solver_1_1EasySolver.html#ae6e3c634ea34e47131d8e9a407906f4a">sol_holder_ptr_</a>-&gt;get_tts(); }</div>
<div class="line"><a name="l00296"></a><span class="lineno">  296</span>&#160; </div>
<div class="line"><a name="l00297"></a><span class="lineno"><a class="line" href="classqbpp_1_1easy__solver_1_1EasySolver.html#ae75de081bc90bd9b0101a1ca64b3b43a">  297</a></span>&#160;  uint64_t <a class="code" href="classqbpp_1_1easy__solver_1_1EasySolver.html#ae75de081bc90bd9b0101a1ca64b3b43a">get_flip_count</a>()<span class="keyword"> const </span>{ <span class="keywordflow">return</span> <a class="code" href="classqbpp_1_1easy__solver_1_1EasySolver.html#a41b03b454ad9acb22e785df8bba2d717">flip_count_</a>; }</div>
<div class="line"><a name="l00298"></a><span class="lineno">  298</span>&#160; </div>
<div class="line"><a name="l00299"></a><span class="lineno"><a class="line" href="classqbpp_1_1easy__solver_1_1EasySolver.html#ad0ad1068e6105a3a216bd0c70de48ec6">  299</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="classqbpp_1_1easy__solver_1_1EasySolver.html#ad0ad1068e6105a3a216bd0c70de48ec6">enable_default_callback</a>() { <a class="code" href="classqbpp_1_1easy__solver_1_1EasySolver.html#aeb2ca474df5de0ea608d461a486b1d11">enable_default_callback_</a> = <span class="keyword">true</span>; }</div>
<div class="line"><a name="l00300"></a><span class="lineno">  300</span>&#160; </div>
<div class="line"><a name="l00301"></a><span class="lineno"><a class="line" href="classqbpp_1_1easy__solver_1_1EasySolver.html#adbadd766f998ea7171e18493a3538d9d">  301</a></span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">void</span> <a class="code" href="classqbpp_1_1easy__solver_1_1EasySolver.html#adbadd766f998ea7171e18493a3538d9d">callback</a>(<span class="keyword">const</span> <a class="code" href="classqbpp_1_1Sol.html">qbpp::Sol</a> &amp;sol, <span class="keywordtype">double</span> tts)<span class="keyword"> const </span>{</div>
<div class="line"><a name="l00302"></a><span class="lineno">  302</span>&#160;    std::lock_guard&lt;std::mutex&gt; lock(<a class="code" href="classqbpp_1_1easy__solver_1_1EasySolver.html#a737bb24b042e6d038ffd2b2a80aaab7f">callback_mutex_</a>);</div>
<div class="line"><a name="l00303"></a><span class="lineno">  303</span>&#160;    <span class="keywordflow">if</span> (<a class="code" href="classqbpp_1_1easy__solver_1_1EasySolver.html#aeb2ca474df5de0ea608d461a486b1d11">enable_default_callback_</a>) {</div>
<div class="line"><a name="l00304"></a><span class="lineno">  304</span>&#160;      <span class="keywordflow">if</span> (!<a class="code" href="classqbpp_1_1easy__solver_1_1EasySolver.html#a801ffcc65ab06fa8f6bd0cf1cface28a">prev_energy_</a>.has_value() || sol.<a class="code" href="classqbpp_1_1Sol.html#a3ad455870df155ffed4701ce070cf335">energy</a>() &lt; <a class="code" href="classqbpp_1_1easy__solver_1_1EasySolver.html#a801ffcc65ab06fa8f6bd0cf1cface28a">prev_energy_</a>.value()) {</div>
<div class="line"><a name="l00305"></a><span class="lineno">  305</span>&#160;        std::cout &lt;&lt; <span class="stringliteral">&quot;TTS = &quot;</span> &lt;&lt; std::fixed &lt;&lt; std::setprecision(3)</div>
<div class="line"><a name="l00306"></a><span class="lineno">  306</span>&#160;                  &lt;&lt; std::setfill(<span class="charliteral">&#39;0&#39;</span>) &lt;&lt; tts &lt;&lt; <span class="stringliteral">&quot;s Energy = &quot;</span> &lt;&lt; sol.<a class="code" href="classqbpp_1_1Sol.html#a3ad455870df155ffed4701ce070cf335">energy</a>()</div>
<div class="line"><a name="l00307"></a><span class="lineno">  307</span>&#160;                  &lt;&lt; <span class="stringliteral">&quot; thread = &quot;</span></div>
<div class="line"><a name="l00308"></a><span class="lineno">  308</span>&#160;                  &lt;&lt; tbb::this_task_arena::current_thread_index() &lt;&lt; std::endl;</div>
<div class="line"><a name="l00309"></a><span class="lineno">  309</span>&#160;        <a class="code" href="classqbpp_1_1easy__solver_1_1EasySolver.html#a801ffcc65ab06fa8f6bd0cf1cface28a">prev_energy_</a> = sol.<a class="code" href="classqbpp_1_1Sol.html#a3ad455870df155ffed4701ce070cf335">energy</a>();</div>
<div class="line"><a name="l00310"></a><span class="lineno">  310</span>&#160;      }</div>
<div class="line"><a name="l00311"></a><span class="lineno">  311</span>&#160;    }</div>
<div class="line"><a name="l00312"></a><span class="lineno">  312</span>&#160;  }</div>
<div class="line"><a name="l00313"></a><span class="lineno">  313</span>&#160; </div>
<div class="line"><a name="l00314"></a><span class="lineno">  314</span>&#160;  <a class="code" href="classqbpp_1_1Sol.html">qbpp::Sol</a> <a class="code" href="classqbpp_1_1easy__solver_1_1EasySolver.html#a9a61ebfb6366c734e7af2b08890753f0">search</a>(<span class="keywordtype">bool</span> has_initial_sol = <span class="keyword">false</span>);</div>
<div class="line"><a name="l00315"></a><span class="lineno">  315</span>&#160; </div>
<div class="line"><a name="l00316"></a><span class="lineno"><a class="line" href="classqbpp_1_1easy__solver_1_1EasySolver.html#a8d5bc85759b94bc1c1bf2db59fc323b0">  316</a></span>&#160;  <a class="code" href="classqbpp_1_1Sol.html">qbpp::Sol</a> <a class="code" href="classqbpp_1_1easy__solver_1_1EasySolver.html#a8d5bc85759b94bc1c1bf2db59fc323b0">search</a>(<span class="keyword">const</span> <a class="code" href="classqbpp_1_1Sol.html">qbpp::Sol</a> &amp;initial_sol) {</div>
<div class="line"><a name="l00317"></a><span class="lineno">  317</span>&#160;    <a class="code" href="classqbpp_1_1easy__solver_1_1EasySolver.html#ae6e3c634ea34e47131d8e9a407906f4a">sol_holder_ptr_</a>-&gt;set_sol(initial_sol);</div>
<div class="line"><a name="l00318"></a><span class="lineno">  318</span>&#160;    <span class="keywordflow">return</span> <a class="code" href="classqbpp_1_1easy__solver_1_1EasySolver.html#a9a61ebfb6366c734e7af2b08890753f0">search</a>(<span class="keyword">true</span>);</div>
<div class="line"><a name="l00319"></a><span class="lineno">  319</span>&#160;  }</div>
<div class="line"><a name="l00320"></a><span class="lineno">  320</span>&#160; </div>
<div class="line"><a name="l00321"></a><span class="lineno"><a class="line" href="classqbpp_1_1easy__solver_1_1EasySolver.html#a514d671a4aced019d797dfbe2240b46c">  321</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="classqbpp_1_1easy__solver_1_1EasySolver.html#a514d671a4aced019d797dfbe2240b46c">enable_best_sols</a>(<span class="keywordtype">size_t</span> max_best_sol_count) {</div>
<div class="line"><a name="l00322"></a><span class="lineno">  322</span>&#160;    <span class="keywordflow">if</span> (max_best_sol_count == 0) {</div>
<div class="line"><a name="l00323"></a><span class="lineno">  323</span>&#160;      <a class="code" href="classqbpp_1_1easy__solver_1_1EasySolver.html#a01d3912bcf0c4741103f85106fc72037">best_sols_ptr_</a> = <span class="keyword">nullptr</span>;</div>
<div class="line"><a name="l00324"></a><span class="lineno">  324</span>&#160;      <span class="keywordflow">return</span>;</div>
<div class="line"><a name="l00325"></a><span class="lineno">  325</span>&#160;    }</div>
<div class="line"><a name="l00326"></a><span class="lineno">  326</span>&#160;    <a class="code" href="classqbpp_1_1easy__solver_1_1EasySolver.html#a01d3912bcf0c4741103f85106fc72037">best_sols_ptr_</a> = std::make_shared&lt;BestSols&gt;(max_best_sol_count);</div>
<div class="line"><a name="l00327"></a><span class="lineno">  327</span>&#160;  }</div>
<div class="line"><a name="l00328"></a><span class="lineno">  328</span>&#160; </div>
<div class="line"><a name="l00329"></a><span class="lineno"><a class="line" href="classqbpp_1_1easy__solver_1_1EasySolver.html#a8015a24fa02f969e216516657a5868f9">  329</a></span>&#160;  std::shared_ptr&lt;BestSols&gt; &amp;<a class="code" href="classqbpp_1_1easy__solver_1_1EasySolver.html#a8015a24fa02f969e216516657a5868f9">best_sols_ptr</a>() { <span class="keywordflow">return</span> <a class="code" href="classqbpp_1_1easy__solver_1_1EasySolver.html#a01d3912bcf0c4741103f85106fc72037">best_sols_ptr_</a>; }</div>
<div class="line"><a name="l00330"></a><span class="lineno"><a class="line" href="classqbpp_1_1easy__solver_1_1EasySolver.html#ab0f97f5dcd3ccce1dad6ea64fc2a994f">  330</a></span>&#160;  <span class="keyword">const</span> std::shared_ptr&lt;BestSols&gt; &amp;<a class="code" href="classqbpp_1_1easy__solver_1_1EasySolver.html#ab0f97f5dcd3ccce1dad6ea64fc2a994f">best_sols_ptr</a>()<span class="keyword"> const </span>{</div>
<div class="line"><a name="l00331"></a><span class="lineno">  331</span>&#160;    <span class="keywordflow">return</span> <a class="code" href="classqbpp_1_1easy__solver_1_1EasySolver.html#a01d3912bcf0c4741103f85106fc72037">best_sols_ptr_</a>;</div>
<div class="line"><a name="l00332"></a><span class="lineno">  332</span>&#160;  }</div>
<div class="line"><a name="l00333"></a><span class="lineno">  333</span>&#160; </div>
<div class="line"><a name="l00334"></a><span class="lineno"><a class="line" href="classqbpp_1_1easy__solver_1_1EasySolver.html#ae9d620e2fd58c6aa01e6169957e551d9">  334</a></span>&#160;  <a class="code" href="classqbpp_1_1easy__solver_1_1BestSols.html">BestSols</a> &amp;<a class="code" href="classqbpp_1_1easy__solver_1_1EasySolver.html#ae9d620e2fd58c6aa01e6169957e551d9">best_sols</a>() {</div>
<div class="line"><a name="l00335"></a><span class="lineno">  335</span>&#160;    <span class="keywordflow">if</span> (<a class="code" href="classqbpp_1_1easy__solver_1_1EasySolver.html#a01d3912bcf0c4741103f85106fc72037">best_sols_ptr_</a> == <span class="keyword">nullptr</span>) {</div>
<div class="line"><a name="l00336"></a><span class="lineno">  336</span>&#160;      <span class="keywordflow">throw</span> std::runtime_error(</div>
<div class="line"><a name="l00337"></a><span class="lineno">  337</span>&#160;          <span class="stringliteral">&quot;BestSols is not set. Please call enable_best_sols() first.&quot;</span>);</div>
<div class="line"><a name="l00338"></a><span class="lineno">  338</span>&#160;    }</div>
<div class="line"><a name="l00339"></a><span class="lineno">  339</span>&#160;    <span class="keywordflow">return</span> *<a class="code" href="classqbpp_1_1easy__solver_1_1EasySolver.html#a01d3912bcf0c4741103f85106fc72037">best_sols_ptr_</a>;</div>
<div class="line"><a name="l00340"></a><span class="lineno">  340</span>&#160;  }</div>
<div class="line"><a name="l00341"></a><span class="lineno"><a class="line" href="classqbpp_1_1easy__solver_1_1EasySolver.html#a79747cef9d1d81d1d4a0c0514646bbce">  341</a></span>&#160;  <span class="keyword">const</span> <a class="code" href="classqbpp_1_1easy__solver_1_1BestSols.html">BestSols</a> &amp;<a class="code" href="classqbpp_1_1easy__solver_1_1EasySolver.html#a79747cef9d1d81d1d4a0c0514646bbce">best_sols</a>()<span class="keyword"> const </span>{</div>
<div class="line"><a name="l00342"></a><span class="lineno">  342</span>&#160;    <span class="keywordflow">if</span> (<a class="code" href="classqbpp_1_1easy__solver_1_1EasySolver.html#a01d3912bcf0c4741103f85106fc72037">best_sols_ptr_</a> == <span class="keyword">nullptr</span>) {</div>
<div class="line"><a name="l00343"></a><span class="lineno">  343</span>&#160;      <span class="keywordflow">throw</span> std::runtime_error(</div>
<div class="line"><a name="l00344"></a><span class="lineno">  344</span>&#160;          <span class="stringliteral">&quot;BestSols is not set. Please call enable_best_sols() first.&quot;</span>);</div>
<div class="line"><a name="l00345"></a><span class="lineno">  345</span>&#160;    }</div>
<div class="line"><a name="l00346"></a><span class="lineno">  346</span>&#160;    <span class="keywordflow">return</span> *<a class="code" href="classqbpp_1_1easy__solver_1_1EasySolver.html#a01d3912bcf0c4741103f85106fc72037">best_sols_ptr_</a>;</div>
<div class="line"><a name="l00347"></a><span class="lineno">  347</span>&#160;  }</div>
<div class="line"><a name="l00348"></a><span class="lineno">  348</span>&#160; </div>
<div class="line"><a name="l00349"></a><span class="lineno">  349</span>&#160;};</div>
<div class="line"><a name="l00350"></a><span class="lineno">  350</span>&#160; </div>
<div class="line"><a name="l00351"></a><span class="lineno">  351</span>&#160; </div>
<div class="line"><a name="l00352"></a><span class="lineno"><a class="line" href="classqbpp_1_1easy__solver_1_1SolDelta.html#a98373d99b30ddd261919266c2ac59f57">  352</a></span>&#160;<span class="keyword">inline</span> <a class="code" href="namespaceqbpp.html#a5cf436100ede362797d0c0501ea50a5a">vindex_t</a> <a class="code" href="classqbpp_1_1easy__solver_1_1SolDelta.html#a98373d99b30ddd261919266c2ac59f57">SolDelta::var_count</a>()<span class="keyword"> const </span>{ <span class="keywordflow">return</span> <a class="code" href="classqbpp_1_1easy__solver_1_1SolDelta.html#a688778b8eb570de31703bf26d38cf01d">easy_solver_</a>.<a class="code" href="classqbpp_1_1easy__solver_1_1EasySolver.html#a5aca8737256d008df76e3f53cce1f53a">var_count</a>(); }</div>
<div class="line"><a name="l00353"></a><span class="lineno">  353</span>&#160; </div>
<div class="line"><a name="l00354"></a><span class="lineno"><a class="line" href="classqbpp_1_1easy__solver_1_1SolDelta.html#a2a6c5b08bc697bd6f633b68fcdd550e1">  354</a></span>&#160;<span class="keyword">inline</span> <a class="code" href="classqbpp_1_1easy__solver_1_1SolDelta.html#a2a6c5b08bc697bd6f633b68fcdd550e1">SolDelta::SolDelta</a>(<span class="keyword">const</span> <a class="code" href="classqbpp_1_1easy__solver_1_1EasySolver.html">EasySolver</a> &amp;easy_solver)</div>
<div class="line"><a name="l00355"></a><span class="lineno">  355</span>&#160;    : <a class="code" href="classqbpp_1_1Sol.html">Sol</a>(easy_solver.get_quad_model()), easy_solver_(easy_solver),</div>
<div class="line"><a name="l00356"></a><span class="lineno">  356</span>&#160;      delta_(var_count()), neg_set_(var_count()),</div>
<div class="line"><a name="l00357"></a><span class="lineno">  357</span>&#160;      sol_holder_ptr_(easy_solver.sol_holder_ptr()),</div>
<div class="line"><a name="l00358"></a><span class="lineno">  358</span>&#160;      best_sols_ptr_(easy_solver.best_sols_ptr()) {</div>
<div class="line"><a name="l00359"></a><span class="lineno">  359</span>&#160;  <span class="keywordflow">for</span> (<a class="code" href="namespaceqbpp.html#a5cf436100ede362797d0c0501ea50a5a">vindex_t</a> i = 0; i &lt; <a class="code" href="classqbpp_1_1easy__solver_1_1SolDelta.html#a98373d99b30ddd261919266c2ac59f57">var_count</a>(); ++i) {</div>
<div class="line"><a name="l00360"></a><span class="lineno">  360</span>&#160;    <a class="code" href="classqbpp_1_1easy__solver_1_1SolDelta.html#aaa3700bdd76001664905d47719a3e9da">delta_</a>[i] = <a class="code" href="classqbpp_1_1Sol.html#a11752b0b3742bec8da842db87ba94230">quad_model_</a>.<a class="code" href="classqbpp_1_1QuadModel.html#a591e74de688dbff1339c2c9d2352df65">linear</a>(i);</div>
<div class="line"><a name="l00361"></a><span class="lineno">  361</span>&#160;  }</div>
<div class="line"><a name="l00362"></a><span class="lineno">  362</span>&#160;}</div>
<div class="line"><a name="l00363"></a><span class="lineno">  363</span>&#160; </div>
<div class="line"><a name="l00364"></a><span class="lineno"><a class="line" href="classqbpp_1_1easy__solver_1_1SolDelta.html#a1a107d8974386a07d201e88fb2c9aed9">  364</a></span>&#160;<span class="keyword">inline</span> <span class="keywordtype">void</span> <a class="code" href="classqbpp_1_1easy__solver_1_1SolDelta.html#a1a107d8974386a07d201e88fb2c9aed9">SolDelta::flip</a>(<a class="code" href="namespaceqbpp.html#a5cf436100ede362797d0c0501ea50a5a">vindex_t</a> index) {</div>
<div class="line"><a name="l00365"></a><span class="lineno">  365</span>&#160;  <span class="keywordflow">if</span> (<a class="code" href="classqbpp_1_1Sol.html#a24f5a53fa68071dd9bcda910533bbad7">index</a> &gt;= <a class="code" href="classqbpp_1_1easy__solver_1_1SolDelta.html#a98373d99b30ddd261919266c2ac59f57">var_count</a>()) {</div>
<div class="line"><a name="l00366"></a><span class="lineno">  366</span>&#160;    <span class="keywordflow">throw</span> std::out_of_range(</div>
<div class="line"><a name="l00367"></a><span class="lineno">  367</span>&#160;        <a class="code" href="qbpp_8hpp.html#a265e0828fcea8e75fbc9c0be99f7156e">THROW_MESSAGE</a>(<span class="stringliteral">&quot;Flip index (&quot;</span>, <a class="code" href="classqbpp_1_1Sol.html#a24f5a53fa68071dd9bcda910533bbad7">index</a>, <span class="stringliteral">&quot;) is out of range.&quot;</span>));</div>
<div class="line"><a name="l00368"></a><span class="lineno">  368</span>&#160;  }</div>
<div class="line"><a name="l00369"></a><span class="lineno">  369</span>&#160; </div>
<div class="line"><a name="l00370"></a><span class="lineno">  370</span>&#160;  <span class="keywordflow">for</span> (<a class="code" href="namespaceqbpp.html#a5cf436100ede362797d0c0501ea50a5a">vindex_t</a> j = 0; j &lt; <a class="code" href="classqbpp_1_1Sol.html#a11752b0b3742bec8da842db87ba94230">quad_model_</a>.<a class="code" href="classqbpp_1_1QuadModel.html#ada71ec32906c9d8c73bd25c4267bafc5">degree</a>(<a class="code" href="classqbpp_1_1Sol.html#a24f5a53fa68071dd9bcda910533bbad7">index</a>); ++j) {</div>
<div class="line"><a name="l00371"></a><span class="lineno">  371</span>&#160;    <span class="keyword">auto</span> [k, coeff] = <a class="code" href="classqbpp_1_1Sol.html#a11752b0b3742bec8da842db87ba94230">quad_model_</a>.<a class="code" href="classqbpp_1_1QuadModel.html#a6624038d4ff117176c37de2d1260f471">quadratic</a>(<a class="code" href="classqbpp_1_1Sol.html#a24f5a53fa68071dd9bcda910533bbad7">index</a>, j);</div>
<div class="line"><a name="l00372"></a><span class="lineno">  372</span>&#160; </div>
<div class="line"><a name="l00373"></a><span class="lineno">  373</span>&#160;    <span class="keywordflow">if</span> (<a class="code" href="classqbpp_1_1easy__solver_1_1SolDelta.html#aaa3700bdd76001664905d47719a3e9da">delta_</a>[k] &lt; 0) {</div>
<div class="line"><a name="l00374"></a><span class="lineno">  374</span>&#160;      <a class="code" href="classqbpp_1_1easy__solver_1_1SolDelta.html#a9a87ab5a497acf8597110c7b54dd4278">neg_set_</a>.<a class="code" href="classqbpp_1_1misc_1_1MinHeap.html#aa209280cf2cfd0dd5ee04131bc96a582">erase</a>(k);</div>
<div class="line"><a name="l00375"></a><span class="lineno">  375</span>&#160;    }</div>
<div class="line"><a name="l00376"></a><span class="lineno">  376</span>&#160;    <a class="code" href="classqbpp_1_1easy__solver_1_1SolDelta.html#a948cb17c3b50104d7dbd333a88909757">before_delta_updated</a>(k);</div>
<div class="line"><a name="l00377"></a><span class="lineno">  377</span>&#160; </div>
<div class="line"><a name="l00378"></a><span class="lineno">  378</span>&#160;    <a class="code" href="classqbpp_1_1easy__solver_1_1SolDelta.html#aaa3700bdd76001664905d47719a3e9da">delta_</a>[k] +=</div>
<div class="line"><a name="l00379"></a><span class="lineno">  379</span>&#160;        <span class="keyword">static_cast&lt;</span><a class="code" href="namespaceqbpp.html#a9c324f641779d1a7a3c7723f28d8cff4">energy_t</a><span class="keyword">&gt;</span>((2 * <a class="code" href="classqbpp_1_1Sol.html#abdb4f6b779678720715f7845f264440f">get</a>(<a class="code" href="classqbpp_1_1Sol.html#a24f5a53fa68071dd9bcda910533bbad7">index</a>) - 1) * (2 * <a class="code" href="classqbpp_1_1Sol.html#abdb4f6b779678720715f7845f264440f">get</a>(k) - 1) * coeff);</div>
<div class="line"><a name="l00380"></a><span class="lineno">  380</span>&#160; </div>
<div class="line"><a name="l00381"></a><span class="lineno">  381</span>&#160;    <span class="keywordflow">if</span> (<a class="code" href="classqbpp_1_1easy__solver_1_1SolDelta.html#aaa3700bdd76001664905d47719a3e9da">delta_</a>[k] &lt; 0) {</div>
<div class="line"><a name="l00382"></a><span class="lineno">  382</span>&#160;      <a class="code" href="classqbpp_1_1easy__solver_1_1SolDelta.html#a9a87ab5a497acf8597110c7b54dd4278">neg_set_</a>.<a class="code" href="classqbpp_1_1misc_1_1MinHeap.html#a4ffbfe7ef3c611efd3de67cd523c94bd">insert</a>(k, <a class="code" href="classqbpp_1_1easy__solver_1_1SolDelta.html#aaa3700bdd76001664905d47719a3e9da">delta_</a>[k]);</div>
<div class="line"><a name="l00383"></a><span class="lineno">  383</span>&#160;    }</div>
<div class="line"><a name="l00384"></a><span class="lineno">  384</span>&#160;    <a class="code" href="classqbpp_1_1easy__solver_1_1SolDelta.html#ac82df38362383a0c302a979c0b394844">after_delta_updated</a>(k);</div>
<div class="line"><a name="l00385"></a><span class="lineno">  385</span>&#160;  }</div>
<div class="line"><a name="l00386"></a><span class="lineno">  386</span>&#160; </div>
<div class="line"><a name="l00387"></a><span class="lineno">  387</span>&#160;  <span class="keywordflow">if</span> (<a class="code" href="classqbpp_1_1easy__solver_1_1SolDelta.html#aaa3700bdd76001664905d47719a3e9da">delta_</a>[<a class="code" href="classqbpp_1_1Sol.html#a24f5a53fa68071dd9bcda910533bbad7">index</a>] &lt; 0) {</div>
<div class="line"><a name="l00388"></a><span class="lineno">  388</span>&#160;    <a class="code" href="classqbpp_1_1easy__solver_1_1SolDelta.html#a9a87ab5a497acf8597110c7b54dd4278">neg_set_</a>.<a class="code" href="classqbpp_1_1misc_1_1MinHeap.html#aa209280cf2cfd0dd5ee04131bc96a582">erase</a>(<a class="code" href="classqbpp_1_1Sol.html#a24f5a53fa68071dd9bcda910533bbad7">index</a>);</div>
<div class="line"><a name="l00389"></a><span class="lineno">  389</span>&#160;  }</div>
<div class="line"><a name="l00390"></a><span class="lineno">  390</span>&#160;  <a class="code" href="classqbpp_1_1easy__solver_1_1SolDelta.html#a948cb17c3b50104d7dbd333a88909757">before_delta_updated</a>(<a class="code" href="classqbpp_1_1Sol.html#a24f5a53fa68071dd9bcda910533bbad7">index</a>);</div>
<div class="line"><a name="l00391"></a><span class="lineno">  391</span>&#160; </div>
<div class="line"><a name="l00392"></a><span class="lineno">  392</span>&#160;  <a class="code" href="classqbpp_1_1easy__solver_1_1SolDelta.html#aaa3700bdd76001664905d47719a3e9da">delta_</a>[<a class="code" href="classqbpp_1_1Sol.html#a24f5a53fa68071dd9bcda910533bbad7">index</a>] = -<a class="code" href="classqbpp_1_1easy__solver_1_1SolDelta.html#aaa3700bdd76001664905d47719a3e9da">delta_</a>[<a class="code" href="classqbpp_1_1Sol.html#a24f5a53fa68071dd9bcda910533bbad7">index</a>];</div>
<div class="line"><a name="l00393"></a><span class="lineno">  393</span>&#160; </div>
<div class="line"><a name="l00394"></a><span class="lineno">  394</span>&#160;  <span class="keywordflow">if</span> (<a class="code" href="classqbpp_1_1easy__solver_1_1SolDelta.html#aaa3700bdd76001664905d47719a3e9da">delta_</a>[<a class="code" href="classqbpp_1_1Sol.html#a24f5a53fa68071dd9bcda910533bbad7">index</a>] &lt; 0) {</div>
<div class="line"><a name="l00395"></a><span class="lineno">  395</span>&#160;    <a class="code" href="classqbpp_1_1easy__solver_1_1SolDelta.html#a9a87ab5a497acf8597110c7b54dd4278">neg_set_</a>.<a class="code" href="classqbpp_1_1misc_1_1MinHeap.html#a4ffbfe7ef3c611efd3de67cd523c94bd">insert</a>(<a class="code" href="classqbpp_1_1Sol.html#a24f5a53fa68071dd9bcda910533bbad7">index</a>, <a class="code" href="classqbpp_1_1easy__solver_1_1SolDelta.html#aaa3700bdd76001664905d47719a3e9da">delta_</a>[<a class="code" href="classqbpp_1_1Sol.html#a24f5a53fa68071dd9bcda910533bbad7">index</a>]);</div>
<div class="line"><a name="l00396"></a><span class="lineno">  396</span>&#160;  }</div>
<div class="line"><a name="l00397"></a><span class="lineno">  397</span>&#160; </div>
<div class="line"><a name="l00398"></a><span class="lineno">  398</span>&#160;  <a class="code" href="classqbpp_1_1easy__solver_1_1SolDelta.html#ac82df38362383a0c302a979c0b394844">after_delta_updated</a>(<a class="code" href="classqbpp_1_1Sol.html#a24f5a53fa68071dd9bcda910533bbad7">index</a>);</div>
<div class="line"><a name="l00399"></a><span class="lineno">  399</span>&#160; </div>
<div class="line"><a name="l00400"></a><span class="lineno">  400</span>&#160;  <a class="code" href="classqbpp_1_1Sol.html#aae6d875ede912bcbf6254b74ffc66018">flip_bit_add_delta</a>(<a class="code" href="classqbpp_1_1Sol.html#a24f5a53fa68071dd9bcda910533bbad7">index</a>, -<a class="code" href="classqbpp_1_1easy__solver_1_1SolDelta.html#aaa3700bdd76001664905d47719a3e9da">delta_</a>[<a class="code" href="classqbpp_1_1Sol.html#a24f5a53fa68071dd9bcda910533bbad7">index</a>]);</div>
<div class="line"><a name="l00401"></a><span class="lineno">  401</span>&#160; </div>
<div class="line"><a name="l00402"></a><span class="lineno">  402</span>&#160;  ++<a class="code" href="classqbpp_1_1easy__solver_1_1SolDelta.html#a80634501337c3b39960cf323dd075705">flip_count_</a>;</div>
<div class="line"><a name="l00403"></a><span class="lineno">  403</span>&#160;}</div>
<div class="line"><a name="l00404"></a><span class="lineno">  404</span>&#160; </div>
<div class="line"><a name="l00405"></a><span class="lineno"><a class="line" href="classqbpp_1_1easy__solver_1_1SolDelta.html#acf11d3f5b86a20077d4c20dc9c18e044">  405</a></span>&#160;<span class="keyword">inline</span> <span class="keywordtype">void</span> <a class="code" href="classqbpp_1_1easy__solver_1_1SolDelta.html#acf11d3f5b86a20077d4c20dc9c18e044">SolDelta::greedy</a>() {</div>
<div class="line"><a name="l00406"></a><span class="lineno">  406</span>&#160;  <span class="keywordflow">while</span> (<a class="code" href="classqbpp_1_1easy__solver_1_1SolDelta.html#a9b594e0f5962872772bad177ec3a41be">neg_count</a>() &gt; 0) {</div>
<div class="line"><a name="l00407"></a><span class="lineno">  407</span>&#160;    <a class="code" href="namespaceqbpp.html#a5cf436100ede362797d0c0501ea50a5a">vindex_t</a> min_index = <a class="code" href="classqbpp_1_1easy__solver_1_1SolDelta.html#a5675af40ee9d8ec2f39a61ff5d1ac322">neg_min</a>();</div>
<div class="line"><a name="l00408"></a><span class="lineno">  408</span>&#160;    <a class="code" href="classqbpp_1_1easy__solver_1_1SolDelta.html#a1a107d8974386a07d201e88fb2c9aed9">flip</a>(min_index);</div>
<div class="line"><a name="l00409"></a><span class="lineno">  409</span>&#160;  }</div>
<div class="line"><a name="l00410"></a><span class="lineno">  410</span>&#160;  <a class="code" href="classqbpp_1_1easy__solver_1_1SolDelta.html#ac0f902682f6255272a79333a947e1a24">set_if_better</a>(<span class="stringliteral">&quot;Easy&quot;</span>);</div>
<div class="line"><a name="l00411"></a><span class="lineno">  411</span>&#160;}</div>
<div class="line"><a name="l00412"></a><span class="lineno">  412</span>&#160; </div>
<div class="line"><a name="l00413"></a><span class="lineno"><a class="line" href="classqbpp_1_1easy__solver_1_1SolDelta.html#ad0505230c61ae317b5414f06327daeb4">  413</a></span>&#160;<span class="keyword">inline</span> <span class="keywordtype">void</span> <a class="code" href="classqbpp_1_1easy__solver_1_1SolDelta.html#ad0505230c61ae317b5414f06327daeb4">SolDelta::random_flip</a>(<span class="keywordtype">size_t</span> iteration) {</div>
<div class="line"><a name="l00414"></a><span class="lineno">  414</span>&#160;  <span class="keywordflow">for</span> (<a class="code" href="namespaceqbpp.html#a5cf436100ede362797d0c0501ea50a5a">vindex_t</a> i = 0; i &lt; iteration; ++i) {</div>
<div class="line"><a name="l00415"></a><span class="lineno">  415</span>&#160;    <a class="code" href="namespaceqbpp.html#a5cf436100ede362797d0c0501ea50a5a">vindex_t</a> flip_index = <a class="code" href="classqbpp_1_1misc_1_1RandomEngine.html#ad836550b67c1c3784cf95a23d43bcd96">qbpp::misc::RandomEngine::gen</a>(<a class="code" href="classqbpp_1_1easy__solver_1_1SolDelta.html#a98373d99b30ddd261919266c2ac59f57">var_count</a>());</div>
<div class="line"><a name="l00416"></a><span class="lineno">  416</span>&#160;    <a class="code" href="classqbpp_1_1easy__solver_1_1SolDelta.html#a1a107d8974386a07d201e88fb2c9aed9">flip</a>(flip_index);</div>
<div class="line"><a name="l00417"></a><span class="lineno">  417</span>&#160;    <a class="code" href="classqbpp_1_1easy__solver_1_1SolDelta.html#ac0f902682f6255272a79333a947e1a24">set_if_better</a>(<span class="stringliteral">&quot;Easy&quot;</span>);</div>
<div class="line"><a name="l00418"></a><span class="lineno">  418</span>&#160;  }</div>
<div class="line"><a name="l00419"></a><span class="lineno">  419</span>&#160;}</div>
<div class="line"><a name="l00420"></a><span class="lineno">  420</span>&#160; </div>
<div class="line"><a name="l00421"></a><span class="lineno"><a class="line" href="classqbpp_1_1easy__solver_1_1SolDelta.html#ae5e7dbc16325f4f3b26895fe8686afc5">  421</a></span>&#160;<span class="keyword">inline</span> <span class="keywordtype">void</span> <a class="code" href="classqbpp_1_1easy__solver_1_1SolDelta.html#ae5e7dbc16325f4f3b26895fe8686afc5">SolDelta::move_to</a>(<a class="code" href="classqbpp_1_1Sol.html">qbpp::Sol</a> destination) {</div>
<div class="line"><a name="l00422"></a><span class="lineno">  422</span>&#160;  <a class="code" href="classqbpp_1_1misc_1_1MinSet.html">misc::MinSet</a> to_be_flipped;</div>
<div class="line"><a name="l00423"></a><span class="lineno">  423</span>&#160;  <span class="keywordflow">for</span> (<a class="code" href="namespaceqbpp.html#a5cf436100ede362797d0c0501ea50a5a">vindex_t</a> i = 0; i &lt; <a class="code" href="classqbpp_1_1easy__solver_1_1SolDelta.html#a98373d99b30ddd261919266c2ac59f57">var_count</a>(); ++i) {</div>
<div class="line"><a name="l00424"></a><span class="lineno">  424</span>&#160;    <span class="keywordflow">if</span> (<a class="code" href="classqbpp_1_1Sol.html#abdb4f6b779678720715f7845f264440f">get</a>(i) != destination.<a class="code" href="classqbpp_1_1Sol.html#abdb4f6b779678720715f7845f264440f">get</a>(i)) {</div>
<div class="line"><a name="l00425"></a><span class="lineno">  425</span>&#160;      to_be_flipped.<a class="code" href="classqbpp_1_1misc_1_1MinSet.html#a63576a251e5c467e6f4b1eae8a1b1f03">insert</a>(i, <a class="code" href="classqbpp_1_1easy__solver_1_1SolDelta.html#aaa3700bdd76001664905d47719a3e9da">delta_</a>[i]);</div>
<div class="line"><a name="l00426"></a><span class="lineno">  426</span>&#160;    }</div>
<div class="line"><a name="l00427"></a><span class="lineno">  427</span>&#160;  }</div>
<div class="line"><a name="l00428"></a><span class="lineno">  428</span>&#160;  <span class="keywordflow">while</span> (to_be_flipped.<a class="code" href="classqbpp_1_1misc_1_1MinSet.html#ad9f980fba77f1f8d017354953a5cb434">var_count</a>() &gt; 0) {</div>
<div class="line"><a name="l00429"></a><span class="lineno">  429</span>&#160;    <a class="code" href="namespaceqbpp.html#a5cf436100ede362797d0c0501ea50a5a">vindex_t</a> min_index = to_be_flipped.<a class="code" href="classqbpp_1_1misc_1_1MinSet.html#aa534cb8cd4b9d3646c243ab8c8d448e1">get_first</a>();</div>
<div class="line"><a name="l00430"></a><span class="lineno">  430</span>&#160;    <span class="keywordflow">for</span> (<a class="code" href="namespaceqbpp.html#a5cf436100ede362797d0c0501ea50a5a">vindex_t</a> j = 0; j &lt; <a class="code" href="classqbpp_1_1Sol.html#a11752b0b3742bec8da842db87ba94230">quad_model_</a>.<a class="code" href="classqbpp_1_1QuadModel.html#ada71ec32906c9d8c73bd25c4267bafc5">degree</a>(min_index); ++j) {</div>
<div class="line"><a name="l00431"></a><span class="lineno">  431</span>&#160;      <span class="keyword">auto</span> pair = <a class="code" href="classqbpp_1_1Sol.html#a11752b0b3742bec8da842db87ba94230">quad_model_</a>.<a class="code" href="classqbpp_1_1QuadModel.html#a6624038d4ff117176c37de2d1260f471">quadratic</a>(min_index, j);</div>
<div class="line"><a name="l00432"></a><span class="lineno">  432</span>&#160;      <span class="keywordflow">if</span> (<a class="code" href="classqbpp_1_1Sol.html#abdb4f6b779678720715f7845f264440f">get</a>(pair.first) != destination.<a class="code" href="classqbpp_1_1Sol.html#abdb4f6b779678720715f7845f264440f">get</a>(pair.first)) {</div>
<div class="line"><a name="l00433"></a><span class="lineno">  433</span>&#160;        to_be_flipped.<a class="code" href="classqbpp_1_1misc_1_1MinSet.html#abfb846a3ff4277f39e5cc4798933ca80">erase</a>(pair.first, <a class="code" href="classqbpp_1_1easy__solver_1_1SolDelta.html#aaa3700bdd76001664905d47719a3e9da">delta_</a>[pair.first]);</div>
<div class="line"><a name="l00434"></a><span class="lineno">  434</span>&#160;      }</div>
<div class="line"><a name="l00435"></a><span class="lineno">  435</span>&#160;    }</div>
<div class="line"><a name="l00436"></a><span class="lineno">  436</span>&#160;    to_be_flipped.<a class="code" href="classqbpp_1_1misc_1_1MinSet.html#abfb846a3ff4277f39e5cc4798933ca80">erase</a>(min_index, <a class="code" href="classqbpp_1_1easy__solver_1_1SolDelta.html#aaa3700bdd76001664905d47719a3e9da">delta_</a>[min_index]);</div>
<div class="line"><a name="l00437"></a><span class="lineno">  437</span>&#160;    <a class="code" href="classqbpp_1_1easy__solver_1_1SolDelta.html#a1a107d8974386a07d201e88fb2c9aed9">flip</a>(min_index);</div>
<div class="line"><a name="l00438"></a><span class="lineno">  438</span>&#160;    <span class="keywordflow">for</span> (<a class="code" href="namespaceqbpp.html#a5cf436100ede362797d0c0501ea50a5a">vindex_t</a> j = 0; j &lt; <a class="code" href="classqbpp_1_1Sol.html#a11752b0b3742bec8da842db87ba94230">quad_model_</a>.<a class="code" href="classqbpp_1_1QuadModel.html#ada71ec32906c9d8c73bd25c4267bafc5">degree</a>(min_index); ++j) {</div>
<div class="line"><a name="l00439"></a><span class="lineno">  439</span>&#160;      <span class="keyword">auto</span> pair = <a class="code" href="classqbpp_1_1Sol.html#a11752b0b3742bec8da842db87ba94230">quad_model_</a>.<a class="code" href="classqbpp_1_1QuadModel.html#a6624038d4ff117176c37de2d1260f471">quadratic</a>(min_index, j);</div>
<div class="line"><a name="l00440"></a><span class="lineno">  440</span>&#160;      <span class="keywordflow">if</span> (<a class="code" href="classqbpp_1_1Sol.html#abdb4f6b779678720715f7845f264440f">get</a>(pair.first) != destination.<a class="code" href="classqbpp_1_1Sol.html#abdb4f6b779678720715f7845f264440f">get</a>(pair.first))</div>
<div class="line"><a name="l00441"></a><span class="lineno">  441</span>&#160;        to_be_flipped.<a class="code" href="classqbpp_1_1misc_1_1MinSet.html#a63576a251e5c467e6f4b1eae8a1b1f03">insert</a>(pair.first, <a class="code" href="classqbpp_1_1easy__solver_1_1SolDelta.html#aaa3700bdd76001664905d47719a3e9da">delta_</a>[pair.first]);</div>
<div class="line"><a name="l00442"></a><span class="lineno">  442</span>&#160;    }</div>
<div class="line"><a name="l00443"></a><span class="lineno">  443</span>&#160;    <a class="code" href="classqbpp_1_1easy__solver_1_1SolDelta.html#ac0f902682f6255272a79333a947e1a24">set_if_better</a>(<span class="stringliteral">&quot;Easy&quot;</span>);</div>
<div class="line"><a name="l00444"></a><span class="lineno">  444</span>&#160;  }</div>
<div class="line"><a name="l00445"></a><span class="lineno">  445</span>&#160;}</div>
<div class="line"><a name="l00446"></a><span class="lineno">  446</span>&#160; </div>
<div class="line"><a name="l00447"></a><span class="lineno">  447</span>&#160;<span class="keyword">inline</span> std::optional&lt;double&gt;</div>
<div class="line"><a name="l00448"></a><span class="lineno"><a class="line" href="classqbpp_1_1easy__solver_1_1SolDelta.html#ac0f902682f6255272a79333a947e1a24">  448</a></span>&#160;<a class="code" href="classqbpp_1_1easy__solver_1_1SolDelta.html#ac0f902682f6255272a79333a947e1a24">SolDelta::set_if_better</a>(<span class="keyword">const</span> std::string &amp;solver_name) {</div>
<div class="line"><a name="l00449"></a><span class="lineno">  449</span>&#160;  std::optional&lt;double&gt; tts =</div>
<div class="line"><a name="l00450"></a><span class="lineno">  450</span>&#160;      <a class="code" href="classqbpp_1_1easy__solver_1_1SolDelta.html#a9f9ac387600b8004d161c6c4a17e6446">sol_holder_ptr_</a>-&gt;set_if_better(*<span class="keyword">this</span>, solver_name);</div>
<div class="line"><a name="l00451"></a><span class="lineno">  451</span>&#160;  <span class="keywordflow">if</span> (tts.has_value()) {</div>
<div class="line"><a name="l00452"></a><span class="lineno">  452</span>&#160;    <a class="code" href="classqbpp_1_1easy__solver_1_1SolDelta.html#a688778b8eb570de31703bf26d38cf01d">easy_solver_</a>.<a class="code" href="classqbpp_1_1easy__solver_1_1EasySolver.html#adbadd766f998ea7171e18493a3538d9d">callback</a>(*<span class="keyword">this</span>, tts.value());</div>
<div class="line"><a name="l00453"></a><span class="lineno">  453</span>&#160;  }</div>
<div class="line"><a name="l00454"></a><span class="lineno">  454</span>&#160;  <span class="keywordflow">if</span> (<a class="code" href="classqbpp_1_1easy__solver_1_1SolDelta.html#a28965e9da467b6c28f46caad2b0d6a37">best_sols_ptr</a>() != <span class="keyword">nullptr</span>) {</div>
<div class="line"><a name="l00455"></a><span class="lineno">  455</span>&#160;    <a class="code" href="classqbpp_1_1easy__solver_1_1SolDelta.html#a28965e9da467b6c28f46caad2b0d6a37">best_sols_ptr</a>()-&gt;insert_if_better(*<span class="keyword">this</span>);</div>
<div class="line"><a name="l00456"></a><span class="lineno">  456</span>&#160;  }</div>
<div class="line"><a name="l00457"></a><span class="lineno">  457</span>&#160;  <span class="keywordflow">return</span> tts;</div>
<div class="line"><a name="l00458"></a><span class="lineno">  458</span>&#160;}</div>
<div class="line"><a name="l00459"></a><span class="lineno">  459</span>&#160; </div>
<div class="line"><a name="l00460"></a><span class="lineno">  460</span>&#160; </div>
<div class="line"><a name="l00461"></a><span class="lineno"><a class="line" href="classqbpp_1_1easy__solver_1_1PosMinSolDelta.html#af512f524573c98e13ee2e236cc147c17">  461</a></span>&#160;<span class="keyword">inline</span> <a class="code" href="classqbpp_1_1easy__solver_1_1PosMinSolDelta.html#af512f524573c98e13ee2e236cc147c17">PosMinSolDelta::PosMinSolDelta</a>(<span class="keyword">const</span> <a class="code" href="classqbpp_1_1easy__solver_1_1EasySolver.html">EasySolver</a> &amp;easy_solver)</div>
<div class="line"><a name="l00462"></a><span class="lineno">  462</span>&#160;    : <a class="code" href="classqbpp_1_1easy__solver_1_1TabuSolDelta.html">TabuSolDelta</a>(easy_solver, std::min(static_cast&lt;<a class="code" href="namespaceqbpp.html#a5cf436100ede362797d0c0501ea50a5a">vindex_t</a>&gt;(5),</div>
<div class="line"><a name="l00463"></a><span class="lineno">  463</span>&#160;                                         easy_solver.var_count() / 5)),</div>
<div class="line"><a name="l00464"></a><span class="lineno">  464</span>&#160;      pos_set_(var_count()) {</div>
<div class="line"><a name="l00465"></a><span class="lineno">  465</span>&#160;  <span class="keywordflow">for</span> (<a class="code" href="namespaceqbpp.html#a5cf436100ede362797d0c0501ea50a5a">vindex_t</a> i = 0; i &lt; <a class="code" href="classqbpp_1_1easy__solver_1_1SolDelta.html#a98373d99b30ddd261919266c2ac59f57">var_count</a>(); ++i) {</div>
<div class="line"><a name="l00466"></a><span class="lineno">  466</span>&#160;    <span class="keywordflow">if</span> (<a class="code" href="classqbpp_1_1easy__solver_1_1SolDelta.html#aaa3700bdd76001664905d47719a3e9da">delta_</a>[i] &lt; 0) {</div>
<div class="line"><a name="l00467"></a><span class="lineno">  467</span>&#160;      <a class="code" href="classqbpp_1_1easy__solver_1_1SolDelta.html#a9a87ab5a497acf8597110c7b54dd4278">neg_set_</a>.<a class="code" href="classqbpp_1_1misc_1_1MinHeap.html#a4ffbfe7ef3c611efd3de67cd523c94bd">insert</a>(i, <a class="code" href="classqbpp_1_1easy__solver_1_1SolDelta.html#aaa3700bdd76001664905d47719a3e9da">delta_</a>[i]);</div>
<div class="line"><a name="l00468"></a><span class="lineno">  468</span>&#160;    } <span class="keywordflow">else</span> <span class="keywordflow">if</span> (<a class="code" href="classqbpp_1_1easy__solver_1_1SolDelta.html#aaa3700bdd76001664905d47719a3e9da">delta_</a>[i] &gt; 0) {</div>
<div class="line"><a name="l00469"></a><span class="lineno">  469</span>&#160;      <a class="code" href="classqbpp_1_1easy__solver_1_1PosMinSolDelta.html#a1b2b9ca3a340f92871de75954d335d40">pos_set_</a>.<a class="code" href="classqbpp_1_1misc_1_1MinHeap.html#a4ffbfe7ef3c611efd3de67cd523c94bd">insert</a>(i, <a class="code" href="classqbpp_1_1easy__solver_1_1SolDelta.html#aaa3700bdd76001664905d47719a3e9da">delta_</a>[i]);</div>
<div class="line"><a name="l00470"></a><span class="lineno">  470</span>&#160;    }</div>
<div class="line"><a name="l00471"></a><span class="lineno">  471</span>&#160;  }</div>
<div class="line"><a name="l00472"></a><span class="lineno">  472</span>&#160;}</div>
<div class="line"><a name="l00473"></a><span class="lineno">  473</span>&#160; </div>
<div class="line"><a name="l00474"></a><span class="lineno"><a class="line" href="classqbpp_1_1easy__solver_1_1PosMinSolDelta.html#aff102bc17a0d06a78a285f1ab2c9632e">  474</a></span>&#160;<span class="keyword">inline</span> <span class="keywordtype">void</span> <a class="code" href="classqbpp_1_1easy__solver_1_1PosMinSolDelta.html#aff102bc17a0d06a78a285f1ab2c9632e">PosMinSolDelta::search</a>(<span class="keywordtype">size_t</span> iteration) {</div>
<div class="line"><a name="l00475"></a><span class="lineno">  475</span>&#160;  <a class="code" href="classqbpp_1_1easy__solver_1_1SolDelta.html#acf11d3f5b86a20077d4c20dc9c18e044">greedy</a>();</div>
<div class="line"><a name="l00476"></a><span class="lineno">  476</span>&#160;  <span class="keywordflow">for</span> (<a class="code" href="namespaceqbpp.html#a5cf436100ede362797d0c0501ea50a5a">vindex_t</a> i = 0; i &lt; iteration; ++i) {</div>
<div class="line"><a name="l00477"></a><span class="lineno">  477</span>&#160;    std::optional&lt;vindex_t&gt; flip_index = std::nullopt;</div>
<div class="line"><a name="l00478"></a><span class="lineno">  478</span>&#160;    <span class="keywordflow">for</span> (<a class="code" href="namespaceqbpp.html#a5cf436100ede362797d0c0501ea50a5a">vindex_t</a> j = 0; j &lt; <a class="code" href="classqbpp_1_1easy__solver_1_1TabuSolDelta.html#a1381fbf8ed64faf60c3ad0556a37b400">tabu_size_</a> * 2; ++j) {</div>
<div class="line"><a name="l00479"></a><span class="lineno">  479</span>&#160;      <a class="code" href="namespaceqbpp.html#a5cf436100ede362797d0c0501ea50a5a">vindex_t</a> candidate = <a class="code" href="namespaceqbpp.html#ac0b50505a1738411ca7c96487b296234">vindex_limit</a>;</div>
<div class="line"><a name="l00480"></a><span class="lineno">  480</span>&#160;      <span class="keywordflow">if</span> (<a class="code" href="classqbpp_1_1easy__solver_1_1PosMinSolDelta.html#ad7fb2fffb0e77d69983865304fa11008">pos_count</a>() == 0 || <a class="code" href="classqbpp_1_1easy__solver_1_1SolDelta.html#a9b594e0f5962872772bad177ec3a41be">neg_count</a>() == 0) {</div>
<div class="line"><a name="l00481"></a><span class="lineno">  481</span>&#160;        candidate = <a class="code" href="classqbpp_1_1misc_1_1RandomGenerator.html#a95308c2300aeef9a881ba97786367977">qbpp::misc::RandomGenerator::gen</a>(<a class="code" href="classqbpp_1_1easy__solver_1_1SolDelta.html#a98373d99b30ddd261919266c2ac59f57">var_count</a>());</div>
<div class="line"><a name="l00482"></a><span class="lineno">  482</span>&#160;      } <span class="keywordflow">else</span> <span class="keywordflow">if</span> (<a class="code" href="classqbpp_1_1misc_1_1RandomGenerator.html#a95308c2300aeef9a881ba97786367977">qbpp::misc::RandomGenerator::gen</a>(<a class="code" href="classqbpp_1_1easy__solver_1_1SolDelta.html#a9b594e0f5962872772bad177ec3a41be">neg_count</a>() + 1) == 0) {</div>
<div class="line"><a name="l00483"></a><span class="lineno">  483</span>&#160;        candidate = <a class="code" href="classqbpp_1_1easy__solver_1_1PosMinSolDelta.html#a66bae180e06d9fb7ae8461ba81111223">pos_min</a>();</div>
<div class="line"><a name="l00484"></a><span class="lineno">  484</span>&#160;      } <span class="keywordflow">else</span> {</div>
<div class="line"><a name="l00485"></a><span class="lineno">  485</span>&#160;        candidate = <a class="code" href="classqbpp_1_1easy__solver_1_1SolDelta.html#a03de2250fb7d272df91c43dcf9a937c2">neg_random</a>();</div>
<div class="line"><a name="l00486"></a><span class="lineno">  486</span>&#160;      }</div>
<div class="line"><a name="l00487"></a><span class="lineno">  487</span>&#160;      <span class="keywordflow">if</span> (candidate == <a class="code" href="namespaceqbpp.html#ac0b50505a1738411ca7c96487b296234">vindex_limit</a>) {</div>
<div class="line"><a name="l00488"></a><span class="lineno">  488</span>&#160;        <span class="keywordflow">throw</span> std::runtime_error(<a class="code" href="qbpp_8hpp.html#a265e0828fcea8e75fbc9c0be99f7156e">THROW_MESSAGE</a>(<span class="stringliteral">&quot;Unexpected error.&quot;</span>));</div>
<div class="line"><a name="l00489"></a><span class="lineno">  489</span>&#160;      }</div>
<div class="line"><a name="l00490"></a><span class="lineno">  490</span>&#160;      <span class="keywordflow">if</span> (!<a class="code" href="classqbpp_1_1easy__solver_1_1TabuSolDelta.html#ac17653919bfa66975c06ed71e41d5b68">tabu_</a>.<a class="code" href="classqbpp_1_1misc_1_1Tabu.html#a44a9ecb55678821cc1021e33b4faba72">has</a>(candidate)) {</div>
<div class="line"><a name="l00491"></a><span class="lineno">  491</span>&#160;        flip_index = candidate;</div>
<div class="line"><a name="l00492"></a><span class="lineno">  492</span>&#160;        <span class="keywordflow">break</span>;</div>
<div class="line"><a name="l00493"></a><span class="lineno">  493</span>&#160;      }</div>
<div class="line"><a name="l00494"></a><span class="lineno">  494</span>&#160;    }</div>
<div class="line"><a name="l00495"></a><span class="lineno">  495</span>&#160;    <span class="keywordflow">if</span> (!flip_index.has_value()) {</div>
<div class="line"><a name="l00496"></a><span class="lineno">  496</span>&#160;      flip_index = <a class="code" href="classqbpp_1_1easy__solver_1_1TabuSolDelta.html#ac17653919bfa66975c06ed71e41d5b68">tabu_</a>.<a class="code" href="classqbpp_1_1misc_1_1Tabu.html#ac79f02e24591065f8224dc1aa5355e20">non_tabu_random</a>();</div>
<div class="line"><a name="l00497"></a><span class="lineno">  497</span>&#160;    }</div>
<div class="line"><a name="l00498"></a><span class="lineno">  498</span>&#160;    <a class="code" href="classqbpp_1_1easy__solver_1_1TabuSolDelta.html#abddd3367c047935e82e517c421cb5a9a">flip</a>(flip_index.value());</div>
<div class="line"><a name="l00499"></a><span class="lineno">  499</span>&#160;    <a class="code" href="classqbpp_1_1easy__solver_1_1TabuSolDelta.html#ac17653919bfa66975c06ed71e41d5b68">tabu_</a>.<a class="code" href="classqbpp_1_1misc_1_1Tabu.html#a549866fee09f51bb17a90e3a1831bf77">insert</a>(flip_index.value());</div>
<div class="line"><a name="l00500"></a><span class="lineno">  500</span>&#160;    <a class="code" href="classqbpp_1_1easy__solver_1_1SolDelta.html#ac0f902682f6255272a79333a947e1a24">set_if_better</a>(<span class="stringliteral">&quot;Easy&quot;</span>);</div>
<div class="line"><a name="l00501"></a><span class="lineno">  501</span>&#160;    <span class="keywordflow">if</span> (<a class="code" href="classqbpp_1_1easy__solver_1_1SolDelta.html#a688778b8eb570de31703bf26d38cf01d">easy_solver_</a>.<a class="code" href="classqbpp_1_1easy__solver_1_1EasySolver.html#a011d395b4907d86b49efdcd43fbf6d3b">get_target_energy</a>().has_value() &amp;&amp;</div>
<div class="line"><a name="l00502"></a><span class="lineno">  502</span>&#160;        <a class="code" href="classqbpp_1_1easy__solver_1_1SolDelta.html#a9f9ac387600b8004d161c6c4a17e6446">sol_holder_ptr_</a>-&gt;energy() &lt;= <a class="code" href="classqbpp_1_1easy__solver_1_1SolDelta.html#a688778b8eb570de31703bf26d38cf01d">easy_solver_</a>.<a class="code" href="classqbpp_1_1easy__solver_1_1EasySolver.html#a011d395b4907d86b49efdcd43fbf6d3b">get_target_energy</a>())</div>
<div class="line"><a name="l00503"></a><span class="lineno">  503</span>&#160;      <span class="keywordflow">break</span>;</div>
<div class="line"><a name="l00504"></a><span class="lineno">  504</span>&#160;  }</div>
<div class="line"><a name="l00505"></a><span class="lineno">  505</span>&#160;  <a class="code" href="classqbpp_1_1easy__solver_1_1SolDelta.html#acf11d3f5b86a20077d4c20dc9c18e044">greedy</a>();</div>
<div class="line"><a name="l00506"></a><span class="lineno">  506</span>&#160;}</div>
<div class="line"><a name="l00507"></a><span class="lineno">  507</span>&#160; </div>
<div class="line"><a name="l00508"></a><span class="lineno"><a class="line" href="classqbpp_1_1easy__solver_1_1EasySolver.html#a329a03e4e5c401ec471e0f7f4c42cb51">  508</a></span>&#160;<span class="keyword">inline</span> <span class="keywordtype">void</span> <a class="code" href="classqbpp_1_1easy__solver_1_1EasySolver.html#a329a03e4e5c401ec471e0f7f4c42cb51">EasySolver::single_search</a>(<span class="keywordtype">size_t</span> thread_id) {</div>
<div class="line"><a name="l00509"></a><span class="lineno">  509</span>&#160;  <span class="keyword">const</span> <span class="keywordtype">size_t</span> min_flip_count = 100;</div>
<div class="line"><a name="l00510"></a><span class="lineno">  510</span>&#160;  <a class="code" href="classqbpp_1_1easy__solver_1_1PosMinSolDelta.html">PosMinSolDelta</a> pos_min_sol_delta(*<span class="keyword">this</span>);</div>
<div class="line"><a name="l00511"></a><span class="lineno">  511</span>&#160;  <span class="keywordtype">size_t</span> max_flip_count = (<a class="code" href="classqbpp_1_1easy__solver_1_1EasySolver.html#a5aca8737256d008df76e3f53cce1f53a">var_count</a>() / 2);</div>
<div class="line"><a name="l00512"></a><span class="lineno">  512</span>&#160;  <span class="keywordflow">if</span> (<a class="code" href="classqbpp_1_1easy__solver_1_1EasySolver.html#aced832de618720dce416415ba5f58c5c">thread_count_</a> &gt; 1) {</div>
<div class="line"><a name="l00513"></a><span class="lineno">  513</span>&#160;    <span class="keywordtype">double</span> k = std::pow(<span class="keyword">static_cast&lt;</span><span class="keywordtype">double</span><span class="keyword">&gt;</span>(max_flip_count) / min_flip_count,</div>
<div class="line"><a name="l00514"></a><span class="lineno">  514</span>&#160;                        1.0 / <span class="keyword">static_cast&lt;</span><span class="keywordtype">double</span><span class="keyword">&gt;</span>(<a class="code" href="classqbpp_1_1easy__solver_1_1EasySolver.html#aced832de618720dce416415ba5f58c5c">thread_count_</a> - 1));</div>
<div class="line"><a name="l00515"></a><span class="lineno">  515</span>&#160;    max_flip_count =</div>
<div class="line"><a name="l00516"></a><span class="lineno">  516</span>&#160;        <span class="keyword">static_cast&lt;</span><span class="keywordtype">size_t</span><span class="keyword">&gt;</span>(min_flip_count * std::pow(k, thread_id));</div>
<div class="line"><a name="l00517"></a><span class="lineno">  517</span>&#160;    <span class="keywordflow">if</span> (max_flip_count &lt; min_flip_count)</div>
<div class="line"><a name="l00518"></a><span class="lineno">  518</span>&#160;      max_flip_count = min_flip_count;</div>
<div class="line"><a name="l00519"></a><span class="lineno">  519</span>&#160;  }</div>
<div class="line"><a name="l00520"></a><span class="lineno">  520</span>&#160;  <a class="code" href="namespaceqbpp.html#a9c324f641779d1a7a3c7723f28d8cff4">energy_t</a> prev_energy = <a class="code" href="classqbpp_1_1easy__solver_1_1EasySolver.html#ae6e3c634ea34e47131d8e9a407906f4a">sol_holder_ptr_</a>-&gt;energy();</div>
<div class="line"><a name="l00521"></a><span class="lineno">  521</span>&#160;  <span class="keywordtype">size_t</span> random_flip_count = 1;</div>
<div class="line"><a name="l00522"></a><span class="lineno">  522</span>&#160;  <span class="keywordflow">while</span> (!<a class="code" href="classqbpp_1_1easy__solver_1_1EasySolver.html#ab99254a861b99ba22b5132ffa67416c2">time_limit_</a>.has_value() ||</div>
<div class="line"><a name="l00523"></a><span class="lineno">  523</span>&#160;         <a class="code" href="namespaceqbpp.html#a4184afa358539dbcf5f35e63b49ce05b">qbpp::get_time</a>() - <a class="code" href="classqbpp_1_1easy__solver_1_1EasySolver.html#a9a4b07b571358cf4627c0c5d2b138222">start_time_</a> &lt; <a class="code" href="classqbpp_1_1easy__solver_1_1EasySolver.html#ab99254a861b99ba22b5132ffa67416c2">time_limit_</a>.value()) {</div>
<div class="line"><a name="l00524"></a><span class="lineno">  524</span>&#160;    pos_min_sol_delta.<a class="code" href="classqbpp_1_1easy__solver_1_1SolDelta.html#ae5e7dbc16325f4f3b26895fe8686afc5">move_to</a>(<a class="code" href="classqbpp_1_1easy__solver_1_1EasySolver.html#ae6e3c634ea34e47131d8e9a407906f4a">sol_holder_ptr_</a>-&gt;get_sol());</div>
<div class="line"><a name="l00525"></a><span class="lineno">  525</span>&#160;    pos_min_sol_delta.<a class="code" href="classqbpp_1_1easy__solver_1_1SolDelta.html#ad0505230c61ae317b5414f06327daeb4">random_flip</a>(random_flip_count);</div>
<div class="line"><a name="l00526"></a><span class="lineno">  526</span>&#160;    pos_min_sol_delta.<a class="code" href="classqbpp_1_1easy__solver_1_1PosMinSolDelta.html#aff102bc17a0d06a78a285f1ab2c9632e">search</a>(max_flip_count);</div>
<div class="line"><a name="l00527"></a><span class="lineno">  527</span>&#160;    <span class="keywordflow">if</span> (<a class="code" href="classqbpp_1_1easy__solver_1_1EasySolver.html#a5114362571468d84f507f9e3d17e0995">target_energy_</a>.has_value() &amp;&amp;</div>
<div class="line"><a name="l00528"></a><span class="lineno">  528</span>&#160;        <a class="code" href="classqbpp_1_1easy__solver_1_1EasySolver.html#ae6e3c634ea34e47131d8e9a407906f4a">sol_holder_ptr_</a>-&gt;energy() &lt;= <a class="code" href="classqbpp_1_1easy__solver_1_1EasySolver.html#a5114362571468d84f507f9e3d17e0995">target_energy_</a>.value())</div>
<div class="line"><a name="l00529"></a><span class="lineno">  529</span>&#160;      <span class="keywordflow">break</span>;</div>
<div class="line"><a name="l00530"></a><span class="lineno">  530</span>&#160;    <span class="keywordflow">if</span> (prev_energy == <a class="code" href="classqbpp_1_1easy__solver_1_1EasySolver.html#ae6e3c634ea34e47131d8e9a407906f4a">sol_holder_ptr_</a>-&gt;energy()) {</div>
<div class="line"><a name="l00531"></a><span class="lineno">  531</span>&#160;      random_flip_count = (random_flip_count + 1) % (max_flip_count / 2);</div>
<div class="line"><a name="l00532"></a><span class="lineno">  532</span>&#160;      <span class="keywordflow">if</span> (random_flip_count == 0)</div>
<div class="line"><a name="l00533"></a><span class="lineno">  533</span>&#160;        random_flip_count = 1;</div>
<div class="line"><a name="l00534"></a><span class="lineno">  534</span>&#160;    } <span class="keywordflow">else</span> {</div>
<div class="line"><a name="l00535"></a><span class="lineno">  535</span>&#160;      random_flip_count = 1;</div>
<div class="line"><a name="l00536"></a><span class="lineno">  536</span>&#160;    }</div>
<div class="line"><a name="l00537"></a><span class="lineno">  537</span>&#160;    prev_energy = <a class="code" href="classqbpp_1_1easy__solver_1_1EasySolver.html#ae6e3c634ea34e47131d8e9a407906f4a">sol_holder_ptr_</a>-&gt;energy();</div>
<div class="line"><a name="l00538"></a><span class="lineno">  538</span>&#160;  }</div>
<div class="line"><a name="l00539"></a><span class="lineno">  539</span>&#160; </div>
<div class="line"><a name="l00540"></a><span class="lineno">  540</span>&#160;  {</div>
<div class="line"><a name="l00541"></a><span class="lineno">  541</span>&#160;    std::lock_guard&lt;std::mutex&gt; lock(<a class="code" href="classqbpp_1_1easy__solver_1_1EasySolver.html#ac66675c187e2add239134fd04df4e5ce">flip_count_mutex_</a>);</div>
<div class="line"><a name="l00542"></a><span class="lineno">  542</span>&#160;    <a class="code" href="classqbpp_1_1easy__solver_1_1EasySolver.html#a41b03b454ad9acb22e785df8bba2d717">flip_count_</a> += pos_min_sol_delta.<a class="code" href="classqbpp_1_1easy__solver_1_1SolDelta.html#a4288d75e854df11c4023461d03c319e3">get_flip_count</a>();</div>
<div class="line"><a name="l00543"></a><span class="lineno">  543</span>&#160;  }</div>
<div class="line"><a name="l00544"></a><span class="lineno">  544</span>&#160;}</div>
<div class="line"><a name="l00545"></a><span class="lineno">  545</span>&#160; </div>
<div class="line"><a name="l00546"></a><span class="lineno"><a class="line" href="classqbpp_1_1easy__solver_1_1EasySolver.html#a9a61ebfb6366c734e7af2b08890753f0">  546</a></span>&#160;<span class="keyword">inline</span> <a class="code" href="classqbpp_1_1Sol.html">qbpp::Sol</a> <a class="code" href="classqbpp_1_1easy__solver_1_1EasySolver.html#a9a61ebfb6366c734e7af2b08890753f0">EasySolver::search</a>(<span class="keywordtype">bool</span> has_initial_sol) {</div>
<div class="line"><a name="l00547"></a><span class="lineno">  547</span>&#160;  <span class="keywordflow">if</span> (!has_initial_sol &amp;&amp; <a class="code" href="classqbpp_1_1easy__solver_1_1EasySolver.html#a41a71df4e0f7029335c0bcf9f407881e">is_internal_sol_holder_</a>) {</div>
<div class="line"><a name="l00548"></a><span class="lineno">  548</span>&#160;    <a class="code" href="classqbpp_1_1easy__solver_1_1EasySolver.html#ae6e3c634ea34e47131d8e9a407906f4a">sol_holder_ptr_</a> = std::make_shared&lt;qbpp::SolHolder&gt;(<a class="code" href="classqbpp_1_1easy__solver_1_1EasySolver.html#a1d55e04ffc11f6c8460f184efc6d2ea3">quad_model_</a>);</div>
<div class="line"><a name="l00549"></a><span class="lineno">  549</span>&#160;  }</div>
<div class="line"><a name="l00550"></a><span class="lineno">  550</span>&#160;  <a class="code" href="classqbpp_1_1easy__solver_1_1EasySolver.html#a9a4b07b571358cf4627c0c5d2b138222">start_time_</a> = <a class="code" href="namespaceqbpp.html#a4184afa358539dbcf5f35e63b49ce05b">qbpp::get_time</a>();</div>
<div class="line"><a name="l00551"></a><span class="lineno">  551</span>&#160;  <span class="keywordflow">if</span> (<a class="code" href="classqbpp_1_1easy__solver_1_1EasySolver.html#a1d55e04ffc11f6c8460f184efc6d2ea3">quad_model_</a>.<a class="code" href="classqbpp_1_1QuadModel.html#a16af851530b1f63aca9baa8ec67ed01f">term_count</a>() == 0)</div>
<div class="line"><a name="l00552"></a><span class="lineno">  552</span>&#160;    <span class="keywordflow">return</span> <a class="code" href="classqbpp_1_1easy__solver_1_1EasySolver.html#ae6e3c634ea34e47131d8e9a407906f4a">sol_holder_ptr_</a>-&gt;get_sol();</div>
<div class="line"><a name="l00553"></a><span class="lineno">  553</span>&#160;  <a class="code" href="classqbpp_1_1easy__solver_1_1EasySolver.html#a801ffcc65ab06fa8f6bd0cf1cface28a">prev_energy_</a> = std::nullopt;</div>
<div class="line"><a name="l00554"></a><span class="lineno">  554</span>&#160; </div>
<div class="line"><a name="l00555"></a><span class="lineno">  555</span>&#160;  tbb::parallel_for(<span class="keywordtype">size_t</span>(0), <span class="keyword">static_cast&lt;</span><span class="keywordtype">size_t</span><span class="keyword">&gt;</span>(<a class="code" href="classqbpp_1_1easy__solver_1_1EasySolver.html#aced832de618720dce416415ba5f58c5c">thread_count_</a>),</div>
<div class="line"><a name="l00556"></a><span class="lineno">  556</span>&#160;                    [<span class="keyword">this</span>](<span class="keywordtype">size_t</span> thread_id) { <a class="code" href="classqbpp_1_1easy__solver_1_1EasySolver.html#a329a03e4e5c401ec471e0f7f4c42cb51">single_search</a>(thread_id); });</div>
<div class="line"><a name="l00557"></a><span class="lineno">  557</span>&#160;  <span class="keywordflow">return</span> <a class="code" href="classqbpp_1_1easy__solver_1_1EasySolver.html#ae6e3c634ea34e47131d8e9a407906f4a">sol_holder_ptr_</a>-&gt;get_sol();</div>
<div class="line"><a name="l00558"></a><span class="lineno">  558</span>&#160;}</div>
<div class="line"><a name="l00559"></a><span class="lineno">  559</span>&#160; </div>
<div class="line"><a name="l00560"></a><span class="lineno">  560</span>&#160;} </div>
<div class="line"><a name="l00561"></a><span class="lineno">  561</span>&#160;} </div>
<div class="line"><a name="l00562"></a><span class="lineno">  562</span>&#160; </div>
<div class="line"><a name="l00563"></a><span class="lineno">  563</span>&#160; </div>
<div class="line"><a name="l00564"></a><span class="lineno">  564</span>&#160;<span class="preprocessor">#endif </span></div>
</div><!-- fragment --></div><!-- contents -->
<div class="ttc" id="aclassqbpp_1_1easy__solver_1_1EasySolver_html_a801ffcc65ab06fa8f6bd0cf1cface28a"><div class="ttname"><a href="classqbpp_1_1easy__solver_1_1EasySolver.html#a801ffcc65ab06fa8f6bd0cf1cface28a">qbpp::easy_solver::EasySolver::prev_energy_</a></div><div class="ttdeci">std::optional&lt; energy_t &gt; prev_energy_</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__easy__solver_8hpp_source.html#l00254">qbpp_easy_solver.hpp:254</a></div></div>
<div class="ttc" id="aclassqbpp_1_1easy__solver_1_1SolDelta_html_a28965e9da467b6c28f46caad2b0d6a37"><div class="ttname"><a href="classqbpp_1_1easy__solver_1_1SolDelta.html#a28965e9da467b6c28f46caad2b0d6a37">qbpp::easy_solver::SolDelta::best_sols_ptr</a></div><div class="ttdeci">const std::shared_ptr&lt; BestSols &gt; &amp; best_sols_ptr() const</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__easy__solver_8hpp_source.html#l00161">qbpp_easy_solver.hpp:161</a></div></div>
<div class="ttc" id="aclassqbpp_1_1easy__solver_1_1SolDelta_html_a80634501337c3b39960cf323dd075705"><div class="ttname"><a href="classqbpp_1_1easy__solver_1_1SolDelta.html#a80634501337c3b39960cf323dd075705">qbpp::easy_solver::SolDelta::flip_count_</a></div><div class="ttdeci">uint64_t flip_count_</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__easy__solver_8hpp_source.html#l00126">qbpp_easy_solver.hpp:126</a></div></div>
<div class="ttc" id="aclassqbpp_1_1easy__solver_1_1SolDelta_html_a4c83ce5d5273ec31abf361612cc7d0bb"><div class="ttname"><a href="classqbpp_1_1easy__solver_1_1SolDelta.html#a4c83ce5d5273ec31abf361612cc7d0bb">qbpp::easy_solver::SolDelta::get_delta</a></div><div class="ttdeci">energy_t get_delta(vindex_t i) const</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__easy__solver_8hpp_source.html#l00133">qbpp_easy_solver.hpp:133</a></div></div>
<div class="ttc" id="aclassqbpp_1_1easy__solver_1_1EasySolver_html_af79a776cac94fa618d27cd403ac9b2cf"><div class="ttname"><a href="classqbpp_1_1easy__solver_1_1EasySolver.html#af79a776cac94fa618d27cd403ac9b2cf">qbpp::easy_solver::EasySolver::get_sol</a></div><div class="ttdeci">qbpp::Sol get_sol() const</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__easy__solver_8hpp_source.html#l00289">qbpp_easy_solver.hpp:289</a></div></div>
<div class="ttc" id="aclassqbpp_1_1easy__solver_1_1EasySolver_html_ae9d620e2fd58c6aa01e6169957e551d9"><div class="ttname"><a href="classqbpp_1_1easy__solver_1_1EasySolver.html#ae9d620e2fd58c6aa01e6169957e551d9">qbpp::easy_solver::EasySolver::best_sols</a></div><div class="ttdeci">BestSols &amp; best_sols()</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__easy__solver_8hpp_source.html#l00334">qbpp_easy_solver.hpp:334</a></div></div>
<div class="ttc" id="aclassqbpp_1_1easy__solver_1_1SolDelta_html_ac82df38362383a0c302a979c0b394844"><div class="ttname"><a href="classqbpp_1_1easy__solver_1_1SolDelta.html#ac82df38362383a0c302a979c0b394844">qbpp::easy_solver::SolDelta::after_delta_updated</a></div><div class="ttdeci">virtual void after_delta_updated([[maybe_unused]] vindex_t update_delta_index)</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__easy__solver_8hpp_source.html#l00151">qbpp_easy_solver.hpp:151</a></div></div>
<div class="ttc" id="anamespaceqbpp_1_1easy__solver_html_ae5027899639f91d80883ae1ca2cf1ddd"><div class="ttname"><a href="namespaceqbpp_1_1easy__solver.html#ae5027899639f91d80883ae1ca2cf1ddd">qbpp::easy_solver::HashType</a></div><div class="ttdeci">uint64_t HashType</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__easy__solver_8hpp_source.html#l00052">qbpp_easy_solver.hpp:52</a></div></div>
<div class="ttc" id="aclassqbpp_1_1easy__solver_1_1EasySolver_html_ac66675c187e2add239134fd04df4e5ce"><div class="ttname"><a href="classqbpp_1_1easy__solver_1_1EasySolver.html#ac66675c187e2add239134fd04df4e5ce">qbpp::easy_solver::EasySolver::flip_count_mutex_</a></div><div class="ttdeci">std::mutex flip_count_mutex_</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__easy__solver_8hpp_source.html#l00251">qbpp_easy_solver.hpp:251</a></div></div>
<div class="ttc" id="aclassqbpp_1_1easy__solver_1_1EasySolver_html_a959d4f5bf764f394c9a99cb1cb6194f7"><div class="ttname"><a href="classqbpp_1_1easy__solver_1_1EasySolver.html#a959d4f5bf764f394c9a99cb1cb6194f7">qbpp::easy_solver::EasySolver::set_sol</a></div><div class="ttdeci">const qbpp::Sol &amp; set_sol(const qbpp::Sol &amp;sol)</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__easy__solver_8hpp_source.html#l00291">qbpp_easy_solver.hpp:291</a></div></div>
<div class="ttc" id="aclassqbpp_1_1easy__solver_1_1EasySolver_html_a737bb24b042e6d038ffd2b2a80aaab7f"><div class="ttname"><a href="classqbpp_1_1easy__solver_1_1EasySolver.html#a737bb24b042e6d038ffd2b2a80aaab7f">qbpp::easy_solver::EasySolver::callback_mutex_</a></div><div class="ttdeci">std::mutex callback_mutex_</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__easy__solver_8hpp_source.html#l00243">qbpp_easy_solver.hpp:243</a></div></div>
<div class="ttc" id="aclassqbpp_1_1easy__solver_1_1PosMinSolDelta_html_a7d159351964937d75adac8acad67a4d4"><div class="ttname"><a href="classqbpp_1_1easy__solver_1_1PosMinSolDelta.html#a7d159351964937d75adac8acad67a4d4">qbpp::easy_solver::PosMinSolDelta::after_delta_updated</a></div><div class="ttdeci">void after_delta_updated(vindex_t k) override</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__easy__solver_8hpp_source.html#l00207">qbpp_easy_solver.hpp:207</a></div></div>
<div class="ttc" id="aclassqbpp_1_1easy__solver_1_1EasySolver_html_a9a4b07b571358cf4627c0c5d2b138222"><div class="ttname"><a href="classqbpp_1_1easy__solver_1_1EasySolver.html#a9a4b07b571358cf4627c0c5d2b138222">qbpp::easy_solver::EasySolver::start_time_</a></div><div class="ttdeci">double start_time_</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__easy__solver_8hpp_source.html#l00249">qbpp_easy_solver.hpp:249</a></div></div>
<div class="ttc" id="astructqbpp_1_1easy__solver_1_1SolHash_html"><div class="ttname"><a href="structqbpp_1_1easy__solver_1_1SolHash.html">qbpp::easy_solver::SolHash</a></div><div class="ttdef"><b>Definition:</b> <a href="qbpp__easy__solver_8hpp_source.html#l00063">qbpp_easy_solver.hpp:63</a></div></div>
<div class="ttc" id="aclassqbpp_1_1easy__solver_1_1BestSols_html_a281472c9f50678102a7820e4368a3b58"><div class="ttname"><a href="classqbpp_1_1easy__solver_1_1BestSols.html#a281472c9f50678102a7820e4368a3b58">qbpp::easy_solver::BestSols::get</a></div><div class="ttdeci">const std::vector&lt; qbpp::Sol &gt; &amp; get() const</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__easy__solver_8hpp_source.html#l00110">qbpp_easy_solver.hpp:110</a></div></div>
<div class="ttc" id="aclassqbpp_1_1Sol_html_abdb4f6b779678720715f7845f264440f"><div class="ttname"><a href="classqbpp_1_1Sol.html#abdb4f6b779678720715f7845f264440f">qbpp::Sol::get</a></div><div class="ttdeci">var_val_t get(vindex_t index) const</div><div class="ttdef"><b>Definition:</b> <a href="qbpp_8hpp_source.html#l02243">qbpp.hpp:2243</a></div></div>
<div class="ttc" id="aclassqbpp_1_1easy__solver_1_1SolDelta_html_a9a87ab5a497acf8597110c7b54dd4278"><div class="ttname"><a href="classqbpp_1_1easy__solver_1_1SolDelta.html#a9a87ab5a497acf8597110c7b54dd4278">qbpp::easy_solver::SolDelta::neg_set_</a></div><div class="ttdeci">misc::MinHeap&lt; energy_t &gt; neg_set_</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__easy__solver_8hpp_source.html#l00120">qbpp_easy_solver.hpp:120</a></div></div>
<div class="ttc" id="aclassqbpp_1_1easy__solver_1_1PosMinSolDelta_html_a66bae180e06d9fb7ae8461ba81111223"><div class="ttname"><a href="classqbpp_1_1easy__solver_1_1PosMinSolDelta.html#a66bae180e06d9fb7ae8461ba81111223">qbpp::easy_solver::PosMinSolDelta::pos_min</a></div><div class="ttdeci">vindex_t pos_min() const</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__easy__solver_8hpp_source.html#l00199">qbpp_easy_solver.hpp:199</a></div></div>
<div class="ttc" id="aclassqbpp_1_1easy__solver_1_1SolDelta_html"><div class="ttname"><a href="classqbpp_1_1easy__solver_1_1SolDelta.html">qbpp::easy_solver::SolDelta</a></div><div class="ttdef"><b>Definition:</b> <a href="qbpp__easy__solver_8hpp_source.html#l00114">qbpp_easy_solver.hpp:114</a></div></div>
<div class="ttc" id="aclassqbpp_1_1easy__solver_1_1PosMinSolDelta_html_ad8811137073388d35654ad8d6f8db0f2"><div class="ttname"><a href="classqbpp_1_1easy__solver_1_1PosMinSolDelta.html#ad8811137073388d35654ad8d6f8db0f2">qbpp::easy_solver::PosMinSolDelta::print</a></div><div class="ttdeci">void print() const</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__easy__solver_8hpp_source.html#l00215">qbpp_easy_solver.hpp:215</a></div></div>
<div class="ttc" id="aclassqbpp_1_1impl_1_1BitVector_html"><div class="ttname"><a href="classqbpp_1_1impl_1_1BitVector.html">qbpp::impl::BitVector</a></div><div class="ttdef"><b>Definition:</b> <a href="qbpp_8hpp_source.html#l02011">qbpp.hpp:2011</a></div></div>
<div class="ttc" id="astructqbpp_1_1easy__solver_1_1SolHash_html_af8635a88d82c853e1521797eefe88969"><div class="ttname"><a href="structqbpp_1_1easy__solver_1_1SolHash.html#af8635a88d82c853e1521797eefe88969">qbpp::easy_solver::SolHash::operator()</a></div><div class="ttdeci">HashType operator()(const qbpp::Sol &amp;sol) const noexcept</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__easy__solver_8hpp_source.html#l00064">qbpp_easy_solver.hpp:64</a></div></div>
<div class="ttc" id="aclassqbpp_1_1easy__solver_1_1EasySolver_html_ae347d0227c297c799f5a5b6168c09add"><div class="ttname"><a href="classqbpp_1_1easy__solver_1_1EasySolver.html#ae347d0227c297c799f5a5b6168c09add">qbpp::easy_solver::EasySolver::get_thread_count</a></div><div class="ttdeci">size_t get_thread_count()</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__easy__solver_8hpp_source.html#l00287">qbpp_easy_solver.hpp:287</a></div></div>
<div class="ttc" id="aclassqbpp_1_1easy__solver_1_1EasySolver_html_ae6e3c634ea34e47131d8e9a407906f4a"><div class="ttname"><a href="classqbpp_1_1easy__solver_1_1EasySolver.html#ae6e3c634ea34e47131d8e9a407906f4a">qbpp::easy_solver::EasySolver::sol_holder_ptr_</a></div><div class="ttdeci">std::shared_ptr&lt; qbpp::SolHolder &gt; sol_holder_ptr_</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__easy__solver_8hpp_source.html#l00239">qbpp_easy_solver.hpp:239</a></div></div>
<div class="ttc" id="aclassqbpp_1_1easy__solver_1_1EasySolver_html_ad0ad1068e6105a3a216bd0c70de48ec6"><div class="ttname"><a href="classqbpp_1_1easy__solver_1_1EasySolver.html#ad0ad1068e6105a3a216bd0c70de48ec6">qbpp::easy_solver::EasySolver::enable_default_callback</a></div><div class="ttdeci">void enable_default_callback()</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__easy__solver_8hpp_source.html#l00299">qbpp_easy_solver.hpp:299</a></div></div>
<div class="ttc" id="aclassqbpp_1_1easy__solver_1_1BestSols_html_a13dff8120e443f0faeb0fae3218f9a00"><div class="ttname"><a href="classqbpp_1_1easy__solver_1_1BestSols.html#a13dff8120e443f0faeb0fae3218f9a00">qbpp::easy_solver::BestSols::worst_energy_</a></div><div class="ttdeci">energy_t worst_energy_</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__easy__solver_8hpp_source.html#l00073">qbpp_easy_solver.hpp:73</a></div></div>
<div class="ttc" id="aclassqbpp_1_1easy__solver_1_1TabuSolDelta_html_ac17653919bfa66975c06ed71e41d5b68"><div class="ttname"><a href="classqbpp_1_1easy__solver_1_1TabuSolDelta.html#ac17653919bfa66975c06ed71e41d5b68">qbpp::easy_solver::TabuSolDelta::tabu_</a></div><div class="ttdeci">misc::Tabu tabu_</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__easy__solver_8hpp_source.html#l00170">qbpp_easy_solver.hpp:170</a></div></div>
<div class="ttc" id="aclassqbpp_1_1misc_1_1MinHeap_html_ae61d02fae1382293fb465d2c52477dd0"><div class="ttname"><a href="classqbpp_1_1misc_1_1MinHeap.html#ae61d02fae1382293fb465d2c52477dd0">qbpp::misc::MinHeap::heap_size</a></div><div class="ttdeci">vindex_t heap_size() const</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__misc_8hpp_source.html#l00326">qbpp_misc.hpp:326</a></div></div>
<div class="ttc" id="anamespaceqbpp_html_a9c324f641779d1a7a3c7723f28d8cff4"><div class="ttname"><a href="namespaceqbpp.html#a9c324f641779d1a7a3c7723f28d8cff4">qbpp::energy_t</a></div><div class="ttdeci">ENERGY_TYPE energy_t</div><div class="ttdef"><b>Definition:</b> <a href="qbpp_8hpp_source.html#l00137">qbpp.hpp:137</a></div></div>
<div class="ttc" id="aclassqbpp_1_1QuadModel_html"><div class="ttname"><a href="classqbpp_1_1QuadModel.html">qbpp::QuadModel</a></div><div class="ttdef"><b>Definition:</b> <a href="qbpp_8hpp_source.html#l01892">qbpp.hpp:1892</a></div></div>
<div class="ttc" id="aclassqbpp_1_1easy__solver_1_1EasySolver_html_ab0f97f5dcd3ccce1dad6ea64fc2a994f"><div class="ttname"><a href="classqbpp_1_1easy__solver_1_1EasySolver.html#ab0f97f5dcd3ccce1dad6ea64fc2a994f">qbpp::easy_solver::EasySolver::best_sols_ptr</a></div><div class="ttdeci">const std::shared_ptr&lt; BestSols &gt; &amp; best_sols_ptr() const</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__easy__solver_8hpp_source.html#l00330">qbpp_easy_solver.hpp:330</a></div></div>
<div class="ttc" id="aclassqbpp_1_1QuadModel_html_a591e74de688dbff1339c2c9d2352df65"><div class="ttname"><a href="classqbpp_1_1QuadModel.html#a591e74de688dbff1339c2c9d2352df65">qbpp::QuadModel::linear</a></div><div class="ttdeci">const std::vector&lt; coeff_t &gt; &amp; linear() const</div><div class="ttdef"><b>Definition:</b> <a href="qbpp_8hpp_source.html#l01967">qbpp.hpp:1967</a></div></div>
<div class="ttc" id="aclassqbpp_1_1easy__solver_1_1EasySolver_html_ae75de081bc90bd9b0101a1ca64b3b43a"><div class="ttname"><a href="classqbpp_1_1easy__solver_1_1EasySolver.html#ae75de081bc90bd9b0101a1ca64b3b43a">qbpp::easy_solver::EasySolver::get_flip_count</a></div><div class="ttdeci">uint64_t get_flip_count() const</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__easy__solver_8hpp_source.html#l00297">qbpp_easy_solver.hpp:297</a></div></div>
<div class="ttc" id="aclassqbpp_1_1easy__solver_1_1SolDelta_html_a4288d75e854df11c4023461d03c319e3"><div class="ttname"><a href="classqbpp_1_1easy__solver_1_1SolDelta.html#a4288d75e854df11c4023461d03c319e3">qbpp::easy_solver::SolDelta::get_flip_count</a></div><div class="ttdeci">uint64_t get_flip_count() const</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__easy__solver_8hpp_source.html#l00159">qbpp_easy_solver.hpp:159</a></div></div>
<div class="ttc" id="aclassqbpp_1_1misc_1_1RandomEngine_html_ad836550b67c1c3784cf95a23d43bcd96"><div class="ttname"><a href="classqbpp_1_1misc_1_1RandomEngine.html#ad836550b67c1c3784cf95a23d43bcd96">qbpp::misc::RandomEngine::gen</a></div><div class="ttdeci">static T gen(T n)</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__misc_8hpp_source.html#l00090">qbpp_misc.hpp:90</a></div></div>
<div class="ttc" id="aclassqbpp_1_1easy__solver_1_1EasySolver_html_a79747cef9d1d81d1d4a0c0514646bbce"><div class="ttname"><a href="classqbpp_1_1easy__solver_1_1EasySolver.html#a79747cef9d1d81d1d4a0c0514646bbce">qbpp::easy_solver::EasySolver::best_sols</a></div><div class="ttdeci">const BestSols &amp; best_sols() const</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__easy__solver_8hpp_source.html#l00341">qbpp_easy_solver.hpp:341</a></div></div>
<div class="ttc" id="aclassqbpp_1_1easy__solver_1_1EasySolver_html_a6e57baef4356ac67e7b25e7a31d2a8f6"><div class="ttname"><a href="classqbpp_1_1easy__solver_1_1EasySolver.html#a6e57baef4356ac67e7b25e7a31d2a8f6">qbpp::easy_solver::EasySolver::~EasySolver</a></div><div class="ttdeci">virtual ~EasySolver()=default</div></div>
<div class="ttc" id="aclassqbpp_1_1easy__solver_1_1BestSols_html"><div class="ttname"><a href="classqbpp_1_1easy__solver_1_1BestSols.html">qbpp::easy_solver::BestSols</a></div><div class="ttdef"><b>Definition:</b> <a href="qbpp__easy__solver_8hpp_source.html#l00069">qbpp_easy_solver.hpp:69</a></div></div>
<div class="ttc" id="aclassqbpp_1_1easy__solver_1_1EasySolver_html_a514d671a4aced019d797dfbe2240b46c"><div class="ttname"><a href="classqbpp_1_1easy__solver_1_1EasySolver.html#a514d671a4aced019d797dfbe2240b46c">qbpp::easy_solver::EasySolver::enable_best_sols</a></div><div class="ttdeci">void enable_best_sols(size_t max_best_sol_count)</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__easy__solver_8hpp_source.html#l00321">qbpp_easy_solver.hpp:321</a></div></div>
<div class="ttc" id="aclassqbpp_1_1easy__solver_1_1BestSols_html_ad7b7e97daf1860cafd68fe9a743f25cf"><div class="ttname"><a href="classqbpp_1_1easy__solver_1_1BestSols.html#ad7b7e97daf1860cafd68fe9a743f25cf">qbpp::easy_solver::BestSols::get</a></div><div class="ttdeci">const qbpp::Sol &amp; get(size_t i) const</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__easy__solver_8hpp_source.html#l00111">qbpp_easy_solver.hpp:111</a></div></div>
<div class="ttc" id="aclassqbpp_1_1misc_1_1MinHeap_html_ad5e42b4bd3f622c159a7863a1b7c011f"><div class="ttname"><a href="classqbpp_1_1misc_1_1MinHeap.html#ad5e42b4bd3f622c159a7863a1b7c011f">qbpp::misc::MinHeap::get_first</a></div><div class="ttdeci">vindex_t get_first() const</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__misc_8hpp_source.html#l00327">qbpp_misc.hpp:327</a></div></div>
<div class="ttc" id="aclassqbpp_1_1easy__solver_1_1TabuSolDelta_html_af665d76e9e237ecc6e0280f3b5db2679"><div class="ttname"><a href="classqbpp_1_1easy__solver_1_1TabuSolDelta.html#af665d76e9e237ecc6e0280f3b5db2679">qbpp::easy_solver::TabuSolDelta::TabuSolDelta</a></div><div class="ttdeci">TabuSolDelta(const EasySolver &amp;easy_solver, vindex_t tabu_size)</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__easy__solver_8hpp_source.html#l00173">qbpp_easy_solver.hpp:173</a></div></div>
<div class="ttc" id="aclassqbpp_1_1easy__solver_1_1EasySolver_html_a5114362571468d84f507f9e3d17e0995"><div class="ttname"><a href="classqbpp_1_1easy__solver_1_1EasySolver.html#a5114362571468d84f507f9e3d17e0995">qbpp::easy_solver::EasySolver::target_energy_</a></div><div class="ttdeci">std::optional&lt; energy_t &gt; target_energy_</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__easy__solver_8hpp_source.html#l00235">qbpp_easy_solver.hpp:235</a></div></div>
<div class="ttc" id="aclassqbpp_1_1easy__solver_1_1BestSols_html_a2acd14bf69fb55561c2c859d8c2ed771"><div class="ttname"><a href="classqbpp_1_1easy__solver_1_1BestSols.html#a2acd14bf69fb55561c2c859d8c2ed771">qbpp::easy_solver::BestSols::size</a></div><div class="ttdeci">size_t size() const</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__easy__solver_8hpp_source.html#l00109">qbpp_easy_solver.hpp:109</a></div></div>
<div class="ttc" id="aclassqbpp_1_1easy__solver_1_1EasySolver_html_a41a71df4e0f7029335c0bcf9f407881e"><div class="ttname"><a href="classqbpp_1_1easy__solver_1_1EasySolver.html#a41a71df4e0f7029335c0bcf9f407881e">qbpp::easy_solver::EasySolver::is_internal_sol_holder_</a></div><div class="ttdeci">const bool is_internal_sol_holder_</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__easy__solver_8hpp_source.html#l00237">qbpp_easy_solver.hpp:237</a></div></div>
<div class="ttc" id="anamespaceqbpp_html_a35435559d3a02f18b80ae5554dc65c75"><div class="ttname"><a href="namespaceqbpp.html#a35435559d3a02f18b80ae5554dc65c75">qbpp::uint128_t</a></div><div class="ttdeci">boost::multiprecision::uint128_t uint128_t</div><div class="ttdef"><b>Definition:</b> <a href="qbpp_8hpp_source.html#l00112">qbpp.hpp:112</a></div></div>
<div class="ttc" id="aclassqbpp_1_1easy__solver_1_1TabuSolDelta_html_a57c97c62983526b68ab764846a7f3060"><div class="ttname"><a href="classqbpp_1_1easy__solver_1_1TabuSolDelta.html#a57c97c62983526b68ab764846a7f3060">qbpp::easy_solver::TabuSolDelta::non_tabu_random</a></div><div class="ttdeci">vindex_t non_tabu_random()</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__easy__solver_8hpp_source.html#l00186">qbpp_easy_solver.hpp:186</a></div></div>
<div class="ttc" id="aclassqbpp_1_1misc_1_1MinHeap_html_aa209280cf2cfd0dd5ee04131bc96a582"><div class="ttname"><a href="classqbpp_1_1misc_1_1MinHeap.html#aa209280cf2cfd0dd5ee04131bc96a582">qbpp::misc::MinHeap::erase</a></div><div class="ttdeci">void erase(vindex_t index)</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__misc_8hpp_source.html#l00348">qbpp_misc.hpp:348</a></div></div>
<div class="ttc" id="aclassqbpp_1_1easy__solver_1_1PosMinSolDelta_html_af803882846692d994043dfc1fa9c4206"><div class="ttname"><a href="classqbpp_1_1easy__solver_1_1PosMinSolDelta.html#af803882846692d994043dfc1fa9c4206">qbpp::easy_solver::PosMinSolDelta::~PosMinSolDelta</a></div><div class="ttdeci">virtual ~PosMinSolDelta()=default</div></div>
<div class="ttc" id="aclassqbpp_1_1easy__solver_1_1EasySolver_html_a8015a24fa02f969e216516657a5868f9"><div class="ttname"><a href="classqbpp_1_1easy__solver_1_1EasySolver.html#a8015a24fa02f969e216516657a5868f9">qbpp::easy_solver::EasySolver::best_sols_ptr</a></div><div class="ttdeci">std::shared_ptr&lt; BestSols &gt; &amp; best_sols_ptr()</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__easy__solver_8hpp_source.html#l00329">qbpp_easy_solver.hpp:329</a></div></div>
<div class="ttc" id="aclassqbpp_1_1easy__solver_1_1PosMinSolDelta_html"><div class="ttname"><a href="classqbpp_1_1easy__solver_1_1PosMinSolDelta.html">qbpp::easy_solver::PosMinSolDelta</a></div><div class="ttdef"><b>Definition:</b> <a href="qbpp__easy__solver_8hpp_source.html#l00189">qbpp_easy_solver.hpp:189</a></div></div>
<div class="ttc" id="aclassqbpp_1_1misc_1_1MinHeap_html_ae0d409ca2b14bae78db373f1eecbd99c"><div class="ttname"><a href="classqbpp_1_1misc_1_1MinHeap.html#ae0d409ca2b14bae78db373f1eecbd99c">qbpp::misc::MinHeap::print</a></div><div class="ttdeci">void print(const std::string &amp;prefix) const</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__misc_8hpp_source.html#l00365">qbpp_misc.hpp:365</a></div></div>
<div class="ttc" id="aclassqbpp_1_1misc_1_1Tabu_html_ac79f02e24591065f8224dc1aa5355e20"><div class="ttname"><a href="classqbpp_1_1misc_1_1Tabu.html#ac79f02e24591065f8224dc1aa5355e20">qbpp::misc::Tabu::non_tabu_random</a></div><div class="ttdeci">vindex_t non_tabu_random()</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__misc_8hpp_source.html#l00484">qbpp_misc.hpp:484</a></div></div>
<div class="ttc" id="aclassqbpp_1_1easy__solver_1_1EasySolver_html_a011d395b4907d86b49efdcd43fbf6d3b"><div class="ttname"><a href="classqbpp_1_1easy__solver_1_1EasySolver.html#a011d395b4907d86b49efdcd43fbf6d3b">qbpp::easy_solver::EasySolver::get_target_energy</a></div><div class="ttdeci">const std::optional&lt; energy_t &gt; get_target_energy() const</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__easy__solver_8hpp_source.html#l00271">qbpp_easy_solver.hpp:271</a></div></div>
<div class="ttc" id="aclassqbpp_1_1easy__solver_1_1SolDelta_html_a1a107d8974386a07d201e88fb2c9aed9"><div class="ttname"><a href="classqbpp_1_1easy__solver_1_1SolDelta.html#a1a107d8974386a07d201e88fb2c9aed9">qbpp::easy_solver::SolDelta::flip</a></div><div class="ttdeci">void flip(vindex_t index) override</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__easy__solver_8hpp_source.html#l00364">qbpp_easy_solver.hpp:364</a></div></div>
<div class="ttc" id="aclassqbpp_1_1easy__solver_1_1EasySolver_html_ad8b81b7d0fe63ed7b0f00d5ac9fad2e1"><div class="ttname"><a href="classqbpp_1_1easy__solver_1_1EasySolver.html#ad8b81b7d0fe63ed7b0f00d5ac9fad2e1">qbpp::easy_solver::EasySolver::get_time_limit</a></div><div class="ttdeci">const std::optional&lt; uint32_t &gt; get_time_limit() const</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__easy__solver_8hpp_source.html#l00269">qbpp_easy_solver.hpp:269</a></div></div>
<div class="ttc" id="aclassqbpp_1_1Sol_html_aae6d875ede912bcbf6254b74ffc66018"><div class="ttname"><a href="classqbpp_1_1Sol.html#aae6d875ede912bcbf6254b74ffc66018">qbpp::Sol::flip_bit_add_delta</a></div><div class="ttdeci">virtual void flip_bit_add_delta(vindex_t index, energy_t delta)</div><div class="ttdef"><b>Definition:</b> <a href="qbpp_8hpp_source.html#l02292">qbpp.hpp:2292</a></div></div>
<div class="ttc" id="aclassqbpp_1_1easy__solver_1_1BestSols_html_aff80d3f4bfebc38e4799d6e0fd78f95d"><div class="ttname"><a href="classqbpp_1_1easy__solver_1_1BestSols.html#aff80d3f4bfebc38e4799d6e0fd78f95d">qbpp::easy_solver::BestSols::max_best_sol_count_</a></div><div class="ttdeci">const size_t max_best_sol_count_</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__easy__solver_8hpp_source.html#l00070">qbpp_easy_solver.hpp:70</a></div></div>
<div class="ttc" id="aclassqbpp_1_1QuadModel_html_a6624038d4ff117176c37de2d1260f471"><div class="ttname"><a href="classqbpp_1_1QuadModel.html#a6624038d4ff117176c37de2d1260f471">qbpp::QuadModel::quadratic</a></div><div class="ttdeci">const std::vector&lt; std::vector&lt; std::pair&lt; vindex_t, coeff_t &gt; &gt; &gt; &amp; quadratic() const</div><div class="ttdef"><b>Definition:</b> <a href="qbpp_8hpp_source.html#l01971">qbpp.hpp:1971</a></div></div>
<div class="ttc" id="aclassqbpp_1_1easy__solver_1_1EasySolver_html_aced832de618720dce416415ba5f58c5c"><div class="ttname"><a href="classqbpp_1_1easy__solver_1_1EasySolver.html#aced832de618720dce416415ba5f58c5c">qbpp::easy_solver::EasySolver::thread_count_</a></div><div class="ttdeci">size_t thread_count_</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__easy__solver_8hpp_source.html#l00231">qbpp_easy_solver.hpp:231</a></div></div>
<div class="ttc" id="aclassqbpp_1_1misc_1_1RandomGenerator_html_a95308c2300aeef9a881ba97786367977"><div class="ttname"><a href="classqbpp_1_1misc_1_1RandomGenerator.html#a95308c2300aeef9a881ba97786367977">qbpp::misc::RandomGenerator::gen</a></div><div class="ttdeci">static uint64_t gen()</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__misc_8hpp_source.html#l00116">qbpp_misc.hpp:116</a></div></div>
<div class="ttc" id="aclassqbpp_1_1easy__solver_1_1EasySolver_html_a2511d160e47784e4543aa7a8efb4194a"><div class="ttname"><a href="classqbpp_1_1easy__solver_1_1EasySolver.html#a2511d160e47784e4543aa7a8efb4194a">qbpp::easy_solver::EasySolver::EasySolver</a></div><div class="ttdeci">EasySolver(const qbpp::QuadModel &amp;quad_model, std::shared_ptr&lt; qbpp::SolHolder &gt; sol_holder_ptr=nullptr)</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__easy__solver_8hpp_source.html#l00258">qbpp_easy_solver.hpp:258</a></div></div>
<div class="ttc" id="aclassqbpp_1_1easy__solver_1_1BestSols_html_a50e4d669aace640e64c43cebac64dc78"><div class="ttname"><a href="classqbpp_1_1easy__solver_1_1BestSols.html#a50e4d669aace640e64c43cebac64dc78">qbpp::easy_solver::BestSols::insert_if_better</a></div><div class="ttdeci">void insert_if_better(const qbpp::Sol &amp;sol)</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__easy__solver_8hpp_source.html#l00079">qbpp_easy_solver.hpp:79</a></div></div>
<div class="ttc" id="aclassqbpp_1_1easy__solver_1_1EasySolver_html_a8d5bc85759b94bc1c1bf2db59fc323b0"><div class="ttname"><a href="classqbpp_1_1easy__solver_1_1EasySolver.html#a8d5bc85759b94bc1c1bf2db59fc323b0">qbpp::easy_solver::EasySolver::search</a></div><div class="ttdeci">qbpp::Sol search(const qbpp::Sol &amp;initial_sol)</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__easy__solver_8hpp_source.html#l00316">qbpp_easy_solver.hpp:316</a></div></div>
<div class="ttc" id="aclassqbpp_1_1misc_1_1MinSet_html_a63576a251e5c467e6f4b1eae8a1b1f03"><div class="ttname"><a href="classqbpp_1_1misc_1_1MinSet.html#a63576a251e5c467e6f4b1eae8a1b1f03">qbpp::misc::MinSet::insert</a></div><div class="ttdeci">void insert(vindex_t index, T delta)</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__misc_8hpp_source.html#l00236">qbpp_misc.hpp:236</a></div></div>
<div class="ttc" id="aclassqbpp_1_1misc_1_1MinHeap_html_acba907f46558aefc996cc8aafa381249"><div class="ttname"><a href="classqbpp_1_1misc_1_1MinHeap.html#acba907f46558aefc996cc8aafa381249">qbpp::misc::MinHeap::select_at_random</a></div><div class="ttdeci">vindex_t select_at_random() const</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__misc_8hpp_source.html#l00333">qbpp_misc.hpp:333</a></div></div>
<div class="ttc" id="aclassqbpp_1_1easy__solver_1_1TabuSolDelta_html_abddd3367c047935e82e517c421cb5a9a"><div class="ttname"><a href="classqbpp_1_1easy__solver_1_1TabuSolDelta.html#abddd3367c047935e82e517c421cb5a9a">qbpp::easy_solver::TabuSolDelta::flip</a></div><div class="ttdeci">void flip(vindex_t index) override</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__easy__solver_8hpp_source.html#l00179">qbpp_easy_solver.hpp:179</a></div></div>
<div class="ttc" id="aclassqbpp_1_1easy__solver_1_1SolDelta_html_a5675af40ee9d8ec2f39a61ff5d1ac322"><div class="ttname"><a href="classqbpp_1_1easy__solver_1_1SolDelta.html#a5675af40ee9d8ec2f39a61ff5d1ac322">qbpp::easy_solver::SolDelta::neg_min</a></div><div class="ttdeci">vindex_t neg_min() const</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__easy__solver_8hpp_source.html#l00145">qbpp_easy_solver.hpp:145</a></div></div>
<div class="ttc" id="aclassqbpp_1_1QuadModel_html_a16af851530b1f63aca9baa8ec67ed01f"><div class="ttname"><a href="classqbpp_1_1QuadModel.html#a16af851530b1f63aca9baa8ec67ed01f">qbpp::QuadModel::term_count</a></div><div class="ttdeci">size_t term_count(vindex_t deg) const</div><div class="ttdef"><b>Definition:</b> <a href="qbpp_8hpp_source.html#l01992">qbpp.hpp:1992</a></div></div>
<div class="ttc" id="aqbpp_8hpp_html"><div class="ttname"><a href="qbpp_8hpp.html">qbpp.hpp</a></div><div class="ttdoc">QUBO++, a C++ library for generating expressions for binary and spin variables.</div></div>
<div class="ttc" id="aclassqbpp_1_1impl_1_1BitVector_html_ae5bea2dd45ef269fee44932908717e1e"><div class="ttname"><a href="classqbpp_1_1impl_1_1BitVector.html#ae5bea2dd45ef269fee44932908717e1e">qbpp::impl::BitVector::get_bits_ptr</a></div><div class="ttdeci">const uint64_t * get_bits_ptr() const</div><div class="ttdef"><b>Definition:</b> <a href="qbpp_8hpp_source.html#l02119">qbpp.hpp:2119</a></div></div>
<div class="ttc" id="aclassqbpp_1_1easy__solver_1_1EasySolver_html_a5aca8737256d008df76e3f53cce1f53a"><div class="ttname"><a href="classqbpp_1_1easy__solver_1_1EasySolver.html#a5aca8737256d008df76e3f53cce1f53a">qbpp::easy_solver::EasySolver::var_count</a></div><div class="ttdeci">vindex_t var_count() const</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__easy__solver_8hpp_source.html#l00279">qbpp_easy_solver.hpp:279</a></div></div>
<div class="ttc" id="aclassqbpp_1_1easy__solver_1_1SolDelta_html_acf11d3f5b86a20077d4c20dc9c18e044"><div class="ttname"><a href="classqbpp_1_1easy__solver_1_1SolDelta.html#acf11d3f5b86a20077d4c20dc9c18e044">qbpp::easy_solver::SolDelta::greedy</a></div><div class="ttdeci">void greedy()</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__easy__solver_8hpp_source.html#l00405">qbpp_easy_solver.hpp:405</a></div></div>
<div class="ttc" id="aclassqbpp_1_1easy__solver_1_1EasySolver_html_aceec9cd9048576610b0cc1fba0b792c5"><div class="ttname"><a href="classqbpp_1_1easy__solver_1_1EasySolver.html#aceec9cd9048576610b0cc1fba0b792c5">qbpp::easy_solver::EasySolver::set_target_energy</a></div><div class="ttdeci">void set_target_energy(energy_t energy)</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__easy__solver_8hpp_source.html#l00283">qbpp_easy_solver.hpp:283</a></div></div>
<div class="ttc" id="aclassqbpp_1_1easy__solver_1_1SolDelta_html_a9f9ac387600b8004d161c6c4a17e6446"><div class="ttname"><a href="classqbpp_1_1easy__solver_1_1SolDelta.html#a9f9ac387600b8004d161c6c4a17e6446">qbpp::easy_solver::SolDelta::sol_holder_ptr_</a></div><div class="ttdeci">const std::shared_ptr&lt; qbpp::SolHolder &gt; sol_holder_ptr_</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__easy__solver_8hpp_source.html#l00122">qbpp_easy_solver.hpp:122</a></div></div>
<div class="ttc" id="aqbpp__misc_8hpp_html"><div class="ttname"><a href="qbpp__misc_8hpp.html">qbpp_misc.hpp</a></div><div class="ttdoc">A miscellaneous library used for sample programs of the QUBO++ library.</div></div>
<div class="ttc" id="anamespaceqbpp_html_ac0b50505a1738411ca7c96487b296234"><div class="ttname"><a href="namespaceqbpp.html#ac0b50505a1738411ca7c96487b296234">qbpp::vindex_limit</a></div><div class="ttdeci">const vindex_t vindex_limit</div><div class="ttdef"><b>Definition:</b> <a href="qbpp_8hpp_source.html#l00131">qbpp.hpp:131</a></div></div>
<div class="ttc" id="aclassqbpp_1_1misc_1_1MinSet_html_ad9f980fba77f1f8d017354953a5cb434"><div class="ttname"><a href="classqbpp_1_1misc_1_1MinSet.html#ad9f980fba77f1f8d017354953a5cb434">qbpp::misc::MinSet::var_count</a></div><div class="ttdeci">vindex_t var_count() const</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__misc_8hpp_source.html#l00234">qbpp_misc.hpp:234</a></div></div>
<div class="ttc" id="aclassqbpp_1_1Sol_html_a11752b0b3742bec8da842db87ba94230"><div class="ttname"><a href="classqbpp_1_1Sol.html#a11752b0b3742bec8da842db87ba94230">qbpp::Sol::quad_model_</a></div><div class="ttdeci">const QuadModel quad_model_</div><div class="ttdef"><b>Definition:</b> <a href="qbpp_8hpp_source.html#l02184">qbpp.hpp:2184</a></div></div>
<div class="ttc" id="aclassqbpp_1_1easy__solver_1_1SolDelta_html_a948cb17c3b50104d7dbd333a88909757"><div class="ttname"><a href="classqbpp_1_1easy__solver_1_1SolDelta.html#a948cb17c3b50104d7dbd333a88909757">qbpp::easy_solver::SolDelta::before_delta_updated</a></div><div class="ttdeci">virtual void before_delta_updated([[maybe_unused]] vindex_t update_delta_index)</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__easy__solver_8hpp_source.html#l00148">qbpp_easy_solver.hpp:148</a></div></div>
<div class="ttc" id="aclassqbpp_1_1misc_1_1Tabu_html"><div class="ttname"><a href="classqbpp_1_1misc_1_1Tabu.html">qbpp::misc::Tabu</a></div><div class="ttdef"><b>Definition:</b> <a href="qbpp__misc_8hpp_source.html#l00412">qbpp_misc.hpp:412</a></div></div>
<div class="ttc" id="aclassqbpp_1_1easy__solver_1_1EasySolver_html_a9a61ebfb6366c734e7af2b08890753f0"><div class="ttname"><a href="classqbpp_1_1easy__solver_1_1EasySolver.html#a9a61ebfb6366c734e7af2b08890753f0">qbpp::easy_solver::EasySolver::search</a></div><div class="ttdeci">qbpp::Sol search(bool has_initial_sol=false)</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__easy__solver_8hpp_source.html#l00546">qbpp_easy_solver.hpp:546</a></div></div>
<div class="ttc" id="aclassqbpp_1_1misc_1_1Tabu_html_a549866fee09f51bb17a90e3a1831bf77"><div class="ttname"><a href="classqbpp_1_1misc_1_1Tabu.html#a549866fee09f51bb17a90e3a1831bf77">qbpp::misc::Tabu::insert</a></div><div class="ttdeci">std::optional&lt; vindex_t &gt; insert(vindex_t index, bool must_be_new)</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__misc_8hpp_source.html#l00439">qbpp_misc.hpp:439</a></div></div>
<div class="ttc" id="aclassqbpp_1_1easy__solver_1_1EasySolver_html_a41b03b454ad9acb22e785df8bba2d717"><div class="ttname"><a href="classqbpp_1_1easy__solver_1_1EasySolver.html#a41b03b454ad9acb22e785df8bba2d717">qbpp::easy_solver::EasySolver::flip_count_</a></div><div class="ttdeci">uint64_t flip_count_</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__easy__solver_8hpp_source.html#l00252">qbpp_easy_solver.hpp:252</a></div></div>
<div class="ttc" id="aclassqbpp_1_1easy__solver_1_1TabuSolDelta_html_a514b9fd7511de63ea113064e40c50862"><div class="ttname"><a href="classqbpp_1_1easy__solver_1_1TabuSolDelta.html#a514b9fd7511de63ea113064e40c50862">qbpp::easy_solver::TabuSolDelta::tabu_has</a></div><div class="ttdeci">bool tabu_has(vindex_t index) const</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__easy__solver_8hpp_source.html#l00184">qbpp_easy_solver.hpp:184</a></div></div>
<div class="ttc" id="aclassqbpp_1_1easy__solver_1_1EasySolver_html_a1d55e04ffc11f6c8460f184efc6d2ea3"><div class="ttname"><a href="classqbpp_1_1easy__solver_1_1EasySolver.html#a1d55e04ffc11f6c8460f184efc6d2ea3">qbpp::easy_solver::EasySolver::quad_model_</a></div><div class="ttdeci">const qbpp::QuadModel quad_model_</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__easy__solver_8hpp_source.html#l00227">qbpp_easy_solver.hpp:227</a></div></div>
<div class="ttc" id="aclassqbpp_1_1misc_1_1MinSet_html_abfb846a3ff4277f39e5cc4798933ca80"><div class="ttname"><a href="classqbpp_1_1misc_1_1MinSet.html#abfb846a3ff4277f39e5cc4798933ca80">qbpp::misc::MinSet::erase</a></div><div class="ttdeci">void erase(vindex_t index, T delta)</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__misc_8hpp_source.html#l00245">qbpp_misc.hpp:245</a></div></div>
<div class="ttc" id="aclassqbpp_1_1easy__solver_1_1PosMinSolDelta_html_a1b2b9ca3a340f92871de75954d335d40"><div class="ttname"><a href="classqbpp_1_1easy__solver_1_1PosMinSolDelta.html#a1b2b9ca3a340f92871de75954d335d40">qbpp::easy_solver::PosMinSolDelta::pos_set_</a></div><div class="ttdeci">misc::MinHeap&lt; energy_t &gt; pos_set_</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__easy__solver_8hpp_source.html#l00190">qbpp_easy_solver.hpp:190</a></div></div>
<div class="ttc" id="aclassqbpp_1_1easy__solver_1_1TabuSolDelta_html_a26d698066983b574a1a8d64fe879f5ee"><div class="ttname"><a href="classqbpp_1_1easy__solver_1_1TabuSolDelta.html#a26d698066983b574a1a8d64fe879f5ee">qbpp::easy_solver::TabuSolDelta::~TabuSolDelta</a></div><div class="ttdeci">virtual ~TabuSolDelta()=default</div></div>
<div class="ttc" id="anamespaceqbpp_1_1easy__solver_html_a99d432f46cb5703bcf15a858bc58c742"><div class="ttname"><a href="namespaceqbpp_1_1easy__solver.html#a99d432f46cb5703bcf15a858bc58c742">qbpp::easy_solver::bit_vector_hash</a></div><div class="ttdeci">HashType bit_vector_hash(const qbpp::impl::BitVector &amp;bit_vector)</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__easy__solver_8hpp_source.html#l00055">qbpp_easy_solver.hpp:55</a></div></div>
<div class="ttc" id="anamespaceqbpp_html_a4184afa358539dbcf5f35e63b49ce05b"><div class="ttname"><a href="namespaceqbpp.html#a4184afa358539dbcf5f35e63b49ce05b">qbpp::get_time</a></div><div class="ttdeci">double get_time()</div><div class="ttdef"><b>Definition:</b> <a href="qbpp_8hpp_source.html#l05311">qbpp.hpp:5311</a></div></div>
<div class="ttc" id="aclassqbpp_1_1misc_1_1MinSet_html_aa534cb8cd4b9d3646c243ab8c8d448e1"><div class="ttname"><a href="classqbpp_1_1misc_1_1MinSet.html#aa534cb8cd4b9d3646c243ab8c8d448e1">qbpp::misc::MinSet::get_first</a></div><div class="ttdeci">vindex_t get_first() const</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__misc_8hpp_source.html#l00254">qbpp_misc.hpp:254</a></div></div>
<div class="ttc" id="aclassqbpp_1_1easy__solver_1_1SolDelta_html_aaa3700bdd76001664905d47719a3e9da"><div class="ttname"><a href="classqbpp_1_1easy__solver_1_1SolDelta.html#aaa3700bdd76001664905d47719a3e9da">qbpp::easy_solver::SolDelta::delta_</a></div><div class="ttdeci">std::vector&lt; energy_t &gt; delta_</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__easy__solver_8hpp_source.html#l00118">qbpp_easy_solver.hpp:118</a></div></div>
<div class="ttc" id="anamespaceqbpp_html"><div class="ttname"><a href="namespaceqbpp.html">qbpp</a></div><div class="ttdoc">Generates a QUBO Expression for the Graph Coloring Problem using QUBO++ library.</div><div class="ttdef"><b>Definition:</b> <a href="qbpp_8hpp_source.html#l00052">qbpp.hpp:52</a></div></div>
<div class="ttc" id="aclassqbpp_1_1easy__solver_1_1EasySolver_html_adae92e04d52216b23f89a0e8e22f238e"><div class="ttname"><a href="classqbpp_1_1easy__solver_1_1EasySolver.html#adae92e04d52216b23f89a0e8e22f238e">qbpp::easy_solver::EasySolver::get_tts</a></div><div class="ttdeci">double get_tts() const</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__easy__solver_8hpp_source.html#l00295">qbpp_easy_solver.hpp:295</a></div></div>
<div class="ttc" id="aclassqbpp_1_1easy__solver_1_1TabuSolDelta_html"><div class="ttname"><a href="classqbpp_1_1easy__solver_1_1TabuSolDelta.html">qbpp::easy_solver::TabuSolDelta</a></div><div class="ttdef"><b>Definition:</b> <a href="qbpp__easy__solver_8hpp_source.html#l00166">qbpp_easy_solver.hpp:166</a></div></div>
<div class="ttc" id="aclassqbpp_1_1easy__solver_1_1SolDelta_html_ad0505230c61ae317b5414f06327daeb4"><div class="ttname"><a href="classqbpp_1_1easy__solver_1_1SolDelta.html#ad0505230c61ae317b5414f06327daeb4">qbpp::easy_solver::SolDelta::random_flip</a></div><div class="ttdeci">void random_flip(size_t iteration)</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__easy__solver_8hpp_source.html#l00413">qbpp_easy_solver.hpp:413</a></div></div>
<div class="ttc" id="aclassqbpp_1_1easy__solver_1_1EasySolver_html_a90e3ce85062b4e25bab775948e1e56f1"><div class="ttname"><a href="classqbpp_1_1easy__solver_1_1EasySolver.html#a90e3ce85062b4e25bab775948e1e56f1">qbpp::easy_solver::EasySolver::get_quad_model</a></div><div class="ttdeci">const qbpp::QuadModel &amp; get_quad_model() const</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__easy__solver_8hpp_source.html#l00267">qbpp_easy_solver.hpp:267</a></div></div>
<div class="ttc" id="anamespaceqbpp_html_a5cf436100ede362797d0c0501ea50a5a"><div class="ttname"><a href="namespaceqbpp.html#a5cf436100ede362797d0c0501ea50a5a">qbpp::vindex_t</a></div><div class="ttdeci">uint32_t vindex_t</div><div class="ttdef"><b>Definition:</b> <a href="qbpp_8hpp_source.html#l00129">qbpp.hpp:129</a></div></div>
<div class="ttc" id="aclassqbpp_1_1easy__solver_1_1SolDelta_html_a688778b8eb570de31703bf26d38cf01d"><div class="ttname"><a href="classqbpp_1_1easy__solver_1_1SolDelta.html#a688778b8eb570de31703bf26d38cf01d">qbpp::easy_solver::SolDelta::easy_solver_</a></div><div class="ttdeci">const EasySolver &amp; easy_solver_</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__easy__solver_8hpp_source.html#l00116">qbpp_easy_solver.hpp:116</a></div></div>
<div class="ttc" id="aclassqbpp_1_1easy__solver_1_1EasySolver_html_ada3fbb50b6993a825681a4a712fd61b7"><div class="ttname"><a href="classqbpp_1_1easy__solver_1_1EasySolver.html#ada3fbb50b6993a825681a4a712fd61b7">qbpp::easy_solver::EasySolver::sol_holder_ptr</a></div><div class="ttdeci">const std::shared_ptr&lt; qbpp::SolHolder &gt; sol_holder_ptr() const</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__easy__solver_8hpp_source.html#l00275">qbpp_easy_solver.hpp:275</a></div></div>
<div class="ttc" id="aclassqbpp_1_1easy__solver_1_1EasySolver_html_a01d3912bcf0c4741103f85106fc72037"><div class="ttname"><a href="classqbpp_1_1easy__solver_1_1EasySolver.html#a01d3912bcf0c4741103f85106fc72037">qbpp::easy_solver::EasySolver::best_sols_ptr_</a></div><div class="ttdeci">std::shared_ptr&lt; BestSols &gt; best_sols_ptr_</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__easy__solver_8hpp_source.html#l00241">qbpp_easy_solver.hpp:241</a></div></div>
<div class="ttc" id="aclassqbpp_1_1easy__solver_1_1BestSols_html_a26f8926bea0b2a5d383e2a3d0ac197d0"><div class="ttname"><a href="classqbpp_1_1easy__solver_1_1BestSols.html#a26f8926bea0b2a5d383e2a3d0ac197d0">qbpp::easy_solver::BestSols::BestSols</a></div><div class="ttdeci">BestSols(size_t max_best_sol_count=0)</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__easy__solver_8hpp_source.html#l00076">qbpp_easy_solver.hpp:76</a></div></div>
<div class="ttc" id="aclassqbpp_1_1easy__solver_1_1SolDelta_html_aa6f858dece742eeface9a4353b0677e2"><div class="ttname"><a href="classqbpp_1_1easy__solver_1_1SolDelta.html#aa6f858dece742eeface9a4353b0677e2">qbpp::easy_solver::SolDelta::~SolDelta</a></div><div class="ttdeci">virtual ~SolDelta()=default</div></div>
<div class="ttc" id="aclassqbpp_1_1Sol_html_a3ad455870df155ffed4701ce070cf335"><div class="ttname"><a href="classqbpp_1_1Sol.html#a3ad455870df155ffed4701ce070cf335">qbpp::Sol::energy</a></div><div class="ttdeci">energy_t energy() const</div><div class="ttdef"><b>Definition:</b> <a href="qbpp_8hpp_source.html#l02299">qbpp.hpp:2299</a></div></div>
<div class="ttc" id="aclassqbpp_1_1misc_1_1MinHeap_html"><div class="ttname"><a href="classqbpp_1_1misc_1_1MinHeap.html">qbpp::misc::MinHeap&lt; energy_t &gt;</a></div></div>
<div class="ttc" id="aclassqbpp_1_1easy__solver_1_1EasySolver_html"><div class="ttname"><a href="classqbpp_1_1easy__solver_1_1EasySolver.html">qbpp::easy_solver::EasySolver</a></div><div class="ttdef"><b>Definition:</b> <a href="qbpp__easy__solver_8hpp_source.html#l00225">qbpp_easy_solver.hpp:225</a></div></div>
<div class="ttc" id="aclassqbpp_1_1easy__solver_1_1PosMinSolDelta_html_af512f524573c98e13ee2e236cc147c17"><div class="ttname"><a href="classqbpp_1_1easy__solver_1_1PosMinSolDelta.html#af512f524573c98e13ee2e236cc147c17">qbpp::easy_solver::PosMinSolDelta::PosMinSolDelta</a></div><div class="ttdeci">PosMinSolDelta(const EasySolver &amp;easy_solver)</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__easy__solver_8hpp_source.html#l00461">qbpp_easy_solver.hpp:461</a></div></div>
<div class="ttc" id="aclassqbpp_1_1QuadModel_html_ada71ec32906c9d8c73bd25c4267bafc5"><div class="ttname"><a href="classqbpp_1_1QuadModel.html#ada71ec32906c9d8c73bd25c4267bafc5">qbpp::QuadModel::degree</a></div><div class="ttdeci">const std::vector&lt; vindex_t &gt; &amp; degree() const</div><div class="ttdef"><b>Definition:</b> <a href="qbpp_8hpp_source.html#l01969">qbpp.hpp:1969</a></div></div>
<div class="ttc" id="aclassqbpp_1_1misc_1_1Tabu_html_a44a9ecb55678821cc1021e33b4faba72"><div class="ttname"><a href="classqbpp_1_1misc_1_1Tabu.html#a44a9ecb55678821cc1021e33b4faba72">qbpp::misc::Tabu::has</a></div><div class="ttdeci">bool has(vindex_t index) const</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__misc_8hpp_source.html#l00435">qbpp_misc.hpp:435</a></div></div>
<div class="ttc" id="aclassqbpp_1_1misc_1_1MinHeap_html_a4ffbfe7ef3c611efd3de67cd523c94bd"><div class="ttname"><a href="classqbpp_1_1misc_1_1MinHeap.html#a4ffbfe7ef3c611efd3de67cd523c94bd">qbpp::misc::MinHeap::insert</a></div><div class="ttdeci">void insert(vindex_t index, energy_t delta)</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__misc_8hpp_source.html#l00337">qbpp_misc.hpp:337</a></div></div>
<div class="ttc" id="aclassqbpp_1_1easy__solver_1_1SolDelta_html_ac0f902682f6255272a79333a947e1a24"><div class="ttname"><a href="classqbpp_1_1easy__solver_1_1SolDelta.html#ac0f902682f6255272a79333a947e1a24">qbpp::easy_solver::SolDelta::set_if_better</a></div><div class="ttdeci">std::optional&lt; double &gt; set_if_better(const std::string &amp;solver_name)</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__easy__solver_8hpp_source.html#l00448">qbpp_easy_solver.hpp:448</a></div></div>
<div class="ttc" id="aclassqbpp_1_1easy__solver_1_1PosMinSolDelta_html_ad7fb2fffb0e77d69983865304fa11008"><div class="ttname"><a href="classqbpp_1_1easy__solver_1_1PosMinSolDelta.html#ad7fb2fffb0e77d69983865304fa11008">qbpp::easy_solver::PosMinSolDelta::pos_count</a></div><div class="ttdeci">vindex_t pos_count() const</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__easy__solver_8hpp_source.html#l00197">qbpp_easy_solver.hpp:197</a></div></div>
<div class="ttc" id="aclassqbpp_1_1easy__solver_1_1EasySolver_html_ae8fcef1929f94e0a05dd1b039a151d57"><div class="ttname"><a href="classqbpp_1_1easy__solver_1_1EasySolver.html#ae8fcef1929f94e0a05dd1b039a151d57">qbpp::easy_solver::EasySolver::set_time_limit</a></div><div class="ttdeci">void set_time_limit(uint32_t limit)</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__easy__solver_8hpp_source.html#l00281">qbpp_easy_solver.hpp:281</a></div></div>
<div class="ttc" id="aclassqbpp_1_1Sol_html"><div class="ttname"><a href="classqbpp_1_1Sol.html">qbpp::Sol</a></div><div class="ttdef"><b>Definition:</b> <a href="qbpp_8hpp_source.html#l02182">qbpp.hpp:2182</a></div></div>
<div class="ttc" id="aclassqbpp_1_1easy__solver_1_1SolDelta_html_a98373d99b30ddd261919266c2ac59f57"><div class="ttname"><a href="classqbpp_1_1easy__solver_1_1SolDelta.html#a98373d99b30ddd261919266c2ac59f57">qbpp::easy_solver::SolDelta::var_count</a></div><div class="ttdeci">vindex_t var_count() const</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__easy__solver_8hpp_source.html#l00352">qbpp_easy_solver.hpp:352</a></div></div>
<div class="ttc" id="aclassqbpp_1_1Sol_html_a24f5a53fa68071dd9bcda910533bbad7"><div class="ttname"><a href="classqbpp_1_1Sol.html#a24f5a53fa68071dd9bcda910533bbad7">qbpp::Sol::index</a></div><div class="ttdeci">vindex_t index(Var var) const</div><div class="ttdef"><b>Definition:</b> <a href="qbpp_8hpp_source.html#l02325">qbpp.hpp:2325</a></div></div>
<div class="ttc" id="aclassqbpp_1_1easy__solver_1_1SolDelta_html_a2a6c5b08bc697bd6f633b68fcdd550e1"><div class="ttname"><a href="classqbpp_1_1easy__solver_1_1SolDelta.html#a2a6c5b08bc697bd6f633b68fcdd550e1">qbpp::easy_solver::SolDelta::SolDelta</a></div><div class="ttdeci">SolDelta(const EasySolver &amp;easy_solver)</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__easy__solver_8hpp_source.html#l00354">qbpp_easy_solver.hpp:354</a></div></div>
<div class="ttc" id="aqbpp_8hpp_html_a265e0828fcea8e75fbc9c0be99f7156e"><div class="ttname"><a href="qbpp_8hpp.html#a265e0828fcea8e75fbc9c0be99f7156e">THROW_MESSAGE</a></div><div class="ttdeci">#define THROW_MESSAGE(...)</div><div class="ttdef"><b>Definition:</b> <a href="qbpp_8hpp_source.html#l00091">qbpp.hpp:91</a></div></div>
<div class="ttc" id="aclassqbpp_1_1easy__solver_1_1EasySolver_html_adbadd766f998ea7171e18493a3538d9d"><div class="ttname"><a href="classqbpp_1_1easy__solver_1_1EasySolver.html#adbadd766f998ea7171e18493a3538d9d">qbpp::easy_solver::EasySolver::callback</a></div><div class="ttdeci">virtual void callback(const qbpp::Sol &amp;sol, double tts) const</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__easy__solver_8hpp_source.html#l00301">qbpp_easy_solver.hpp:301</a></div></div>
<div class="ttc" id="aclassqbpp_1_1easy__solver_1_1EasySolver_html_aeb2ca474df5de0ea608d461a486b1d11"><div class="ttname"><a href="classqbpp_1_1easy__solver_1_1EasySolver.html#aeb2ca474df5de0ea608d461a486b1d11">qbpp::easy_solver::EasySolver::enable_default_callback_</a></div><div class="ttdeci">bool enable_default_callback_</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__easy__solver_8hpp_source.html#l00245">qbpp_easy_solver.hpp:245</a></div></div>
<div class="ttc" id="aclassqbpp_1_1easy__solver_1_1BestSols_html_aea0db6e737aa21fc745af8a781cdc9a4"><div class="ttname"><a href="classqbpp_1_1easy__solver_1_1BestSols.html#aea0db6e737aa21fc745af8a781cdc9a4">qbpp::easy_solver::BestSols::sol_vector_</a></div><div class="ttdeci">std::vector&lt; qbpp::Sol &gt; sol_vector_</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__easy__solver_8hpp_source.html#l00071">qbpp_easy_solver.hpp:71</a></div></div>
<div class="ttc" id="aclassqbpp_1_1easy__solver_1_1PosMinSolDelta_html_adf4ed5f4493c8ee2daae7b3f5b0a5443"><div class="ttname"><a href="classqbpp_1_1easy__solver_1_1PosMinSolDelta.html#adf4ed5f4493c8ee2daae7b3f5b0a5443">qbpp::easy_solver::PosMinSolDelta::before_delta_updated</a></div><div class="ttdeci">void before_delta_updated(vindex_t k) override</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__easy__solver_8hpp_source.html#l00201">qbpp_easy_solver.hpp:201</a></div></div>
<div class="ttc" id="aclassqbpp_1_1easy__solver_1_1EasySolver_html_ab99254a861b99ba22b5132ffa67416c2"><div class="ttname"><a href="classqbpp_1_1easy__solver_1_1EasySolver.html#ab99254a861b99ba22b5132ffa67416c2">qbpp::easy_solver::EasySolver::time_limit_</a></div><div class="ttdeci">std::optional&lt; uint32_t &gt; time_limit_</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__easy__solver_8hpp_source.html#l00229">qbpp_easy_solver.hpp:229</a></div></div>
<div class="ttc" id="aclassqbpp_1_1easy__solver_1_1EasySolver_html_a963bc1625873dc0e7351ec72bf7e6fe5"><div class="ttname"><a href="classqbpp_1_1easy__solver_1_1EasySolver.html#a963bc1625873dc0e7351ec72bf7e6fe5">qbpp::easy_solver::EasySolver::set_thread_count</a></div><div class="ttdeci">void set_thread_count(unsigned int count)</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__easy__solver_8hpp_source.html#l00285">qbpp_easy_solver.hpp:285</a></div></div>
<div class="ttc" id="aclassqbpp_1_1easy__solver_1_1SolDelta_html_ae5e7dbc16325f4f3b26895fe8686afc5"><div class="ttname"><a href="classqbpp_1_1easy__solver_1_1SolDelta.html#ae5e7dbc16325f4f3b26895fe8686afc5">qbpp::easy_solver::SolDelta::move_to</a></div><div class="ttdeci">void move_to(qbpp::Sol destination)</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__easy__solver_8hpp_source.html#l00421">qbpp_easy_solver.hpp:421</a></div></div>
<div class="ttc" id="aclassqbpp_1_1easy__solver_1_1SolDelta_html_a9b594e0f5962872772bad177ec3a41be"><div class="ttname"><a href="classqbpp_1_1easy__solver_1_1SolDelta.html#a9b594e0f5962872772bad177ec3a41be">qbpp::easy_solver::SolDelta::neg_count</a></div><div class="ttdeci">vindex_t neg_count() const</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__easy__solver_8hpp_source.html#l00141">qbpp_easy_solver.hpp:141</a></div></div>
<div class="ttc" id="aclassqbpp_1_1easy__solver_1_1BestSols_html_a9b53d76ce7a7edbbf9b17229f51ec0b9"><div class="ttname"><a href="classqbpp_1_1easy__solver_1_1BestSols.html#a9b53d76ce7a7edbbf9b17229f51ec0b9">qbpp::easy_solver::BestSols::sol_set_</a></div><div class="ttdeci">std::unordered_set&lt; qbpp::Sol, SolHash &gt; sol_set_</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__easy__solver_8hpp_source.html#l00072">qbpp_easy_solver.hpp:72</a></div></div>
<div class="ttc" id="aclassqbpp_1_1easy__solver_1_1SolDelta_html_a5532efad1e99357b8930a7d0634647cd"><div class="ttname"><a href="classqbpp_1_1easy__solver_1_1SolDelta.html#a5532efad1e99357b8930a7d0634647cd">qbpp::easy_solver::SolDelta::best_sols_ptr_</a></div><div class="ttdeci">const std::shared_ptr&lt; BestSols &gt; best_sols_ptr_</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__easy__solver_8hpp_source.html#l00124">qbpp_easy_solver.hpp:124</a></div></div>
<div class="ttc" id="aclassqbpp_1_1easy__solver_1_1SolDelta_html_a03de2250fb7d272df91c43dcf9a937c2"><div class="ttname"><a href="classqbpp_1_1easy__solver_1_1SolDelta.html#a03de2250fb7d272df91c43dcf9a937c2">qbpp::easy_solver::SolDelta::neg_random</a></div><div class="ttdeci">vindex_t neg_random()</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__easy__solver_8hpp_source.html#l00143">qbpp_easy_solver.hpp:143</a></div></div>
<div class="ttc" id="aclassqbpp_1_1impl_1_1BitVector_html_a6326d5d93d29fad3e96035a56abbee98"><div class="ttname"><a href="classqbpp_1_1impl_1_1BitVector.html#a6326d5d93d29fad3e96035a56abbee98">qbpp::impl::BitVector::size64</a></div><div class="ttdeci">vindex_t size64() const</div><div class="ttdef"><b>Definition:</b> <a href="qbpp_8hpp_source.html#l02117">qbpp.hpp:2117</a></div></div>
<div class="ttc" id="aclassqbpp_1_1easy__solver_1_1PosMinSolDelta_html_aff102bc17a0d06a78a285f1ab2c9632e"><div class="ttname"><a href="classqbpp_1_1easy__solver_1_1PosMinSolDelta.html#aff102bc17a0d06a78a285f1ab2c9632e">qbpp::easy_solver::PosMinSolDelta::search</a></div><div class="ttdeci">void search(size_t iteration)</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__easy__solver_8hpp_source.html#l00474">qbpp_easy_solver.hpp:474</a></div></div>
<div class="ttc" id="aclassqbpp_1_1Model_html_a5cf2bb955c3aa25951ca499d7d8635d2"><div class="ttname"><a href="classqbpp_1_1Model.html#a5cf2bb955c3aa25951ca499d7d8635d2">qbpp::Model::var_count</a></div><div class="ttdeci">vindex_t var_count() const</div><div class="ttdef"><b>Definition:</b> <a href="qbpp_8hpp_source.html#l01859">qbpp.hpp:1859</a></div></div>
<div class="ttc" id="aclassqbpp_1_1easy__solver_1_1EasySolver_html_a329a03e4e5c401ec471e0f7f4c42cb51"><div class="ttname"><a href="classqbpp_1_1easy__solver_1_1EasySolver.html#a329a03e4e5c401ec471e0f7f4c42cb51">qbpp::easy_solver::EasySolver::single_search</a></div><div class="ttdeci">void single_search(size_t thread_id)</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__easy__solver_8hpp_source.html#l00508">qbpp_easy_solver.hpp:508</a></div></div>
<div class="ttc" id="aclassqbpp_1_1misc_1_1MinSet_html"><div class="ttname"><a href="classqbpp_1_1misc_1_1MinSet.html">qbpp::misc::MinSet</a></div><div class="ttdef"><b>Definition:</b> <a href="qbpp__misc_8hpp_source.html#l00225">qbpp_misc.hpp:225</a></div></div>
<div class="ttc" id="aclassqbpp_1_1easy__solver_1_1TabuSolDelta_html_a1381fbf8ed64faf60c3ad0556a37b400"><div class="ttname"><a href="classqbpp_1_1easy__solver_1_1TabuSolDelta.html#a1381fbf8ed64faf60c3ad0556a37b400">qbpp::easy_solver::TabuSolDelta::tabu_size_</a></div><div class="ttdeci">const vindex_t tabu_size_</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__easy__solver_8hpp_source.html#l00168">qbpp_easy_solver.hpp:168</a></div></div>
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.17
</small></address>
</body>
</html>
