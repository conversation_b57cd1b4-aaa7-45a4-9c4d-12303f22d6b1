<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.17"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>QUBO++ Library with QUBO Solver APIs: sample/simple_factorization_easy.cpp Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/x-mathjax-config">
  MathJax.Hub.Config({
    extensions: ["tex2jax.js", "TeX/AMSmath.js", "TeX/AMSsymbols.js"],
    jax: ["input/TeX","output/HTML-CSS"],
});
</script>
<script type="text/javascript" async="async" src="https://cdn.jsdelivr.net/npm/mathjax@2/MathJax.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">QUBO++ Library with QUBO Solver APIs
   </div>
   <div id="projectbrief">Author: Koji Nakano, License: Non-commercial research and evaluation purposes without any guarantees.</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.17 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_c29eeb5af533606caea8a09e66794740.html">sample</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">simple_factorization_easy.cpp</div>  </div>
</div><!--header-->
<div class="contents">
<a href="simple__factorization__easy_8cpp.html">Go to the documentation of this file.</a><div class="fragment"><div class="line"><a name="l00001"></a><span class="lineno">    1</span>&#160; </div>
<div class="line"><a name="l00008"></a><span class="lineno">    8</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="qbpp_8hpp.html">qbpp.hpp</a>&quot;</span></div>
<div class="line"><a name="l00009"></a><span class="lineno">    9</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="qbpp__easy__solver_8hpp.html">qbpp_easy_solver.hpp</a>&quot;</span></div>
<div class="line"><a name="l00010"></a><span class="lineno">   10</span>&#160; </div>
<div class="line"><a name="l00011"></a><span class="lineno"><a class="line" href="simple__factorization__easy_8cpp.html#ae66f6b31b5ad750f1fe042a706a4e3d4">   11</a></span>&#160;<span class="keywordtype">int</span> <a class="code" href="simple__factorization__easy_8cpp.html#ae66f6b31b5ad750f1fe042a706a4e3d4">main</a>() {</div>
<div class="line"><a name="l00012"></a><span class="lineno">   12</span>&#160;  <span class="keyword">auto</span> x = 1 &lt;= <a class="code" href="namespaceqbpp.html#a34b0039c6486ef9f1373775d5a897bae">qbpp::var_int</a>(<span class="stringliteral">&quot;x&quot;</span>) &lt;= 100;</div>
<div class="line"><a name="l00013"></a><span class="lineno">   13</span>&#160;  <span class="keyword">auto</span> y = 1 &lt;= <a class="code" href="namespaceqbpp.html#a34b0039c6486ef9f1373775d5a897bae">qbpp::var_int</a>(<span class="stringliteral">&quot;y&quot;</span>) &lt;= 100;</div>
<div class="line"><a name="l00014"></a><span class="lineno">   14</span>&#160;  <span class="keyword">auto</span> f = x * y == 97 * 89;</div>
<div class="line"><a name="l00015"></a><span class="lineno">   15</span>&#160;  <span class="keyword">auto</span> quad_model = <a class="code" href="classqbpp_1_1QuadModel.html">qbpp::QuadModel</a>(<a class="code" href="namespaceqbpp.html#aa3ce65464a38ba36e0b6e99b97234245">simplify_as_binary</a>(<a class="code" href="namespaceqbpp.html#ac05f27a511fa1e2870091ea5d696a3c3">reduce</a>(f)));</div>
<div class="line"><a name="l00016"></a><span class="lineno">   16</span>&#160;  std::cout &lt;&lt; <span class="stringliteral">&quot;Quad_model has &quot;</span> &lt;&lt; quad_model.var_count() &lt;&lt; <span class="stringliteral">&quot; variables, &quot;</span></div>
<div class="line"><a name="l00017"></a><span class="lineno">   17</span>&#160;            &lt;&lt; quad_model.term_count(1) &lt;&lt; <span class="stringliteral">&quot; linear terms, and &quot;</span></div>
<div class="line"><a name="l00018"></a><span class="lineno">   18</span>&#160;            &lt;&lt; quad_model.term_count(2) &lt;&lt; <span class="stringliteral">&quot; quadratic terms&quot;</span> &lt;&lt; std::endl;</div>
<div class="line"><a name="l00019"></a><span class="lineno">   19</span>&#160;  <span class="keyword">auto</span> solver = <a class="code" href="classqbpp_1_1easy__solver_1_1EasySolver.html">qbpp::easy_solver::EasySolver</a>(quad_model);</div>
<div class="line"><a name="l00020"></a><span class="lineno">   20</span>&#160;  solver.set_time_limit(10);</div>
<div class="line"><a name="l00021"></a><span class="lineno">   21</span>&#160;  solver.set_target_energy(0);</div>
<div class="line"><a name="l00022"></a><span class="lineno">   22</span>&#160;  solver.enable_default_callback();</div>
<div class="line"><a name="l00023"></a><span class="lineno">   23</span>&#160;  <span class="keyword">auto</span> sol = solver.search();</div>
<div class="line"><a name="l00024"></a><span class="lineno">   24</span>&#160;  std::cout &lt;&lt; <span class="stringliteral">&quot;x = &quot;</span> &lt;&lt; x &lt;&lt; <span class="stringliteral">&quot; = &quot;</span> &lt;&lt; sol.get(x) &lt;&lt; std::endl;</div>
<div class="line"><a name="l00025"></a><span class="lineno">   25</span>&#160;  std::cout &lt;&lt; <span class="stringliteral">&quot;y = &quot;</span> &lt;&lt; y &lt;&lt; <span class="stringliteral">&quot; = &quot;</span> &lt;&lt; sol.get(y) &lt;&lt; std::endl;</div>
<div class="line"><a name="l00026"></a><span class="lineno">   26</span>&#160;}</div>
</div><!-- fragment --></div><!-- contents -->
<div class="ttc" id="anamespaceqbpp_html_a34b0039c6486ef9f1373775d5a897bae"><div class="ttname"><a href="namespaceqbpp.html#a34b0039c6486ef9f1373775d5a897bae">qbpp::var_int</a></div><div class="ttdeci">VarIntCore var_int(const std::string &amp;var_str)</div><div class="ttdef"><b>Definition:</b> <a href="qbpp_8hpp_source.html#l02932">qbpp.hpp:2932</a></div></div>
<div class="ttc" id="aclassqbpp_1_1QuadModel_html"><div class="ttname"><a href="classqbpp_1_1QuadModel.html">qbpp::QuadModel</a></div><div class="ttdef"><b>Definition:</b> <a href="qbpp_8hpp_source.html#l01892">qbpp.hpp:1892</a></div></div>
<div class="ttc" id="aqbpp_8hpp_html"><div class="ttname"><a href="qbpp_8hpp.html">qbpp.hpp</a></div><div class="ttdoc">QUBO++, a C++ library for generating expressions for binary and spin variables.</div></div>
<div class="ttc" id="asimple__factorization__easy_8cpp_html_ae66f6b31b5ad750f1fe042a706a4e3d4"><div class="ttname"><a href="simple__factorization__easy_8cpp.html#ae66f6b31b5ad750f1fe042a706a4e3d4">main</a></div><div class="ttdeci">int main()</div><div class="ttdef"><b>Definition:</b> <a href="simple__factorization__easy_8cpp_source.html#l00011">simple_factorization_easy.cpp:11</a></div></div>
<div class="ttc" id="aqbpp__easy__solver_8hpp_html"><div class="ttname"><a href="qbpp__easy__solver_8hpp.html">qbpp_easy_solver.hpp</a></div><div class="ttdoc">Easy QUBO Solver for solving QUBO problems.</div></div>
<div class="ttc" id="anamespaceqbpp_html_ac05f27a511fa1e2870091ea5d696a3c3"><div class="ttname"><a href="namespaceqbpp.html#ac05f27a511fa1e2870091ea5d696a3c3">qbpp::reduce</a></div><div class="ttdeci">Expr reduce(const Expr &amp;expr)</div><div class="ttdef"><b>Definition:</b> <a href="qbpp_8hpp_source.html#l03561">qbpp.hpp:3561</a></div></div>
<div class="ttc" id="aclassqbpp_1_1easy__solver_1_1EasySolver_html"><div class="ttname"><a href="classqbpp_1_1easy__solver_1_1EasySolver.html">qbpp::easy_solver::EasySolver</a></div><div class="ttdef"><b>Definition:</b> <a href="qbpp__easy__solver_8hpp_source.html#l00225">qbpp_easy_solver.hpp:225</a></div></div>
<div class="ttc" id="anamespaceqbpp_html_aa3ce65464a38ba36e0b6e99b97234245"><div class="ttname"><a href="namespaceqbpp.html#aa3ce65464a38ba36e0b6e99b97234245">qbpp::simplify_as_binary</a></div><div class="ttdeci">Expr simplify_as_binary(const Expr &amp;expr)</div><div class="ttdef"><b>Definition:</b> <a href="qbpp_8hpp_source.html#l03442">qbpp.hpp:3442</a></div></div>
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.17
</small></address>
</body>
</html>
