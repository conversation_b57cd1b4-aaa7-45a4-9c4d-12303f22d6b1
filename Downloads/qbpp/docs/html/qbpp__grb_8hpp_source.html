<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.17"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>QUBO++ Library with QUBO Solver APIs: include/qbpp_grb.hpp Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/x-mathjax-config">
  MathJax.Hub.Config({
    extensions: ["tex2jax.js", "TeX/AMSmath.js", "TeX/AMSsymbols.js"],
    jax: ["input/TeX","output/HTML-CSS"],
});
</script>
<script type="text/javascript" async="async" src="https://cdn.jsdelivr.net/npm/mathjax@2/MathJax.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">QUBO++ Library with QUBO Solver APIs
   </div>
   <div id="projectbrief">Author: Koji Nakano, License: Non-commercial research and evaluation purposes without any guarantees.</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.17 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_d44c64559bbebec7f509842c48db8b23.html">include</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">qbpp_grb.hpp</div>  </div>
</div><!--header-->
<div class="contents">
<a href="qbpp__grb_8hpp.html">Go to the documentation of this file.</a><div class="fragment"><div class="line"><a name="l00001"></a><span class="lineno">    1</span>&#160; </div>
<div class="line"><a name="l00007"></a><span class="lineno">    7</span>&#160;<span class="preprocessor">#ifndef GUROBI_QBPP_HPP</span></div>
<div class="line"><a name="l00008"></a><span class="lineno">    8</span>&#160;<span class="preprocessor">#define GUROBI_QBPP_HPP</span></div>
<div class="line"><a name="l00009"></a><span class="lineno">    9</span>&#160; </div>
<div class="line"><a name="l00010"></a><span class="lineno">   10</span>&#160;<span class="preprocessor">#include &lt;cmath&gt;</span></div>
<div class="line"><a name="l00011"></a><span class="lineno">   11</span>&#160; </div>
<div class="line"><a name="l00012"></a><span class="lineno">   12</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="abs2_8hpp.html">abs2.hpp</a>&quot;</span></div>
<div class="line"><a name="l00013"></a><span class="lineno">   13</span>&#160;<span class="preprocessor">#include &quot;gurobi_c++.h&quot;</span></div>
<div class="line"><a name="l00014"></a><span class="lineno">   14</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="qbpp_8hpp.html">qbpp.hpp</a>&quot;</span></div>
<div class="line"><a name="l00015"></a><span class="lineno">   15</span>&#160; </div>
<div class="line"><a name="l00016"></a><span class="lineno"><a class="line" href="qbpp__grb_8hpp.html#aed547c72240001f6a5ef86636bb62d66">   16</a></span>&#160;<span class="preprocessor">#define GRB_SAFE_CALL(func)                                               \</span></div>
<div class="line"><a name="l00017"></a><span class="lineno">   17</span>&#160;<span class="preprocessor">  try {                                                                   \</span></div>
<div class="line"><a name="l00018"></a><span class="lineno">   18</span>&#160;<span class="preprocessor">    func;                                                                 \</span></div>
<div class="line"><a name="l00019"></a><span class="lineno">   19</span>&#160;<span class="preprocessor">  } catch (GRBException e) {                                              \</span></div>
<div class="line"><a name="l00020"></a><span class="lineno">   20</span>&#160;<span class="preprocessor">    std::cerr &lt;&lt; e.getErrorCode() &lt;&lt; &quot;: &quot; &lt;&lt; e.getMessage() &lt;&lt; std::endl; \</span></div>
<div class="line"><a name="l00021"></a><span class="lineno">   21</span>&#160;<span class="preprocessor">    exit(1);                                                              \</span></div>
<div class="line"><a name="l00022"></a><span class="lineno">   22</span>&#160;<span class="preprocessor">  }</span></div>
<div class="line"><a name="l00023"></a><span class="lineno">   23</span>&#160; </div>
<div class="line"><a name="l00025"></a><span class="lineno"><a class="line" href="namespaceqbpp__grb.html">   25</a></span>&#160;<span class="keyword">namespace </span><a class="code" href="namespaceqbpp__grb.html">qbpp_grb</a> {</div>
<div class="line"><a name="l00026"></a><span class="lineno">   26</span>&#160; </div>
<div class="line"><a name="l00027"></a><span class="lineno">   27</span>&#160;<span class="comment">//===========================</span></div>
<div class="line"><a name="l00028"></a><span class="lineno">   28</span>&#160;<span class="comment">// Class forward declarations</span></div>
<div class="line"><a name="l00029"></a><span class="lineno">   29</span>&#160;<span class="comment">//===========================</span></div>
<div class="line"><a name="l00030"></a><span class="lineno">   30</span>&#160;<span class="keyword">class </span><a class="code" href="classqbpp__grb_1_1QuadModel.html">QuadModel</a>;</div>
<div class="line"><a name="l00031"></a><span class="lineno">   31</span>&#160;<span class="keyword">class </span><a class="code" href="classqbpp__grb_1_1Sol.html">Sol</a>;</div>
<div class="line"><a name="l00032"></a><span class="lineno">   32</span>&#160;<span class="keyword">class </span><a class="code" href="classqbpp__grb_1_1Callback.html">Callback</a>;</div>
<div class="line"><a name="l00033"></a><span class="lineno">   33</span>&#160; </div>
<div class="line"><a name="l00034"></a><span class="lineno">   34</span>&#160;<span class="comment">//===================</span></div>
<div class="line"><a name="l00035"></a><span class="lineno">   35</span>&#160;<span class="comment">// Class declarations</span></div>
<div class="line"><a name="l00036"></a><span class="lineno">   36</span>&#160;<span class="comment">//===================</span></div>
<div class="line"><a name="l00037"></a><span class="lineno">   37</span>&#160; </div>
<div class="line"><a name="l00042"></a><span class="lineno"><a class="line" href="classqbpp__grb_1_1QuadModel.html">   42</a></span>&#160;<span class="keyword">class </span><a class="code" href="classqbpp__grb_1_1QuadModel.html">QuadModel</a> : <span class="keyword">public</span> <a class="code" href="classqbpp_1_1QuadModel.html">qbpp::QuadModel</a> {</div>
<div class="line"><a name="l00043"></a><span class="lineno">   43</span>&#160; <span class="keyword">protected</span>:</div>
<div class="line"><a name="l00045"></a><span class="lineno"><a class="line" href="classqbpp__grb_1_1QuadModel.html#adc1c24076ad72f4a4f83eee52dcc06ae">   45</a></span>&#160;  GRBEnv <a class="code" href="classqbpp__grb_1_1QuadModel.html#adc1c24076ad72f4a4f83eee52dcc06ae">grb_env</a>;</div>
<div class="line"><a name="l00047"></a><span class="lineno"><a class="line" href="classqbpp__grb_1_1QuadModel.html#a8f716ca578f5d6f35b168662f107bce6">   47</a></span>&#160;  std::shared_ptr&lt;GRBModel&gt; <a class="code" href="classqbpp__grb_1_1QuadModel.html#a8f716ca578f5d6f35b168662f107bce6">grb_model_ptr</a>;</div>
<div class="line"><a name="l00049"></a><span class="lineno"><a class="line" href="classqbpp__grb_1_1QuadModel.html#ad7ee65c3f5130a2c5b2e70e75dce1d41">   49</a></span>&#160;  GRBVar *<a class="code" href="classqbpp__grb_1_1QuadModel.html#ad7ee65c3f5130a2c5b2e70e75dce1d41">grb_x</a>;</div>
<div class="line"><a name="l00050"></a><span class="lineno">   50</span>&#160; </div>
<div class="line"><a name="l00051"></a><span class="lineno">   51</span>&#160; <span class="keyword">public</span>:</div>
<div class="line"><a name="l00057"></a><span class="lineno">   57</span>&#160; </div>
<div class="line"><a name="l00061"></a><span class="lineno">   61</span>&#160;  <a class="code" href="group__qbpp__grb.html#ga3e5106bc14a17c6521c932753c76fe96">QuadModel</a>(<span class="keyword">const</span> <a class="code" href="classqbpp_1_1QuadModel.html">qbpp::QuadModel</a> &amp;quad_model, <span class="keywordtype">bool</span> verbose = <span class="keyword">false</span>);</div>
<div class="line"><a name="l00062"></a><span class="lineno">   62</span>&#160; </div>
<div class="line"><a name="l00065"></a><span class="lineno">   65</span>&#160;  <a class="code" href="group__qbpp__grb.html#ga3e5106bc14a17c6521c932753c76fe96">QuadModel</a>(<span class="keyword">const</span> <a class="code" href="classqbpp__grb_1_1QuadModel.html">QuadModel</a> &amp;grb_model) = <span class="keywordflow">default</span>;</div>
<div class="line"><a name="l00066"></a><span class="lineno">   66</span>&#160; </div>
<div class="line"><a name="l00070"></a><span class="lineno"><a class="line" href="group__qbpp__grb.html#ga896e8acb781305f1539e390d360a12de">   70</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="group__qbpp__grb.html#ga896e8acb781305f1539e390d360a12de">set</a>(<span class="keyword">const</span> std::string &amp;key, <span class="keyword">const</span> std::string &amp;val) {</div>
<div class="line"><a name="l00071"></a><span class="lineno">   71</span>&#160;    <a class="code" href="classqbpp__grb_1_1QuadModel.html#a8f716ca578f5d6f35b168662f107bce6">grb_model_ptr</a>-&gt;getEnv().set(key, val);</div>
<div class="line"><a name="l00072"></a><span class="lineno">   72</span>&#160;  };</div>
<div class="line"><a name="l00073"></a><span class="lineno">   73</span>&#160; </div>
<div class="line"><a name="l00076"></a><span class="lineno"><a class="line" href="group__qbpp__grb.html#ga3e57391fc84e3e58a8fee50b0e511af1">   76</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="group__qbpp__grb.html#ga3e57391fc84e3e58a8fee50b0e511af1">set_time_limit</a>(uint32_t time_limit) {</div>
<div class="line"><a name="l00077"></a><span class="lineno">   77</span>&#160;    <a class="code" href="group__qbpp__grb.html#ga896e8acb781305f1539e390d360a12de">set</a>(<span class="stringliteral">&quot;TimeLimit&quot;</span>, std::to_string(time_limit));</div>
<div class="line"><a name="l00078"></a><span class="lineno">   78</span>&#160;  }</div>
<div class="line"><a name="l00079"></a><span class="lineno">   79</span>&#160; </div>
<div class="line"><a name="l00085"></a><span class="lineno">   85</span>&#160;  <span class="keywordtype">void</span> <a class="code" href="group__qbpp__grb.html#ga896e8acb781305f1539e390d360a12de">set</a>(<a class="code" href="classqbpp__grb_1_1Callback.html">Callback</a> &amp;cb);</div>
<div class="line"><a name="l00086"></a><span class="lineno">   86</span>&#160; </div>
<div class="line"><a name="l00088"></a><span class="lineno">   88</span>&#160; </div>
<div class="line"><a name="l00092"></a><span class="lineno"><a class="line" href="classqbpp__grb_1_1QuadModel.html#a9725ebdf79f6eb70a09de6425f7c5b68">   92</a></span>&#160;  GRBVar <a class="code" href="classqbpp__grb_1_1QuadModel.html#a9725ebdf79f6eb70a09de6425f7c5b68">get_grb_var</a>(<span class="keywordtype">int</span> <a class="code" href="classqbpp_1_1Model.html#a0712035184681aeb478c5e80bb5221bd">index</a>)<span class="keyword"> const </span>{ <span class="keywordflow">return</span> <a class="code" href="classqbpp__grb_1_1QuadModel.html#ad7ee65c3f5130a2c5b2e70e75dce1d41">grb_x</a>[<a class="code" href="classqbpp_1_1Model.html#a0712035184681aeb478c5e80bb5221bd">index</a>]; };</div>
<div class="line"><a name="l00093"></a><span class="lineno">   93</span>&#160; </div>
<div class="line"><a name="l00096"></a><span class="lineno"><a class="line" href="classqbpp__grb_1_1QuadModel.html#aebda307a8066b4601533834cfbeb1b6e">   96</a></span>&#160;  GRBModel &amp;<a class="code" href="classqbpp__grb_1_1QuadModel.html#aebda307a8066b4601533834cfbeb1b6e">get_grb_model</a>() { <span class="keywordflow">return</span> *<a class="code" href="classqbpp__grb_1_1QuadModel.html#a8f716ca578f5d6f35b168662f107bce6">grb_model_ptr</a>; }</div>
<div class="line"><a name="l00097"></a><span class="lineno">   97</span>&#160; </div>
<div class="line"><a name="l00100"></a><span class="lineno"><a class="line" href="classqbpp__grb_1_1QuadModel.html#a6c21b263939fac514f788ee6d7fadf9b">  100</a></span>&#160;  <span class="keyword">const</span> GRBModel &amp;<a class="code" href="classqbpp__grb_1_1QuadModel.html#a6c21b263939fac514f788ee6d7fadf9b">get_grb_model</a>()<span class="keyword"> const </span>{ <span class="keywordflow">return</span> *<a class="code" href="classqbpp__grb_1_1QuadModel.html#a8f716ca578f5d6f35b168662f107bce6">grb_model_ptr</a>; }</div>
<div class="line"><a name="l00101"></a><span class="lineno">  101</span>&#160; </div>
<div class="line"><a name="l00105"></a><span class="lineno">  105</span>&#160;  <a class="code" href="classqbpp__grb_1_1Sol.html">Sol</a> <a class="code" href="classqbpp__grb_1_1QuadModel.html#ad3add2ab2e0d80e5d0d8123deebc6e98">optimize</a>();</div>
<div class="line"><a name="l00107"></a><span class="lineno">  107</span>&#160; </div>
<div class="line"><a name="l00112"></a><span class="lineno"><a class="line" href="classqbpp__grb_1_1QuadModel.html#a7261179c8f0e1a20859b84df22ab6e5f">  112</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="classqbpp__grb_1_1QuadModel.html#a7261179c8f0e1a20859b84df22ab6e5f">write</a>(std::string filename) {</div>
<div class="line"><a name="l00113"></a><span class="lineno">  113</span>&#160;    <a class="code" href="qbpp__grb_8hpp.html#aed547c72240001f6a5ef86636bb62d66">GRB_SAFE_CALL</a>(<a class="code" href="classqbpp__grb_1_1QuadModel.html#a8f716ca578f5d6f35b168662f107bce6">grb_model_ptr</a>-&gt;write(filename));</div>
<div class="line"><a name="l00114"></a><span class="lineno">  114</span>&#160;  }</div>
<div class="line"><a name="l00115"></a><span class="lineno">  115</span>&#160;};</div>
<div class="line"><a name="l00116"></a><span class="lineno">  116</span>&#160; </div>
<div class="line"><a name="l00120"></a><span class="lineno"><a class="line" href="classqbpp__grb_1_1Sol.html">  120</a></span>&#160;<span class="keyword">class </span><a class="code" href="classqbpp__grb_1_1Sol.html">Sol</a> : <span class="keyword">public</span> <a class="code" href="classqbpp_1_1Sol.html">qbpp::Sol</a> {</div>
<div class="line"><a name="l00122"></a><span class="lineno"><a class="line" href="classqbpp__grb_1_1Sol.html#a76420c04ca359bf4b295f0a2f49e42ef">  122</a></span>&#160;  <a class="code" href="namespaceqbpp.html#a9c324f641779d1a7a3c7723f28d8cff4">qbpp::energy_t</a> <a class="code" href="classqbpp__grb_1_1Sol.html#a76420c04ca359bf4b295f0a2f49e42ef">bound</a>;</div>
<div class="line"><a name="l00123"></a><span class="lineno">  123</span>&#160; </div>
<div class="line"><a name="l00124"></a><span class="lineno">  124</span>&#160; <span class="keyword">public</span>:</div>
<div class="line"><a name="l00127"></a><span class="lineno">  127</span>&#160; </div>
<div class="line"><a name="l00130"></a><span class="lineno"><a class="line" href="group__qbpp__grb.html#gab69ed0d8b924d9bae6c49c975ba51dcc">  130</a></span>&#160;  <a class="code" href="group__qbpp__grb.html#gab69ed0d8b924d9bae6c49c975ba51dcc">Sol</a>(<span class="keyword">const</span> <a class="code" href="classqbpp_1_1QuadModel.html">qbpp::QuadModel</a> &amp;quad_model) : <a class="code" href="namespaceqbpp.html">qbpp</a>::<a class="code" href="classqbpp__grb_1_1Sol.html">Sol</a>(quad_model) {}</div>
<div class="line"><a name="l00131"></a><span class="lineno">  131</span>&#160; </div>
<div class="line"><a name="l00133"></a><span class="lineno">  133</span>&#160; </div>
<div class="line"><a name="l00136"></a><span class="lineno"><a class="line" href="classqbpp__grb_1_1Sol.html#a0ec6fb21d73edc9e7bbf6a585619b4f4">  136</a></span>&#160;  <a class="code" href="namespaceqbpp.html#a9c324f641779d1a7a3c7723f28d8cff4">qbpp::energy_t</a> <a class="code" href="classqbpp__grb_1_1Sol.html#a0ec6fb21d73edc9e7bbf6a585619b4f4">get_bound</a>()<span class="keyword"> const </span>{ <span class="keywordflow">return</span> <a class="code" href="classqbpp__grb_1_1Sol.html#a76420c04ca359bf4b295f0a2f49e42ef">bound</a>; }</div>
<div class="line"><a name="l00137"></a><span class="lineno">  137</span>&#160; </div>
<div class="line"><a name="l00140"></a><span class="lineno"><a class="line" href="classqbpp__grb_1_1Sol.html#ab77394cbb7dade280051026987c264c9">  140</a></span>&#160;  <a class="code" href="namespaceqbpp.html#a9c324f641779d1a7a3c7723f28d8cff4">qbpp::energy_t</a> <a class="code" href="classqbpp__grb_1_1Sol.html#ab77394cbb7dade280051026987c264c9">set_bound</a>(<a class="code" href="namespaceqbpp.html#a9c324f641779d1a7a3c7723f28d8cff4">qbpp::energy_t</a> <a class="code" href="namespaceqbpp.html#ab5da6d0960920098998bb7347082ee6b">eval</a>) {</div>
<div class="line"><a name="l00141"></a><span class="lineno">  141</span>&#160;    <a class="code" href="classqbpp__grb_1_1Sol.html#a76420c04ca359bf4b295f0a2f49e42ef">bound</a> = <a class="code" href="namespaceqbpp.html#ab5da6d0960920098998bb7347082ee6b">eval</a>;</div>
<div class="line"><a name="l00142"></a><span class="lineno">  142</span>&#160;    <span class="keywordflow">return</span> <a class="code" href="classqbpp__grb_1_1Sol.html#a76420c04ca359bf4b295f0a2f49e42ef">bound</a>;</div>
<div class="line"><a name="l00143"></a><span class="lineno">  143</span>&#160;  }</div>
<div class="line"><a name="l00144"></a><span class="lineno">  144</span>&#160;};</div>
<div class="line"><a name="l00145"></a><span class="lineno">  145</span>&#160; </div>
<div class="line"><a name="l00149"></a><span class="lineno"><a class="line" href="classqbpp__grb_1_1Callback.html">  149</a></span>&#160;<span class="keyword">class </span><a class="code" href="classqbpp__grb_1_1Callback.html">Callback</a> : <span class="keyword">public</span> GRBCallback {</div>
<div class="line"><a name="l00150"></a><span class="lineno">  150</span>&#160; <span class="keyword">protected</span>:</div>
<div class="line"><a name="l00152"></a><span class="lineno"><a class="line" href="classqbpp__grb_1_1Callback.html#a1f4dc0f4fe776c295267f83a044a3028">  152</a></span>&#160;  <span class="keyword">const</span> <a class="code" href="classqbpp__grb_1_1QuadModel.html">QuadModel</a> <a class="code" href="classqbpp__grb_1_1Callback.html#a1f4dc0f4fe776c295267f83a044a3028">quad_model</a>;</div>
<div class="line"><a name="l00153"></a><span class="lineno">  153</span>&#160; </div>
<div class="line"><a name="l00155"></a><span class="lineno"><a class="line" href="classqbpp__grb_1_1Callback.html#a82a40b2deeafa027d72f778273efb2ba">  155</a></span>&#160;  <span class="keyword">const</span> GRBModel &amp;<a class="code" href="classqbpp__grb_1_1Callback.html#a82a40b2deeafa027d72f778273efb2ba">grb_model</a>;</div>
<div class="line"><a name="l00156"></a><span class="lineno">  156</span>&#160; </div>
<div class="line"><a name="l00158"></a><span class="lineno"><a class="line" href="classqbpp__grb_1_1Callback.html#ae44d18cfb962fc7ccf90d5305e76f288">  158</a></span>&#160;  std::optional&lt;qbpp::energy_t&gt; <a class="code" href="classqbpp__grb_1_1Callback.html#ae44d18cfb962fc7ccf90d5305e76f288">target_energy</a> = std::nullopt;</div>
<div class="line"><a name="l00159"></a><span class="lineno">  159</span>&#160; </div>
<div class="line"><a name="l00160"></a><span class="lineno">  160</span>&#160;  <span class="comment">//  std::chrono::high_resolution_clock::time_point start_time =</span></div>
<div class="line"><a name="l00161"></a><span class="lineno">  161</span>&#160;  <span class="comment">//      std::chrono::high_resolution_clock::now();</span></div>
<div class="line"><a name="l00162"></a><span class="lineno">  162</span>&#160; </div>
<div class="line"><a name="l00164"></a><span class="lineno"><a class="line" href="classqbpp__grb_1_1Callback.html#ab215ecd1f6d8a98759815e7279edb7b3">  164</a></span>&#160;  <span class="keyword">mutable</span> std::mutex <a class="code" href="classqbpp__grb_1_1Callback.html#ab215ecd1f6d8a98759815e7279edb7b3">mtx</a>;</div>
<div class="line"><a name="l00165"></a><span class="lineno">  165</span>&#160; </div>
<div class="line"><a name="l00169"></a><span class="lineno"><a class="line" href="classqbpp__grb_1_1Callback.html#a14b0a63d9776a7839e34f1323d239f08">  169</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="classqbpp__grb_1_1Callback.html#a14b0a63d9776a7839e34f1323d239f08">abort_if_target_energy</a>(<a class="code" href="namespaceqbpp.html#a9c324f641779d1a7a3c7723f28d8cff4">qbpp::energy_t</a> energy) {</div>
<div class="line"><a name="l00170"></a><span class="lineno">  170</span>&#160;    <span class="keywordflow">if</span> (<a class="code" href="classqbpp__grb_1_1Callback.html#ae44d18cfb962fc7ccf90d5305e76f288">target_energy</a>.has_value() &amp;&amp; energy &lt;= <a class="code" href="classqbpp__grb_1_1Callback.html#ae44d18cfb962fc7ccf90d5305e76f288">target_energy</a>) {</div>
<div class="line"><a name="l00171"></a><span class="lineno">  171</span>&#160;      abort();</div>
<div class="line"><a name="l00172"></a><span class="lineno">  172</span>&#160;    }</div>
<div class="line"><a name="l00173"></a><span class="lineno">  173</span>&#160;  }</div>
<div class="line"><a name="l00174"></a><span class="lineno">  174</span>&#160; </div>
<div class="line"><a name="l00175"></a><span class="lineno">  175</span>&#160; <span class="keyword">public</span>:</div>
<div class="line"><a name="l00178"></a><span class="lineno"><a class="line" href="classqbpp__grb_1_1Callback.html#a91592303df972ec49c2279459a681f05">  178</a></span>&#160;  <a class="code" href="classqbpp__grb_1_1Callback.html#a91592303df972ec49c2279459a681f05">Callback</a>(<span class="keyword">const</span> <a class="code" href="classqbpp__grb_1_1QuadModel.html">QuadModel</a> &amp;<a class="code" href="classqbpp__grb_1_1Callback.html#a1f4dc0f4fe776c295267f83a044a3028">quad_model</a>)</div>
<div class="line"><a name="l00179"></a><span class="lineno">  179</span>&#160;      : <a class="code" href="classqbpp__grb_1_1Callback.html#a1f4dc0f4fe776c295267f83a044a3028">quad_model</a>(<a class="code" href="classqbpp__grb_1_1Callback.html#a1f4dc0f4fe776c295267f83a044a3028">quad_model</a>), <a class="code" href="classqbpp__grb_1_1Callback.html#a82a40b2deeafa027d72f778273efb2ba">grb_model</a>(<a class="code" href="classqbpp__grb_1_1Callback.html#a1f4dc0f4fe776c295267f83a044a3028">quad_model</a>.get_grb_model()) {</div>
<div class="line"><a name="l00180"></a><span class="lineno">  180</span>&#160;    <a class="code" href="namespaceqbpp.html#a4184afa358539dbcf5f35e63b49ce05b">qbpp::get_time</a>();</div>
<div class="line"><a name="l00181"></a><span class="lineno">  181</span>&#160;  };</div>
<div class="line"><a name="l00182"></a><span class="lineno">  182</span>&#160; </div>
<div class="line"><a name="l00183"></a><span class="lineno">  183</span>&#160;  <span class="keyword">virtual</span> <a class="code" href="classqbpp__grb_1_1Callback.html#a1f1879a1755fa6d7d6e944c468cf1e4c">~Callback</a>() = <span class="keywordflow">default</span>;</div>
<div class="line"><a name="l00184"></a><span class="lineno">  184</span>&#160; </div>
<div class="line"><a name="l00187"></a><span class="lineno">  187</span>&#160;  <a class="code" href="classqbpp__grb_1_1Sol.html">Sol</a> <a class="code" href="classqbpp__grb_1_1Callback.html#a6d111191d65a98194fec8779fac6dadd">get_sol</a>();</div>
<div class="line"><a name="l00188"></a><span class="lineno">  188</span>&#160; </div>
<div class="line"><a name="l00192"></a><span class="lineno"><a class="line" href="classqbpp__grb_1_1Callback.html#a8b785f76ab9c73fa92b97bb8da6f5e37">  192</a></span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">void</span> <a class="code" href="classqbpp__grb_1_1Callback.html#a8b785f76ab9c73fa92b97bb8da6f5e37">callback</a>()<span class="keyword"> override </span>{</div>
<div class="line"><a name="l00193"></a><span class="lineno">  193</span>&#160;    <span class="keywordflow">if</span> (where == GRB_CB_MIPSOL) {</div>
<div class="line"><a name="l00194"></a><span class="lineno">  194</span>&#160;      <a class="code" href="namespaceqbpp.html#a9c324f641779d1a7a3c7723f28d8cff4">qbpp::energy_t</a> energy = <a class="code" href="classqbpp__grb_1_1Callback.html#a6d111191d65a98194fec8779fac6dadd">get_sol</a>().<a class="code" href="classqbpp_1_1Sol.html#a3ad455870df155ffed4701ce070cf335">energy</a>();</div>
<div class="line"><a name="l00195"></a><span class="lineno">  195</span>&#160;      std::cout &lt;&lt; <span class="stringliteral">&quot;TTS = &quot;</span> &lt;&lt; std::fixed &lt;&lt; std::setprecision(3)</div>
<div class="line"><a name="l00196"></a><span class="lineno">  196</span>&#160;                &lt;&lt; std::setfill(<span class="charliteral">&#39;0&#39;</span>) &lt;&lt; <a class="code" href="namespaceqbpp.html#a4184afa358539dbcf5f35e63b49ce05b">qbpp::get_time</a>()</div>
<div class="line"><a name="l00197"></a><span class="lineno">  197</span>&#160;                &lt;&lt; <span class="stringliteral">&quot;s Energy = &quot;</span> &lt;&lt; energy &lt;&lt; std::endl;</div>
<div class="line"><a name="l00198"></a><span class="lineno">  198</span>&#160;      <a class="code" href="classqbpp__grb_1_1Callback.html#a14b0a63d9776a7839e34f1323d239f08">abort_if_target_energy</a>(energy);</div>
<div class="line"><a name="l00199"></a><span class="lineno">  199</span>&#160;    }</div>
<div class="line"><a name="l00200"></a><span class="lineno">  200</span>&#160;  }</div>
<div class="line"><a name="l00201"></a><span class="lineno">  201</span>&#160; </div>
<div class="line"><a name="l00203"></a><span class="lineno"><a class="line" href="classqbpp__grb_1_1Callback.html#acc6b06db26a7cf40e83cb1521c3431f3">  203</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="classqbpp__grb_1_1Callback.html#acc6b06db26a7cf40e83cb1521c3431f3">set_target_energy</a>(<a class="code" href="namespaceqbpp.html#a9c324f641779d1a7a3c7723f28d8cff4">qbpp::energy_t</a> <a class="code" href="classqbpp__grb_1_1Callback.html#ae44d18cfb962fc7ccf90d5305e76f288">target_energy</a>) {</div>
<div class="line"><a name="l00204"></a><span class="lineno">  204</span>&#160;    this-&gt;target_energy = <a class="code" href="classqbpp__grb_1_1Callback.html#ae44d18cfb962fc7ccf90d5305e76f288">target_energy</a>;</div>
<div class="line"><a name="l00205"></a><span class="lineno">  205</span>&#160;  }</div>
<div class="line"><a name="l00206"></a><span class="lineno">  206</span>&#160; </div>
<div class="line"><a name="l00210"></a><span class="lineno"><a class="line" href="classqbpp__grb_1_1Callback.html#a5cb91d68abcb10374e9f56d540b26a83">  210</a></span>&#160;  <span class="keywordtype">double</span> <a class="code" href="classqbpp__grb_1_1Callback.html#a5cb91d68abcb10374e9f56d540b26a83">getDoubleInfoPublic</a>(<span class="keywordtype">int</span> what) { <span class="keywordflow">return</span> getDoubleInfo(what); }</div>
<div class="line"><a name="l00211"></a><span class="lineno">  211</span>&#160; </div>
<div class="line"><a name="l00215"></a><span class="lineno"><a class="line" href="classqbpp__grb_1_1Callback.html#a40e9f9bd012d81ab6d96884eee327c20">  215</a></span>&#160;  <span class="keywordtype">double</span> <a class="code" href="classqbpp__grb_1_1Callback.html#a40e9f9bd012d81ab6d96884eee327c20">getSolutionPublic</a>(GRBVar v) { <span class="keywordflow">return</span> getSolution(v); }</div>
<div class="line"><a name="l00216"></a><span class="lineno">  216</span>&#160;};</div>
<div class="line"><a name="l00217"></a><span class="lineno">  217</span>&#160; </div>
<div class="line"><a name="l00218"></a><span class="lineno">  218</span>&#160;<span class="comment">//=============================</span></div>
<div class="line"><a name="l00219"></a><span class="lineno">  219</span>&#160;<span class="comment">// Class QuadModel member functions</span></div>
<div class="line"><a name="l00220"></a><span class="lineno">  220</span>&#160;<span class="comment">//=============================</span></div>
<div class="line"><a name="l00221"></a><span class="lineno">  221</span>&#160; </div>
<div class="line"><a name="l00222"></a><span class="lineno"><a class="line" href="group__qbpp__grb.html#ga3e5106bc14a17c6521c932753c76fe96">  222</a></span>&#160;<span class="keyword">inline</span> <a class="code" href="group__qbpp__grb.html#ga3e5106bc14a17c6521c932753c76fe96">QuadModel::QuadModel</a>(<span class="keyword">const</span> <a class="code" href="classqbpp_1_1QuadModel.html">qbpp::QuadModel</a> &amp;quad_model, <span class="keywordtype">bool</span> verbose)</div>
<div class="line"><a name="l00223"></a><span class="lineno">  223</span>&#160;    : <a class="code" href="namespaceqbpp.html">qbpp</a>::<a class="code" href="classqbpp__grb_1_1QuadModel.html">QuadModel</a>(quad_model), grb_env(true) {</div>
<div class="line"><a name="l00224"></a><span class="lineno">  224</span>&#160;  <span class="comment">// Suppress Gurobi outputs to the screen.</span></div>
<div class="line"><a name="l00225"></a><span class="lineno">  225</span>&#160;  <a class="code" href="classqbpp__grb_1_1QuadModel.html#adc1c24076ad72f4a4f83eee52dcc06ae">grb_env</a>.set(GRB_IntParam_OutputFlag, 0);</div>
<div class="line"><a name="l00226"></a><span class="lineno">  226</span>&#160;  <span class="keywordflow">if</span> (verbose) {</div>
<div class="line"><a name="l00227"></a><span class="lineno">  227</span>&#160;    <a class="code" href="classqbpp__grb_1_1QuadModel.html#adc1c24076ad72f4a4f83eee52dcc06ae">grb_env</a>.set(<span class="stringliteral">&quot;OutputFlag&quot;</span>, <span class="stringliteral">&quot;1&quot;</span>);</div>
<div class="line"><a name="l00228"></a><span class="lineno">  228</span>&#160;  }</div>
<div class="line"><a name="l00229"></a><span class="lineno">  229</span>&#160;  <a class="code" href="qbpp__grb_8hpp.html#aed547c72240001f6a5ef86636bb62d66">GRB_SAFE_CALL</a>(<a class="code" href="classqbpp__grb_1_1QuadModel.html#adc1c24076ad72f4a4f83eee52dcc06ae">grb_env</a>.start());</div>
<div class="line"><a name="l00230"></a><span class="lineno">  230</span>&#160; </div>
<div class="line"><a name="l00231"></a><span class="lineno">  231</span>&#160;  <a class="code" href="classqbpp__grb_1_1QuadModel.html#a8f716ca578f5d6f35b168662f107bce6">grb_model_ptr</a> = std::make_unique&lt;GRBModel&gt;(<a class="code" href="classqbpp__grb_1_1QuadModel.html#adc1c24076ad72f4a4f83eee52dcc06ae">grb_env</a>);</div>
<div class="line"><a name="l00232"></a><span class="lineno">  232</span>&#160; </div>
<div class="line"><a name="l00233"></a><span class="lineno">  233</span>&#160;  <span class="comment">//  Objective is minimization.</span></div>
<div class="line"><a name="l00234"></a><span class="lineno">  234</span>&#160;  <a class="code" href="qbpp__grb_8hpp.html#aed547c72240001f6a5ef86636bb62d66">GRB_SAFE_CALL</a>(<a class="code" href="classqbpp__grb_1_1QuadModel.html#a8f716ca578f5d6f35b168662f107bce6">grb_model_ptr</a>-&gt;set(GRB_IntAttr_ModelSense, GRB_MINIMIZE));</div>
<div class="line"><a name="l00235"></a><span class="lineno">  235</span>&#160; </div>
<div class="line"><a name="l00236"></a><span class="lineno">  236</span>&#160;  <span class="comment">// Add binary variables to the Gurobi model.</span></div>
<div class="line"><a name="l00237"></a><span class="lineno">  237</span>&#160;  <a class="code" href="classqbpp__grb_1_1QuadModel.html#ad7ee65c3f5130a2c5b2e70e75dce1d41">grb_x</a> = <a class="code" href="classqbpp__grb_1_1QuadModel.html#a8f716ca578f5d6f35b168662f107bce6">grb_model_ptr</a>-&gt;addVars(<a class="code" href="classqbpp_1_1Model.html#a5cf2bb955c3aa25951ca499d7d8635d2">var_count</a>(), GRB_BINARY);</div>
<div class="line"><a name="l00238"></a><span class="lineno">  238</span>&#160;  <span class="comment">// Compute objective function of QUBO problem.</span></div>
<div class="line"><a name="l00239"></a><span class="lineno">  239</span>&#160;  GRBQuadExpr obj;</div>
<div class="line"><a name="l00240"></a><span class="lineno">  240</span>&#160;  obj += quad_model.<a class="code" href="classqbpp_1_1Model.html#a3b0641c97573b248f1877c2826277102">constant</a>();</div>
<div class="line"><a name="l00241"></a><span class="lineno">  241</span>&#160;  <span class="keywordflow">for</span> (<a class="code" href="namespaceqbpp.html#a5cf436100ede362797d0c0501ea50a5a">qbpp::vindex_t</a> i = 0; i &lt; <a class="code" href="classqbpp_1_1Model.html#a5cf2bb955c3aa25951ca499d7d8635d2">var_count</a>(); ++i) {</div>
<div class="line"><a name="l00242"></a><span class="lineno">  242</span>&#160;    <span class="keywordflow">if</span> (quad_model.<a class="code" href="classqbpp_1_1QuadModel.html#a591e74de688dbff1339c2c9d2352df65">linear</a>(i) != 0) obj += quad_model.<a class="code" href="classqbpp_1_1QuadModel.html#a591e74de688dbff1339c2c9d2352df65">linear</a>(i) * <a class="code" href="classqbpp__grb_1_1QuadModel.html#ad7ee65c3f5130a2c5b2e70e75dce1d41">grb_x</a>[i];</div>
<div class="line"><a name="l00243"></a><span class="lineno">  243</span>&#160;  }</div>
<div class="line"><a name="l00244"></a><span class="lineno">  244</span>&#160;  <span class="keywordflow">for</span> (<a class="code" href="namespaceqbpp.html#a5cf436100ede362797d0c0501ea50a5a">qbpp::vindex_t</a> i = 0; i &lt; <a class="code" href="classqbpp_1_1Model.html#a5cf2bb955c3aa25951ca499d7d8635d2">var_count</a>(); ++i) {</div>
<div class="line"><a name="l00245"></a><span class="lineno">  245</span>&#160;    <span class="keywordflow">for</span> (<a class="code" href="namespaceqbpp.html#a5cf436100ede362797d0c0501ea50a5a">qbpp::vindex_t</a> j = 0; j &lt; quad_model.<a class="code" href="classqbpp_1_1QuadModel.html#ada71ec32906c9d8c73bd25c4267bafc5">degree</a>(i); ++j) {</div>
<div class="line"><a name="l00246"></a><span class="lineno">  246</span>&#160;      <span class="keyword">auto</span> [k, coeff] = quad_model.<a class="code" href="classqbpp_1_1QuadModel.html#a6624038d4ff117176c37de2d1260f471">quadratic</a>(i, j);</div>
<div class="line"><a name="l00247"></a><span class="lineno">  247</span>&#160;      <span class="keywordflow">if</span> (i &lt; k) obj += coeff * <a class="code" href="classqbpp__grb_1_1QuadModel.html#ad7ee65c3f5130a2c5b2e70e75dce1d41">grb_x</a>[i] * <a class="code" href="classqbpp__grb_1_1QuadModel.html#ad7ee65c3f5130a2c5b2e70e75dce1d41">grb_x</a>[k];</div>
<div class="line"><a name="l00248"></a><span class="lineno">  248</span>&#160;    }</div>
<div class="line"><a name="l00249"></a><span class="lineno">  249</span>&#160;  }</div>
<div class="line"><a name="l00250"></a><span class="lineno">  250</span>&#160; </div>
<div class="line"><a name="l00251"></a><span class="lineno">  251</span>&#160;  <span class="comment">// Set the objective function to the Gurobi model.</span></div>
<div class="line"><a name="l00252"></a><span class="lineno">  252</span>&#160;  <a class="code" href="qbpp__grb_8hpp.html#aed547c72240001f6a5ef86636bb62d66">GRB_SAFE_CALL</a>(<a class="code" href="classqbpp__grb_1_1QuadModel.html#a8f716ca578f5d6f35b168662f107bce6">grb_model_ptr</a>-&gt;setObjective(obj));</div>
<div class="line"><a name="l00253"></a><span class="lineno">  253</span>&#160;}</div>
<div class="line"><a name="l00254"></a><span class="lineno">  254</span>&#160; </div>
<div class="line"><a name="l00255"></a><span class="lineno"><a class="line" href="group__qbpp__grb.html#ga26577449808e4be323597053db5adeff">  255</a></span>&#160;<span class="keyword">inline</span> <span class="keywordtype">void</span> <a class="code" href="group__qbpp__grb.html#ga896e8acb781305f1539e390d360a12de">QuadModel::set</a>(<a class="code" href="classqbpp__grb_1_1Callback.html">Callback</a> &amp;cb) { <a class="code" href="classqbpp__grb_1_1QuadModel.html#a8f716ca578f5d6f35b168662f107bce6">grb_model_ptr</a>-&gt;setCallback(&amp;cb); }</div>
<div class="line"><a name="l00256"></a><span class="lineno">  256</span>&#160; </div>
<div class="line"><a name="l00257"></a><span class="lineno"><a class="line" href="classqbpp__grb_1_1QuadModel.html#ad3add2ab2e0d80e5d0d8123deebc6e98">  257</a></span>&#160;<span class="keyword">inline</span> <a class="code" href="classqbpp__grb_1_1Sol.html">Sol</a> <a class="code" href="classqbpp__grb_1_1QuadModel.html#ad3add2ab2e0d80e5d0d8123deebc6e98">QuadModel::optimize</a>() {</div>
<div class="line"><a name="l00258"></a><span class="lineno">  258</span>&#160;  <a class="code" href="classqbpp__grb_1_1Sol.html">Sol</a> sol(*<span class="keyword">this</span>);</div>
<div class="line"><a name="l00259"></a><span class="lineno">  259</span>&#160;  <a class="code" href="qbpp__grb_8hpp.html#aed547c72240001f6a5ef86636bb62d66">GRB_SAFE_CALL</a>(<a class="code" href="classqbpp__grb_1_1QuadModel.html#a8f716ca578f5d6f35b168662f107bce6">grb_model_ptr</a>-&gt;optimize());</div>
<div class="line"><a name="l00260"></a><span class="lineno">  260</span>&#160;  sol.<a class="code" href="classqbpp_1_1Sol.html#a16504ce467c6fbfcefea4171a492bef1">set_energy</a>(std::round(<a class="code" href="classqbpp__grb_1_1QuadModel.html#a8f716ca578f5d6f35b168662f107bce6">grb_model_ptr</a>-&gt;get(GRB_DoubleAttr_ObjVal)));</div>
<div class="line"><a name="l00261"></a><span class="lineno">  261</span>&#160;  sol.<a class="code" href="classqbpp__grb_1_1Sol.html#ab77394cbb7dade280051026987c264c9">set_bound</a>(std::round(<a class="code" href="classqbpp__grb_1_1QuadModel.html#a8f716ca578f5d6f35b168662f107bce6">grb_model_ptr</a>-&gt;get(GRB_DoubleAttr_ObjBound)));</div>
<div class="line"><a name="l00262"></a><span class="lineno">  262</span>&#160;  <span class="keywordflow">for</span> (<a class="code" href="namespaceqbpp.html#a5cf436100ede362797d0c0501ea50a5a">qbpp::vindex_t</a> i = 0; i &lt; <a class="code" href="classqbpp_1_1Model.html#a5cf2bb955c3aa25951ca499d7d8635d2">var_count</a>(); ++i)</div>
<div class="line"><a name="l00263"></a><span class="lineno">  263</span>&#160;    sol.<a class="code" href="classqbpp_1_1Sol.html#a2f7494eb4a254dd19b0261e801bacffb">set</a>(i, <span class="keywordtype">int</span>(<a class="code" href="classqbpp__grb_1_1QuadModel.html#ad7ee65c3f5130a2c5b2e70e75dce1d41">grb_x</a>[i].get(GRB_DoubleAttr_X)));</div>
<div class="line"><a name="l00264"></a><span class="lineno">  264</span>&#160;  <span class="keywordflow">return</span> sol;</div>
<div class="line"><a name="l00265"></a><span class="lineno">  265</span>&#160;}</div>
<div class="line"><a name="l00266"></a><span class="lineno">  266</span>&#160; </div>
<div class="line"><a name="l00267"></a><span class="lineno">  267</span>&#160;<span class="comment">//================================</span></div>
<div class="line"><a name="l00268"></a><span class="lineno">  268</span>&#160;<span class="comment">// Class Callback member functions</span></div>
<div class="line"><a name="l00269"></a><span class="lineno">  269</span>&#160;<span class="comment">//================================</span></div>
<div class="line"><a name="l00270"></a><span class="lineno">  270</span>&#160; </div>
<div class="line"><a name="l00271"></a><span class="lineno"><a class="line" href="classqbpp__grb_1_1Callback.html#a6d111191d65a98194fec8779fac6dadd">  271</a></span>&#160;<span class="keyword">inline</span> <a class="code" href="classqbpp__grb_1_1Sol.html">Sol</a> <a class="code" href="classqbpp__grb_1_1Callback.html#a6d111191d65a98194fec8779fac6dadd">Callback::get_sol</a>() {</div>
<div class="line"><a name="l00272"></a><span class="lineno">  272</span>&#160;  <a class="code" href="classqbpp__grb_1_1Sol.html">Sol</a> sol(<a class="code" href="classqbpp__grb_1_1Callback.html#a1f4dc0f4fe776c295267f83a044a3028">quad_model</a>);</div>
<div class="line"><a name="l00273"></a><span class="lineno">  273</span>&#160;  std::lock_guard&lt;std::mutex&gt; lock(<a class="code" href="classqbpp__grb_1_1Callback.html#ab215ecd1f6d8a98759815e7279edb7b3">mtx</a>);</div>
<div class="line"><a name="l00274"></a><span class="lineno">  274</span>&#160;  <span class="comment">// Intentionally commented out because the solution obtained by getSolution</span></div>
<div class="line"><a name="l00275"></a><span class="lineno">  275</span>&#160;  <span class="comment">// may provide corrupted values when Gurobi finds an optimal solution.</span></div>
<div class="line"><a name="l00276"></a><span class="lineno">  276</span>&#160;  <span class="comment">// sol.set_energy(std::round(getDoubleInfoPublic(GRB_CB_MIPSOL_OBJ)));</span></div>
<div class="line"><a name="l00277"></a><span class="lineno">  277</span>&#160;  sol.<a class="code" href="classqbpp__grb_1_1Sol.html#ab77394cbb7dade280051026987c264c9">set_bound</a>(std::round(<a class="code" href="classqbpp__grb_1_1Callback.html#a5cb91d68abcb10374e9f56d540b26a83">getDoubleInfoPublic</a>(GRB_CB_MIPSOL_OBJBND)));</div>
<div class="line"><a name="l00278"></a><span class="lineno">  278</span>&#160;  <span class="comment">// Get the solution from Gurobi Optimizer.</span></div>
<div class="line"><a name="l00279"></a><span class="lineno">  279</span>&#160;  <span class="keywordflow">for</span> (<a class="code" href="namespaceqbpp.html#a5cf436100ede362797d0c0501ea50a5a">qbpp::vindex_t</a> i = 0; i &lt; <a class="code" href="classqbpp__grb_1_1Callback.html#a1f4dc0f4fe776c295267f83a044a3028">quad_model</a>.<a class="code" href="classqbpp_1_1Model.html#a5cf2bb955c3aa25951ca499d7d8635d2">var_count</a>(); ++i) {</div>
<div class="line"><a name="l00280"></a><span class="lineno">  280</span>&#160;    sol.<a class="code" href="classqbpp_1_1Sol.html#a2f7494eb4a254dd19b0261e801bacffb">set</a>(i, <span class="keywordtype">int</span>(<a class="code" href="classqbpp__grb_1_1Callback.html#a40e9f9bd012d81ab6d96884eee327c20">getSolutionPublic</a>(<a class="code" href="classqbpp__grb_1_1Callback.html#a1f4dc0f4fe776c295267f83a044a3028">quad_model</a>.<a class="code" href="classqbpp__grb_1_1QuadModel.html#a9725ebdf79f6eb70a09de6425f7c5b68">get_grb_var</a>(i))));</div>
<div class="line"><a name="l00281"></a><span class="lineno">  281</span>&#160;  }</div>
<div class="line"><a name="l00282"></a><span class="lineno">  282</span>&#160;  <span class="comment">// eval is used to compute the energy of the solution instead of MIPSOL_OBJ.</span></div>
<div class="line"><a name="l00283"></a><span class="lineno">  283</span>&#160;  sol.<a class="code" href="classqbpp_1_1Sol.html#a16504ce467c6fbfcefea4171a492bef1">set_energy</a>(<a class="code" href="namespaceqbpp.html#ab5da6d0960920098998bb7347082ee6b">eval</a>(<a class="code" href="classqbpp__grb_1_1Callback.html#a1f4dc0f4fe776c295267f83a044a3028">quad_model</a>, sol));</div>
<div class="line"><a name="l00284"></a><span class="lineno">  284</span>&#160;  <span class="keywordflow">return</span> sol;</div>
<div class="line"><a name="l00285"></a><span class="lineno">  285</span>&#160;}</div>
<div class="line"><a name="l00286"></a><span class="lineno">  286</span>&#160; </div>
<div class="line"><a name="l00287"></a><span class="lineno">  287</span>&#160;}  <span class="comment">// namespace qbpp_grb</span></div>
<div class="line"><a name="l00288"></a><span class="lineno">  288</span>&#160;<span class="preprocessor">#endif  // __GUROBI_QBPP_HPP__</span></div>
</div><!-- fragment --></div><!-- contents -->
<div class="ttc" id="aclassqbpp_1_1Sol_html_a16504ce467c6fbfcefea4171a492bef1"><div class="ttname"><a href="classqbpp_1_1Sol.html#a16504ce467c6fbfcefea4171a492bef1">qbpp::Sol::set_energy</a></div><div class="ttdeci">void set_energy(energy_t energy)</div><div class="ttdef"><b>Definition:</b> <a href="qbpp_8hpp_source.html#l02304">qbpp.hpp:2304</a></div></div>
<div class="ttc" id="aclassqbpp__grb_1_1Callback_html_a40e9f9bd012d81ab6d96884eee327c20"><div class="ttname"><a href="classqbpp__grb_1_1Callback.html#a40e9f9bd012d81ab6d96884eee327c20">qbpp_grb::Callback::getSolutionPublic</a></div><div class="ttdeci">double getSolutionPublic(GRBVar v)</div><div class="ttdoc">Calls getSolution() of GRBCallback.</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__grb_8hpp_source.html#l00215">qbpp_grb.hpp:215</a></div></div>
<div class="ttc" id="aqbpp__grb_8hpp_html_aed547c72240001f6a5ef86636bb62d66"><div class="ttname"><a href="qbpp__grb_8hpp.html#aed547c72240001f6a5ef86636bb62d66">GRB_SAFE_CALL</a></div><div class="ttdeci">#define GRB_SAFE_CALL(func)</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__grb_8hpp_source.html#l00016">qbpp_grb.hpp:16</a></div></div>
<div class="ttc" id="aclassqbpp__grb_1_1Callback_html_a6d111191d65a98194fec8779fac6dadd"><div class="ttname"><a href="classqbpp__grb_1_1Callback.html#a6d111191d65a98194fec8779fac6dadd">qbpp_grb::Callback::get_sol</a></div><div class="ttdeci">Sol get_sol()</div><div class="ttdoc">Get the solution obtained by Gurobi Optimizer.</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__grb_8hpp_source.html#l00271">qbpp_grb.hpp:271</a></div></div>
<div class="ttc" id="agroup__qbpp__grb_html_ga896e8acb781305f1539e390d360a12de"><div class="ttname"><a href="group__qbpp__grb.html#ga896e8acb781305f1539e390d360a12de">qbpp_grb::QuadModel::set</a></div><div class="ttdeci">void set(const std::string &amp;key, const std::string &amp;val)</div><div class="ttdoc">Sets a parameter to the Gurobi environment.</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__grb_8hpp_source.html#l00070">qbpp_grb.hpp:70</a></div></div>
<div class="ttc" id="aclassqbpp__grb_1_1Callback_html_a5cb91d68abcb10374e9f56d540b26a83"><div class="ttname"><a href="classqbpp__grb_1_1Callback.html#a5cb91d68abcb10374e9f56d540b26a83">qbpp_grb::Callback::getDoubleInfoPublic</a></div><div class="ttdeci">double getDoubleInfoPublic(int what)</div><div class="ttdoc">Calls GetDoubleInfo() of GRBCallback.</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__grb_8hpp_source.html#l00210">qbpp_grb.hpp:210</a></div></div>
<div class="ttc" id="anamespaceqbpp_html_a9c324f641779d1a7a3c7723f28d8cff4"><div class="ttname"><a href="namespaceqbpp.html#a9c324f641779d1a7a3c7723f28d8cff4">qbpp::energy_t</a></div><div class="ttdeci">ENERGY_TYPE energy_t</div><div class="ttdef"><b>Definition:</b> <a href="qbpp_8hpp_source.html#l00137">qbpp.hpp:137</a></div></div>
<div class="ttc" id="aclassqbpp_1_1QuadModel_html"><div class="ttname"><a href="classqbpp_1_1QuadModel.html">qbpp::QuadModel</a></div><div class="ttdef"><b>Definition:</b> <a href="qbpp_8hpp_source.html#l01892">qbpp.hpp:1892</a></div></div>
<div class="ttc" id="agroup__qbpp__grb_html_ga3e57391fc84e3e58a8fee50b0e511af1"><div class="ttname"><a href="group__qbpp__grb.html#ga3e57391fc84e3e58a8fee50b0e511af1">qbpp_grb::QuadModel::set_time_limit</a></div><div class="ttdeci">void set_time_limit(uint32_t time_limit)</div><div class="ttdoc">Sets time limit to the Gurobi model.</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__grb_8hpp_source.html#l00076">qbpp_grb.hpp:76</a></div></div>
<div class="ttc" id="aclassqbpp_1_1QuadModel_html_a591e74de688dbff1339c2c9d2352df65"><div class="ttname"><a href="classqbpp_1_1QuadModel.html#a591e74de688dbff1339c2c9d2352df65">qbpp::QuadModel::linear</a></div><div class="ttdeci">const std::vector&lt; coeff_t &gt; &amp; linear() const</div><div class="ttdef"><b>Definition:</b> <a href="qbpp_8hpp_source.html#l01967">qbpp.hpp:1967</a></div></div>
<div class="ttc" id="aclassqbpp_1_1Model_html_a0712035184681aeb478c5e80bb5221bd"><div class="ttname"><a href="classqbpp_1_1Model.html#a0712035184681aeb478c5e80bb5221bd">qbpp::Model::index</a></div><div class="ttdeci">vindex_t index(Var var) const</div><div class="ttdef"><b>Definition:</b> <a href="qbpp_8hpp_source.html#l01863">qbpp.hpp:1863</a></div></div>
<div class="ttc" id="aclassqbpp__grb_1_1QuadModel_html_ad3add2ab2e0d80e5d0d8123deebc6e98"><div class="ttname"><a href="classqbpp__grb_1_1QuadModel.html#ad3add2ab2e0d80e5d0d8123deebc6e98">qbpp_grb::QuadModel::optimize</a></div><div class="ttdeci">Sol optimize()</div><div class="ttdoc">Optimize the QUBO model.</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__grb_8hpp_source.html#l00257">qbpp_grb.hpp:257</a></div></div>
<div class="ttc" id="aclassqbpp__grb_1_1Callback_html_a91592303df972ec49c2279459a681f05"><div class="ttname"><a href="classqbpp__grb_1_1Callback.html#a91592303df972ec49c2279459a681f05">qbpp_grb::Callback::Callback</a></div><div class="ttdeci">Callback(const QuadModel &amp;quad_model)</div><div class="ttdoc">Constructor: a new Callback object.</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__grb_8hpp_source.html#l00178">qbpp_grb.hpp:178</a></div></div>
<div class="ttc" id="aclassqbpp_1_1QuadModel_html_a6624038d4ff117176c37de2d1260f471"><div class="ttname"><a href="classqbpp_1_1QuadModel.html#a6624038d4ff117176c37de2d1260f471">qbpp::QuadModel::quadratic</a></div><div class="ttdeci">const std::vector&lt; std::vector&lt; std::pair&lt; vindex_t, coeff_t &gt; &gt; &gt; &amp; quadratic() const</div><div class="ttdef"><b>Definition:</b> <a href="qbpp_8hpp_source.html#l01971">qbpp.hpp:1971</a></div></div>
<div class="ttc" id="aclassqbpp__grb_1_1QuadModel_html_a8f716ca578f5d6f35b168662f107bce6"><div class="ttname"><a href="classqbpp__grb_1_1QuadModel.html#a8f716ca578f5d6f35b168662f107bce6">qbpp_grb::QuadModel::grb_model_ptr</a></div><div class="ttdeci">std::shared_ptr&lt; GRBModel &gt; grb_model_ptr</div><div class="ttdoc">Gurobi model.</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__grb_8hpp_source.html#l00047">qbpp_grb.hpp:47</a></div></div>
<div class="ttc" id="aclassqbpp__grb_1_1QuadModel_html_a7261179c8f0e1a20859b84df22ab6e5f"><div class="ttname"><a href="classqbpp__grb_1_1QuadModel.html#a7261179c8f0e1a20859b84df22ab6e5f">qbpp_grb::QuadModel::write</a></div><div class="ttdeci">void write(std::string filename)</div><div class="ttdoc">Write the model to a file.</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__grb_8hpp_source.html#l00112">qbpp_grb.hpp:112</a></div></div>
<div class="ttc" id="aqbpp_8hpp_html"><div class="ttname"><a href="qbpp_8hpp.html">qbpp.hpp</a></div><div class="ttdoc">QUBO++, a C++ library for generating expressions for binary and spin variables.</div></div>
<div class="ttc" id="aclassqbpp__grb_1_1Callback_html_a1f1879a1755fa6d7d6e944c468cf1e4c"><div class="ttname"><a href="classqbpp__grb_1_1Callback.html#a1f1879a1755fa6d7d6e944c468cf1e4c">qbpp_grb::Callback::~Callback</a></div><div class="ttdeci">virtual ~Callback()=default</div></div>
<div class="ttc" id="aclassqbpp_1_1Model_html_a3b0641c97573b248f1877c2826277102"><div class="ttname"><a href="classqbpp_1_1Model.html#a3b0641c97573b248f1877c2826277102">qbpp::Model::constant</a></div><div class="ttdeci">energy_t constant() const</div><div class="ttdef"><b>Definition:</b> <a href="qbpp_8hpp_source.html#l01873">qbpp.hpp:1873</a></div></div>
<div class="ttc" id="aclassqbpp__grb_1_1QuadModel_html_aebda307a8066b4601533834cfbeb1b6e"><div class="ttname"><a href="classqbpp__grb_1_1QuadModel.html#aebda307a8066b4601533834cfbeb1b6e">qbpp_grb::QuadModel::get_grb_model</a></div><div class="ttdeci">GRBModel &amp; get_grb_model()</div><div class="ttdoc">Get the Gurobi model.</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__grb_8hpp_source.html#l00096">qbpp_grb.hpp:96</a></div></div>
<div class="ttc" id="aclassqbpp__grb_1_1Sol_html_a0ec6fb21d73edc9e7bbf6a585619b4f4"><div class="ttname"><a href="classqbpp__grb_1_1Sol.html#a0ec6fb21d73edc9e7bbf6a585619b4f4">qbpp_grb::Sol::get_bound</a></div><div class="ttdeci">qbpp::energy_t get_bound() const</div><div class="ttdoc">Gets the energy bound of the solution.</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__grb_8hpp_source.html#l00136">qbpp_grb.hpp:136</a></div></div>
<div class="ttc" id="aclassqbpp__grb_1_1Sol_html_ab77394cbb7dade280051026987c264c9"><div class="ttname"><a href="classqbpp__grb_1_1Sol.html#ab77394cbb7dade280051026987c264c9">qbpp_grb::Sol::set_bound</a></div><div class="ttdeci">qbpp::energy_t set_bound(qbpp::energy_t eval)</div><div class="ttdoc">Sets the energy bound of the solution.</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__grb_8hpp_source.html#l00140">qbpp_grb.hpp:140</a></div></div>
<div class="ttc" id="aclassqbpp__grb_1_1QuadModel_html_a9725ebdf79f6eb70a09de6425f7c5b68"><div class="ttname"><a href="classqbpp__grb_1_1QuadModel.html#a9725ebdf79f6eb70a09de6425f7c5b68">qbpp_grb::QuadModel::get_grb_var</a></div><div class="ttdeci">GRBVar get_grb_var(int index) const</div><div class="ttdoc">Get the Gurobi variable from the index.</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__grb_8hpp_source.html#l00092">qbpp_grb.hpp:92</a></div></div>
<div class="ttc" id="aclassqbpp__grb_1_1Callback_html_a82a40b2deeafa027d72f778273efb2ba"><div class="ttname"><a href="classqbpp__grb_1_1Callback.html#a82a40b2deeafa027d72f778273efb2ba">qbpp_grb::Callback::grb_model</a></div><div class="ttdeci">const GRBModel &amp; grb_model</div><div class="ttdoc">Shortcut to the GRBModel in QuadModel.</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__grb_8hpp_source.html#l00155">qbpp_grb.hpp:155</a></div></div>
<div class="ttc" id="aclassqbpp__grb_1_1Callback_html_ab215ecd1f6d8a98759815e7279edb7b3"><div class="ttname"><a href="classqbpp__grb_1_1Callback.html#ab215ecd1f6d8a98759815e7279edb7b3">qbpp_grb::Callback::mtx</a></div><div class="ttdeci">std::mutex mtx</div><div class="ttdoc">Mutex to lock the critical section in get_sol()</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__grb_8hpp_source.html#l00164">qbpp_grb.hpp:164</a></div></div>
<div class="ttc" id="aabs2_8hpp_html"><div class="ttname"><a href="abs2_8hpp.html">abs2.hpp</a></div><div class="ttdoc">API for the ABS2 GPU QUBO Solver.</div></div>
<div class="ttc" id="aclassqbpp__grb_1_1Sol_html_a76420c04ca359bf4b295f0a2f49e42ef"><div class="ttname"><a href="classqbpp__grb_1_1Sol.html#a76420c04ca359bf4b295f0a2f49e42ef">qbpp_grb::Sol::bound</a></div><div class="ttdeci">qbpp::energy_t bound</div><div class="ttdoc">Energy Bound obtained by of the Grobi Optimizer.</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__grb_8hpp_source.html#l00122">qbpp_grb.hpp:122</a></div></div>
<div class="ttc" id="anamespaceqbpp_html_a4184afa358539dbcf5f35e63b49ce05b"><div class="ttname"><a href="namespaceqbpp.html#a4184afa358539dbcf5f35e63b49ce05b">qbpp::get_time</a></div><div class="ttdeci">double get_time()</div><div class="ttdef"><b>Definition:</b> <a href="qbpp_8hpp_source.html#l05311">qbpp.hpp:5311</a></div></div>
<div class="ttc" id="agroup__qbpp__grb_html_gab69ed0d8b924d9bae6c49c975ba51dcc"><div class="ttname"><a href="group__qbpp__grb.html#gab69ed0d8b924d9bae6c49c975ba51dcc">qbpp_grb::Sol::Sol</a></div><div class="ttdeci">Sol(const qbpp::QuadModel &amp;quad_model)</div><div class="ttdoc">Creates a solution object from a model.</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__grb_8hpp_source.html#l00130">qbpp_grb.hpp:130</a></div></div>
<div class="ttc" id="anamespaceqbpp_html"><div class="ttname"><a href="namespaceqbpp.html">qbpp</a></div><div class="ttdoc">Generates a QUBO Expression for the Graph Coloring Problem using QUBO++ library.</div><div class="ttdef"><b>Definition:</b> <a href="qbpp_8hpp_source.html#l00052">qbpp.hpp:52</a></div></div>
<div class="ttc" id="anamespaceqbpp__grb_html"><div class="ttname"><a href="namespaceqbpp__grb.html">qbpp_grb</a></div><div class="ttdoc">Namespace to use Gurobi optimizer from QUBO++ library.</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__grb_8hpp_source.html#l00025">qbpp_grb.hpp:25</a></div></div>
<div class="ttc" id="aclassqbpp__grb_1_1QuadModel_html_ad7ee65c3f5130a2c5b2e70e75dce1d41"><div class="ttname"><a href="classqbpp__grb_1_1QuadModel.html#ad7ee65c3f5130a2c5b2e70e75dce1d41">qbpp_grb::QuadModel::grb_x</a></div><div class="ttdeci">GRBVar * grb_x</div><div class="ttdoc">Pointer to Gurobi variables.</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__grb_8hpp_source.html#l00049">qbpp_grb.hpp:49</a></div></div>
<div class="ttc" id="aclassqbpp__grb_1_1Callback_html_a14b0a63d9776a7839e34f1323d239f08"><div class="ttname"><a href="classqbpp__grb_1_1Callback.html#a14b0a63d9776a7839e34f1323d239f08">qbpp_grb::Callback::abort_if_target_energy</a></div><div class="ttdeci">void abort_if_target_energy(qbpp::energy_t energy)</div><div class="ttdoc">Abort the optimization process if the target energy is achieved.</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__grb_8hpp_source.html#l00169">qbpp_grb.hpp:169</a></div></div>
<div class="ttc" id="aclassqbpp__grb_1_1Callback_html_a1f4dc0f4fe776c295267f83a044a3028"><div class="ttname"><a href="classqbpp__grb_1_1Callback.html#a1f4dc0f4fe776c295267f83a044a3028">qbpp_grb::Callback::quad_model</a></div><div class="ttdeci">const QuadModel quad_model</div><div class="ttdoc">QUBO model.</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__grb_8hpp_source.html#l00152">qbpp_grb.hpp:152</a></div></div>
<div class="ttc" id="anamespaceqbpp_html_a5cf436100ede362797d0c0501ea50a5a"><div class="ttname"><a href="namespaceqbpp.html#a5cf436100ede362797d0c0501ea50a5a">qbpp::vindex_t</a></div><div class="ttdeci">uint32_t vindex_t</div><div class="ttdef"><b>Definition:</b> <a href="qbpp_8hpp_source.html#l00129">qbpp.hpp:129</a></div></div>
<div class="ttc" id="aclassqbpp_1_1Sol_html_a2f7494eb4a254dd19b0261e801bacffb"><div class="ttname"><a href="classqbpp_1_1Sol.html#a2f7494eb4a254dd19b0261e801bacffb">qbpp::Sol::set</a></div><div class="ttdeci">void set(vindex_t index, bool value)</div><div class="ttdef"><b>Definition:</b> <a href="qbpp_8hpp_source.html#l02270">qbpp.hpp:2270</a></div></div>
<div class="ttc" id="aclassqbpp__grb_1_1Sol_html"><div class="ttname"><a href="classqbpp__grb_1_1Sol.html">qbpp_grb::Sol</a></div><div class="ttdoc">Class to store a solution of a QUBO model using Gurobi Optimizer.</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__grb_8hpp_source.html#l00120">qbpp_grb.hpp:120</a></div></div>
<div class="ttc" id="aclassqbpp_1_1Sol_html_a3ad455870df155ffed4701ce070cf335"><div class="ttname"><a href="classqbpp_1_1Sol.html#a3ad455870df155ffed4701ce070cf335">qbpp::Sol::energy</a></div><div class="ttdeci">energy_t energy() const</div><div class="ttdef"><b>Definition:</b> <a href="qbpp_8hpp_source.html#l02299">qbpp.hpp:2299</a></div></div>
<div class="ttc" id="aclassqbpp__grb_1_1QuadModel_html"><div class="ttname"><a href="classqbpp__grb_1_1QuadModel.html">qbpp_grb::QuadModel</a></div><div class="ttdoc">Class to store a QUBO model using Gurobi Optimizer through QUBO++ library.</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__grb_8hpp_source.html#l00042">qbpp_grb.hpp:42</a></div></div>
<div class="ttc" id="aclassqbpp_1_1QuadModel_html_ada71ec32906c9d8c73bd25c4267bafc5"><div class="ttname"><a href="classqbpp_1_1QuadModel.html#ada71ec32906c9d8c73bd25c4267bafc5">qbpp::QuadModel::degree</a></div><div class="ttdeci">const std::vector&lt; vindex_t &gt; &amp; degree() const</div><div class="ttdef"><b>Definition:</b> <a href="qbpp_8hpp_source.html#l01969">qbpp.hpp:1969</a></div></div>
<div class="ttc" id="aclassqbpp__grb_1_1Callback_html_a8b785f76ab9c73fa92b97bb8da6f5e37"><div class="ttname"><a href="classqbpp__grb_1_1Callback.html#a8b785f76ab9c73fa92b97bb8da6f5e37">qbpp_grb::Callback::callback</a></div><div class="ttdeci">virtual void callback() override</div><div class="ttdoc">Default callback function for Gurobi Optimizer.</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__grb_8hpp_source.html#l00192">qbpp_grb.hpp:192</a></div></div>
<div class="ttc" id="aclassqbpp_1_1Sol_html"><div class="ttname"><a href="classqbpp_1_1Sol.html">qbpp::Sol</a></div><div class="ttdef"><b>Definition:</b> <a href="qbpp_8hpp_source.html#l02182">qbpp.hpp:2182</a></div></div>
<div class="ttc" id="aclassqbpp__grb_1_1Callback_html"><div class="ttname"><a href="classqbpp__grb_1_1Callback.html">qbpp_grb::Callback</a></div><div class="ttdoc">Class to manage a callback function called by Gurobi Optimizer.</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__grb_8hpp_source.html#l00149">qbpp_grb.hpp:149</a></div></div>
<div class="ttc" id="aclassqbpp__grb_1_1Callback_html_ae44d18cfb962fc7ccf90d5305e76f288"><div class="ttname"><a href="classqbpp__grb_1_1Callback.html#ae44d18cfb962fc7ccf90d5305e76f288">qbpp_grb::Callback::target_energy</a></div><div class="ttdeci">std::optional&lt; qbpp::energy_t &gt; target_energy</div><div class="ttdoc">Target energy to stop the Gurobi optimizer.</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__grb_8hpp_source.html#l00158">qbpp_grb.hpp:158</a></div></div>
<div class="ttc" id="aclassqbpp__grb_1_1Callback_html_acc6b06db26a7cf40e83cb1521c3431f3"><div class="ttname"><a href="classqbpp__grb_1_1Callback.html#acc6b06db26a7cf40e83cb1521c3431f3">qbpp_grb::Callback::set_target_energy</a></div><div class="ttdeci">void set_target_energy(qbpp::energy_t target_energy)</div><div class="ttdoc">Set the target energy for Gurobi Optimizer.</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__grb_8hpp_source.html#l00203">qbpp_grb.hpp:203</a></div></div>
<div class="ttc" id="anamespaceqbpp_html_ab5da6d0960920098998bb7347082ee6b"><div class="ttname"><a href="namespaceqbpp.html#ab5da6d0960920098998bb7347082ee6b">qbpp::eval</a></div><div class="ttdeci">energy_t eval(const Expr &amp;expr, const Sol &amp;sol)</div><div class="ttdef"><b>Definition:</b> <a href="qbpp_8hpp_source.html#l02868">qbpp.hpp:2868</a></div></div>
<div class="ttc" id="aclassqbpp_1_1Model_html_a5cf2bb955c3aa25951ca499d7d8635d2"><div class="ttname"><a href="classqbpp_1_1Model.html#a5cf2bb955c3aa25951ca499d7d8635d2">qbpp::Model::var_count</a></div><div class="ttdeci">vindex_t var_count() const</div><div class="ttdef"><b>Definition:</b> <a href="qbpp_8hpp_source.html#l01859">qbpp.hpp:1859</a></div></div>
<div class="ttc" id="agroup__qbpp__grb_html_ga3e5106bc14a17c6521c932753c76fe96"><div class="ttname"><a href="group__qbpp__grb.html#ga3e5106bc14a17c6521c932753c76fe96">qbpp_grb::QuadModel::QuadModel</a></div><div class="ttdeci">QuadModel(const qbpp::QuadModel &amp;quad_model, bool verbose=false)</div><div class="ttdoc">Constructor to create a model from QuadModel object.</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__grb_8hpp_source.html#l00222">qbpp_grb.hpp:222</a></div></div>
<div class="ttc" id="aclassqbpp__grb_1_1QuadModel_html_adc1c24076ad72f4a4f83eee52dcc06ae"><div class="ttname"><a href="classqbpp__grb_1_1QuadModel.html#adc1c24076ad72f4a4f83eee52dcc06ae">qbpp_grb::QuadModel::grb_env</a></div><div class="ttdeci">GRBEnv grb_env</div><div class="ttdoc">Gurobi environment.</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__grb_8hpp_source.html#l00045">qbpp_grb.hpp:45</a></div></div>
<div class="ttc" id="aclassqbpp__grb_1_1QuadModel_html_a6c21b263939fac514f788ee6d7fadf9b"><div class="ttname"><a href="classqbpp__grb_1_1QuadModel.html#a6c21b263939fac514f788ee6d7fadf9b">qbpp_grb::QuadModel::get_grb_model</a></div><div class="ttdeci">const GRBModel &amp; get_grb_model() const</div><div class="ttdoc">Get the Gurobi model.</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__grb_8hpp_source.html#l00100">qbpp_grb.hpp:100</a></div></div>
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.17
</small></address>
</body>
</html>
