<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.17"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>QUBO++ Library with QUBO Solver APIs: sample/partition_exhaustive.cpp File Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/x-mathjax-config">
  MathJax.Hub.Config({
    extensions: ["tex2jax.js", "TeX/AMSmath.js", "TeX/AMSsymbols.js"],
    jax: ["input/TeX","output/HTML-CSS"],
});
</script>
<script type="text/javascript" async="async" src="https://cdn.jsdelivr.net/npm/mathjax@2/MathJax.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">QUBO++ Library with QUBO Solver APIs
   </div>
   <div id="projectbrief">Author: Koji Nakano, License: Non-commercial research and evaluation purposes without any guarantees.</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.17 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_c29eeb5af533606caea8a09e66794740.html">sample</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#func-members">Functions</a>  </div>
  <div class="headertitle">
<div class="title">partition_exhaustive.cpp File Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p>Solves the Partitioning problem using the QUBO++ Exhaustive solver.  
<a href="#details">More...</a></p>
<div class="textblock"><code>#include &lt;boost/program_options.hpp&gt;</code><br />
<code>#include &quot;<a class="el" href="qbpp_8hpp_source.html">qbpp.hpp</a>&quot;</code><br />
<code>#include &quot;<a class="el" href="qbpp__exhaustive__solver_8hpp_source.html">qbpp_exhaustive_solver.hpp</a>&quot;</code><br />
<code>#include &quot;<a class="el" href="qbpp__misc_8hpp_source.html">qbpp_misc.hpp</a>&quot;</code><br />
</div><div class="textblock"><div class="dynheader">
Include dependency graph for partition_exhaustive.cpp:</div>
<div class="dyncontent">
<div class="center"><img src="partition__exhaustive_8cpp__incl.png" border="0" usemap="#sample_2partition__exhaustive_8cpp" alt=""/></div>
<map name="sample_2partition__exhaustive_8cpp" id="sample_2partition__exhaustive_8cpp">
<area shape="rect" title="Solves the Partitioning problem using the QUBO++ Exhaustive solver." alt="" coords="2149,5,2358,32"/>
<area shape="rect" title=" " alt="" coords="1972,80,2151,107"/>
<area shape="rect" href="qbpp_8hpp.html" title="QUBO++, a C++ library for generating expressions for binary and spin variables." alt="" coords="2144,162,2219,189"/>
<area shape="rect" href="qbpp__exhaustive__solver_8hpp.html" title="Exhaustive QUBO Solver for solving QUBO problems." alt="" coords="2225,80,2412,107"/>
<area shape="rect" href="qbpp__misc_8hpp.html" title="A miscellaneous library used for sample programs of the QUBO++ library." alt="" coords="2619,80,2730,107"/>
<area shape="rect" title=" " alt="" coords="5,251,144,278"/>
<area shape="rect" title=" " alt="" coords="168,251,285,278"/>
<area shape="rect" title=" " alt="" coords="843,251,986,278"/>
<area shape="rect" title=" " alt="" coords="1011,251,1136,278"/>
<area shape="rect" title=" " alt="" coords="3009,251,3084,278"/>
<area shape="rect" title=" " alt="" coords="1160,244,1307,285"/>
<area shape="rect" title=" " alt="" coords="1331,244,1477,285"/>
<area shape="rect" title=" " alt="" coords="1502,251,1690,278"/>
<area shape="rect" title=" " alt="" coords="1714,251,1859,278"/>
<area shape="rect" title=" " alt="" coords="1884,244,2023,285"/>
<area shape="rect" title=" " alt="" coords="2047,251,2108,278"/>
<area shape="rect" title=" " alt="" coords="3108,251,3167,278"/>
<area shape="rect" title=" " alt="" coords="2133,251,2230,278"/>
<area shape="rect" title=" " alt="" coords="2255,251,2321,278"/>
<area shape="rect" title=" " alt="" coords="3191,251,3263,278"/>
<area shape="rect" title=" " alt="" coords="2345,251,2399,278"/>
<area shape="rect" title=" " alt="" coords="2423,251,2462,278"/>
<area shape="rect" title=" " alt="" coords="2487,251,2556,278"/>
<area shape="rect" title=" " alt="" coords="2580,251,2639,278"/>
<area shape="rect" title=" " alt="" coords="2663,251,2729,278"/>
<area shape="rect" title=" " alt="" coords="2753,251,2823,278"/>
<area shape="rect" title=" " alt="" coords="2847,251,2902,278"/>
<area shape="rect" title=" " alt="" coords="2927,251,2985,278"/>
<area shape="rect" title=" " alt="" coords="309,251,392,278"/>
<area shape="rect" title=" " alt="" coords="417,251,527,278"/>
<area shape="rect" title=" " alt="" coords="552,251,656,278"/>
<area shape="rect" title=" " alt="" coords="680,251,733,278"/>
<area shape="rect" title=" " alt="" coords="757,251,819,278"/>
<area shape="rect" title=" " alt="" coords="3287,251,3345,278"/>
<area shape="rect" title=" " alt="" coords="2243,162,2407,189"/>
<area shape="rect" title=" " alt="" coords="2431,162,2497,189"/>
<area shape="rect" title=" " alt="" coords="2521,162,2561,189"/>
<area shape="rect" title=" " alt="" coords="2788,162,2849,189"/>
<area shape="rect" title=" " alt="" coords="2874,162,3043,189"/>
<area shape="rect" title=" " alt="" coords="3067,155,3215,196"/>
<area shape="rect" title=" " alt="" coords="3239,155,3387,196"/>
<area shape="rect" title=" " alt="" coords="3411,162,3479,189"/>
</map>
</div>
</div>
<p><a href="partition__exhaustive_8cpp_source.html">Go to the source code of this file.</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:a3c04138a5bfe5d72780bb7e82a18e627"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="partition__exhaustive_8cpp.html#a3c04138a5bfe5d72780bb7e82a18e627">main</a> (int argc, char **argv)</td></tr>
<tr class="memdesc:a3c04138a5bfe5d72780bb7e82a18e627"><td class="mdescLeft">&#160;</td><td class="mdescRight">Solves the Partitioning problem using the QUBO++ Exhaustive Solver.  <a href="partition__exhaustive_8cpp.html#a3c04138a5bfe5d72780bb7e82a18e627">More...</a><br /></td></tr>
<tr class="separator:a3c04138a5bfe5d72780bb7e82a18e627"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p>Solves the Partitioning problem using the QUBO++ Exhaustive solver. </p>
<dl class="section author"><dt>Author</dt><dd>Koji Nakano </dd></dl>
<dl class="section version"><dt>Version</dt><dd>2025-01-04 </dd></dl>

<p class="definition">Definition in file <a class="el" href="partition__exhaustive_8cpp_source.html">partition_exhaustive.cpp</a>.</p>
</div><h2 class="groupheader">Function Documentation</h2>
<a id="a3c04138a5bfe5d72780bb7e82a18e627"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a3c04138a5bfe5d72780bb7e82a18e627">&#9670;&nbsp;</a></span>main()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int main </td>
          <td>(</td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>argc</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">char **&#160;</td>
          <td class="paramname"><em>argv</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Solves the Partitioning problem using the QUBO++ Exhaustive Solver. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">argc</td><td>Number of command-line arguments </td></tr>
    <tr><td class="paramname">argv</td><td>Command-line arguments </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>Exit code </dd></dl>
<p>The size of the input set</p>
<p>The maximum value of the input set</p>
<p>A vector to store the input set</p>
<p>Generates the input set</p>
<p>Prints the input set</p>

<p class="definition">Definition at line <a class="el" href="partition__exhaustive_8cpp_source.html#l00018">18</a> of file <a class="el" href="partition__exhaustive_8cpp_source.html">partition_exhaustive.cpp</a>.</p>
<div class="dynheader">
Here is the call graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="partition__exhaustive_8cpp_a3c04138a5bfe5d72780bb7e82a18e627_cgraph.png" border="0" usemap="#partition__exhaustive_8cpp_a3c04138a5bfe5d72780bb7e82a18e627_cgraph" alt=""/></div>
<map name="partition__exhaustive_8cpp_a3c04138a5bfe5d72780bb7e82a18e627_cgraph" id="partition__exhaustive_8cpp_a3c04138a5bfe5d72780bb7e82a18e627_cgraph">
<area shape="rect" title="Solves the Partitioning problem using the QUBO++ Exhaustive Solver." alt="" coords="5,208,56,235"/>
<area shape="rect" href="classqbpp_1_1Vector.html#a05545411bd760deb7206b0079f4f5833" title=" " alt="" coords="125,5,313,32"/>
<area shape="rect" href="namespaceqbpp.html#ab5da6d0960920098998bb7347082ee6b" title=" " alt="" coords="177,56,260,83"/>
<area shape="rect" href="classqbpp_1_1misc_1_1RandomGenerator.html#a95308c2300aeef9a881ba97786367977" title=" " alt="" coords="104,233,333,260"/>
<area shape="rect" href="classqbpp_1_1misc_1_1RandomGenerator.html#a1f4f3d675aee33dce174c8222c3bc22a" title=" " alt="" coords="119,285,319,326"/>
<area shape="rect" href="namespaceqbpp.html#abd03cc17ab61f6ab2be95751c7b92626" title=" " alt="" coords="181,107,257,133"/>
<area shape="rect" href="namespaceqbpp.html#a41f8243940eb837c282aed85287bd3af" title=" " alt="" coords="182,351,255,377"/>
<area shape="rect" href="namespaceqbpp.html#a1cf3f678bcc7eee089c75d4bc5a0b959" title=" " alt="" coords="443,401,519,428"/>
<area shape="rect" href="namespaceqbpp.html#a9041f8ee7d3f4a7c04a0d385babae285" title=" " alt="" coords="177,183,260,209"/>
<area shape="rect" href="namespaceqbpp.html#add51cb422256f9e89483b8cd0deb4721" title=" " alt="" coords="671,19,754,45"/>
<area shape="rect" href="classqbpp_1_1misc_1_1RandomGenerator.html#a6b19f6bde3a3b6ff087a908d11ece3c1" title=" " alt="" coords="381,285,581,326"/>
<area shape="rect" href="namespaceqbpp.html#a3ef6a17c9562b454351f51950aab4392" title=" " alt="" coords="412,81,551,108"/>
<area shape="rect" href="namespaceqbpp.html#a97185e5bf8d17779523cb3f13b1d54d7" title=" " alt="" coords="443,31,519,57"/>
<area shape="rect" href="classqbpp_1_1Vector.html#af719287ecdb5f44bbd0aa5d9ba3aaea1" title=" " alt="" coords="649,95,777,121"/>
<area shape="rect" href="classqbpp_1_1impl_1_1VarSet.html#a9caf4bffd9ec02f1c165dff7ddc66065" title=" " alt="" coords="405,351,557,377"/>
<area shape="rect" href="classqbpp_1_1Var.html#ac7b09bf9026a6082d8d6d63201610c24" title=" " alt="" coords="655,296,771,323"/>
<area shape="rect" href="classqbpp_1_1impl_1_1VarSet.html#a7be1fbd47c0dd05c35131c17a78a7a33" title=" " alt="" coords="1021,390,1151,431"/>
<area shape="rect" href="classqbpp_1_1impl_1_1VarSet.html#ac34a1b486a4509a9435314c7ff52339a" title=" " alt="" coords="635,397,790,424"/>
<area shape="rect" href="classqbpp_1_1impl_1_1VarSet.html#a38d1746ee4a96d7dda2ce4220b6853ec" title=" " alt="" coords="844,449,973,490"/>
<area shape="rect" href="namespaceqbpp.html#a844402a465157e00bca032af58c0a795" title=" " alt="" coords="675,448,751,475"/>
<area shape="rect" href="classqbpp_1_1Terms.html#a8c00d28cedf65dadeff5b4b5bf00492b" title=" " alt="" coords="413,183,550,209"/>
<area shape="rect" href="namespaceqbpp.html#ae06a111bb3040263deff02c501635c10" title=" " alt="" coords="433,233,529,260"/>
<area shape="rect" href="classqbpp_1_1Vector.html#a23f6c18870445548d8db321606a0a9fb" title=" " alt="" coords="629,187,796,213"/>
<area shape="rect" href="classqbpp_1_1Vector.html#a5e8bb55fb9236e098401e23a758b5927" title=" " alt="" coords="639,237,786,264"/>
</map>
</div>

</div>
</div>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.17
</small></address>
</body>
</html>
