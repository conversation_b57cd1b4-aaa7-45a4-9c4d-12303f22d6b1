<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.17"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>QUBO++ Library with QUBO Solver APIs: sample/tsp_grb.cpp File Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/x-mathjax-config">
  MathJax.Hub.Config({
    extensions: ["tex2jax.js", "TeX/AMSmath.js", "TeX/AMSsymbols.js"],
    jax: ["input/TeX","output/HTML-CSS"],
});
</script>
<script type="text/javascript" async="async" src="https://cdn.jsdelivr.net/npm/mathjax@2/MathJax.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">QUBO++ Library with QUBO Solver APIs
   </div>
   <div id="projectbrief">Author: Koji Nakano, License: Non-commercial research and evaluation purposes without any guarantees.</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.17 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_c29eeb5af533606caea8a09e66794740.html">sample</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#nested-classes">Classes</a> &#124;
<a href="#func-members">Functions</a>  </div>
  <div class="headertitle">
<div class="title">tsp_grb.cpp File Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p>Solves randomly generated Traveling Salesman Problem (TSP) using the QUBO++ Easy solver.  
<a href="#details">More...</a></p>
<div class="textblock"><code>#include &lt;boost/program_options.hpp&gt;</code><br />
<code>#include &quot;<a class="el" href="qbpp_8hpp_source.html">qbpp.hpp</a>&quot;</code><br />
<code>#include &quot;<a class="el" href="qbpp__grb_8hpp_source.html">qbpp_grb.hpp</a>&quot;</code><br />
<code>#include &quot;<a class="el" href="qbpp__misc_8hpp_source.html">qbpp_misc.hpp</a>&quot;</code><br />
<code>#include &quot;<a class="el" href="qbpp__tsp_8hpp_source.html">qbpp_tsp.hpp</a>&quot;</code><br />
</div><div class="textblock"><div class="dynheader">
Include dependency graph for tsp_grb.cpp:</div>
<div class="dyncontent">
<div class="center"><img src="tsp__grb_8cpp__incl.png" border="0" usemap="#sample_2tsp__grb_8cpp" alt=""/></div>
<map name="sample_2tsp__grb_8cpp" id="sample_2tsp__grb_8cpp">
<area shape="rect" title="Solves randomly generated Traveling Salesman Problem (TSP) using the QUBO++ Easy solver." alt="" coords="1569,5,1705,32"/>
<area shape="rect" title=" " alt="" coords="1371,80,1549,107"/>
<area shape="rect" href="qbpp_8hpp.html" title="QUBO++, a C++ library for generating expressions for binary and spin variables." alt="" coords="1613,237,1688,263"/>
<area shape="rect" href="qbpp__grb_8hpp.html" title="QUBO++ interface to call Gurobi Optimizer." alt="" coords="1499,155,1599,181"/>
<area shape="rect" href="qbpp__misc_8hpp.html" title="A miscellaneous library used for sample programs of the QUBO++ library." alt="" coords="2570,155,2681,181"/>
<area shape="rect" href="qbpp__tsp_8hpp.html" title="Generates a QUBO Expression for the Traveling Salesman Problem (TSP) using QUBO++ library." alt="" coords="2894,80,2994,107"/>
<area shape="rect" title=" " alt="" coords="1708,326,1847,353"/>
<area shape="rect" title=" " alt="" coords="1871,326,1988,353"/>
<area shape="rect" title=" " alt="" coords="2011,326,2154,353"/>
<area shape="rect" title=" " alt="" coords="2179,326,2304,353"/>
<area shape="rect" title=" " alt="" coords="2939,326,3013,353"/>
<area shape="rect" title=" " alt="" coords="2328,319,2475,360"/>
<area shape="rect" title=" " alt="" coords="2499,319,2645,360"/>
<area shape="rect" title=" " alt="" coords="89,326,277,353"/>
<area shape="rect" title=" " alt="" coords="301,326,446,353"/>
<area shape="rect" title=" " alt="" coords="471,319,609,360"/>
<area shape="rect" title=" " alt="" coords="633,326,695,353"/>
<area shape="rect" title=" " alt="" coords="5,326,64,353"/>
<area shape="rect" title=" " alt="" coords="719,326,817,353"/>
<area shape="rect" title=" " alt="" coords="841,326,908,353"/>
<area shape="rect" title=" " alt="" coords="2843,326,2915,353"/>
<area shape="rect" title=" " alt="" coords="3037,326,3091,353"/>
<area shape="rect" title=" " alt="" coords="933,326,971,353"/>
<area shape="rect" title=" " alt="" coords="2669,326,2739,353"/>
<area shape="rect" title=" " alt="" coords="996,326,1055,353"/>
<area shape="rect" title=" " alt="" coords="1079,326,1145,353"/>
<area shape="rect" title=" " alt="" coords="3115,326,3184,353"/>
<area shape="rect" title=" " alt="" coords="2763,326,2818,353"/>
<area shape="rect" title=" " alt="" coords="1169,326,1228,353"/>
<area shape="rect" title=" " alt="" coords="1252,326,1335,353"/>
<area shape="rect" title=" " alt="" coords="1359,326,1470,353"/>
<area shape="rect" title=" " alt="" coords="1495,326,1599,353"/>
<area shape="rect" title=" " alt="" coords="3208,326,3261,353"/>
<area shape="rect" title=" " alt="" coords="1623,326,1684,353"/>
<area shape="rect" title=" " alt="" coords="3285,326,3344,353"/>
<area shape="rect" href="abs2_8hpp.html" title="API for the ABS2 GPU QUBO Solver." alt="" coords="1763,237,1837,263"/>
<area shape="rect" title=" " alt="" coords="1440,237,1539,263"/>
<area shape="rect" title=" " alt="" coords="1912,237,1973,263"/>
<area shape="rect" title=" " alt="" coords="1998,237,2162,263"/>
<area shape="rect" title=" " alt="" coords="2186,237,2355,263"/>
<area shape="rect" title=" " alt="" coords="2379,229,2527,271"/>
<area shape="rect" title=" " alt="" coords="2551,229,2699,271"/>
<area shape="rect" title=" " alt="" coords="2723,237,2791,263"/>
<area shape="rect" title=" " alt="" coords="3083,237,3149,263"/>
<area shape="rect" title=" " alt="" coords="2816,237,2856,263"/>
</map>
</div>
</div>
<p><a href="tsp__grb_8cpp_source.html">Go to the source code of this file.</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="nested-classes"></a>
Classes</h2></td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">class &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classGRB__Callback.html">GRB_Callback</a></td></tr>
<tr class="memdesc:"><td class="mdescLeft">&#160;</td><td class="mdescRight">Class to define Gurobi callback function for factorization.  <a href="classGRB__Callback.html#details">More...</a><br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:a3c04138a5bfe5d72780bb7e82a18e627"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="tsp__grb_8cpp.html#a3c04138a5bfe5d72780bb7e82a18e627">main</a> (int argc, char **argv)</td></tr>
<tr class="memdesc:a3c04138a5bfe5d72780bb7e82a18e627"><td class="mdescLeft">&#160;</td><td class="mdescRight">Main function to generate a random map and solve the Traveling Salesman Problem (TSP) using the Gurobi optimizer.  <a href="tsp__grb_8cpp.html#a3c04138a5bfe5d72780bb7e82a18e627">More...</a><br /></td></tr>
<tr class="separator:a3c04138a5bfe5d72780bb7e82a18e627"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p>Solves randomly generated Traveling Salesman Problem (TSP) using the QUBO++ Easy solver. </p>
<dl class="section author"><dt>Author</dt><dd>Koji Nakano </dd></dl>
<dl class="section version"><dt>Version</dt><dd>2024-11-11 </dd></dl>

<p class="definition">Definition in file <a class="el" href="tsp__grb_8cpp_source.html">tsp_grb.cpp</a>.</p>
</div><h2 class="groupheader">Function Documentation</h2>
<a id="a3c04138a5bfe5d72780bb7e82a18e627"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a3c04138a5bfe5d72780bb7e82a18e627">&#9670;&nbsp;</a></span>main()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int main </td>
          <td>(</td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>argc</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">char **&#160;</td>
          <td class="paramname"><em>argv</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Main function to generate a random map and solve the Traveling Salesman Problem (TSP) using the Gurobi optimizer. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">argc</td><td>Number of command-line arguments. </td></tr>
    <tr><td class="paramname">argv</td><td>List of command-line arguments.</td></tr>
  </table>
  </dd>
</dl>
<p>Generates a random map with the specified number of nodes and random seed, and solves it using the Gurobi optimizer. </p>
<p>The size of the input set</p>
<p>The time limit for the solver</p>
<p>True if node 0 is fixed as the starting node</p>

<p class="definition">Definition at line <a class="el" href="tsp__grb_8cpp_source.html#l00056">56</a> of file <a class="el" href="tsp__grb_8cpp_source.html">tsp_grb.cpp</a>.</p>
<div class="dynheader">
Here is the call graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="tsp__grb_8cpp_a3c04138a5bfe5d72780bb7e82a18e627_cgraph.png" border="0" usemap="#tsp__grb_8cpp_a3c04138a5bfe5d72780bb7e82a18e627_cgraph" alt=""/></div>
<map name="tsp__grb_8cpp_a3c04138a5bfe5d72780bb7e82a18e627_cgraph" id="tsp__grb_8cpp_a3c04138a5bfe5d72780bb7e82a18e627_cgraph">
<area shape="rect" title="Main function to generate a random map and solve the Traveling Salesman Problem (TSP) using the Gurob..." alt="" coords="5,377,56,403"/>
<area shape="rect" href="classqbpp_1_1tsp_1_1DrawSimpleGraph.html#a99955897a283a208c4093d4f747491d5" title="Add an edge to the graph." alt="" coords="109,5,299,47"/>
<area shape="rect" href="classqbpp_1_1tsp_1_1DrawSimpleGraph.html#ae58cb44d06b764de7fae386abf86c0de" title="Add a node to the graph." alt="" coords="109,71,299,112"/>
<area shape="rect" href="classqbpp_1_1tsp_1_1DrawSimpleGraph.html#ae4a6e9be4fa3e068d2dd4a4ace7548b5" title="Draw the graph in a file." alt="" coords="109,136,299,177"/>
<area shape="rect" href="classqbpp_1_1tsp_1_1TSPMap.html#ace1b4039b9447fae50b62c325acefc6e" title="Generate a random map with n nodes." alt="" coords="133,201,275,243"/>
<area shape="rect" href="classqbpp__grb_1_1QuadModel.html#ad3add2ab2e0d80e5d0d8123deebc6e98" title="Optimize the QUBO model." alt="" coords="129,401,279,443"/>
<area shape="rect" href="classqbpp_1_1Model.html#a5cf2bb955c3aa25951ca499d7d8635d2" title=" " alt="" coords="387,543,546,570"/>
<area shape="rect" href="classqbpp_1_1tsp_1_1TSPSol.html#ab2abe5283ba0f9fefad71b1beee340e3" title="Print the tour." alt="" coords="135,519,273,560"/>
<area shape="rect" href="classqbpp_1_1misc_1_1RandomGenerator.html#a513a93d293d9fa08a7fee2bba9cb73ff" title=" " alt="" coords="367,259,567,300"/>
<area shape="rect" href="group__qbpp__grb.html#ga896e8acb781305f1539e390d360a12de" title="Sets a parameter to the Gurobi environment." alt="" coords="379,645,554,671"/>
<area shape="rect" href="classqbpp_1_1misc_1_1RandomGenerator.html#a1f4f3d675aee33dce174c8222c3bc22a" title=" " alt="" coords="104,327,304,368"/>
<area shape="rect" href="group__qbpp__grb.html#ga3e57391fc84e3e58a8fee50b0e511af1" title="Sets time limit to the Gurobi model." alt="" coords="129,636,279,677"/>
<area shape="rect" href="classqbpp_1_1QuadModel.html#a16af851530b1f63aca9baa8ec67ed01f" title=" " alt="" coords="125,701,283,743"/>
<area shape="rect" href="classqbpp_1_1tsp_1_1TSPMap.html#abb5ed5d5beb5b8c639384b42adae35d5" title="Add a node to the map." alt="" coords="395,24,538,65"/>
<area shape="rect" href="classqbpp_1_1tsp_1_1TSPMap.html#a6ca32411c34b040e6f09790f8376b0d7" title="Compute the Euclidean distance between two nodes." alt="" coords="647,119,811,146"/>
<area shape="rect" href="classqbpp_1_1misc_1_1RandomGenerator.html#a95308c2300aeef9a881ba97786367977" title=" " alt="" coords="352,207,581,234"/>
<area shape="rect" href="classqbpp_1_1tsp_1_1TSPMap.html#a611e61c620a84590c1ff675cc3a6e332" title="Compute the minimum distance between a new node and all other nodes." alt="" coords="395,141,538,183"/>
<area shape="rect" href="classqbpp_1_1misc_1_1RandomGenerator.html#a6b19f6bde3a3b6ff087a908d11ece3c1" title=" " alt="" coords="629,259,829,300"/>
<area shape="rect" href="classqbpp_1_1Sol.html#a2f7494eb4a254dd19b0261e801bacffb" title=" " alt="" coords="415,375,518,402"/>
<area shape="rect" href="classqbpp__grb_1_1Sol.html#ab77394cbb7dade280051026987c264c9" title="Sets the energy bound of the solution." alt="" coords="403,427,531,468"/>
<area shape="rect" href="classqbpp_1_1Sol.html#a16504ce467c6fbfcefea4171a492bef1" title=" " alt="" coords="392,493,541,519"/>
<area shape="rect" href="classqbpp_1_1Sol.html#a24f5a53fa68071dd9bcda910533bbad7" title=" " alt="" coords="671,329,787,355"/>
<area shape="rect" href="classqbpp_1_1impl_1_1BitVector.html#a4694931fe6e873494026bbbe9334f848" title=" " alt="" coords="644,379,815,406"/>
<area shape="rect" href="classqbpp_1_1Model.html#a0712035184681aeb478c5e80bb5221bd" title=" " alt="" coords="893,303,1025,330"/>
<area shape="rect" href="namespaceqbpp.html#a1cf3f678bcc7eee089c75d4bc5a0b959" title=" " alt="" coords="921,354,997,381"/>
<area shape="rect" href="classqbpp_1_1Model.html#a52da2204913c7f529be7e8db017aa14a" title=" " alt="" coords="1088,303,1207,330"/>
<area shape="rect" href="namespaceqbpp.html#a844402a465157e00bca032af58c0a795" title=" " alt="" coords="1109,354,1185,381"/>
<area shape="rect" href="namespaceqbpp.html#ab5da6d0960920098998bb7347082ee6b" title=" " alt="" coords="688,434,771,461"/>
<area shape="rect" href="namespaceqbpp.html#add51cb422256f9e89483b8cd0deb4721" title=" " alt="" coords="917,434,1000,461"/>
<area shape="rect" href="classqbpp_1_1Sol.html#a3ad455870df155ffed4701ce070cf335" title=" " alt="" coords="667,543,791,570"/>
<area shape="rect" href="classqbpp_1_1Sol.html#a90c464b3df40d670646cb6f940462275" title=" " alt="" coords="877,543,1040,570"/>
</map>
</div>

</div>
</div>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.17
</small></address>
</body>
</html>
