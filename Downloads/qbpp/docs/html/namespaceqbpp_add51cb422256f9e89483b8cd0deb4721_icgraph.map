<map id="qbpp::expr" name="qbpp::expr">
<area shape="rect" id="node1" title=" " alt="" coords="923,484,1005,510"/>
<area shape="rect" id="node2" href="$namespaceqbpp.html#a0fdba59cf44ada4b55c1d8ea6d3f3007" title=" " alt="" coords="262,58,405,85"/>
<area shape="rect" id="node5" href="$namespaceqbpp.html#a370670ee4b0902d6ef81142eebd6fdeb" title=" " alt="" coords="720,138,845,165"/>
<area shape="rect" id="node7" href="$classqbpp_1_1impl_1_1IndexVarMapper.html#a25972cbe202eb365218cf4eff4f66c91" title=" " alt="" coords="691,190,875,231"/>
<area shape="rect" id="node8" href="$namespaceqbpp.html#af3762a9acba3cac9b7f8d6d8549cf012" title=" " alt="" coords="741,256,824,282"/>
<area shape="rect" id="node9" href="$namespaceqbpp.html#adf383c43f74885fa9316f6ed31c06008" title=" " alt="" coords="743,306,822,333"/>
<area shape="rect" id="node10" href="$namespaceqbpp.html#a991b983896321dfc672af18786a3208b" title=" " alt="" coords="727,357,838,384"/>
<area shape="rect" id="node11" href="$namespaceqbpp.html#a7bd562623faf590810ac1950dd7ea23b" title=" " alt="" coords="717,408,848,434"/>
<area shape="rect" id="node13" href="$namespaceqbpp.html#a20f18e97069a78ff709eca10523114ab" title=" " alt="" coords="722,458,843,485"/>
<area shape="rect" id="node14" href="$namespaceqbpp.html#afd23f996df8c2cae65e6f7088c3b7425" title=" " alt="" coords="498,458,619,485"/>
<area shape="rect" id="node15" href="$namespaceqbpp.html#ac05f27a511fa1e2870091ea5d696a3c3" title=" " alt="" coords="734,960,831,986"/>
<area shape="rect" id="node19" href="$namespaceqbpp.html#a9456e7fa594d0169fcc26f37fdda29ad" title=" " alt="" coords="507,1128,610,1154"/>
<area shape="rect" id="node22" href="$namespaceqbpp.html#aa3ce65464a38ba36e0b6e99b97234245" title=" " alt="" coords="699,778,866,805"/>
<area shape="rect" id="node35" href="$namespaceqbpp.html#a17ebda985a4b0bb22a6b4485b59c3774" title=" " alt="" coords="262,610,405,637"/>
<area shape="rect" id="node38" href="$namespaceqbpp.html#a13c9a177636866d79431415283122768" title=" " alt="" coords="705,88,860,114"/>
<area shape="rect" id="node40" href="$namespaceqbpp.html#abbed8c57213e7a8eefe7aa9fd60281ac" title=" " alt="" coords="717,1061,848,1088"/>
<area shape="rect" id="node41" href="$namespaceqbpp.html#a97185e5bf8d17779523cb3f13b1d54d7" title=" " alt="" coords="745,560,821,586"/>
<area shape="rect" id="node47" href="$namespaceqbpp.html#a852d3ca4caf367d8475ccb839f021557" title=" " alt="" coords="741,1112,824,1138"/>
<area shape="rect" id="node49" href="$namespaceqbpp.html#ad69bc1ad0ee4d44292f527727cecea19" title=" " alt="" coords="741,1162,825,1189"/>
<area shape="rect" id="node3" href="$classqbpp_1_1Vector.html#a7320b96903c5c01a65ce542d960b192c" title=" " alt="" coords="30,19,167,60"/>
<area shape="rect" id="node4" href="$classqbpp_1_1Expr.html#ac5338dd87bd705490907f68afae33fe2" title=" " alt="" coords="35,84,163,126"/>
<area shape="rect" id="node6" href="$namespaceqbpp.html#a09f90db0eadadb2d20f60fb87012ecc8" title=" " alt="" coords="498,146,619,173"/>
<area shape="rect" id="node12" href="$classqbpp_1_1Model.html#a67c0d56d86152d5540c73491528656de" title=" " alt="" coords="475,408,643,434"/>
<area shape="rect" id="node16" href="$simple__factorization__abs2_8cpp.html#ae66f6b31b5ad750f1fe042a706a4e3d4" title=" " alt="" coords="533,960,584,986"/>
<area shape="rect" id="node17" href="$classqbpp_1_1Vector.html#ac3bab7f853a0261449daea0ef614ccb9" title=" " alt="" coords="491,1011,627,1052"/>
<area shape="rect" id="node18" href="$classqbpp_1_1Expr.html#ac7fd3e8384b8c236ac17e74916d0456e" title=" " alt="" coords="493,1077,625,1104"/>
<area shape="rect" id="node20" href="$classqbpp_1_1Vector.html#a3012b501cdf4d76312c87d052996a66c" title=" " alt="" coords="265,1150,401,1191"/>
<area shape="rect" id="node21" href="$classqbpp_1_1Expr.html#afd67aef307d047fbe58759c6c39aba13" title=" " alt="" coords="264,1098,403,1125"/>
<area shape="rect" id="node23" href="$classqbpp_1_1graph__color_1_1GraphColorQuadModel.html#a5137f108589790091a08b0c8f1bfc049" title="Helper function to generate the QUBO expression for the GraphColorMap object and the number of colors..." alt="" coords="481,829,636,885"/>
<area shape="rect" id="node24" href="$classqbpp_1_1Expr.html#a993b1cdebcad2f69afd95a1bb313a860" title=" " alt="" coords="485,909,632,936"/>
<area shape="rect" id="node25" href="$classqbpp_1_1nqueen_1_1NQueenQuadModel.html#aa8c191760731a1804efc38e50ce6cf24" title="Generates the QUBO expression for the N&#45;Queens problem using TBB in parallel." alt="" coords="240,910,427,951"/>
<area shape="rect" id="node27" href="$classqbpp_1_1Expr.html#a62e5306958c409b8099d56a5ae664c6c" title=" " alt="" coords="485,778,632,805"/>
<area shape="rect" id="node29" href="$classqbpp_1_1Expr.html#a21b1c63a0930ff6ff426d852b2ccd011" title=" " alt="" coords="489,662,628,703"/>
<area shape="rect" id="node26" href="$classqbpp_1_1nqueen_1_1NQueenQuadModel.html#a940443a8bb6cadae0d38f96b92661f58" title="Helper function to compute initial values for member variables." alt="" coords="5,844,192,886"/>
<area shape="rect" id="node28" href="$namespaceqbpp.html#a23e20254bb8ddd83cf2673a8ea204a94" title=" " alt="" coords="273,976,394,1002"/>
<area shape="rect" id="node30" href="$classqbpp_1_1nqueen_1_1NQueenQuadModel.html#a7a3c1501bc6230d15a3b033b26584575" title="Generates qbpp::Expr for the N&#45;Queens problem." alt="" coords="240,727,427,768"/>
<area shape="rect" id="node31" href="$classqbpp_1_1nqueen_1_1NQueenQuadModel.html#af0ccd64d202250088cc2846f4c86516b" title="Generates the QUBO expression for the N&#45;Queens problem." alt="" coords="240,844,427,886"/>
<area shape="rect" id="node32" href="$bin__packing__easy_8cpp.html#ae66f6b31b5ad750f1fe042a706a4e3d4" title=" " alt="" coords="308,793,359,820"/>
<area shape="rect" id="node33" href="$namespaceqbpp_1_1factorization.html#a0c2bd955d729e3d15ebad8da4835229f" title="Function to generate QUBO expression for multiplier." alt="" coords="269,662,398,703"/>
<area shape="rect" id="node34" href="$factorization_8cpp.html#a0ddf1224851353fc92bfbff6f499fa97" title="Solves the factorization problem using ABS2 QUBO Solver and Gurobi optimizer from QUBO++ library." alt="" coords="73,669,124,696"/>
<area shape="rect" id="node36" href="$classqbpp_1_1Vector.html#afdd07cc64c0c98ea6808efd1dddc547e" title=" " alt="" coords="30,538,167,579"/>
<area shape="rect" id="node37" href="$classqbpp_1_1Expr.html#ad72e191219081766dd77bd9ac592cb19" title=" " alt="" coords="31,603,166,644"/>
<area shape="rect" id="node39" href="$classqbpp_1_1Expr.html#ac211496d7eaa75f675ed5431fdaeed07" title=" " alt="" coords="489,80,628,122"/>
<area shape="rect" id="node42" href="$namespaceqbpp.html#a52854218b14a58db1c7a1660137c5375" title=" " alt="" coords="521,509,597,536"/>
<area shape="rect" id="node43" href="$namespaceqbpp.html#abd03cc17ab61f6ab2be95751c7b92626" title=" " alt="" coords="521,560,597,586"/>
<area shape="rect" id="node44" href="$partition__easy_8cpp.html#a3c04138a5bfe5d72780bb7e82a18e627" title="Solves the Partitioning problem using the QUBO++ Easy Solver." alt="" coords="308,458,359,485"/>
<area shape="rect" id="node45" href="$classqbpp_1_1VarOnehot.html#a4e91e790e15943420cdce37c44c6de65" title=" " alt="" coords="244,509,423,536"/>
<area shape="rect" id="node46" href="$namespaceqbpp.html#a444e1f7dfbf971dc812d41a99f849ca0" title=" " alt="" coords="273,560,394,586"/>
<area shape="rect" id="node48" href="$namespaceqbpp.html#aabb752897db4cf9a74a0642a55ea30be" title=" " alt="" coords="480,1178,637,1205"/>
</map>
