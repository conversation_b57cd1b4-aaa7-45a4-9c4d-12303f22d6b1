<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.17"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>QUBO++ Library with QUBO Solver APIs: qbpp_abs2::QuadModel Class Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/x-mathjax-config">
  MathJax.Hub.Config({
    extensions: ["tex2jax.js", "TeX/AMSmath.js", "TeX/AMSsymbols.js"],
    jax: ["input/TeX","output/HTML-CSS"],
});
</script>
<script type="text/javascript" async="async" src="https://cdn.jsdelivr.net/npm/mathjax@2/MathJax.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">QUBO++ Library with QUBO Solver APIs
   </div>
   <div id="projectbrief">Author: Koji Nakano, License: Non-commercial research and evaluation purposes without any guarantees.</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.17 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="namespaceqbpp__abs2.html">qbpp_abs2</a></li><li class="navelem"><a class="el" href="classqbpp__abs2_1_1QuadModel.html">QuadModel</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#pub-methods">Public Member Functions</a> &#124;
<a href="#pro-static-methods">Static Protected Member Functions</a> &#124;
<a href="#pro-attribs">Protected Attributes</a> &#124;
<a href="#pri-methods">Private Member Functions</a> &#124;
<a href="#pri-attribs">Private Attributes</a> &#124;
<a href="classqbpp__abs2_1_1QuadModel-members.html">List of all members</a>  </div>
  <div class="headertitle">
<div class="title">qbpp_abs2::QuadModel Class Reference<div class="ingroups"><a class="el" href="group__qbpp__abs2.html">QUBO++ API for ABS2 QUBO Solver</a></div></div>  </div>
</div><!--header-->
<div class="contents">

<p>A class for storing both the <a class="el" href="classqbpp_1_1QuadModel.html">qbpp::QuadModel</a> and <a class="el" href="classabs2_1_1Model.html" title="Class to store and manipulate a QUBO model.">abs2::Model</a>.  
 <a href="classqbpp__abs2_1_1QuadModel.html#details">More...</a></p>

<p><code>#include &lt;<a class="el" href="qbpp__abs2_8hpp_source.html">qbpp_abs2.hpp</a>&gt;</code></p>
<div class="dynheader">
Inheritance diagram for qbpp_abs2::QuadModel:</div>
<div class="dyncontent">
<div class="center"><img src="classqbpp__abs2_1_1QuadModel__inherit__graph.png" border="0" usemap="#qbpp__abs2_1_1QuadModel_inherit__map" alt="Inheritance graph"/></div>
<map name="qbpp__abs2_1_1QuadModel_inherit__map" id="qbpp__abs2_1_1QuadModel_inherit__map">
<area shape="rect" title="A class for storing both the qbpp::QuadModel and abs2::Model." alt="" coords="5,155,164,181"/>
<area shape="rect" href="classqbpp_1_1QuadModel.html" title=" " alt="" coords="23,80,147,107"/>
<area shape="rect" href="classqbpp_1_1Model.html" title=" " alt="" coords="38,5,131,32"/>
</map>
<center><span class="legend">[<a href="graph_legend.html">legend</a>]</span></center></div>
<div class="dynheader">
Collaboration diagram for qbpp_abs2::QuadModel:</div>
<div class="dyncontent">
<div class="center"><img src="classqbpp__abs2_1_1QuadModel__coll__graph.png" border="0" usemap="#qbpp__abs2_1_1QuadModel_coll__map" alt="Collaboration graph"/></div>
<map name="qbpp__abs2_1_1QuadModel_coll__map" id="qbpp__abs2_1_1QuadModel_coll__map">
<area shape="rect" title="A class for storing both the qbpp::QuadModel and abs2::Model." alt="" coords="5,155,164,181"/>
<area shape="rect" href="classqbpp_1_1QuadModel.html" title=" " alt="" coords="23,80,147,107"/>
<area shape="rect" href="classqbpp_1_1Model.html" title=" " alt="" coords="38,5,131,32"/>
</map>
<center><span class="legend">[<a href="graph_legend.html">legend</a>]</span></center></div>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-methods"></a>
Public Member Functions</h2></td></tr>
<tr class="memitem:gab125241142be7f65e2a913158e00394a"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__qbpp__abs2.html#gab125241142be7f65e2a913158e00394a">QuadModel</a> (const <a class="el" href="classqbpp_1_1QuadModel.html">qbpp::QuadModel</a> &amp;quad_model)</td></tr>
<tr class="memdesc:gab125241142be7f65e2a913158e00394a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Constructor: Create an ABS2 model from a QUBO model.  <a href="group__qbpp__abs2.html#gab125241142be7f65e2a913158e00394a">More...</a><br /></td></tr>
<tr class="separator:gab125241142be7f65e2a913158e00394a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae79cbaf57fdafd9fd834aba1e0e615f5"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classqbpp__abs2_1_1QuadModel.html#ae79cbaf57fdafd9fd834aba1e0e615f5">QuadModel</a> (const <a class="el" href="classqbpp__abs2_1_1QuadModel.html">QuadModel</a> &amp;quad_model)</td></tr>
<tr class="memdesc:ae79cbaf57fdafd9fd834aba1e0e615f5"><td class="mdescLeft">&#160;</td><td class="mdescRight">Copy constructor.  <a href="classqbpp__abs2_1_1QuadModel.html#ae79cbaf57fdafd9fd834aba1e0e615f5">More...</a><br /></td></tr>
<tr class="separator:ae79cbaf57fdafd9fd834aba1e0e615f5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a81d36c5c8b3ae978124c217d11a14351"><td class="memItemLeft" align="right" valign="top">const <a class="el" href="classabs2_1_1Model.html">abs2::Model</a> &amp;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classqbpp__abs2_1_1QuadModel.html#a81d36c5c8b3ae978124c217d11a14351">get_abs2_model</a> () const</td></tr>
<tr class="memdesc:a81d36c5c8b3ae978124c217d11a14351"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get the ABS2 model.  <a href="classqbpp__abs2_1_1QuadModel.html#a81d36c5c8b3ae978124c217d11a14351">More...</a><br /></td></tr>
<tr class="separator:a81d36c5c8b3ae978124c217d11a14351"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a591e74de688dbff1339c2c9d2352df65"><td class="memItemLeft" align="right" valign="top">const std::vector&lt; coeff_t &gt; &amp;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classqbpp_1_1QuadModel.html#a591e74de688dbff1339c2c9d2352df65">linear</a> () const</td></tr>
<tr class="separator:a591e74de688dbff1339c2c9d2352df65"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a4279bbb06acab59034c1afec9ef3148c"><td class="memItemLeft" align="right" valign="top">coeff_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classqbpp_1_1QuadModel.html#a4279bbb06acab59034c1afec9ef3148c">linear</a> (vindex_t <a class="el" href="classqbpp_1_1Model.html#a0712035184681aeb478c5e80bb5221bd">index</a>) const</td></tr>
<tr class="separator:a4279bbb06acab59034c1afec9ef3148c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ada71ec32906c9d8c73bd25c4267bafc5"><td class="memItemLeft" align="right" valign="top">const std::vector&lt; vindex_t &gt; &amp;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classqbpp_1_1QuadModel.html#ada71ec32906c9d8c73bd25c4267bafc5">degree</a> () const</td></tr>
<tr class="separator:ada71ec32906c9d8c73bd25c4267bafc5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a05a4566b95df159713e98a0c093ec1ef"><td class="memItemLeft" align="right" valign="top">vindex_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classqbpp_1_1QuadModel.html#a05a4566b95df159713e98a0c093ec1ef">degree</a> (vindex_t <a class="el" href="classqbpp_1_1Model.html#a0712035184681aeb478c5e80bb5221bd">index</a>) const</td></tr>
<tr class="separator:a05a4566b95df159713e98a0c093ec1ef"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6624038d4ff117176c37de2d1260f471"><td class="memItemLeft" align="right" valign="top">const std::vector&lt; std::vector&lt; std::pair&lt; vindex_t, coeff_t &gt; &gt; &gt; &amp;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classqbpp_1_1QuadModel.html#a6624038d4ff117176c37de2d1260f471">quadratic</a> () const</td></tr>
<tr class="separator:a6624038d4ff117176c37de2d1260f471"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:acbed633d6a032fc2fb51411850e76762"><td class="memItemLeft" align="right" valign="top">std::pair&lt; vindex_t, coeff_t &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classqbpp_1_1QuadModel.html#acbed633d6a032fc2fb51411850e76762">quadratic</a> (vindex_t i, vindex_t j) const</td></tr>
<tr class="separator:acbed633d6a032fc2fb51411850e76762"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a59aa11462585f09c3f68dcfd062545c3"><td class="memItemLeft" align="right" valign="top">std::pair&lt; vindex_t, coeff_t &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classqbpp_1_1QuadModel.html#a59aa11462585f09c3f68dcfd062545c3">get_quadratic_end</a> (vindex_t i, vindex_t j) const</td></tr>
<tr class="separator:a59aa11462585f09c3f68dcfd062545c3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a16af851530b1f63aca9baa8ec67ed01f"><td class="memItemLeft" align="right" valign="top">size_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classqbpp_1_1QuadModel.html#a16af851530b1f63aca9baa8ec67ed01f">term_count</a> (vindex_t deg) const</td></tr>
<tr class="separator:a16af851530b1f63aca9baa8ec67ed01f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5eabcb9b840eb2cc6083fe943996f230"><td class="memItemLeft" align="right" valign="top">size_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classqbpp_1_1QuadModel.html#a5eabcb9b840eb2cc6083fe943996f230">term_count</a> () const override</td></tr>
<tr class="separator:a5eabcb9b840eb2cc6083fe943996f230"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a06b84580a41556201ab2f4bb3d838403"><td class="memItemLeft" align="right" valign="top">coeff_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classqbpp_1_1QuadModel.html#a06b84580a41556201ab2f4bb3d838403">min_coeff</a> () const</td></tr>
<tr class="separator:a06b84580a41556201ab2f4bb3d838403"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab6ac73dcc23030f0f7e303e94d3c2bd3"><td class="memItemLeft" align="right" valign="top">coeff_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classqbpp_1_1QuadModel.html#ab6ac73dcc23030f0f7e303e94d3c2bd3">max_coeff</a> () const</td></tr>
<tr class="separator:ab6ac73dcc23030f0f7e303e94d3c2bd3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5cf2bb955c3aa25951ca499d7d8635d2"><td class="memItemLeft" align="right" valign="top">vindex_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classqbpp_1_1Model.html#a5cf2bb955c3aa25951ca499d7d8635d2">var_count</a> () const</td></tr>
<tr class="separator:a5cf2bb955c3aa25951ca499d7d8635d2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a52da2204913c7f529be7e8db017aa14a"><td class="memItemLeft" align="right" valign="top">Var&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classqbpp_1_1Model.html#a52da2204913c7f529be7e8db017aa14a">var</a> (vindex_t <a class="el" href="classqbpp_1_1Model.html#a0712035184681aeb478c5e80bb5221bd">index</a>) const</td></tr>
<tr class="separator:a52da2204913c7f529be7e8db017aa14a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0712035184681aeb478c5e80bb5221bd"><td class="memItemLeft" align="right" valign="top">vindex_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classqbpp_1_1Model.html#a0712035184681aeb478c5e80bb5221bd">index</a> (Var <a class="el" href="classqbpp_1_1Model.html#a52da2204913c7f529be7e8db017aa14a">var</a>) const</td></tr>
<tr class="separator:a0712035184681aeb478c5e80bb5221bd"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aed96d863b56813737a55c18402588719"><td class="memItemLeft" align="right" valign="top">bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classqbpp_1_1Model.html#aed96d863b56813737a55c18402588719">has</a> (Var <a class="el" href="classqbpp_1_1Model.html#a52da2204913c7f529be7e8db017aa14a">var</a>) const</td></tr>
<tr class="separator:aed96d863b56813737a55c18402588719"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a18d86c7c55ae188f35cfafb9cb1f8f85"><td class="memItemLeft" align="right" valign="top">const Expr &amp;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classqbpp_1_1Model.html#a18d86c7c55ae188f35cfafb9cb1f8f85">expr</a> () const</td></tr>
<tr class="separator:a18d86c7c55ae188f35cfafb9cb1f8f85"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab6813448e5b12810826fdb9e04aeee83"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classqbpp_1_1Model.html#ab6813448e5b12810826fdb9e04aeee83">operator const Expr &amp;</a> () const</td></tr>
<tr class="separator:ab6813448e5b12810826fdb9e04aeee83"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3b0641c97573b248f1877c2826277102"><td class="memItemLeft" align="right" valign="top">energy_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classqbpp_1_1Model.html#a3b0641c97573b248f1877c2826277102">constant</a> () const</td></tr>
<tr class="separator:a3b0641c97573b248f1877c2826277102"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a24930e1898939d5f4a6f6d0c9d56cca1"><td class="memItemLeft" align="right" valign="top">const Terms &amp;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classqbpp_1_1Model.html#a24930e1898939d5f4a6f6d0c9d56cca1">terms</a> () const</td></tr>
<tr class="separator:a24930e1898939d5f4a6f6d0c9d56cca1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0657005b0ed9c8d697793e222ea1aecd"><td class="memItemLeft" align="right" valign="top">const impl::IndexVarMapper &amp;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classqbpp_1_1Model.html#a0657005b0ed9c8d697793e222ea1aecd">index_var_mapper</a> () const</td></tr>
<tr class="separator:a0657005b0ed9c8d697793e222ea1aecd"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5ccc6ba4f93252db998f1ec2d50fe622"><td class="memItemLeft" align="right" valign="top">const std::vector&lt; Var &gt; &amp;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classqbpp_1_1Model.html#a5ccc6ba4f93252db998f1ec2d50fe622">index_var</a> () const</td></tr>
<tr class="separator:a5ccc6ba4f93252db998f1ec2d50fe622"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aca538cfecdad8a952309a5a6fc62ddfc"><td class="memItemLeft" align="right" valign="top">const std::vector&lt; vindex_t &gt; &amp;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classqbpp_1_1Model.html#aca538cfecdad8a952309a5a6fc62ddfc">var_index</a> () const</td></tr>
<tr class="separator:aca538cfecdad8a952309a5a6fc62ddfc"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pro-static-methods"></a>
Static Protected Member Functions</h2></td></tr>
<tr class="memitem:a67c0d56d86152d5540c73491528656de"><td class="memItemLeft" align="right" valign="top">static const Expr &amp;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classqbpp_1_1Model.html#a67c0d56d86152d5540c73491528656de">check_expr</a> (const Expr &amp;<a class="el" href="classqbpp_1_1Model.html#a18d86c7c55ae188f35cfafb9cb1f8f85">expr</a>)</td></tr>
<tr class="separator:a67c0d56d86152d5540c73491528656de"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aec1e4698f99592d3f3834d26b09faf2f"><td class="memItemLeft" align="right" valign="top">static Expr&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classqbpp_1_1Model.html#aec1e4698f99592d3f3834d26b09faf2f">check_expr</a> (Expr &amp;&amp;<a class="el" href="classqbpp_1_1Model.html#a18d86c7c55ae188f35cfafb9cb1f8f85">expr</a>)</td></tr>
<tr class="separator:aec1e4698f99592d3f3834d26b09faf2f"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pro-attribs"></a>
Protected Attributes</h2></td></tr>
<tr class="memitem:a4f61a395e27a143162c85bb66ff3c5ab"><td class="memItemLeft" align="right" valign="top">const std::shared_ptr&lt; const Expr &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classqbpp_1_1Model.html#a4f61a395e27a143162c85bb66ff3c5ab">expr_ptr_</a></td></tr>
<tr class="separator:a4f61a395e27a143162c85bb66ff3c5ab"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa73d48897a165a209db4531357018e27"><td class="memItemLeft" align="right" valign="top">const std::shared_ptr&lt; const impl::IndexVarMapper &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classqbpp_1_1Model.html#aa73d48897a165a209db4531357018e27">index_var_ptr_</a></td></tr>
<tr class="separator:aa73d48897a165a209db4531357018e27"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pri-methods"></a>
Private Member Functions</h2></td></tr>
<tr class="memitem:ae407053ddc1028ef6253ee3f5d23073d"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classqbpp__abs2_1_1QuadModel.html#ae407053ddc1028ef6253ee3f5d23073d">QuadModel</a> ()=delete</td></tr>
<tr class="memdesc:ae407053ddc1028ef6253ee3f5d23073d"><td class="mdescLeft">&#160;</td><td class="mdescRight">Default constructor is deleted to avoid creating an empty model.  <a href="classqbpp__abs2_1_1QuadModel.html#ae407053ddc1028ef6253ee3f5d23073d">More...</a><br /></td></tr>
<tr class="separator:ae407053ddc1028ef6253ee3f5d23073d"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pri-attribs"></a>
Private Attributes</h2></td></tr>
<tr class="memitem:a763aa7381304ed9bd82054b6614c929f"><td class="memItemLeft" align="right" valign="top">std::shared_ptr&lt; <a class="el" href="classabs2_1_1Model.html">abs2::Model</a> &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classqbpp__abs2_1_1QuadModel.html#a763aa7381304ed9bd82054b6614c929f">abs2model_ptr</a></td></tr>
<tr class="memdesc:a763aa7381304ed9bd82054b6614c929f"><td class="mdescLeft">&#160;</td><td class="mdescRight">ABS2 model associated with the QUBO model.  <a href="classqbpp__abs2_1_1QuadModel.html#a763aa7381304ed9bd82054b6614c929f">More...</a><br /></td></tr>
<tr class="separator:a763aa7381304ed9bd82054b6614c929f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa8e77f59612c13be01cf3d5bbdcf88e1"><td class="memItemLeft" align="right" valign="top">const std::shared_ptr&lt; const <a class="el" href="structqbpp_1_1QuadModel_1_1Impl.html">Impl</a> &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classqbpp_1_1QuadModel.html#aa8e77f59612c13be01cf3d5bbdcf88e1">pimpl_</a></td></tr>
<tr class="separator:aa8e77f59612c13be01cf3d5bbdcf88e1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aff40b2cba811161ddf864245ca1fa84d"><td class="memItemLeft" align="right" valign="top">const std::vector&lt; coeff_t &gt; &amp;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classqbpp_1_1QuadModel.html#aff40b2cba811161ddf864245ca1fa84d">linear_</a></td></tr>
<tr class="separator:aff40b2cba811161ddf864245ca1fa84d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af7854162ac2aa7cd282a2f0113b658d4"><td class="memItemLeft" align="right" valign="top">const std::vector&lt; vindex_t &gt; &amp;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classqbpp_1_1QuadModel.html#af7854162ac2aa7cd282a2f0113b658d4">degree_</a></td></tr>
<tr class="separator:af7854162ac2aa7cd282a2f0113b658d4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a313cf3b52f2816aa5cb02bd4332357ec"><td class="memItemLeft" align="right" valign="top">const std::vector&lt; std::vector&lt; std::pair&lt; vindex_t, coeff_t &gt; &gt; &gt; &amp;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classqbpp_1_1QuadModel.html#a313cf3b52f2816aa5cb02bd4332357ec">quadratic_</a></td></tr>
<tr class="separator:a313cf3b52f2816aa5cb02bd4332357ec"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p>A class for storing both the <a class="el" href="classqbpp_1_1QuadModel.html">qbpp::QuadModel</a> and <a class="el" href="classabs2_1_1Model.html" title="Class to store and manipulate a QUBO model.">abs2::Model</a>. </p>
<p>This class is derived from <a class="el" href="classqbpp_1_1QuadModel.html">qbpp::QuadModel</a> and containing an <a class="el" href="classabs2_1_1Model.html" title="Class to store and manipulate a QUBO model.">abs2::Model</a> object. </p>

<p class="definition">Definition at line <a class="el" href="qbpp__abs2_8hpp_source.html#l00076">76</a> of file <a class="el" href="qbpp__abs2_8hpp_source.html">qbpp_abs2.hpp</a>.</p>
</div><h2 class="groupheader">Constructor &amp; Destructor Documentation</h2>
<a id="ae407053ddc1028ef6253ee3f5d23073d"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ae407053ddc1028ef6253ee3f5d23073d">&#9670;&nbsp;</a></span>QuadModel() <span class="overload">[1/2]</span></h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">qbpp_abs2::QuadModel::QuadModel </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">private</span><span class="mlabel">delete</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Default constructor is deleted to avoid creating an empty model. </p>

</div>
</div>
<a id="ae79cbaf57fdafd9fd834aba1e0e615f5"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ae79cbaf57fdafd9fd834aba1e0e615f5">&#9670;&nbsp;</a></span>QuadModel() <span class="overload">[2/2]</span></h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">qbpp_abs2::QuadModel::QuadModel </td>
          <td>(</td>
          <td class="paramtype">const <a class="el" href="classqbpp__abs2_1_1QuadModel.html">QuadModel</a> &amp;&#160;</td>
          <td class="paramname"><em>quad_model</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Copy constructor. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">quad_model</td><td><a class="el" href="classqbpp__abs2_1_1QuadModel.html" title="A class for storing both the qbpp::QuadModel and abs2::Model.">QuadModel</a> object </td></tr>
  </table>
  </dd>
</dl>

<p class="definition">Definition at line <a class="el" href="qbpp__abs2_8hpp_source.html#l00090">90</a> of file <a class="el" href="qbpp__abs2_8hpp_source.html">qbpp_abs2.hpp</a>.</p>

</div>
</div>
<h2 class="groupheader">Member Function Documentation</h2>
<a id="a81d36c5c8b3ae978124c217d11a14351"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a81d36c5c8b3ae978124c217d11a14351">&#9670;&nbsp;</a></span>get_abs2_model()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">const <a class="el" href="classabs2_1_1Model.html">abs2::Model</a>&amp; qbpp_abs2::QuadModel::get_abs2_model </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Get the ABS2 model. </p>
<dl class="section return"><dt>Returns</dt><dd>ABS2 model </dd></dl>

<p class="definition">Definition at line <a class="el" href="qbpp__abs2_8hpp_source.html#l00095">95</a> of file <a class="el" href="qbpp__abs2_8hpp_source.html">qbpp_abs2.hpp</a>.</p>
<div class="dynheader">
Here is the caller graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="classqbpp__abs2_1_1QuadModel_a81d36c5c8b3ae978124c217d11a14351_icgraph.png" border="0" usemap="#classqbpp__abs2_1_1QuadModel_a81d36c5c8b3ae978124c217d11a14351_icgraph" alt=""/></div>
<map name="classqbpp__abs2_1_1QuadModel_a81d36c5c8b3ae978124c217d11a14351_icgraph" id="classqbpp__abs2_1_1QuadModel_a81d36c5c8b3ae978124c217d11a14351_icgraph">
<area shape="rect" title="Get the ABS2 model." alt="" coords="192,5,351,47"/>
<area shape="rect" href="group__qbpp__abs2.html#ga7665b9e646b22156b8680c64cd08c92c" title="Executes ABS2 for &quot;quad_model&quot; with &quot;param&quot; and returns &quot;sol&quot;." alt="" coords="5,5,144,47"/>
</map>
</div>

</div>
</div>
<a id="a591e74de688dbff1339c2c9d2352df65"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a591e74de688dbff1339c2c9d2352df65">&#9670;&nbsp;</a></span>linear() <span class="overload">[1/2]</span></h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">const std::vector&lt;coeff_t&gt;&amp; qbpp::QuadModel::linear </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span><span class="mlabel">inherited</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p class="definition">Definition at line <a class="el" href="qbpp_8hpp_source.html#l01967">1967</a> of file <a class="el" href="qbpp_8hpp_source.html">qbpp.hpp</a>.</p>
<div class="dynheader">
Here is the caller graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="classqbpp_1_1QuadModel_a591e74de688dbff1339c2c9d2352df65_icgraph.png" border="0" usemap="#classqbpp_1_1QuadModel_a591e74de688dbff1339c2c9d2352df65_icgraph" alt=""/></div>
<map name="classqbpp_1_1QuadModel_a591e74de688dbff1339c2c9d2352df65_icgraph" id="classqbpp_1_1QuadModel_a591e74de688dbff1339c2c9d2352df65_icgraph">
<area shape="rect" title=" " alt="" coords="216,143,379,170"/>
<area shape="rect" href="group__qbpp__grb.html#ga3e5106bc14a17c6521c932753c76fe96" title="Constructor to create a model from QuadModel object." alt="" coords="12,5,161,47"/>
<area shape="rect" href="group__qbpp__abs2.html#gab125241142be7f65e2a913158e00394a" title="Constructor: Create an ABS2 model from a QUBO model." alt="" coords="7,71,166,112"/>
<area shape="rect" href="classqbpp_1_1easy__solver_1_1SolDelta.html#a2a6c5b08bc697bd6f633b68fcdd550e1" title=" " alt="" coords="19,136,155,177"/>
<area shape="rect" href="classqbpp_1_1exhaustive__solver_1_1SolDelta.html#a6f368c3a2224a71803d9ee2711effa9b" title=" " alt="" coords="5,201,168,243"/>
<area shape="rect" href="namespaceqbpp.html#a8fa3af2cffc1996530310aac6e4b0576" title=" " alt="" coords="50,267,123,294"/>
</map>
</div>

</div>
</div>
<a id="a4279bbb06acab59034c1afec9ef3148c"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a4279bbb06acab59034c1afec9ef3148c">&#9670;&nbsp;</a></span>linear() <span class="overload">[2/2]</span></h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">coeff_t qbpp::QuadModel::linear </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="namespaceqbpp.html#a5cf436100ede362797d0c0501ea50a5a">vindex_t</a>&#160;</td>
          <td class="paramname"><em>index</em></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span><span class="mlabel">inherited</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p class="definition">Definition at line <a class="el" href="qbpp_8hpp_source.html#l01976">1976</a> of file <a class="el" href="qbpp_8hpp_source.html">qbpp.hpp</a>.</p>
<div class="dynheader">
Here is the call graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="classqbpp_1_1QuadModel_a4279bbb06acab59034c1afec9ef3148c_cgraph.png" border="0" usemap="#classqbpp_1_1QuadModel_a4279bbb06acab59034c1afec9ef3148c_cgraph" alt=""/></div>
<map name="classqbpp_1_1QuadModel_a4279bbb06acab59034c1afec9ef3148c_cgraph" id="classqbpp_1_1QuadModel_a4279bbb06acab59034c1afec9ef3148c_cgraph">
<area shape="rect" title=" " alt="" coords="5,5,168,32"/>
<area shape="rect" href="classqbpp_1_1Model.html#a0712035184681aeb478c5e80bb5221bd" title=" " alt="" coords="216,5,348,32"/>
<area shape="rect" href="classqbpp_1_1Model.html#a52da2204913c7f529be7e8db017aa14a" title=" " alt="" coords="396,5,515,32"/>
</map>
</div>

</div>
</div>
<a id="ada71ec32906c9d8c73bd25c4267bafc5"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ada71ec32906c9d8c73bd25c4267bafc5">&#9670;&nbsp;</a></span>degree() <span class="overload">[1/2]</span></h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">const std::vector&lt;vindex_t&gt;&amp; qbpp::QuadModel::degree </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span><span class="mlabel">inherited</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p class="definition">Definition at line <a class="el" href="qbpp_8hpp_source.html#l01969">1969</a> of file <a class="el" href="qbpp_8hpp_source.html">qbpp.hpp</a>.</p>
<div class="dynheader">
Here is the caller graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="classqbpp_1_1QuadModel_ada71ec32906c9d8c73bd25c4267bafc5_icgraph.png" border="0" usemap="#classqbpp_1_1QuadModel_ada71ec32906c9d8c73bd25c4267bafc5_icgraph" alt=""/></div>
<map name="classqbpp_1_1QuadModel_ada71ec32906c9d8c73bd25c4267bafc5_icgraph" id="classqbpp_1_1QuadModel_ada71ec32906c9d8c73bd25c4267bafc5_icgraph">
<area shape="rect" title=" " alt="" coords="1243,338,1413,365"/>
<area shape="rect" href="classqbpp_1_1easy__solver_1_1SolDelta.html#a1a107d8974386a07d201e88fb2c9aed9" title=" " alt="" coords="1045,129,1181,171"/>
<area shape="rect" href="classqbpp_1_1easy__solver_1_1SolDelta.html#ae5e7dbc16325f4f3b26895fe8686afc5" title=" " alt="" coords="835,188,971,229"/>
<area shape="rect" href="classqbpp_1_1exhaustive__solver_1_1SolDelta.html#ae53a1eb415153c3d6e02794123a87b5a" title=" " alt="" coords="1032,257,1195,299"/>
<area shape="rect" href="classqbpp_1_1exhaustive__solver_1_1SearchAlgorithm.html#a4c70861e652f494ba0659c5bfb1bb3f7" title=" " alt="" coords="1032,323,1195,379"/>
<area shape="rect" href="group__qbpp__grb.html#ga3e5106bc14a17c6521c932753c76fe96" title="Constructor to create a model from QuadModel object." alt="" coords="1039,404,1188,445"/>
<area shape="rect" href="group__qbpp__abs2.html#gab125241142be7f65e2a913158e00394a" title="Constructor: Create an ABS2 model from a QUBO model." alt="" coords="1034,469,1193,511"/>
<area shape="rect" href="namespaceqbpp.html#a8fa3af2cffc1996530310aac6e4b0576" title=" " alt="" coords="1077,535,1150,562"/>
<area shape="rect" href="classqbpp_1_1easy__solver_1_1TabuSolDelta.html#abddd3367c047935e82e517c421cb5a9a" title=" " alt="" coords="835,5,971,47"/>
<area shape="rect" href="classqbpp_1_1easy__solver_1_1SolDelta.html#acf11d3f5b86a20077d4c20dc9c18e044" title=" " alt="" coords="835,71,971,112"/>
<area shape="rect" href="classqbpp_1_1easy__solver_1_1SolDelta.html#ad0505230c61ae317b5414f06327daeb4" title=" " alt="" coords="613,129,760,171"/>
<area shape="rect" href="classqbpp_1_1easy__solver_1_1PosMinSolDelta.html#aff102bc17a0d06a78a285f1ab2c9632e" title=" " alt="" coords="605,64,768,105"/>
<area shape="rect" href="classqbpp_1_1easy__solver_1_1EasySolver.html#a329a03e4e5c401ec471e0f7f4c42cb51" title=" " alt="" coords="374,129,551,171"/>
<area shape="rect" href="classqbpp_1_1easy__solver_1_1EasySolver.html#a9a61ebfb6366c734e7af2b08890753f0" title=" " alt="" coords="189,129,325,171"/>
<area shape="rect" href="graph__color__easy_8cpp.html#a3c04138a5bfe5d72780bb7e82a18e627" title="Main function to generate a random map and solve the Graph Coloring Problem using the Easy Solver." alt="" coords="48,107,99,134"/>
<area shape="rect" href="classqbpp_1_1easy__solver_1_1EasySolver.html#a8d5bc85759b94bc1c1bf2db59fc323b0" title=" " alt="" coords="5,159,141,200"/>
<area shape="rect" href="classqbpp_1_1exhaustive__solver_1_1SearchAlgorithm.html#ad031941ba26d6f9e9262803068fbfc7e" title=" " alt="" coords="821,254,984,310"/>
<area shape="rect" href="classqbpp_1_1exhaustive__solver_1_1SolDelta.html#a926e9faffb265b7208b75663e76f77da" title=" " alt="" coords="821,335,984,376"/>
<area shape="rect" href="classqbpp_1_1exhaustive__solver_1_1SearchAlgorithm.html#a3a2b22a2908c11bf374b4f06b2ad1815" title=" " alt="" coords="600,297,773,339"/>
<area shape="rect" href="classqbpp_1_1exhaustive__solver_1_1ExhaustiveSolver.html#a967d19602b7de9fef4198508924c7a25" title=" " alt="" coords="373,297,552,339"/>
</map>
</div>

</div>
</div>
<a id="a05a4566b95df159713e98a0c093ec1ef"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a05a4566b95df159713e98a0c093ec1ef">&#9670;&nbsp;</a></span>degree() <span class="overload">[2/2]</span></h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">vindex_t qbpp::QuadModel::degree </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="namespaceqbpp.html#a5cf436100ede362797d0c0501ea50a5a">vindex_t</a>&#160;</td>
          <td class="paramname"><em>index</em></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span><span class="mlabel">inherited</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p class="definition">Definition at line <a class="el" href="qbpp_8hpp_source.html#l01978">1978</a> of file <a class="el" href="qbpp_8hpp_source.html">qbpp.hpp</a>.</p>
<div class="dynheader">
Here is the call graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="classqbpp_1_1QuadModel_a05a4566b95df159713e98a0c093ec1ef_cgraph.png" border="0" usemap="#classqbpp_1_1QuadModel_a05a4566b95df159713e98a0c093ec1ef_cgraph" alt=""/></div>
<map name="classqbpp_1_1QuadModel_a05a4566b95df159713e98a0c093ec1ef_cgraph" id="classqbpp_1_1QuadModel_a05a4566b95df159713e98a0c093ec1ef_cgraph">
<area shape="rect" title=" " alt="" coords="5,5,176,32"/>
<area shape="rect" href="classqbpp_1_1Model.html#a0712035184681aeb478c5e80bb5221bd" title=" " alt="" coords="224,5,356,32"/>
<area shape="rect" href="classqbpp_1_1Model.html#a52da2204913c7f529be7e8db017aa14a" title=" " alt="" coords="404,5,523,32"/>
</map>
</div>

</div>
</div>
<a id="a6624038d4ff117176c37de2d1260f471"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a6624038d4ff117176c37de2d1260f471">&#9670;&nbsp;</a></span>quadratic() <span class="overload">[1/2]</span></h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">const std::vector&lt;std::vector&lt;std::pair&lt;vindex_t, coeff_t&gt; &gt; &gt;&amp; qbpp::QuadModel::quadratic </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span><span class="mlabel">inherited</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p class="definition">Definition at line <a class="el" href="qbpp_8hpp_source.html#l01971">1971</a> of file <a class="el" href="qbpp_8hpp_source.html">qbpp.hpp</a>.</p>
<div class="dynheader">
Here is the caller graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="classqbpp_1_1QuadModel_a6624038d4ff117176c37de2d1260f471_icgraph.png" border="0" usemap="#classqbpp_1_1QuadModel_a6624038d4ff117176c37de2d1260f471_icgraph" alt=""/></div>
<map name="classqbpp_1_1QuadModel_a6624038d4ff117176c37de2d1260f471_icgraph" id="classqbpp_1_1QuadModel_a6624038d4ff117176c37de2d1260f471_icgraph">
<area shape="rect" title=" " alt="" coords="1243,330,1428,357"/>
<area shape="rect" href="classqbpp_1_1easy__solver_1_1SolDelta.html#a1a107d8974386a07d201e88fb2c9aed9" title=" " alt="" coords="1045,129,1181,171"/>
<area shape="rect" href="classqbpp_1_1easy__solver_1_1SolDelta.html#ae5e7dbc16325f4f3b26895fe8686afc5" title=" " alt="" coords="835,188,971,229"/>
<area shape="rect" href="classqbpp_1_1exhaustive__solver_1_1SolDelta.html#ae53a1eb415153c3d6e02794123a87b5a" title=" " alt="" coords="1032,257,1195,299"/>
<area shape="rect" href="structqbpp_1_1QuadModel_1_1Impl.html#a17282586c31a1881bd2228a3111c2aa8" title=" " alt="" coords="1035,323,1192,364"/>
<area shape="rect" href="group__qbpp__grb.html#ga3e5106bc14a17c6521c932753c76fe96" title="Constructor to create a model from QuadModel object." alt="" coords="1039,388,1188,429"/>
<area shape="rect" href="group__qbpp__abs2.html#gab125241142be7f65e2a913158e00394a" title="Constructor: Create an ABS2 model from a QUBO model." alt="" coords="1034,453,1193,495"/>
<area shape="rect" href="namespaceqbpp.html#a8fa3af2cffc1996530310aac6e4b0576" title=" " alt="" coords="1077,519,1150,546"/>
<area shape="rect" href="classqbpp_1_1easy__solver_1_1TabuSolDelta.html#abddd3367c047935e82e517c421cb5a9a" title=" " alt="" coords="835,5,971,47"/>
<area shape="rect" href="classqbpp_1_1easy__solver_1_1SolDelta.html#acf11d3f5b86a20077d4c20dc9c18e044" title=" " alt="" coords="835,71,971,112"/>
<area shape="rect" href="classqbpp_1_1easy__solver_1_1SolDelta.html#ad0505230c61ae317b5414f06327daeb4" title=" " alt="" coords="613,129,760,171"/>
<area shape="rect" href="classqbpp_1_1easy__solver_1_1PosMinSolDelta.html#aff102bc17a0d06a78a285f1ab2c9632e" title=" " alt="" coords="605,64,768,105"/>
<area shape="rect" href="classqbpp_1_1easy__solver_1_1EasySolver.html#a329a03e4e5c401ec471e0f7f4c42cb51" title=" " alt="" coords="374,129,551,171"/>
<area shape="rect" href="classqbpp_1_1easy__solver_1_1EasySolver.html#a9a61ebfb6366c734e7af2b08890753f0" title=" " alt="" coords="189,129,325,171"/>
<area shape="rect" href="graph__color__easy_8cpp.html#a3c04138a5bfe5d72780bb7e82a18e627" title="Main function to generate a random map and solve the Graph Coloring Problem using the Easy Solver." alt="" coords="48,107,99,134"/>
<area shape="rect" href="classqbpp_1_1easy__solver_1_1EasySolver.html#a8d5bc85759b94bc1c1bf2db59fc323b0" title=" " alt="" coords="5,159,141,200"/>
<area shape="rect" href="classqbpp_1_1exhaustive__solver_1_1SearchAlgorithm.html#ad031941ba26d6f9e9262803068fbfc7e" title=" " alt="" coords="821,254,984,310"/>
<area shape="rect" href="classqbpp_1_1exhaustive__solver_1_1SolDelta.html#a926e9faffb265b7208b75663e76f77da" title=" " alt="" coords="821,335,984,376"/>
<area shape="rect" href="classqbpp_1_1exhaustive__solver_1_1SearchAlgorithm.html#a3a2b22a2908c11bf374b4f06b2ad1815" title=" " alt="" coords="600,297,773,339"/>
<area shape="rect" href="classqbpp_1_1exhaustive__solver_1_1ExhaustiveSolver.html#a967d19602b7de9fef4198508924c7a25" title=" " alt="" coords="373,297,552,339"/>
</map>
</div>

</div>
</div>
<a id="acbed633d6a032fc2fb51411850e76762"></a>
<h2 class="memtitle"><span class="permalink"><a href="#acbed633d6a032fc2fb51411850e76762">&#9670;&nbsp;</a></span>quadratic() <span class="overload">[2/2]</span></h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">std::pair&lt;vindex_t, coeff_t&gt; qbpp::QuadModel::quadratic </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="namespaceqbpp.html#a5cf436100ede362797d0c0501ea50a5a">vindex_t</a>&#160;</td>
          <td class="paramname"><em>i</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="namespaceqbpp.html#a5cf436100ede362797d0c0501ea50a5a">vindex_t</a>&#160;</td>
          <td class="paramname"><em>j</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span><span class="mlabel">inherited</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p class="definition">Definition at line <a class="el" href="qbpp_8hpp_source.html#l01980">1980</a> of file <a class="el" href="qbpp_8hpp_source.html">qbpp.hpp</a>.</p>

</div>
</div>
<a id="a59aa11462585f09c3f68dcfd062545c3"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a59aa11462585f09c3f68dcfd062545c3">&#9670;&nbsp;</a></span>get_quadratic_end()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">std::pair&lt;vindex_t, coeff_t&gt; qbpp::QuadModel::get_quadratic_end </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="namespaceqbpp.html#a5cf436100ede362797d0c0501ea50a5a">vindex_t</a>&#160;</td>
          <td class="paramname"><em>i</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="namespaceqbpp.html#a5cf436100ede362797d0c0501ea50a5a">vindex_t</a>&#160;</td>
          <td class="paramname"><em>j</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span><span class="mlabel">inherited</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p class="definition">Definition at line <a class="el" href="qbpp_8hpp_source.html#l01984">1984</a> of file <a class="el" href="qbpp_8hpp_source.html">qbpp.hpp</a>.</p>

</div>
</div>
<a id="a16af851530b1f63aca9baa8ec67ed01f"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a16af851530b1f63aca9baa8ec67ed01f">&#9670;&nbsp;</a></span>term_count() <span class="overload">[1/2]</span></h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">size_t qbpp::QuadModel::term_count </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="namespaceqbpp.html#a5cf436100ede362797d0c0501ea50a5a">vindex_t</a>&#160;</td>
          <td class="paramname"><em>deg</em></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span><span class="mlabel">inherited</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p class="definition">Definition at line <a class="el" href="qbpp_8hpp_source.html#l01992">1992</a> of file <a class="el" href="qbpp_8hpp_source.html">qbpp.hpp</a>.</p>
<div class="dynheader">
Here is the caller graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="classqbpp_1_1QuadModel_a16af851530b1f63aca9baa8ec67ed01f_icgraph.png" border="0" usemap="#classqbpp_1_1QuadModel_a16af851530b1f63aca9baa8ec67ed01f_icgraph" alt=""/></div>
<map name="classqbpp_1_1QuadModel_a16af851530b1f63aca9baa8ec67ed01f_icgraph" id="classqbpp_1_1QuadModel_a16af851530b1f63aca9baa8ec67ed01f_icgraph">
<area shape="rect" title=" " alt="" coords="453,57,612,98"/>
<area shape="rect" href="graph__color__abs2_8cpp.html#a3c04138a5bfe5d72780bb7e82a18e627" title="Main function to generate a random map and solve the Graph Coloring Problem using the ABS2 QUBO Solve..." alt="" coords="293,5,344,32"/>
<area shape="rect" href="classqbpp_1_1exhaustive__solver_1_1SearchAlgorithm.html#a3a2b22a2908c11bf374b4f06b2ad1815" title=" " alt="" coords="232,57,405,98"/>
<area shape="rect" href="classqbpp_1_1easy__solver_1_1EasySolver.html#a9a61ebfb6366c734e7af2b08890753f0" title=" " alt="" coords="251,122,387,163"/>
<area shape="rect" href="classqbpp_1_1exhaustive__solver_1_1ExhaustiveSolver.html#a967d19602b7de9fef4198508924c7a25" title=" " alt="" coords="5,57,184,98"/>
<area shape="rect" href="graph__color__easy_8cpp.html#a3c04138a5bfe5d72780bb7e82a18e627" title="Main function to generate a random map and solve the Graph Coloring Problem using the Easy Solver." alt="" coords="69,125,120,152"/>
<area shape="rect" href="classqbpp_1_1easy__solver_1_1EasySolver.html#a8d5bc85759b94bc1c1bf2db59fc323b0" title=" " alt="" coords="27,177,163,218"/>
</map>
</div>

</div>
</div>
<a id="a5eabcb9b840eb2cc6083fe943996f230"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a5eabcb9b840eb2cc6083fe943996f230">&#9670;&nbsp;</a></span>term_count() <span class="overload">[2/2]</span></h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">size_t qbpp::QuadModel::term_count </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span><span class="mlabel">override</span><span class="mlabel">virtual</span><span class="mlabel">inherited</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Reimplemented from <a class="el" href="classqbpp_1_1Model.html#a701f2442e6b2aa7e27d924e10883504f">qbpp::Model</a>.</p>

<p class="definition">Definition at line <a class="el" href="qbpp_8hpp_source.html#l02002">2002</a> of file <a class="el" href="qbpp_8hpp_source.html">qbpp.hpp</a>.</p>
<div class="dynheader">
Here is the call graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="classqbpp_1_1QuadModel_a5eabcb9b840eb2cc6083fe943996f230_cgraph.png" border="0" usemap="#classqbpp_1_1QuadModel_a5eabcb9b840eb2cc6083fe943996f230_cgraph" alt=""/></div>
<map name="classqbpp_1_1QuadModel_a5eabcb9b840eb2cc6083fe943996f230_cgraph" id="classqbpp_1_1QuadModel_a5eabcb9b840eb2cc6083fe943996f230_cgraph">
<area shape="rect" title=" " alt="" coords="5,29,164,71"/>
</map>
</div>
<div class="dynheader">
Here is the caller graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="classqbpp_1_1QuadModel_a5eabcb9b840eb2cc6083fe943996f230_icgraph.png" border="0" usemap="#classqbpp_1_1QuadModel_a5eabcb9b840eb2cc6083fe943996f230_icgraph" alt=""/></div>
<map name="classqbpp_1_1QuadModel_a5eabcb9b840eb2cc6083fe943996f230_icgraph" id="classqbpp_1_1QuadModel_a5eabcb9b840eb2cc6083fe943996f230_icgraph">
<area shape="rect" title=" " alt="" coords="5,29,164,71"/>
</map>
</div>

</div>
</div>
<a id="a06b84580a41556201ab2f4bb3d838403"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a06b84580a41556201ab2f4bb3d838403">&#9670;&nbsp;</a></span>min_coeff()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">coeff_t qbpp::QuadModel::min_coeff </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span><span class="mlabel">inherited</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p class="definition">Definition at line <a class="el" href="qbpp_8hpp_source.html#l02004">2004</a> of file <a class="el" href="qbpp_8hpp_source.html">qbpp.hpp</a>.</p>
<div class="dynheader">
Here is the caller graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="classqbpp_1_1QuadModel_a06b84580a41556201ab2f4bb3d838403_icgraph.png" border="0" usemap="#classqbpp_1_1QuadModel_a06b84580a41556201ab2f4bb3d838403_icgraph" alt=""/></div>
<map name="classqbpp_1_1QuadModel_a06b84580a41556201ab2f4bb3d838403_icgraph" id="classqbpp_1_1QuadModel_a06b84580a41556201ab2f4bb3d838403_icgraph">
<area shape="rect" title=" " alt="" coords="212,39,365,80"/>
<area shape="rect" href="structqbpp_1_1QuadModel_1_1Impl.html#a17282586c31a1881bd2228a3111c2aa8" title=" " alt="" coords="6,5,163,47"/>
<area shape="rect" href="group__qbpp__abs2.html#gab125241142be7f65e2a913158e00394a" title="Constructor: Create an ABS2 model from a QUBO model." alt="" coords="5,71,164,112"/>
</map>
</div>

</div>
</div>
<a id="ab6ac73dcc23030f0f7e303e94d3c2bd3"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ab6ac73dcc23030f0f7e303e94d3c2bd3">&#9670;&nbsp;</a></span>max_coeff()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">coeff_t qbpp::QuadModel::max_coeff </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span><span class="mlabel">inherited</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p class="definition">Definition at line <a class="el" href="qbpp_8hpp_source.html#l02006">2006</a> of file <a class="el" href="qbpp_8hpp_source.html">qbpp.hpp</a>.</p>
<div class="dynheader">
Here is the caller graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="classqbpp_1_1QuadModel_ab6ac73dcc23030f0f7e303e94d3c2bd3_icgraph.png" border="0" usemap="#classqbpp_1_1QuadModel_ab6ac73dcc23030f0f7e303e94d3c2bd3_icgraph" alt=""/></div>
<map name="classqbpp_1_1QuadModel_ab6ac73dcc23030f0f7e303e94d3c2bd3_icgraph" id="classqbpp_1_1QuadModel_ab6ac73dcc23030f0f7e303e94d3c2bd3_icgraph">
<area shape="rect" title=" " alt="" coords="212,39,369,80"/>
<area shape="rect" href="structqbpp_1_1QuadModel_1_1Impl.html#a17282586c31a1881bd2228a3111c2aa8" title=" " alt="" coords="6,5,163,47"/>
<area shape="rect" href="group__qbpp__abs2.html#gab125241142be7f65e2a913158e00394a" title="Constructor: Create an ABS2 model from a QUBO model." alt="" coords="5,71,164,112"/>
</map>
</div>

</div>
</div>
<a id="a67c0d56d86152d5540c73491528656de"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a67c0d56d86152d5540c73491528656de">&#9670;&nbsp;</a></span>check_expr() <span class="overload">[1/2]</span></h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">static const Expr&amp; qbpp::Model::check_expr </td>
          <td>(</td>
          <td class="paramtype">const <a class="el" href="classqbpp_1_1Expr.html">Expr</a> &amp;&#160;</td>
          <td class="paramname"><em>expr</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span><span class="mlabel">static</span><span class="mlabel">protected</span><span class="mlabel">inherited</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p class="definition">Definition at line <a class="el" href="qbpp_8hpp_source.html#l01811">1811</a> of file <a class="el" href="qbpp_8hpp_source.html">qbpp.hpp</a>.</p>
<div class="dynheader">
Here is the call graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="classqbpp_1_1Model_a67c0d56d86152d5540c73491528656de_cgraph.png" border="0" usemap="#classqbpp_1_1Model_a67c0d56d86152d5540c73491528656de_cgraph" alt=""/></div>
<map name="classqbpp_1_1Model_a67c0d56d86152d5540c73491528656de_cgraph" id="classqbpp_1_1Model_a67c0d56d86152d5540c73491528656de_cgraph">
<area shape="rect" title=" " alt="" coords="5,31,173,57"/>
<area shape="rect" href="classqbpp_1_1Model.html#a18d86c7c55ae188f35cfafb9cb1f8f85" title=" " alt="" coords="223,5,350,32"/>
<area shape="rect" href="namespaceqbpp.html#a7bd562623faf590810ac1950dd7ea23b" title=" " alt="" coords="221,56,352,83"/>
<area shape="rect" href="namespaceqbpp.html#add51cb422256f9e89483b8cd0deb4721" title=" " alt="" coords="400,56,483,83"/>
</map>
</div>

</div>
</div>
<a id="aec1e4698f99592d3f3834d26b09faf2f"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aec1e4698f99592d3f3834d26b09faf2f">&#9670;&nbsp;</a></span>check_expr() <span class="overload">[2/2]</span></h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">static Expr qbpp::Model::check_expr </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="classqbpp_1_1Expr.html">Expr</a> &amp;&amp;&#160;</td>
          <td class="paramname"><em>expr</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span><span class="mlabel">static</span><span class="mlabel">protected</span><span class="mlabel">inherited</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p class="definition">Definition at line <a class="el" href="qbpp_8hpp_source.html#l01820">1820</a> of file <a class="el" href="qbpp_8hpp_source.html">qbpp.hpp</a>.</p>
<div class="dynheader">
Here is the call graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="classqbpp_1_1Model_aec1e4698f99592d3f3834d26b09faf2f_cgraph.png" border="0" usemap="#classqbpp_1_1Model_aec1e4698f99592d3f3834d26b09faf2f_cgraph" alt=""/></div>
<map name="classqbpp_1_1Model_aec1e4698f99592d3f3834d26b09faf2f_cgraph" id="classqbpp_1_1Model_aec1e4698f99592d3f3834d26b09faf2f_cgraph">
<area shape="rect" title=" " alt="" coords="5,31,173,57"/>
<area shape="rect" href="classqbpp_1_1Model.html#a18d86c7c55ae188f35cfafb9cb1f8f85" title=" " alt="" coords="223,5,350,32"/>
<area shape="rect" href="namespaceqbpp.html#a7bd562623faf590810ac1950dd7ea23b" title=" " alt="" coords="221,56,352,83"/>
<area shape="rect" href="namespaceqbpp.html#add51cb422256f9e89483b8cd0deb4721" title=" " alt="" coords="400,56,483,83"/>
</map>
</div>

</div>
</div>
<a id="a5cf2bb955c3aa25951ca499d7d8635d2"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a5cf2bb955c3aa25951ca499d7d8635d2">&#9670;&nbsp;</a></span>var_count()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">vindex_t qbpp::Model::var_count </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span><span class="mlabel">inherited</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p class="definition">Definition at line <a class="el" href="qbpp_8hpp_source.html#l01859">1859</a> of file <a class="el" href="qbpp_8hpp_source.html">qbpp.hpp</a>.</p>
<div class="dynheader">
Here is the caller graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="classqbpp_1_1Model_a5cf2bb955c3aa25951ca499d7d8635d2_icgraph.png" border="0" usemap="#classqbpp_1_1Model_a5cf2bb955c3aa25951ca499d7d8635d2_icgraph" alt=""/></div>
<map name="classqbpp_1_1Model_a5cf2bb955c3aa25951ca499d7d8635d2_icgraph" id="classqbpp_1_1Model_a5cf2bb955c3aa25951ca499d7d8635d2_icgraph">
<area shape="rect" title=" " alt="" coords="1797,529,1956,555"/>
<area shape="rect" href="classqbpp_1_1factorization_1_1GRB__Callback.html#a5b00538ab4f4123134dacfaa79c3cd4c" title="callback function for Gurobi optimizer." alt="" coords="1322,11,1495,52"/>
<area shape="rect" href="classqbpp__grb_1_1Callback.html#a6d111191d65a98194fec8779fac6dadd" title="Get the solution obtained by Gurobi Optimizer." alt="" coords="1583,69,1718,111"/>
<area shape="rect" href="structqbpp_1_1QuadModel_1_1Impl.html#a17282586c31a1881bd2228a3111c2aa8" title=" " alt="" coords="1572,135,1729,176"/>
<area shape="rect" href="classqbpp_1_1exhaustive__solver_1_1SearchAlgorithm.html#a4c70861e652f494ba0659c5bfb1bb3f7" title=" " alt="" coords="1569,201,1732,257"/>
<area shape="rect" href="graph__color__abs2_8cpp.html#a3c04138a5bfe5d72780bb7e82a18e627" title="Main function to generate a random map and solve the Graph Coloring Problem using the ABS2 QUBO Solve..." alt="" coords="1625,281,1676,307"/>
<area shape="rect" href="classqbpp__grb_1_1QuadModel.html#ad3add2ab2e0d80e5d0d8123deebc6e98" title="Optimize the QUBO model." alt="" coords="1576,332,1725,373"/>
<area shape="rect" href="group__qbpp__grb.html#ga3e5106bc14a17c6521c932753c76fe96" title="Constructor to create a model from QuadModel object." alt="" coords="1576,397,1725,439"/>
<area shape="rect" href="group__qbpp__abs2.html#gab125241142be7f65e2a913158e00394a" title="Constructor: Create an ABS2 model from a QUBO model." alt="" coords="1571,463,1730,504"/>
<area shape="rect" href="classqbpp_1_1exhaustive__solver_1_1SolDelta.html#a3849dd84e16e534fe2e69b63f0079149" title=" " alt="" coords="1327,645,1490,687"/>
<area shape="rect" href="group__qbpp__abs2.html#gaba8e35f84b2f822cd2633b61358370db" title="Construct a new Sol object from an ABS2 solution." alt="" coords="1581,697,1720,723"/>
<area shape="rect" href="classqbpp_1_1exhaustive__solver_1_1SolDelta.html#a6f368c3a2224a71803d9ee2711effa9b" title=" " alt="" coords="1569,748,1732,789"/>
<area shape="rect" href="namespaceqbpp.html#a8fa3af2cffc1996530310aac6e4b0576" title=" " alt="" coords="1614,814,1687,841"/>
<area shape="rect" href="classqbpp_1_1exhaustive__solver_1_1ExhaustiveSolver.html#a156ffd892212e98e1f210fce907a28ec" title=" " alt="" coords="1552,865,1749,907"/>
<area shape="rect" href="classqbpp_1_1exhaustive__solver_1_1SearchAlgorithm.html#a4c1877de640b8a605c36f2f32eae246b" title=" " alt="" coords="1313,515,1504,556"/>
<area shape="rect" href="classqbpp_1_1exhaustive__solver_1_1SolDelta.html#a4ca4e09dec03e09553fc50a7d05c0778" title=" " alt="" coords="1569,580,1732,621"/>
<area shape="rect" href="classqbpp_1_1easy__solver_1_1EasySolver.html#a5aca8737256d008df76e3f53cce1f53a" title=" " alt="" coords="1573,931,1728,972"/>
<area shape="rect" href="classqbpp_1_1Sol.html#a06cbfd519670efbc74363592d5e826b2" title=" " alt="" coords="1579,997,1722,1023"/>
<area shape="rect" href="classGRB__Callback.html#ac08cbd22e13d589be7ca35178d3126d0" title="callback function for Gurobi optimizer." alt="" coords="1326,77,1491,103"/>
<area shape="rect" href="classqbpp__grb_1_1Callback.html#a8b785f76ab9c73fa92b97bb8da6f5e37" title="Default callback function for Gurobi Optimizer." alt="" coords="1341,128,1476,169"/>
<area shape="rect" href="tsp__grb_8cpp.html#a3c04138a5bfe5d72780bb7e82a18e627" title="Main function to generate a random map and solve the Traveling Salesman Problem (TSP) using the Gurob..." alt="" coords="1383,339,1434,366"/>
<area shape="rect" href="classqbpp_1_1exhaustive__solver_1_1SolDelta.html#a926e9faffb265b7208b75663e76f77da" title=" " alt="" coords="1075,645,1237,687"/>
<area shape="rect" href="classqbpp_1_1exhaustive__solver_1_1SearchAlgorithm.html#a3a2b22a2908c11bf374b4f06b2ad1815" title=" " alt="" coords="825,572,999,613"/>
<area shape="rect" href="classqbpp_1_1exhaustive__solver_1_1ExhaustiveSolver.html#a967d19602b7de9fef4198508924c7a25" title=" " alt="" coords="599,572,777,613"/>
<area shape="rect" href="classqbpp_1_1exhaustive__solver_1_1SearchAlgorithm.html#ad031941ba26d6f9e9262803068fbfc7e" title=" " alt="" coords="1075,565,1237,621"/>
<area shape="rect" href="classqbpp_1_1exhaustive__solver_1_1SolDelta.html#ae53a1eb415153c3d6e02794123a87b5a" title=" " alt="" coords="1327,580,1490,621"/>
<area shape="rect" href="classqbpp_1_1easy__solver_1_1EasySolver.html#a329a03e4e5c401ec471e0f7f4c42cb51" title=" " alt="" coords="373,837,551,879"/>
<area shape="rect" href="classqbpp_1_1easy__solver_1_1SolDelta.html#a98373d99b30ddd261919266c2ac59f57" title=" " alt="" coords="1340,931,1477,972"/>
<area shape="rect" href="classqbpp_1_1easy__solver_1_1EasySolver.html#a9a61ebfb6366c734e7af2b08890753f0" title=" " alt="" coords="189,837,325,879"/>
<area shape="rect" href="graph__color__easy_8cpp.html#a3c04138a5bfe5d72780bb7e82a18e627" title="Main function to generate a random map and solve the Graph Coloring Problem using the Easy Solver." alt="" coords="48,815,99,842"/>
<area shape="rect" href="classqbpp_1_1easy__solver_1_1EasySolver.html#a8d5bc85759b94bc1c1bf2db59fc323b0" title=" " alt="" coords="5,867,141,908"/>
<area shape="rect" href="classqbpp_1_1easy__solver_1_1SolDelta.html#a1a107d8974386a07d201e88fb2c9aed9" title=" " alt="" coords="1088,839,1224,880"/>
<area shape="rect" href="classqbpp_1_1easy__solver_1_1PosMinSolDelta.html#aff102bc17a0d06a78a285f1ab2c9632e" title=" " alt="" coords="607,903,769,944"/>
<area shape="rect" href="classqbpp_1_1easy__solver_1_1SolDelta.html#ae5e7dbc16325f4f3b26895fe8686afc5" title=" " alt="" coords="844,1087,980,1128"/>
<area shape="rect" href="classqbpp_1_1easy__solver_1_1SolDelta.html#ad0505230c61ae317b5414f06327daeb4" title=" " alt="" coords="839,788,985,829"/>
<area shape="rect" href="classqbpp_1_1easy__solver_1_1PosMinSolDelta.html#af512f524573c98e13ee2e236cc147c17" title=" " alt="" coords="1047,904,1265,945"/>
<area shape="rect" href="classqbpp_1_1easy__solver_1_1PosMinSolDelta.html#ad8811137073388d35654ad8d6f8db0f2" title=" " alt="" coords="1081,969,1231,1011"/>
<area shape="rect" href="classqbpp_1_1easy__solver_1_1SolDelta.html#a2a6c5b08bc697bd6f633b68fcdd550e1" title=" " alt="" coords="1088,1087,1224,1128"/>
<area shape="rect" href="classqbpp_1_1easy__solver_1_1TabuSolDelta.html#abddd3367c047935e82e517c421cb5a9a" title=" " alt="" coords="844,853,980,895"/>
<area shape="rect" href="classqbpp_1_1easy__solver_1_1SolDelta.html#acf11d3f5b86a20077d4c20dc9c18e044" title=" " alt="" coords="844,919,980,960"/>
<area shape="rect" href="group__qbpp__abs2.html#gac27ae911e72fdeb424a950d41fbef95c" title="Construct a new Sol object from a QUBO++ QUBO solution." alt="" coords="1339,997,1478,1023"/>
</map>
</div>

</div>
</div>
<a id="a52da2204913c7f529be7e8db017aa14a"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a52da2204913c7f529be7e8db017aa14a">&#9670;&nbsp;</a></span>var()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">Var qbpp::Model::var </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="namespaceqbpp.html#a5cf436100ede362797d0c0501ea50a5a">vindex_t</a>&#160;</td>
          <td class="paramname"><em>index</em></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span><span class="mlabel">inherited</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p class="definition">Definition at line <a class="el" href="qbpp_8hpp_source.html#l01861">1861</a> of file <a class="el" href="qbpp_8hpp_source.html">qbpp.hpp</a>.</p>
<div class="dynheader">
Here is the call graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="classqbpp_1_1Model_a52da2204913c7f529be7e8db017aa14a_cgraph.png" border="0" usemap="#classqbpp_1_1Model_a52da2204913c7f529be7e8db017aa14a_cgraph" alt=""/></div>
<map name="classqbpp_1_1Model_a52da2204913c7f529be7e8db017aa14a_cgraph" id="classqbpp_1_1Model_a52da2204913c7f529be7e8db017aa14a_cgraph">
<area shape="rect" title=" " alt="" coords="5,5,124,32"/>
<area shape="rect" href="classqbpp_1_1Model.html#a0712035184681aeb478c5e80bb5221bd" title=" " alt="" coords="172,5,304,32"/>
</map>
</div>
<div class="dynheader">
Here is the caller graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="classqbpp_1_1Model_a52da2204913c7f529be7e8db017aa14a_icgraph.png" border="0" usemap="#classqbpp_1_1Model_a52da2204913c7f529be7e8db017aa14a_icgraph" alt=""/></div>
<map name="classqbpp_1_1Model_a52da2204913c7f529be7e8db017aa14a_icgraph" id="classqbpp_1_1Model_a52da2204913c7f529be7e8db017aa14a_icgraph">
<area shape="rect" title=" " alt="" coords="1509,1137,1628,1164"/>
<area shape="rect" href="classqbpp_1_1Sol.html#a3db07b94f467d91e0be73e15fe5bd453" title=" " alt="" coords="915,296,1043,323"/>
<area shape="rect" href="classqbpp_1_1Model.html#aed96d863b56813737a55c18402588719" title=" " alt="" coords="1334,1188,1457,1215"/>
<area shape="rect" href="classqbpp_1_1Model.html#a0712035184681aeb478c5e80bb5221bd" title=" " alt="" coords="1329,1137,1461,1164"/>
<area shape="rect" href="namespaceqbpp.html#a8fa3af2cffc1996530310aac6e4b0576" title=" " alt="" coords="1359,1239,1432,1265"/>
<area shape="rect" href="classqbpp_1_1Sol.html#a62ab1f54864dd39a79cfd1a3cb4a838c" title=" " alt="" coords="1143,1393,1249,1420"/>
<area shape="rect" href="classqbpp_1_1QuadModel.html#a05a4566b95df159713e98a0c093ec1ef" title=" " alt="" coords="1111,1100,1281,1127"/>
<area shape="rect" href="classqbpp_1_1Sol.html#a6543e96e459b50574cb17643c961b93a" title=" " alt="" coords="1145,1175,1247,1201"/>
<area shape="rect" href="classqbpp__grb_1_1QuadModel.html#a9725ebdf79f6eb70a09de6425f7c5b68" title="Get the Gurobi variable from the index." alt="" coords="905,1050,1054,1091"/>
<area shape="rect" href="structqbpp_1_1QuadModel_1_1Impl.html#a17282586c31a1881bd2228a3111c2aa8" title=" " alt="" coords="1117,1226,1275,1267"/>
<area shape="rect" href="classqbpp_1_1Sol.html#a24f5a53fa68071dd9bcda910533bbad7" title=" " alt="" coords="1138,663,1254,689"/>
<area shape="rect" href="classqbpp_1_1Sol.html#a171de5b36bce9d446849cde5b4324673" title=" " alt="" coords="709,999,812,1025"/>
<area shape="rect" href="classqbpp_1_1QuadModel.html#a4279bbb06acab59034c1afec9ef3148c" title=" " alt="" coords="1115,1292,1277,1319"/>
<area shape="rect" href="namespaceqbpp.html#a6ecdcc61418dea3e7792fc9209924b73" title=" " alt="" coords="1159,1343,1233,1369"/>
<area shape="rect" href="classqbpp_1_1factorization_1_1GRB__Callback.html#a5b00538ab4f4123134dacfaa79c3cd4c" title="callback function for Gurobi optimizer." alt="" coords="452,1109,625,1150"/>
<area shape="rect" href="classqbpp__grb_1_1Callback.html#a6d111191d65a98194fec8779fac6dadd" title="Get the solution obtained by Gurobi Optimizer." alt="" coords="693,1050,828,1091"/>
<area shape="rect" href="classGRB__Callback.html#ac08cbd22e13d589be7ca35178d3126d0" title="callback function for Gurobi optimizer." alt="" coords="456,1057,621,1084"/>
<area shape="rect" href="classqbpp__grb_1_1Callback.html#a8b785f76ab9c73fa92b97bb8da6f5e37" title="Default callback function for Gurobi Optimizer." alt="" coords="471,991,606,1033"/>
<area shape="rect" href="classqbpp_1_1Sol.html#a78255413d025557d55c0c16f4076f9fe" title=" " alt="" coords="896,347,1063,373"/>
<area shape="rect" href="classqbpp_1_1easy__solver_1_1SolDelta.html#a1a107d8974386a07d201e88fb2c9aed9" title=" " alt="" coords="693,714,829,755"/>
<area shape="rect" href="classqbpp_1_1easy__solver_1_1TabuSolDelta.html#abddd3367c047935e82e517c421cb5a9a" title=" " alt="" coords="471,838,607,879"/>
<area shape="rect" href="classqbpp_1_1exhaustive__solver_1_1SolDelta.html#ae53a1eb415153c3d6e02794123a87b5a" title=" " alt="" coords="679,13,842,54"/>
<area shape="rect" href="classqbpp_1_1exhaustive__solver_1_1SolDelta.html#a926e9faffb265b7208b75663e76f77da" title=" " alt="" coords="457,86,620,127"/>
<area shape="rect" href="classqbpp_1_1Sol.html#a4f4c097837ad52f4fd368e048e79aa66" title=" " alt="" coords="928,663,1031,689"/>
<area shape="rect" href="classqbpp_1_1Sol.html#aae6d875ede912bcbf6254b74ffc66018" title=" " alt="" coords="918,714,1041,755"/>
<area shape="rect" href="classqbpp_1_1Sol.html#abdb4f6b779678720715f7845f264440f" title=" " alt="" coords="928,448,1031,475"/>
<area shape="rect" href="classqbpp__abs2_1_1Sol.html#a48d9116f9fcbe00f84640847ee49df23" title="Set the value of the variable with &quot;index&quot; to &quot;value&quot;." alt="" coords="691,831,830,857"/>
<area shape="rect" href="classqbpp_1_1Sol.html#a2f7494eb4a254dd19b0261e801bacffb" title=" " alt="" coords="928,881,1031,908"/>
<area shape="rect" href="classqbpp_1_1easy__solver_1_1TabuSolDelta.html#a514b9fd7511de63ea113064e40c50862" title=" " alt="" coords="899,933,1060,974"/>
<area shape="rect" href="classqbpp_1_1easy__solver_1_1SolDelta.html#acf11d3f5b86a20077d4c20dc9c18e044" title=" " alt="" coords="471,773,607,814"/>
<area shape="rect" href="classqbpp_1_1easy__solver_1_1SolDelta.html#ae5e7dbc16325f4f3b26895fe8686afc5" title=" " alt="" coords="471,655,607,697"/>
<area shape="rect" href="classqbpp_1_1easy__solver_1_1SolDelta.html#ad0505230c61ae317b5414f06327daeb4" title=" " alt="" coords="244,714,391,755"/>
<area shape="rect" href="classqbpp_1_1easy__solver_1_1PosMinSolDelta.html#aff102bc17a0d06a78a285f1ab2c9632e" title=" " alt="" coords="236,779,399,821"/>
<area shape="rect" href="classqbpp_1_1easy__solver_1_1EasySolver.html#a329a03e4e5c401ec471e0f7f4c42cb51" title=" " alt="" coords="5,714,183,755"/>
<area shape="rect" href="classqbpp_1_1exhaustive__solver_1_1SearchAlgorithm.html#ad031941ba26d6f9e9262803068fbfc7e" title=" " alt="" coords="457,5,620,61"/>
<area shape="rect" href="classqbpp_1_1exhaustive__solver_1_1SearchAlgorithm.html#a3a2b22a2908c11bf374b4f06b2ad1815" title=" " alt="" coords="231,50,404,91"/>
<area shape="rect" href="namespaceqbpp_1_1factorization.html#a77868b746b7cb1a8a7e3707ddb5871f9" title="Converts a vector of Var in a solution to a 64&#45;bit unsigned integer." alt="" coords="696,597,825,638"/>
<area shape="rect" href="namespaceqbpp.html#ac429a7530957802c68d0ba6188daad94" title=" " alt="" coords="719,297,802,324"/>
<area shape="rect" href="classqbpp_1_1tsp_1_1TSPSol.html#a850469299d3a30962180e13657e16452" title="helper function to generate a tour from the solution." alt="" coords="692,349,829,390"/>
<area shape="rect" href="classqbpp_1_1Sol.html#a97b2220c9849ce534280e5a54944a54b" title=" " alt="" coords="709,415,812,441"/>
<area shape="rect" href="classqbpp_1_1Var.html#a73945543f5478d768f5a1ecbd3aaf31c" title=" " alt="" coords="691,79,831,105"/>
<area shape="rect" href="classqbpp_1_1tsp_1_1TSPSol.html#aeaddec0d0368bef74ebcbda49fe863fe" title=" " alt="" coords="692,130,829,171"/>
<area shape="rect" href="classqbpp_1_1Sol.html#a0a976747032bda528cc78f2400f601d8" title=" " alt="" coords="709,196,812,223"/>
<area shape="rect" href="classqbpp_1_1graph__color_1_1GraphColorMap.html#ad90af91eeca9f99c1a611d144cae1934" title="Set the color histogram of the nodes." alt="" coords="673,465,848,521"/>
<area shape="rect" href="group__qbpp__abs2.html#gac27ae911e72fdeb424a950d41fbef95c" title="Construct a new Sol object from a QUBO++ QUBO solution." alt="" coords="691,247,830,273"/>
<area shape="rect" href="namespaceqbpp_1_1factorization.html#a36079855e22273fe01e4c9eb34e57557" title="Converts a vector of Var in a solution holder to a 64&#45;bit unsigned integer." alt="" coords="474,590,603,631"/>
<area shape="rect" href="classqbpp_1_1factorization_1_1SolHolder.html#a46e8fb339ad5d7983403f1785f5c24e5" title="Sets new_sol as the best solution if it is better than the current best solution." alt="" coords="474,509,603,565"/>
<area shape="rect" href="graph__color__abs2_8cpp.html#a3c04138a5bfe5d72780bb7e82a18e627" title="Main function to generate a random map and solve the Graph Coloring Problem using the ABS2 QUBO Solve..." alt="" coords="513,459,564,485"/>
<area shape="rect" href="classqbpp__grb_1_1QuadModel.html#ad3add2ab2e0d80e5d0d8123deebc6e98" title="Optimize the QUBO model." alt="" coords="686,933,835,974"/>
<area shape="rect" href="group__qbpp__abs2.html#gaba8e35f84b2f822cd2633b61358370db" title="Construct a new Sol object from an ABS2 solution." alt="" coords="691,881,830,908"/>
</map>
</div>

</div>
</div>
<a id="a0712035184681aeb478c5e80bb5221bd"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a0712035184681aeb478c5e80bb5221bd">&#9670;&nbsp;</a></span>index()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">vindex_t qbpp::Model::index </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="classqbpp_1_1Var.html">Var</a>&#160;</td>
          <td class="paramname"><em>var</em></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span><span class="mlabel">inherited</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p class="definition">Definition at line <a class="el" href="qbpp_8hpp_source.html#l01863">1863</a> of file <a class="el" href="qbpp_8hpp_source.html">qbpp.hpp</a>.</p>
<div class="dynheader">
Here is the call graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="classqbpp_1_1Model_a0712035184681aeb478c5e80bb5221bd_cgraph.png" border="0" usemap="#classqbpp_1_1Model_a0712035184681aeb478c5e80bb5221bd_cgraph" alt=""/></div>
<map name="classqbpp_1_1Model_a0712035184681aeb478c5e80bb5221bd_cgraph" id="classqbpp_1_1Model_a0712035184681aeb478c5e80bb5221bd_cgraph">
<area shape="rect" title=" " alt="" coords="5,5,137,32"/>
<area shape="rect" href="classqbpp_1_1Model.html#a52da2204913c7f529be7e8db017aa14a" title=" " alt="" coords="185,5,304,32"/>
</map>
</div>
<div class="dynheader">
Here is the caller graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="classqbpp_1_1Model_a0712035184681aeb478c5e80bb5221bd_icgraph.png" border="0" usemap="#classqbpp_1_1Model_a0712035184681aeb478c5e80bb5221bd_icgraph" alt=""/></div>
<map name="classqbpp_1_1Model_a0712035184681aeb478c5e80bb5221bd_icgraph" id="classqbpp_1_1Model_a0712035184681aeb478c5e80bb5221bd_icgraph">
<area shape="rect" title=" " alt="" coords="1329,535,1461,561"/>
<area shape="rect" href="classqbpp_1_1QuadModel.html#a05a4566b95df159713e98a0c093ec1ef" title=" " alt="" coords="1111,5,1281,32"/>
<area shape="rect" href="classqbpp_1_1Sol.html#a6543e96e459b50574cb17643c961b93a" title=" " alt="" coords="1145,80,1247,107"/>
<area shape="rect" href="classqbpp__grb_1_1QuadModel.html#a9725ebdf79f6eb70a09de6425f7c5b68" title="Get the Gurobi variable from the index." alt="" coords="905,115,1054,157"/>
<area shape="rect" href="structqbpp_1_1QuadModel_1_1Impl.html#a17282586c31a1881bd2228a3111c2aa8" title=" " alt="" coords="1117,611,1275,653"/>
<area shape="rect" href="classqbpp_1_1Sol.html#a24f5a53fa68071dd9bcda910533bbad7" title=" " alt="" coords="1138,509,1254,536"/>
<area shape="rect" href="classqbpp_1_1Sol.html#a171de5b36bce9d446849cde5b4324673" title=" " alt="" coords="709,228,812,255"/>
<area shape="rect" href="classqbpp_1_1QuadModel.html#a4279bbb06acab59034c1afec9ef3148c" title=" " alt="" coords="1115,677,1277,704"/>
<area shape="rect" href="namespaceqbpp.html#a6ecdcc61418dea3e7792fc9209924b73" title=" " alt="" coords="1159,728,1233,755"/>
<area shape="rect" href="classqbpp_1_1Model.html#a52da2204913c7f529be7e8db017aa14a" title=" " alt="" coords="1137,560,1255,587"/>
<area shape="rect" href="classqbpp_1_1factorization_1_1GRB__Callback.html#a5b00538ab4f4123134dacfaa79c3cd4c" title="callback function for Gurobi optimizer." alt="" coords="452,99,625,141"/>
<area shape="rect" href="classqbpp__grb_1_1Callback.html#a6d111191d65a98194fec8779fac6dadd" title="Get the solution obtained by Gurobi Optimizer." alt="" coords="693,162,828,203"/>
<area shape="rect" href="classGRB__Callback.html#ac08cbd22e13d589be7ca35178d3126d0" title="callback function for Gurobi optimizer." alt="" coords="456,231,621,257"/>
<area shape="rect" href="classqbpp__grb_1_1Callback.html#a8b785f76ab9c73fa92b97bb8da6f5e37" title="Default callback function for Gurobi Optimizer." alt="" coords="471,165,606,206"/>
<area shape="rect" href="classqbpp_1_1Sol.html#a78255413d025557d55c0c16f4076f9fe" title=" " alt="" coords="896,568,1063,595"/>
<area shape="rect" href="classqbpp_1_1easy__solver_1_1SolDelta.html#a1a107d8974386a07d201e88fb2c9aed9" title=" " alt="" coords="693,447,829,489"/>
<area shape="rect" href="classqbpp_1_1easy__solver_1_1TabuSolDelta.html#abddd3367c047935e82e517c421cb5a9a" title=" " alt="" coords="471,506,607,547"/>
<area shape="rect" href="classqbpp_1_1exhaustive__solver_1_1SolDelta.html#ae53a1eb415153c3d6e02794123a87b5a" title=" " alt="" coords="679,1147,842,1189"/>
<area shape="rect" href="classqbpp_1_1exhaustive__solver_1_1SolDelta.html#a926e9faffb265b7208b75663e76f77da" title=" " alt="" coords="457,1221,620,1262"/>
<area shape="rect" href="classqbpp_1_1Sol.html#a4f4c097837ad52f4fd368e048e79aa66" title=" " alt="" coords="928,232,1031,259"/>
<area shape="rect" href="classqbpp_1_1Sol.html#aae6d875ede912bcbf6254b74ffc66018" title=" " alt="" coords="918,502,1041,543"/>
<area shape="rect" href="classqbpp_1_1Sol.html#abdb4f6b779678720715f7845f264440f" title=" " alt="" coords="928,812,1031,839"/>
<area shape="rect" href="classqbpp_1_1Sol.html#a3db07b94f467d91e0be73e15fe5bd453" title=" " alt="" coords="915,1039,1043,1065"/>
<area shape="rect" href="classqbpp__abs2_1_1Sol.html#a48d9116f9fcbe00f84640847ee49df23" title="Set the value of the variable with &quot;index&quot; to &quot;value&quot;." alt="" coords="691,396,830,423"/>
<area shape="rect" href="classqbpp_1_1Sol.html#a2f7494eb4a254dd19b0261e801bacffb" title=" " alt="" coords="928,349,1031,376"/>
<area shape="rect" href="classqbpp_1_1easy__solver_1_1TabuSolDelta.html#a514b9fd7511de63ea113064e40c50862" title=" " alt="" coords="899,283,1060,325"/>
<area shape="rect" href="classqbpp_1_1easy__solver_1_1SolDelta.html#acf11d3f5b86a20077d4c20dc9c18e044" title=" " alt="" coords="471,441,607,482"/>
<area shape="rect" href="classqbpp_1_1easy__solver_1_1SolDelta.html#ae5e7dbc16325f4f3b26895fe8686afc5" title=" " alt="" coords="471,571,607,613"/>
<area shape="rect" href="classqbpp_1_1easy__solver_1_1SolDelta.html#ad0505230c61ae317b5414f06327daeb4" title=" " alt="" coords="244,397,391,438"/>
<area shape="rect" href="classqbpp_1_1easy__solver_1_1PosMinSolDelta.html#aff102bc17a0d06a78a285f1ab2c9632e" title=" " alt="" coords="236,491,399,533"/>
<area shape="rect" href="classqbpp_1_1easy__solver_1_1EasySolver.html#a329a03e4e5c401ec471e0f7f4c42cb51" title=" " alt="" coords="5,491,183,533"/>
<area shape="rect" href="classqbpp_1_1exhaustive__solver_1_1SearchAlgorithm.html#ad031941ba26d6f9e9262803068fbfc7e" title=" " alt="" coords="457,1140,620,1196"/>
<area shape="rect" href="classqbpp_1_1exhaustive__solver_1_1SearchAlgorithm.html#a3a2b22a2908c11bf374b4f06b2ad1815" title=" " alt="" coords="231,1183,404,1225"/>
<area shape="rect" href="namespaceqbpp_1_1factorization.html#a77868b746b7cb1a8a7e3707ddb5871f9" title="Converts a vector of Var in a solution to a 64&#45;bit unsigned integer." alt="" coords="696,1082,825,1123"/>
<area shape="rect" href="namespaceqbpp.html#ac429a7530957802c68d0ba6188daad94" title=" " alt="" coords="719,812,802,839"/>
<area shape="rect" href="classqbpp_1_1tsp_1_1TSPSol.html#a850469299d3a30962180e13657e16452" title="helper function to generate a tour from the solution." alt="" coords="692,863,829,905"/>
<area shape="rect" href="classqbpp_1_1Sol.html#a97b2220c9849ce534280e5a54944a54b" title=" " alt="" coords="709,929,812,956"/>
<area shape="rect" href="classqbpp_1_1Var.html#a73945543f5478d768f5a1ecbd3aaf31c" title=" " alt="" coords="691,980,831,1007"/>
<area shape="rect" href="classqbpp_1_1tsp_1_1TSPSol.html#aeaddec0d0368bef74ebcbda49fe863fe" title=" " alt="" coords="692,645,829,686"/>
<area shape="rect" href="classqbpp_1_1Sol.html#a0a976747032bda528cc78f2400f601d8" title=" " alt="" coords="709,711,812,737"/>
<area shape="rect" href="classqbpp_1_1graph__color_1_1GraphColorMap.html#ad90af91eeca9f99c1a611d144cae1934" title="Set the color histogram of the nodes." alt="" coords="673,564,848,620"/>
<area shape="rect" href="group__qbpp__abs2.html#gac27ae911e72fdeb424a950d41fbef95c" title="Construct a new Sol object from a QUBO++ QUBO solution." alt="" coords="691,761,830,788"/>
<area shape="rect" href="namespaceqbpp_1_1factorization.html#a36079855e22273fe01e4c9eb34e57557" title="Converts a vector of Var in a solution holder to a 64&#45;bit unsigned integer." alt="" coords="474,994,603,1035"/>
<area shape="rect" href="classqbpp_1_1factorization_1_1SolHolder.html#a46e8fb339ad5d7983403f1785f5c24e5" title="Sets new_sol as the best solution if it is better than the current best solution." alt="" coords="474,1060,603,1116"/>
<area shape="rect" href="graph__color__abs2_8cpp.html#a3c04138a5bfe5d72780bb7e82a18e627" title="Main function to generate a random map and solve the Graph Coloring Problem using the ABS2 QUBO Solve..." alt="" coords="513,637,564,664"/>
<area shape="rect" href="classqbpp__grb_1_1QuadModel.html#ad3add2ab2e0d80e5d0d8123deebc6e98" title="Optimize the QUBO model." alt="" coords="686,330,835,371"/>
<area shape="rect" href="group__qbpp__abs2.html#gaba8e35f84b2f822cd2633b61358370db" title="Construct a new Sol object from an ABS2 solution." alt="" coords="691,279,830,305"/>
<area shape="rect" href="classqbpp_1_1Model.html#aed96d863b56813737a55c18402588719" title=" " alt="" coords="918,1239,1041,1265"/>
<area shape="rect" href="namespaceqbpp.html#a8fa3af2cffc1996530310aac6e4b0576" title=" " alt="" coords="943,1089,1016,1116"/>
<area shape="rect" href="classqbpp_1_1Sol.html#a62ab1f54864dd39a79cfd1a3cb4a838c" title=" " alt="" coords="707,1264,814,1291"/>
</map>
</div>

</div>
</div>
<a id="aed96d863b56813737a55c18402588719"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aed96d863b56813737a55c18402588719">&#9670;&nbsp;</a></span>has()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">bool qbpp::Model::has </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="classqbpp_1_1Var.html">Var</a>&#160;</td>
          <td class="paramname"><em>var</em></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span><span class="mlabel">inherited</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p class="definition">Definition at line <a class="el" href="qbpp_8hpp_source.html#l01865">1865</a> of file <a class="el" href="qbpp_8hpp_source.html">qbpp.hpp</a>.</p>
<div class="dynheader">
Here is the call graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="classqbpp_1_1Model_aed96d863b56813737a55c18402588719_cgraph.png" border="0" usemap="#classqbpp_1_1Model_aed96d863b56813737a55c18402588719_cgraph" alt=""/></div>
<map name="classqbpp_1_1Model_aed96d863b56813737a55c18402588719_cgraph" id="classqbpp_1_1Model_aed96d863b56813737a55c18402588719_cgraph">
<area shape="rect" title=" " alt="" coords="5,5,128,32"/>
<area shape="rect" href="classqbpp_1_1Model.html#a52da2204913c7f529be7e8db017aa14a" title=" " alt="" coords="176,5,295,32"/>
<area shape="rect" href="classqbpp_1_1Model.html#a0712035184681aeb478c5e80bb5221bd" title=" " alt="" coords="343,5,475,32"/>
</map>
</div>
<div class="dynheader">
Here is the caller graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="classqbpp_1_1Model_aed96d863b56813737a55c18402588719_icgraph.png" border="0" usemap="#classqbpp_1_1Model_aed96d863b56813737a55c18402588719_icgraph" alt=""/></div>
<map name="classqbpp_1_1Model_aed96d863b56813737a55c18402588719_icgraph" id="classqbpp_1_1Model_aed96d863b56813737a55c18402588719_icgraph">
<area shape="rect" title=" " alt="" coords="160,5,283,32"/>
<area shape="rect" href="classqbpp_1_1Sol.html#a62ab1f54864dd39a79cfd1a3cb4a838c" title=" " alt="" coords="5,5,112,32"/>
</map>
</div>

</div>
</div>
<a id="a18d86c7c55ae188f35cfafb9cb1f8f85"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a18d86c7c55ae188f35cfafb9cb1f8f85">&#9670;&nbsp;</a></span>expr()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">const Expr&amp; qbpp::Model::expr </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span><span class="mlabel">inherited</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p class="definition">Definition at line <a class="el" href="qbpp_8hpp_source.html#l01869">1869</a> of file <a class="el" href="qbpp_8hpp_source.html">qbpp.hpp</a>.</p>
<div class="dynheader">
Here is the caller graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="classqbpp_1_1Model_a18d86c7c55ae188f35cfafb9cb1f8f85_icgraph.png" border="0" usemap="#classqbpp_1_1Model_a18d86c7c55ae188f35cfafb9cb1f8f85_icgraph" alt=""/></div>
<map name="classqbpp_1_1Model_a18d86c7c55ae188f35cfafb9cb1f8f85_icgraph" id="classqbpp_1_1Model_a18d86c7c55ae188f35cfafb9cb1f8f85_icgraph">
<area shape="rect" title=" " alt="" coords="475,163,601,189"/>
<area shape="rect" href="classqbpp_1_1Model.html#a67c0d56d86152d5540c73491528656de" title=" " alt="" coords="249,5,417,32"/>
<area shape="rect" href="classqbpp_1_1nqueen_1_1NQueenQuadModel.html#a7a3c1501bc6230d15a3b033b26584575" title="Generates qbpp::Expr for the N&#45;Queens problem." alt="" coords="240,57,427,98"/>
<area shape="rect" href="classqbpp_1_1nqueen_1_1NQueenQuadModel.html#af0ccd64d202250088cc2846f4c86516b" title="Generates the QUBO expression for the N&#45;Queens problem." alt="" coords="240,122,427,163"/>
<area shape="rect" href="classqbpp_1_1tsp_1_1TSPQuadModel.html#afa2124847942c070b4cb95ab9ae59284" title="Generate a QUBO expression for the Traveling Salesman Problem (TSP)." alt="" coords="245,187,421,229"/>
<area shape="rect" href="structqbpp_1_1QuadModel_1_1Impl.html#a17282586c31a1881bd2228a3111c2aa8" title=" " alt="" coords="255,253,412,294"/>
<area shape="rect" href="namespaceqbpp.html#a6ecdcc61418dea3e7792fc9209924b73" title=" " alt="" coords="297,319,370,345"/>
<area shape="rect" href="classqbpp_1_1nqueen_1_1NQueenQuadModel.html#a940443a8bb6cadae0d38f96b92661f58" title="Helper function to compute initial values for member variables." alt="" coords="5,90,192,131"/>
</map>
</div>

</div>
</div>
<a id="ab6813448e5b12810826fdb9e04aeee83"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ab6813448e5b12810826fdb9e04aeee83">&#9670;&nbsp;</a></span>operator const Expr &amp;()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">qbpp::Model::operator const Expr &amp; </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span><span class="mlabel">inherited</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p class="definition">Definition at line <a class="el" href="qbpp_8hpp_source.html#l01871">1871</a> of file <a class="el" href="qbpp_8hpp_source.html">qbpp.hpp</a>.</p>

</div>
</div>
<a id="a3b0641c97573b248f1877c2826277102"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a3b0641c97573b248f1877c2826277102">&#9670;&nbsp;</a></span>constant()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">energy_t qbpp::Model::constant </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span><span class="mlabel">inherited</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p class="definition">Definition at line <a class="el" href="qbpp_8hpp_source.html#l01873">1873</a> of file <a class="el" href="qbpp_8hpp_source.html">qbpp.hpp</a>.</p>
<div class="dynheader">
Here is the caller graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="classqbpp_1_1Model_a3b0641c97573b248f1877c2826277102_icgraph.png" border="0" usemap="#classqbpp_1_1Model_a3b0641c97573b248f1877c2826277102_icgraph" alt=""/></div>
<map name="classqbpp_1_1Model_a3b0641c97573b248f1877c2826277102_icgraph" id="classqbpp_1_1Model_a3b0641c97573b248f1877c2826277102_icgraph">
<area shape="rect" title=" " alt="" coords="216,180,367,207"/>
<area shape="rect" href="classqbpp_1_1Sol.html#a8acf56be292028a9601a6d7a3517ba7b" title=" " alt="" coords="30,5,143,32"/>
<area shape="rect" href="classqbpp_1_1Sol.html#a776c4f721be7e67bc2c801c126989953" title=" " alt="" coords="19,56,154,83"/>
<area shape="rect" href="group__qbpp__abs2.html#ga7665b9e646b22156b8680c64cd08c92c" title="Executes ABS2 for &quot;quad_model&quot; with &quot;param&quot; and returns &quot;sol&quot;." alt="" coords="17,107,156,149"/>
<area shape="rect" href="group__qbpp__grb.html#ga3e5106bc14a17c6521c932753c76fe96" title="Constructor to create a model from QuadModel object." alt="" coords="12,173,161,214"/>
<area shape="rect" href="group__qbpp__abs2.html#gaba8e35f84b2f822cd2633b61358370db" title="Construct a new Sol object from an ABS2 solution." alt="" coords="17,239,156,265"/>
<area shape="rect" href="classqbpp_1_1exhaustive__solver_1_1SolDelta.html#a6f368c3a2224a71803d9ee2711effa9b" title=" " alt="" coords="5,290,168,331"/>
<area shape="rect" href="namespaceqbpp.html#a8fa3af2cffc1996530310aac6e4b0576" title=" " alt="" coords="50,356,123,383"/>
</map>
</div>

</div>
</div>
<a id="a24930e1898939d5f4a6f6d0c9d56cca1"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a24930e1898939d5f4a6f6d0c9d56cca1">&#9670;&nbsp;</a></span>terms()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">const Terms&amp; qbpp::Model::terms </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span><span class="mlabel">inherited</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p class="definition">Definition at line <a class="el" href="qbpp_8hpp_source.html#l01875">1875</a> of file <a class="el" href="qbpp_8hpp_source.html">qbpp.hpp</a>.</p>
<div class="dynheader">
Here is the caller graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="classqbpp_1_1Model_a24930e1898939d5f4a6f6d0c9d56cca1_icgraph.png" border="0" usemap="#classqbpp_1_1Model_a24930e1898939d5f4a6f6d0c9d56cca1_icgraph" alt=""/></div>
<map name="classqbpp_1_1Model_a24930e1898939d5f4a6f6d0c9d56cca1_icgraph" id="classqbpp_1_1Model_a24930e1898939d5f4a6f6d0c9d56cca1_icgraph">
<area shape="rect" title=" " alt="" coords="211,13,345,39"/>
<area shape="rect" href="structqbpp_1_1QuadModel_1_1Impl.html#a17282586c31a1881bd2228a3111c2aa8" title=" " alt="" coords="5,5,163,47"/>
</map>
</div>

</div>
</div>
<a id="a0657005b0ed9c8d697793e222ea1aecd"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a0657005b0ed9c8d697793e222ea1aecd">&#9670;&nbsp;</a></span>index_var_mapper()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">const impl::IndexVarMapper&amp; qbpp::Model::index_var_mapper </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span><span class="mlabel">inherited</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p class="definition">Definition at line <a class="el" href="qbpp_8hpp_source.html#l01877">1877</a> of file <a class="el" href="qbpp_8hpp_source.html">qbpp.hpp</a>.</p>

</div>
</div>
<a id="a5ccc6ba4f93252db998f1ec2d50fe622"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a5ccc6ba4f93252db998f1ec2d50fe622">&#9670;&nbsp;</a></span>index_var()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">const std::vector&lt;Var&gt;&amp; qbpp::Model::index_var </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span><span class="mlabel">inherited</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p class="definition">Definition at line <a class="el" href="qbpp_8hpp_source.html#l01881">1881</a> of file <a class="el" href="qbpp_8hpp_source.html">qbpp.hpp</a>.</p>
<div class="dynheader">
Here is the caller graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="classqbpp_1_1Model_a5ccc6ba4f93252db998f1ec2d50fe622_icgraph.png" border="0" usemap="#classqbpp_1_1Model_a5ccc6ba4f93252db998f1ec2d50fe622_icgraph" alt=""/></div>
<map name="classqbpp_1_1Model_a5ccc6ba4f93252db998f1ec2d50fe622_icgraph" id="classqbpp_1_1Model_a5ccc6ba4f93252db998f1ec2d50fe622_icgraph">
<area shape="rect" title=" " alt="" coords="156,5,313,32"/>
<area shape="rect" href="classqbpp_1_1Sol.html#a0a976747032bda528cc78f2400f601d8" title=" " alt="" coords="5,5,108,32"/>
</map>
</div>

</div>
</div>
<a id="aca538cfecdad8a952309a5a6fc62ddfc"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aca538cfecdad8a952309a5a6fc62ddfc">&#9670;&nbsp;</a></span>var_index()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">const std::vector&lt;vindex_t&gt;&amp; qbpp::Model::var_index </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span><span class="mlabel">inherited</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p class="definition">Definition at line <a class="el" href="qbpp_8hpp_source.html#l01885">1885</a> of file <a class="el" href="qbpp_8hpp_source.html">qbpp.hpp</a>.</p>

</div>
</div>
<h2 class="groupheader">Member Data Documentation</h2>
<a id="a763aa7381304ed9bd82054b6614c929f"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a763aa7381304ed9bd82054b6614c929f">&#9670;&nbsp;</a></span>abs2model_ptr</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">std::shared_ptr&lt;<a class="el" href="classabs2_1_1Model.html">abs2::Model</a>&gt; qbpp_abs2::QuadModel::abs2model_ptr</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">private</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>ABS2 model associated with the QUBO model. </p>

<p class="definition">Definition at line <a class="el" href="qbpp__abs2_8hpp_source.html#l00078">78</a> of file <a class="el" href="qbpp__abs2_8hpp_source.html">qbpp_abs2.hpp</a>.</p>

</div>
</div>
<a id="aa8e77f59612c13be01cf3d5bbdcf88e1"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aa8e77f59612c13be01cf3d5bbdcf88e1">&#9670;&nbsp;</a></span>pimpl_</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">const std::shared_ptr&lt;const <a class="el" href="structqbpp_1_1QuadModel_1_1Impl.html">Impl</a>&gt; qbpp::QuadModel::pimpl_</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">private</span><span class="mlabel">inherited</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p class="definition">Definition at line <a class="el" href="qbpp_8hpp_source.html#l01912">1912</a> of file <a class="el" href="qbpp_8hpp_source.html">qbpp.hpp</a>.</p>

</div>
</div>
<a id="aff40b2cba811161ddf864245ca1fa84d"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aff40b2cba811161ddf864245ca1fa84d">&#9670;&nbsp;</a></span>linear_</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">const std::vector&lt;coeff_t&gt;&amp; qbpp::QuadModel::linear_</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">private</span><span class="mlabel">inherited</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p class="definition">Definition at line <a class="el" href="qbpp_8hpp_source.html#l01914">1914</a> of file <a class="el" href="qbpp_8hpp_source.html">qbpp.hpp</a>.</p>

</div>
</div>
<a id="af7854162ac2aa7cd282a2f0113b658d4"></a>
<h2 class="memtitle"><span class="permalink"><a href="#af7854162ac2aa7cd282a2f0113b658d4">&#9670;&nbsp;</a></span>degree_</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">const std::vector&lt;vindex_t&gt;&amp; qbpp::QuadModel::degree_</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">private</span><span class="mlabel">inherited</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p class="definition">Definition at line <a class="el" href="qbpp_8hpp_source.html#l01916">1916</a> of file <a class="el" href="qbpp_8hpp_source.html">qbpp.hpp</a>.</p>

</div>
</div>
<a id="a313cf3b52f2816aa5cb02bd4332357ec"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a313cf3b52f2816aa5cb02bd4332357ec">&#9670;&nbsp;</a></span>quadratic_</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">const std::vector&lt;std::vector&lt;std::pair&lt;vindex_t, coeff_t&gt; &gt; &gt;&amp; qbpp::QuadModel::quadratic_</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">private</span><span class="mlabel">inherited</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p class="definition">Definition at line <a class="el" href="qbpp_8hpp_source.html#l01918">1918</a> of file <a class="el" href="qbpp_8hpp_source.html">qbpp.hpp</a>.</p>

</div>
</div>
<a id="a4f61a395e27a143162c85bb66ff3c5ab"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a4f61a395e27a143162c85bb66ff3c5ab">&#9670;&nbsp;</a></span>expr_ptr_</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">const std::shared_ptr&lt;const Expr&gt; qbpp::Model::expr_ptr_</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">protected</span><span class="mlabel">inherited</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p class="definition">Definition at line <a class="el" href="qbpp_8hpp_source.html#l01807">1807</a> of file <a class="el" href="qbpp_8hpp_source.html">qbpp.hpp</a>.</p>

</div>
</div>
<a id="aa73d48897a165a209db4531357018e27"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aa73d48897a165a209db4531357018e27">&#9670;&nbsp;</a></span>index_var_ptr_</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">const std::shared_ptr&lt;const impl::IndexVarMapper&gt; qbpp::Model::index_var_ptr_</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">protected</span><span class="mlabel">inherited</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p class="definition">Definition at line <a class="el" href="qbpp_8hpp_source.html#l01809">1809</a> of file <a class="el" href="qbpp_8hpp_source.html">qbpp.hpp</a>.</p>

</div>
</div>
<hr/>The documentation for this class was generated from the following file:<ul>
<li>include/<a class="el" href="qbpp__abs2_8hpp_source.html">qbpp_abs2.hpp</a></li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.17
</small></address>
</body>
</html>
