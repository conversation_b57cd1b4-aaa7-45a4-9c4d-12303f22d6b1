<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.17"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>QUBO++ Library with QUBO Solver APIs: include/qbpp.hpp File Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/x-mathjax-config">
  MathJax.Hub.Config({
    extensions: ["tex2jax.js", "TeX/AMSmath.js", "TeX/AMSsymbols.js"],
    jax: ["input/TeX","output/HTML-CSS"],
});
</script>
<script type="text/javascript" async="async" src="https://cdn.jsdelivr.net/npm/mathjax@2/MathJax.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">QUBO++ Library with QUBO Solver APIs
   </div>
   <div id="projectbrief">Author: Koji Nakano, License: Non-commercial research and evaluation purposes without any guarantees.</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.17 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_d44c64559bbebec7f509842c48db8b23.html">include</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#nested-classes">Classes</a> &#124;
<a href="#namespaces">Namespaces</a> &#124;
<a href="#define-members">Macros</a> &#124;
<a href="#typedef-members">Typedefs</a> &#124;
<a href="#func-members">Functions</a> &#124;
<a href="#var-members">Variables</a>  </div>
  <div class="headertitle">
<div class="title">qbpp.hpp File Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p>QUBO++, a C++ library for generating expressions for binary and spin variables.  
<a href="#details">More...</a></p>
<div class="textblock"><code>#include &lt;tbb/blocked_range.h&gt;</code><br />
<code>#include &lt;tbb/parallel_for.h&gt;</code><br />
<code>#include &lt;tbb/parallel_reduce.h&gt;</code><br />
<code>#include &lt;tbb/parallel_sort.h&gt;</code><br />
<code>#include &lt;algorithm&gt;</code><br />
<code>#include &lt;boost/container/small_vector.hpp&gt;</code><br />
<code>#include &lt;boost/container/static_vector.hpp&gt;</code><br />
<code>#include &lt;boost/endian/conversion.hpp&gt;</code><br />
<code>#include &lt;boost/multi_array.hpp&gt;</code><br />
<code>#include &lt;boost/multiprecision/cpp_int.hpp&gt;</code><br />
<code>#include &lt;chrono&gt;</code><br />
<code>#include &lt;cmath&gt;</code><br />
<code>#include &lt;initializer_list&gt;</code><br />
<code>#include &lt;iomanip&gt;</code><br />
<code>#include &lt;iostream&gt;</code><br />
<code>#include &lt;limits&gt;</code><br />
<code>#include &lt;list&gt;</code><br />
<code>#include &lt;memory&gt;</code><br />
<code>#include &lt;mutex&gt;</code><br />
<code>#include &lt;optional&gt;</code><br />
<code>#include &lt;sstream&gt;</code><br />
<code>#include &lt;string&gt;</code><br />
<code>#include &lt;thread&gt;</code><br />
<code>#include &lt;type_traits&gt;</code><br />
<code>#include &lt;unordered_map&gt;</code><br />
<code>#include &lt;unordered_set&gt;</code><br />
<code>#include &lt;utility&gt;</code><br />
<code>#include &lt;variant&gt;</code><br />
<code>#include &lt;vector&gt;</code><br />
</div><div class="textblock"><div class="dynheader">
Include dependency graph for qbpp.hpp:</div>
<div class="dyncontent">
<div class="center"><img src="qbpp_8hpp__incl.png" border="0" usemap="#include_2qbpp_8hpp" alt=""/></div>
<map name="include_2qbpp_8hpp" id="include_2qbpp_8hpp">
<area shape="rect" title="QUBO++, a C++ library for generating expressions for binary and spin variables." alt="" coords="1968,5,2088,32"/>
<area shape="rect" title=" " alt="" coords="5,87,144,114"/>
<area shape="rect" title=" " alt="" coords="168,87,285,114"/>
<area shape="rect" title=" " alt="" coords="309,87,451,114"/>
<area shape="rect" title=" " alt="" coords="476,87,601,114"/>
<area shape="rect" title=" " alt="" coords="625,87,700,114"/>
<area shape="rect" title=" " alt="" coords="724,80,871,121"/>
<area shape="rect" title=" " alt="" coords="895,80,1041,121"/>
<area shape="rect" title=" " alt="" coords="1066,87,1254,114"/>
<area shape="rect" title=" " alt="" coords="1278,87,1423,114"/>
<area shape="rect" title=" " alt="" coords="1448,80,1587,121"/>
<area shape="rect" title=" " alt="" coords="1611,87,1672,114"/>
<area shape="rect" title=" " alt="" coords="1696,87,1755,114"/>
<area shape="rect" title=" " alt="" coords="1779,87,1877,114"/>
<area shape="rect" title=" " alt="" coords="1901,87,1968,114"/>
<area shape="rect" title=" " alt="" coords="1992,87,2064,114"/>
<area shape="rect" title=" " alt="" coords="2088,87,2141,114"/>
<area shape="rect" title=" " alt="" coords="2166,87,2205,114"/>
<area shape="rect" title=" " alt="" coords="2229,87,2299,114"/>
<area shape="rect" title=" " alt="" coords="2323,87,2381,114"/>
<area shape="rect" title=" " alt="" coords="2405,87,2472,114"/>
<area shape="rect" title=" " alt="" coords="2496,87,2565,114"/>
<area shape="rect" title=" " alt="" coords="2590,87,2645,114"/>
<area shape="rect" title=" " alt="" coords="2669,87,2728,114"/>
<area shape="rect" title=" " alt="" coords="2752,87,2835,114"/>
<area shape="rect" title=" " alt="" coords="2859,87,2970,114"/>
<area shape="rect" title=" " alt="" coords="2995,87,3099,114"/>
<area shape="rect" title=" " alt="" coords="3123,87,3176,114"/>
<area shape="rect" title=" " alt="" coords="3200,87,3261,114"/>
<area shape="rect" title=" " alt="" coords="3285,87,3344,114"/>
</map>
</div>
</div><div class="textblock"><div class="dynheader">
This graph shows which files directly or indirectly include this file:</div>
<div class="dyncontent">
<div class="center"><img src="qbpp_8hpp__dep__incl.png" border="0" usemap="#include_2qbpp_8hppdep" alt=""/></div>
<map name="include_2qbpp_8hppdep" id="include_2qbpp_8hppdep">
<area shape="rect" title="QUBO++, a C++ library for generating expressions for binary and spin variables." alt="" coords="1764,5,1884,32"/>
<area shape="rect" href="qbpp__abs2_8hpp.html" title="QUBO++ interface to call ABS2 GPU QUBO solver." alt="" coords="225,87,380,114"/>
<area shape="rect" href="graph__color__abs2_8cpp.html" title="Solves randomly generated Graph Coloring Problem using ABS2 QUBO Solver." alt="" coords="190,259,324,300"/>
<area shape="rect" href="ilp__abs2_8cpp.html" title="Solves an Integer Linear Programming (ILP) problem using ABS2 solver through QUBO++ library." alt="" coords="46,177,188,203"/>
<area shape="rect" href="tsp__abs2_8cpp.html" title="Solves randomly generated Traveling Salesman Problem (TSP) using the ABS2 QUBO solver." alt="" coords="562,266,709,293"/>
<area shape="rect" href="simple__factorization__abs2_8cpp.html" title="Simple factorization example using ABS2 QUBO Solver." alt="" coords="364,169,548,211"/>
<area shape="rect" href="factorization_8cpp.html" title="Solves factorization and multiplication problems for two prime numbers using multiple QUBO solvers." alt="" coords="750,266,914,293"/>
<area shape="rect" href="qbpp__grb_8hpp.html" title="QUBO++ interface to call Gurobi Optimizer." alt="" coords="998,87,1143,114"/>
<area shape="rect" href="graph__color__grb_8cpp.html" title="Solves randomly generated Graph Coloring Problem using QUBO++ Gurobi Optimizer." alt="" coords="1276,259,1411,300"/>
<area shape="rect" href="ilp__grb_8cpp.html" title="Solves an Integer Linear Programming (ILP) problem using Gurobi Optimizer through QUBO++ library." alt="" coords="673,177,804,203"/>
<area shape="rect" href="tsp__grb_8cpp.html" title="Solves randomly generated Traveling Salesman Problem (TSP) using the QUBO++ Easy solver." alt="" coords="1629,266,1765,293"/>
<area shape="rect" href="simple__factorization__grb_8cpp.html" title="Simple factorization example using Gurobi Optimizer." alt="" coords="828,169,1012,211"/>
<area shape="rect" href="qbpp__misc_8hpp.html" title="A miscellaneous library used for sample programs of the QUBO++ library." alt="" coords="1804,87,1958,114"/>
<area shape="rect" href="qbpp__easy__solver_8hpp.html" title="Easy QUBO Solver for solving QUBO problems." alt="" coords="2236,177,2432,203"/>
<area shape="rect" href="nqueen__easy_8cpp.html" title="Solves the N&#45;Queens problem using ABS2 QUBO solver from QUBO++ library." alt="" coords="3216,266,3384,293"/>
<area shape="rect" href="partition__easy_8cpp.html" title="Solves the Partitioning problem using the QUBO++ Easy solver." alt="" coords="2143,266,2315,293"/>
<area shape="rect" href="graph__color__easy_8cpp.html" title="Solves randomly generated Graph Coloring Problem using QUBO++ Easy Solver." alt="" coords="1815,259,1950,300"/>
<area shape="rect" href="ilp__easy_8cpp.html" title="Solves an Integer Linear Programming (ILP) problem using EasySolver through QUBO++ library." alt="" coords="2340,266,2478,293"/>
<area shape="rect" href="tsp__easy_8cpp.html" title="Solves randomly generated Traveling Salesman Problem (TSP) using the QUBO++ Easy solver." alt="" coords="1974,266,2118,293"/>
<area shape="rect" href="simple__factorization__easy_8cpp.html" title="Simple factorization example using EasySolver." alt="" coords="2502,259,2686,300"/>
<area shape="rect" href="bin__packing__easy_8cpp.html" title="This file contains an example of solving the bin packing problem using the EasySolver." alt="" coords="2724,259,2862,300"/>
<area shape="rect" href="partition__exhaustive_8cpp.html" title="Solves the Partitioning problem using the QUBO++ Exhaustive solver." alt="" coords="2559,177,2768,203"/>
<area shape="rect" href="qbpp__graph__color_8hpp.html" title=" " alt="" coords="1390,177,1585,203"/>
<area shape="rect" href="qbpp__tsp_8hpp.html" title="Generates a QUBO Expression for the Traveling Salesman Problem (TSP) using QUBO++ library." alt="" coords="1710,177,1857,203"/>
<area shape="rect" href="qbpp__exhaustive__solver_8hpp.html" title="Exhaustive QUBO Solver for solving QUBO problems." alt="" coords="2894,80,3060,121"/>
<area shape="rect" href="ilp__exhaustive_8cpp.html" title="Solves an Integer Linear Programming (ILP) problem using ExhaustiveSolver through QUBO++ library." alt="" coords="2844,177,3020,203"/>
<area shape="rect" href="knapsack__exhaustive_8cpp.html" title="This file contains an example of solving the knapsack problem using the exhaustive solver." alt="" coords="3044,177,3262,203"/>
<area shape="rect" href="qbpp__nqueen_8hpp.html" title="Generates QUBO expression for the N&#45;Queens problem using the QUBO++ library." alt="" coords="3337,87,3508,114"/>
<area shape="rect" href="qbpp__multiplier_8hpp.html" title="Generates QUBO expression for binary multipliers using QUBO++ library." alt="" coords="493,87,672,114"/>
</map>
</div>
</div>
<p><a href="qbpp_8hpp_source.html">Go to the source code of this file.</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="nested-classes"></a>
Classes</h2></td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">class &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classqbpp_1_1Vector.html">qbpp::Vector&lt; T &gt;</a></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">class &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classqbpp_1_1SolHolderTemplate.html">qbpp::SolHolderTemplate&lt; T, U &gt;</a></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">struct &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structqbpp_1_1impl_1_1var__hash.html">qbpp::impl::var_hash</a></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">struct &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structqbpp_1_1impl_1_1vars__hash.html">qbpp::impl::vars_hash</a></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">class &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classqbpp_1_1Inf.html">qbpp::Inf</a></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">class &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classqbpp_1_1Vector.html">qbpp::Vector&lt; T &gt;</a></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">class &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classqbpp_1_1Var.html">qbpp::Var</a></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">class &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classqbpp_1_1impl_1_1VarSet.html">qbpp::impl::VarSet</a></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">class &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classqbpp_1_1impl_1_1VarArray2.html">qbpp::impl::VarArray2</a></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">struct &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structqbpp_1_1impl_1_1VarArray2_1_1EMPTY.html">qbpp::impl::VarArray2::EMPTY</a></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">class &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classqbpp_1_1impl_1_1IndexVarMapper.html">qbpp::impl::IndexVarMapper</a></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">class &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classqbpp_1_1Term.html">qbpp::Term</a></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">class &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classqbpp_1_1Terms.html">qbpp::Terms</a></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">class &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classqbpp_1_1Expr.html">qbpp::Expr</a></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">class &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classqbpp_1_1ExprExpr.html">qbpp::ExprExpr</a></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">class &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classqbpp_1_1VarIntCore.html">qbpp::VarIntCore</a></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">class &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classqbpp_1_1VarInt.html">qbpp::VarInt</a></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">class &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classqbpp_1_1VarOnehotCore.html">qbpp::VarOnehotCore</a></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">class &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classqbpp_1_1VarOnehot.html">qbpp::VarOnehot</a></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">class &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classqbpp_1_1Model.html">qbpp::Model</a></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">class &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classqbpp_1_1QuadModel.html">qbpp::QuadModel</a></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">struct &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structqbpp_1_1QuadModel_1_1Impl.html">qbpp::QuadModel::Impl</a></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">class &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classqbpp_1_1impl_1_1BitVector.html">qbpp::impl::BitVector</a></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">class &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classqbpp_1_1Sol.html">qbpp::Sol</a></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">class &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classqbpp_1_1SolHolderTemplate.html">qbpp::SolHolderTemplate&lt; T, U &gt;</a></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="namespaces"></a>
Namespaces</h2></td></tr>
<tr class="memitem:namespaceqbpp"><td class="memItemLeft" align="right" valign="top"> &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html">qbpp</a></td></tr>
<tr class="memdesc:namespaceqbpp"><td class="mdescLeft">&#160;</td><td class="mdescRight">Generates a QUBO Expression for the Graph Coloring Problem using QUBO++ library. <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:namespaceqbpp_1_1impl"><td class="memItemLeft" align="right" valign="top"> &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp_1_1impl.html">qbpp::impl</a></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="define-members"></a>
Macros</h2></td></tr>
<tr class="memitem:a1c6d5de492ac61ad29aec7aa9a436bbf"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="qbpp_8hpp.html#a1c6d5de492ac61ad29aec7aa9a436bbf">VERSION</a>&#160;&#160;&#160;&quot;2025.05.12&quot;</td></tr>
<tr class="separator:a1c6d5de492ac61ad29aec7aa9a436bbf"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a265e0828fcea8e75fbc9c0be99f7156e"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="qbpp_8hpp.html#a265e0828fcea8e75fbc9c0be99f7156e">THROW_MESSAGE</a>(...)&#160;&#160;&#160;file_line(__FILE__, __LINE__, __VA_ARGS__)</td></tr>
<tr class="separator:a265e0828fcea8e75fbc9c0be99f7156e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8521f0d9a069c55226a07fc055408eed"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="qbpp_8hpp.html#a8521f0d9a069c55226a07fc055408eed">MAXDEG</a>&#160;&#160;&#160;0</td></tr>
<tr class="separator:a8521f0d9a069c55226a07fc055408eed"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3615f8dba24054e0aaa8964d31d2eb24"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="qbpp_8hpp.html#a3615f8dba24054e0aaa8964d31d2eb24">COEFF_TYPE</a>&#160;&#160;&#160;int32_t</td></tr>
<tr class="separator:a3615f8dba24054e0aaa8964d31d2eb24"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a47975cf459e9fa687601c8f30c9848c0"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="qbpp_8hpp.html#a47975cf459e9fa687601c8f30c9848c0">ENERGY_TYPE</a>&#160;&#160;&#160;int64_t</td></tr>
<tr class="separator:a47975cf459e9fa687601c8f30c9848c0"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="typedef-members"></a>
Typedefs</h2></td></tr>
<tr class="memitem:a964b5e616c76cb320d224594d6abe1d6"><td class="memItemLeft" align="right" valign="top">using&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a964b5e616c76cb320d224594d6abe1d6">qbpp::Vars</a> = std::vector&lt; Var &gt;</td></tr>
<tr class="separator:a964b5e616c76cb320d224594d6abe1d6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a344fa91e6aefd8eca3a2f768936d3f62"><td class="memItemLeft" align="right" valign="top">using&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a344fa91e6aefd8eca3a2f768936d3f62">qbpp::int128_t</a> = boost::multiprecision::int128_t</td></tr>
<tr class="separator:a344fa91e6aefd8eca3a2f768936d3f62"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a35435559d3a02f18b80ae5554dc65c75"><td class="memItemLeft" align="right" valign="top">using&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a35435559d3a02f18b80ae5554dc65c75">qbpp::uint128_t</a> = boost::multiprecision::uint128_t</td></tr>
<tr class="separator:a35435559d3a02f18b80ae5554dc65c75"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae3d60bcd1bd798e5159d8e7857b0ebda"><td class="memItemLeft" align="right" valign="top">using&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#ae3d60bcd1bd798e5159d8e7857b0ebda">qbpp::int256_t</a> = boost::multiprecision::int256_t</td></tr>
<tr class="separator:ae3d60bcd1bd798e5159d8e7857b0ebda"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab06d34e70ea33f7c6563df0d8a13c3df"><td class="memItemLeft" align="right" valign="top">using&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#ab06d34e70ea33f7c6563df0d8a13c3df">qbpp::uint256_t</a> = boost::multiprecision::uint256_t</td></tr>
<tr class="separator:ab06d34e70ea33f7c6563df0d8a13c3df"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab79b2bccc18d0d26f1fce40112e2b3bf"><td class="memItemLeft" align="right" valign="top">using&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#ab79b2bccc18d0d26f1fce40112e2b3bf">qbpp::int512_t</a> = boost::multiprecision::int512_t</td></tr>
<tr class="separator:ab79b2bccc18d0d26f1fce40112e2b3bf"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3cd82afb3d5fa229322846a8b5ee43ac"><td class="memItemLeft" align="right" valign="top">using&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a3cd82afb3d5fa229322846a8b5ee43ac">qbpp::uint512_t</a> = boost::multiprecision::uint512_t</td></tr>
<tr class="separator:a3cd82afb3d5fa229322846a8b5ee43ac"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8a8fa0dca27b055f24b1e8cbdd90ab84"><td class="memItemLeft" align="right" valign="top">using&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a8a8fa0dca27b055f24b1e8cbdd90ab84">qbpp::int1024_t</a> = boost::multiprecision::int1024_t</td></tr>
<tr class="separator:a8a8fa0dca27b055f24b1e8cbdd90ab84"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad19ec33b0b44fd05a3c3079fce21a886"><td class="memItemLeft" align="right" valign="top">using&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#ad19ec33b0b44fd05a3c3079fce21a886">qbpp::uint1024_t</a> = boost::multiprecision::uint1024_t</td></tr>
<tr class="separator:ad19ec33b0b44fd05a3c3079fce21a886"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aebbaf7087f1c6180b3b2d9821cddc695"><td class="memItemLeft" align="right" valign="top">using&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#aebbaf7087f1c6180b3b2d9821cddc695">qbpp::cpp_int</a> = boost::multiprecision::cpp_int</td></tr>
<tr class="separator:aebbaf7087f1c6180b3b2d9821cddc695"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5cf436100ede362797d0c0501ea50a5a"><td class="memItemLeft" align="right" valign="top">using&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a5cf436100ede362797d0c0501ea50a5a">qbpp::vindex_t</a> = uint32_t</td></tr>
<tr class="separator:a5cf436100ede362797d0c0501ea50a5a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad2546352247ad384faf13e54991e1cf8"><td class="memItemLeft" align="right" valign="top">using&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#ad2546352247ad384faf13e54991e1cf8">qbpp::var_val_t</a> = int8_t</td></tr>
<tr class="separator:ad2546352247ad384faf13e54991e1cf8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0673c69ae04267b668338d71942fbcf1"><td class="memItemLeft" align="right" valign="top">using&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a0673c69ae04267b668338d71942fbcf1">qbpp::coeff_t</a> = <a class="el" href="qbpp_8hpp.html#a3615f8dba24054e0aaa8964d31d2eb24">COEFF_TYPE</a></td></tr>
<tr class="separator:a0673c69ae04267b668338d71942fbcf1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a9c324f641779d1a7a3c7723f28d8cff4"><td class="memItemLeft" align="right" valign="top">using&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a9c324f641779d1a7a3c7723f28d8cff4">qbpp::energy_t</a> = <a class="el" href="qbpp_8hpp.html#a47975cf459e9fa687601c8f30c9848c0">ENERGY_TYPE</a></td></tr>
<tr class="separator:a9c324f641779d1a7a3c7723f28d8cff4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aeea468454b74e8cf74539f77454bef6b"><td class="memItemLeft" align="right" valign="top">using&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#aeea468454b74e8cf74539f77454bef6b">qbpp::MapList</a> = std::list&lt; std::pair&lt; std::variant&lt; Var, VarInt &gt;, Expr &gt; &gt;</td></tr>
<tr class="separator:aeea468454b74e8cf74539f77454bef6b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac3d8f6928b50df4bbc674b71b58469a3"><td class="memItemLeft" align="right" valign="top">using&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#ac3d8f6928b50df4bbc674b71b58469a3">qbpp::MapDict</a> = std::unordered_map&lt; Var, Expr, impl::var_hash &gt;</td></tr>
<tr class="separator:ac3d8f6928b50df4bbc674b71b58469a3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a26b8edf12e135d3694097ad57c770c3f"><td class="memItemLeft" align="right" valign="top">using&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a26b8edf12e135d3694097ad57c770c3f">qbpp::VarValMap</a> = std::unordered_map&lt; Var, var_val_t, impl::var_hash &gt;</td></tr>
<tr class="separator:a26b8edf12e135d3694097ad57c770c3f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad44c6bf8a85ca345d14600e47d0e8e2a"><td class="memItemLeft" align="right" valign="top">using&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#ad44c6bf8a85ca345d14600e47d0e8e2a">qbpp::VarExprMap</a> = std::unordered_map&lt; Var, Expr, impl::var_hash &gt;</td></tr>
<tr class="separator:ad44c6bf8a85ca345d14600e47d0e8e2a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae3ff8101593902eebda972f955b19201"><td class="memItemLeft" align="right" valign="top">using&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#ae3ff8101593902eebda972f955b19201">qbpp::VarsCoeffMap</a> = std::unordered_map&lt; Vars, coeff_t, impl::vars_hash &gt;</td></tr>
<tr class="separator:ae3ff8101593902eebda972f955b19201"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aadb70b10d2f569db737618d6dcb70bf8"><td class="memItemLeft" align="right" valign="top">using&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#aadb70b10d2f569db737618d6dcb70bf8">qbpp::SolHolder</a> = SolHolderTemplate&lt; Sol, energy_t &gt;</td></tr>
<tr class="separator:aadb70b10d2f569db737618d6dcb70bf8"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:a27a0a96e78ca65e09c12ebdf368cd3fe"><td class="memTemplParams" colspan="2">template&lt;typename... Args&gt; </td></tr>
<tr class="memitem:a27a0a96e78ca65e09c12ebdf368cd3fe"><td class="memTemplItemLeft" align="right" valign="top">std::string&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a27a0a96e78ca65e09c12ebdf368cd3fe">qbpp::file_line</a> (const char *file, int line, Args... args)</td></tr>
<tr class="separator:a27a0a96e78ca65e09c12ebdf368cd3fe"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1cf3f678bcc7eee089c75d4bc5a0b959"><td class="memItemLeft" align="right" valign="top">Var&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a1cf3f678bcc7eee089c75d4bc5a0b959">qbpp::var</a> (const std::string &amp;var_str)</td></tr>
<tr class="separator:a1cf3f678bcc7eee089c75d4bc5a0b959"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2330be79ad2128b25c28e43f7af643ff"><td class="memItemLeft" align="right" valign="top">Var&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a2330be79ad2128b25c28e43f7af643ff">qbpp::var</a> ()</td></tr>
<tr class="separator:a2330be79ad2128b25c28e43f7af643ff"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a41f8243940eb837c282aed85287bd3af"><td class="memItemLeft" align="right" valign="top">std::string&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a41f8243940eb837c282aed85287bd3af">qbpp::str</a> (Var var)</td></tr>
<tr class="separator:a41f8243940eb837c282aed85287bd3af"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a56a890256565a72b2af729722223b083"><td class="memItemLeft" align="right" valign="top">std::string&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a56a890256565a72b2af729722223b083">qbpp::str</a> (const char *param)</td></tr>
<tr class="separator:a56a890256565a72b2af729722223b083"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aba7ff8d4f4db81f830505e827bd954ef"><td class="memItemLeft" align="right" valign="top">std::string&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#aba7ff8d4f4db81f830505e827bd954ef">qbpp::str</a> (const Vars &amp;vars)</td></tr>
<tr class="separator:aba7ff8d4f4db81f830505e827bd954ef"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:adffe2965c67b8069b628aa9801412972"><td class="memItemLeft" align="right" valign="top">std::string&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#adffe2965c67b8069b628aa9801412972">qbpp::str</a> (const Term &amp;term)</td></tr>
<tr class="separator:adffe2965c67b8069b628aa9801412972"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a22ee18cfbe43228cdb8deb8eadadeae2"><td class="memItemLeft" align="right" valign="top">std::string&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a22ee18cfbe43228cdb8deb8eadadeae2">qbpp::str</a> (const Expr &amp;expr, const std::string &amp;prefix)</td></tr>
<tr class="separator:a22ee18cfbe43228cdb8deb8eadadeae2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6ecdcc61418dea3e7792fc9209924b73"><td class="memItemLeft" align="right" valign="top">std::string&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a6ecdcc61418dea3e7792fc9209924b73">qbpp::str</a> (const Model &amp;model)</td></tr>
<tr class="separator:a6ecdcc61418dea3e7792fc9209924b73"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8fa3af2cffc1996530310aac6e4b0576"><td class="memItemLeft" align="right" valign="top">std::string&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a8fa3af2cffc1996530310aac6e4b0576">qbpp::str</a> (const QuadModel &amp;quad_model)</td></tr>
<tr class="separator:a8fa3af2cffc1996530310aac6e4b0576"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0a822c8155843fe78c8ac4ddd95f1681"><td class="memItemLeft" align="right" valign="top">std::string&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a0a822c8155843fe78c8ac4ddd95f1681">qbpp::str</a> (const Sol &amp;sol)</td></tr>
<tr class="separator:a0a822c8155843fe78c8ac4ddd95f1681"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a60ad3ecc8ad6743503ca04e7da2166d1"><td class="memItemLeft" align="right" valign="top">std::string&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a60ad3ecc8ad6743503ca04e7da2166d1">qbpp::str</a> (const MapList &amp;map_list)</td></tr>
<tr class="separator:a60ad3ecc8ad6743503ca04e7da2166d1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a75ec4f70aaf822424842c81e415834c6"><td class="memItemLeft" align="right" valign="top">std::string&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a75ec4f70aaf822424842c81e415834c6">qbpp::str_short</a> (const Expr &amp;expr)</td></tr>
<tr class="separator:a75ec4f70aaf822424842c81e415834c6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:abedbff8292e35b0d7d3ce09a8e724ad1"><td class="memItemLeft" align="right" valign="top">std::ostream &amp;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#abedbff8292e35b0d7d3ce09a8e724ad1">qbpp::operator&lt;&lt;</a> (std::ostream &amp;os, Var var)</td></tr>
<tr class="separator:abedbff8292e35b0d7d3ce09a8e724ad1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac05958bb368dc77973808f27a6891020"><td class="memItemLeft" align="right" valign="top">std::ostream &amp;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#ac05958bb368dc77973808f27a6891020">qbpp::operator&lt;&lt;</a> (std::ostream &amp;os, const Term &amp;term)</td></tr>
<tr class="separator:ac05958bb368dc77973808f27a6891020"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1bc0a4d7293daf6a960d62cfa8e226d4"><td class="memItemLeft" align="right" valign="top">std::ostream &amp;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a1bc0a4d7293daf6a960d62cfa8e226d4">qbpp::operator&lt;&lt;</a> (std::ostream &amp;os, const Terms &amp;terms)</td></tr>
<tr class="separator:a1bc0a4d7293daf6a960d62cfa8e226d4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2676c85b2e5eef57153cd7ea84f5391f"><td class="memItemLeft" align="right" valign="top">std::ostream &amp;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a2676c85b2e5eef57153cd7ea84f5391f">qbpp::operator&lt;&lt;</a> (std::ostream &amp;os, const Expr &amp;expr)</td></tr>
<tr class="separator:a2676c85b2e5eef57153cd7ea84f5391f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7c04b5e1e4f099ad97f8a0c1664657ed"><td class="memItemLeft" align="right" valign="top">std::ostream &amp;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a7c04b5e1e4f099ad97f8a0c1664657ed">qbpp::operator&lt;&lt;</a> (std::ostream &amp;os, const Model &amp;model)</td></tr>
<tr class="separator:a7c04b5e1e4f099ad97f8a0c1664657ed"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a85445a5cdad0d09a736f2c8fdfee9ecd"><td class="memItemLeft" align="right" valign="top">std::ostream &amp;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a85445a5cdad0d09a736f2c8fdfee9ecd">qbpp::operator&lt;&lt;</a> (std::ostream &amp;os, const QuadModel &amp;quad_model)</td></tr>
<tr class="separator:a85445a5cdad0d09a736f2c8fdfee9ecd"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a50b056ed2fa4c1f6e5eb7d577e06a818"><td class="memItemLeft" align="right" valign="top">std::ostream &amp;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a50b056ed2fa4c1f6e5eb7d577e06a818">qbpp::operator&lt;&lt;</a> (std::ostream &amp;os, const Sol &amp;sol)</td></tr>
<tr class="separator:a50b056ed2fa4c1f6e5eb7d577e06a818"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5d56d3d39b9f223f9dd2eb85fe3d2256"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a5d56d3d39b9f223f9dd2eb85fe3d2256">qbpp::sort_vars_in_place</a> (Vars &amp;vars)</td></tr>
<tr class="separator:a5d56d3d39b9f223f9dd2eb85fe3d2256"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae29f30ff45dbdf092fde15f9dd895322"><td class="memItemLeft" align="right" valign="top">Vars&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#ae29f30ff45dbdf092fde15f9dd895322">qbpp::sort_vars</a> (const Vars &amp;vars)</td></tr>
<tr class="separator:ae29f30ff45dbdf092fde15f9dd895322"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a65a9c464505d996431ed4fa7b3556f2b"><td class="memItemLeft" align="right" valign="top">Vars&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a65a9c464505d996431ed4fa7b3556f2b">qbpp::sort_vars_as_binary</a> (const Vars &amp;vars)</td></tr>
<tr class="separator:a65a9c464505d996431ed4fa7b3556f2b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a698b1ce8e67635b6563cf589d9689667"><td class="memItemLeft" align="right" valign="top">Vars&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a698b1ce8e67635b6563cf589d9689667">qbpp::sort_vars_as_spin</a> (const Vars &amp;vars)</td></tr>
<tr class="separator:a698b1ce8e67635b6563cf589d9689667"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a9456e7fa594d0169fcc26f37fdda29ad"><td class="memItemLeft" align="right" valign="top">Expr&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a9456e7fa594d0169fcc26f37fdda29ad">qbpp::simplify</a> (const Expr &amp;expr, Vars(*sort_vars_func)(const Vars &amp;))</td></tr>
<tr class="separator:a9456e7fa594d0169fcc26f37fdda29ad"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa3ce65464a38ba36e0b6e99b97234245"><td class="memItemLeft" align="right" valign="top">Expr&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#aa3ce65464a38ba36e0b6e99b97234245">qbpp::simplify_as_binary</a> (const Expr &amp;expr)</td></tr>
<tr class="separator:aa3ce65464a38ba36e0b6e99b97234245"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a13c9a177636866d79431415283122768"><td class="memItemLeft" align="right" valign="top">Expr&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a13c9a177636866d79431415283122768">qbpp::simplify_as_spin</a> (const Expr &amp;expr)</td></tr>
<tr class="separator:a13c9a177636866d79431415283122768"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7bd562623faf590810ac1950dd7ea23b"><td class="memItemLeft" align="right" valign="top">bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a7bd562623faf590810ac1950dd7ea23b">qbpp::is_simplified</a> (const Expr &amp;expr)</td></tr>
<tr class="separator:a7bd562623faf590810ac1950dd7ea23b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a991b983896321dfc672af18786a3208b"><td class="memItemLeft" align="right" valign="top">bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a991b983896321dfc672af18786a3208b">qbpp::is_binary</a> (const Expr &amp;expr)</td></tr>
<tr class="separator:a991b983896321dfc672af18786a3208b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:abd03cc17ab61f6ab2be95751c7b92626"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:abd03cc17ab61f6ab2be95751c7b92626"><td class="memTemplItemLeft" align="right" valign="top">Vector&lt; Expr &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#abd03cc17ab61f6ab2be95751c7b92626">qbpp::sqr</a> (const Vector&lt; T &gt; &amp;arg)</td></tr>
<tr class="separator:abd03cc17ab61f6ab2be95751c7b92626"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5d4f11504851d06a715bc3f3e194b1eb"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:a5d4f11504851d06a715bc3f3e194b1eb"><td class="memTemplItemLeft" align="right" valign="top">auto&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a5d4f11504851d06a715bc3f3e194b1eb">qbpp::sqr</a> (const Vector&lt; Vector&lt; T &gt;&gt; &amp;arg) -&gt; Vector&lt; decltype(sqr(arg[0]))&gt;</td></tr>
<tr class="separator:a5d4f11504851d06a715bc3f3e194b1eb"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab5da6d0960920098998bb7347082ee6b"><td class="memItemLeft" align="right" valign="top">energy_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#ab5da6d0960920098998bb7347082ee6b">qbpp::eval</a> (const Expr &amp;expr, const Sol &amp;sol)</td></tr>
<tr class="separator:ab5da6d0960920098998bb7347082ee6b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af3762a9acba3cac9b7f8d6d8549cf012"><td class="memItemLeft" align="right" valign="top">energy_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#af3762a9acba3cac9b7f8d6d8549cf012">qbpp::eval</a> (const Expr &amp;expr, const MapList &amp;map_list)</td></tr>
<tr class="separator:af3762a9acba3cac9b7f8d6d8549cf012"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac05f27a511fa1e2870091ea5d696a3c3"><td class="memItemLeft" align="right" valign="top">Expr&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#ac05f27a511fa1e2870091ea5d696a3c3">qbpp::reduce</a> (const Expr &amp;expr)</td></tr>
<tr class="separator:ac05f27a511fa1e2870091ea5d696a3c3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0fdba59cf44ada4b55c1d8ea6d3f3007"><td class="memItemLeft" align="right" valign="top">Expr&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a0fdba59cf44ada4b55c1d8ea6d3f3007">qbpp::binary_to_spin</a> (const Expr &amp;expr)</td></tr>
<tr class="separator:a0fdba59cf44ada4b55c1d8ea6d3f3007"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a17ebda985a4b0bb22a6b4485b59c3774"><td class="memItemLeft" align="right" valign="top">Expr&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a17ebda985a4b0bb22a6b4485b59c3774">qbpp::spin_to_binary</a> (const Expr &amp;expr)</td></tr>
<tr class="separator:a17ebda985a4b0bb22a6b4485b59c3774"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a09ee3c8c0fa775643671888d2f8fba9b"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:a09ee3c8c0fa775643671888d2f8fba9b"><td class="memTemplItemLeft" align="right" valign="top">Vector&lt; Expr &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a09ee3c8c0fa775643671888d2f8fba9b">qbpp::binary_to_spin</a> (const Vector&lt; T &gt; &amp;arg)</td></tr>
<tr class="separator:a09ee3c8c0fa775643671888d2f8fba9b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a4c0812e36817635943bed52f58bb93eb"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:a4c0812e36817635943bed52f58bb93eb"><td class="memTemplItemLeft" align="right" valign="top">auto&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a4c0812e36817635943bed52f58bb93eb">qbpp::binary_to_spin</a> (const Vector&lt; Vector&lt; T &gt;&gt; &amp;arg) -&gt; Vector&lt; decltype(binary_to_spin(arg[0]))&gt;</td></tr>
<tr class="separator:a4c0812e36817635943bed52f58bb93eb"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3bb73161c6a14db48c8c953f32824991"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:a3bb73161c6a14db48c8c953f32824991"><td class="memTemplItemLeft" align="right" valign="top">Vector&lt; Expr &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a3bb73161c6a14db48c8c953f32824991">qbpp::spin_to_binary</a> (const Vector&lt; T &gt; &amp;arg)</td></tr>
<tr class="separator:a3bb73161c6a14db48c8c953f32824991"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:abdcfd6050efc38e03291631576bf83ca"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:abdcfd6050efc38e03291631576bf83ca"><td class="memTemplItemLeft" align="right" valign="top">auto&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#abdcfd6050efc38e03291631576bf83ca">qbpp::spin_to_binary</a> (const Vector&lt; Vector&lt; T &gt;&gt; &amp;arg) -&gt; Vector&lt; decltype(spin_to_binary(arg[0]))&gt;</td></tr>
<tr class="separator:abdcfd6050efc38e03291631576bf83ca"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:abcccf2f1b42fb63cbcce2e8c1aaf6e09"><td class="memItemLeft" align="right" valign="top">Vector&lt; Expr &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#abcccf2f1b42fb63cbcce2e8c1aaf6e09">qbpp::get_row</a> (const Vector&lt; Vector&lt; Expr &gt;&gt; &amp;vec, vindex_t index)</td></tr>
<tr class="separator:abcccf2f1b42fb63cbcce2e8c1aaf6e09"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a489a4f4bbdd95a8a95e9b2952fd38980"><td class="memItemLeft" align="right" valign="top">Vector&lt; Expr &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a489a4f4bbdd95a8a95e9b2952fd38980">qbpp::get_col</a> (const Vector&lt; Vector&lt; Expr &gt;&gt; &amp;vec, size_t index)</td></tr>
<tr class="separator:a489a4f4bbdd95a8a95e9b2952fd38980"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a4184afa358539dbcf5f35e63b49ce05b"><td class="memItemLeft" align="right" valign="top">double&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a4184afa358539dbcf5f35e63b49ce05b">qbpp::get_time</a> ()</td></tr>
<tr class="separator:a4184afa358539dbcf5f35e63b49ce05b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6fb0ded3a21621edea15f503c0b28752"><td class="memItemLeft" align="right" valign="top">vindex_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a6fb0ded3a21621edea15f503c0b28752">qbpp::all_var_count</a> ()</td></tr>
<tr class="separator:a6fb0ded3a21621edea15f503c0b28752"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a9041f8ee7d3f4a7c04a0d385babae285"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:a9041f8ee7d3f4a7c04a0d385babae285"><td class="memTemplItemLeft" align="right" valign="top">Expr&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a9041f8ee7d3f4a7c04a0d385babae285">qbpp::sum</a> (const Vector&lt; T &gt; &amp;items)</td></tr>
<tr class="separator:a9041f8ee7d3f4a7c04a0d385babae285"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a47715eec8c4942d0179134ce224be6c5"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:a47715eec8c4942d0179134ce224be6c5"><td class="memTemplItemLeft" align="right" valign="top">Vector&lt; Vector&lt; Expr &gt; &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a47715eec8c4942d0179134ce224be6c5">qbpp::transpose</a> (const Vector&lt; Vector&lt; T &gt;&gt; &amp;vec)</td></tr>
<tr class="separator:a47715eec8c4942d0179134ce224be6c5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a40db3a2d25bd983bcc745a691acba5c8"><td class="memItemLeft" align="right" valign="top">Expr&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a40db3a2d25bd983bcc745a691acba5c8">qbpp::operator-</a> (const Expr &amp;expr)</td></tr>
<tr class="separator:a40db3a2d25bd983bcc745a691acba5c8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad50977512d7663999ba81fc1e91f3406"><td class="memItemLeft" align="right" valign="top">Expr&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#ad50977512d7663999ba81fc1e91f3406">qbpp::operator-</a> (Expr &amp;&amp;expr)</td></tr>
<tr class="separator:ad50977512d7663999ba81fc1e91f3406"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aae3efda322f3bf5bdb5de8863c26f0c3"><td class="memItemLeft" align="right" valign="top">Expr &amp;&amp;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#aae3efda322f3bf5bdb5de8863c26f0c3">qbpp::operator-</a> (Expr &amp;&amp;lhs, Expr &amp;&amp;rhs)</td></tr>
<tr class="separator:aae3efda322f3bf5bdb5de8863c26f0c3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a97185e5bf8d17779523cb3f13b1d54d7"><td class="memItemLeft" align="right" valign="top">Expr&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a97185e5bf8d17779523cb3f13b1d54d7">qbpp::sqr</a> (const Expr &amp;expr)</td></tr>
<tr class="separator:a97185e5bf8d17779523cb3f13b1d54d7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad69bc1ad0ee4d44292f527727cecea19"><td class="memItemLeft" align="right" valign="top">energy_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#ad69bc1ad0ee4d44292f527727cecea19">qbpp::toInt</a> (const Expr &amp;expr)</td></tr>
<tr class="separator:ad69bc1ad0ee4d44292f527727cecea19"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0b67fbbfcd3ee36f433d4a82616ddde4"><td class="memItemLeft" align="right" valign="top">VarValMap&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a0b67fbbfcd3ee36f433d4a82616ddde4">qbpp::list_to_var_val</a> (const MapList &amp;map_list)</td></tr>
<tr class="separator:a0b67fbbfcd3ee36f433d4a82616ddde4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:adc1b9fda596759e101fa8980cfa9049e"><td class="memTemplParams" colspan="2">template&lt;typename T , typename std::enable_if&lt; std::is_convertible&lt; T, coeff_t &gt;::value, int &gt;::type  = 0&gt; </td></tr>
<tr class="memitem:adc1b9fda596759e101fa8980cfa9049e"><td class="memTemplItemLeft" align="right" valign="top">Term&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#adc1b9fda596759e101fa8980cfa9049e">qbpp::operator*</a> (Var var, T val)</td></tr>
<tr class="separator:adc1b9fda596759e101fa8980cfa9049e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad22b6f1c4af62ed4e001c2f9d0d72e98"><td class="memTemplParams" colspan="2">template&lt;typename T , typename std::enable_if&lt; std::is_convertible&lt; T, coeff_t &gt;::value, int &gt;::type  = 0&gt; </td></tr>
<tr class="memitem:ad22b6f1c4af62ed4e001c2f9d0d72e98"><td class="memTemplItemLeft" align="right" valign="top">Term&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#ad22b6f1c4af62ed4e001c2f9d0d72e98">qbpp::operator*</a> (T val, Var var)</td></tr>
<tr class="separator:ad22b6f1c4af62ed4e001c2f9d0d72e98"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad3ee49d86ed5e11c915e51d1d32c5ff7"><td class="memItemLeft" align="right" valign="top">Term&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#ad3ee49d86ed5e11c915e51d1d32c5ff7">qbpp::operator*</a> (Var var1, Var var2)</td></tr>
<tr class="separator:ad3ee49d86ed5e11c915e51d1d32c5ff7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aaab4ee2a6f00e742121fa7867609a7a0"><td class="memTemplParams" colspan="2">template&lt;typename T , typename std::enable_if&lt; std::is_convertible&lt; T, coeff_t &gt;::value, int &gt;::type  = 0&gt; </td></tr>
<tr class="memitem:aaab4ee2a6f00e742121fa7867609a7a0"><td class="memTemplItemLeft" align="right" valign="top">Term&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#aaab4ee2a6f00e742121fa7867609a7a0">qbpp::operator*</a> (const Term &amp;term, T val)</td></tr>
<tr class="separator:aaab4ee2a6f00e742121fa7867609a7a0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6ce95332ce5d903811127d5aefb584ad"><td class="memTemplParams" colspan="2">template&lt;typename T , typename std::enable_if&lt; std::is_convertible&lt; T, coeff_t &gt;::value, int &gt;::type  = 0&gt; </td></tr>
<tr class="memitem:a6ce95332ce5d903811127d5aefb584ad"><td class="memTemplItemLeft" align="right" valign="top">Term&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a6ce95332ce5d903811127d5aefb584ad">qbpp::operator*</a> (T val, const Term &amp;term)</td></tr>
<tr class="separator:a6ce95332ce5d903811127d5aefb584ad"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:afa3a601f1743887c99c565bc3de31b97"><td class="memTemplParams" colspan="2">template&lt;typename T , typename std::enable_if&lt; std::is_convertible&lt; T, coeff_t &gt;::value, int &gt;::type  = 0&gt; </td></tr>
<tr class="memitem:afa3a601f1743887c99c565bc3de31b97"><td class="memTemplItemLeft" align="right" valign="top">Term&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#afa3a601f1743887c99c565bc3de31b97">qbpp::operator*</a> (Term &amp;&amp;term, T val)</td></tr>
<tr class="separator:afa3a601f1743887c99c565bc3de31b97"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:adee2cdf1b581e2995417b125828bd89a"><td class="memTemplParams" colspan="2">template&lt;typename T , typename std::enable_if&lt; std::is_convertible&lt; T, coeff_t &gt;::value, int &gt;::type  = 0&gt; </td></tr>
<tr class="memitem:adee2cdf1b581e2995417b125828bd89a"><td class="memTemplItemLeft" align="right" valign="top">Term&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#adee2cdf1b581e2995417b125828bd89a">qbpp::operator*</a> (T val, Term &amp;&amp;term)</td></tr>
<tr class="separator:adee2cdf1b581e2995417b125828bd89a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a898daf737e7bd6bf40d3de2c635df244"><td class="memItemLeft" align="right" valign="top">Term&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a898daf737e7bd6bf40d3de2c635df244">qbpp::operator*</a> (const Term &amp;term, Var var)</td></tr>
<tr class="separator:a898daf737e7bd6bf40d3de2c635df244"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:adcc26bc7a82a9e1a0cf172a891ce56b5"><td class="memItemLeft" align="right" valign="top">Term&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#adcc26bc7a82a9e1a0cf172a891ce56b5">qbpp::operator*</a> (Var var, const Term &amp;term)</td></tr>
<tr class="separator:adcc26bc7a82a9e1a0cf172a891ce56b5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa03fdb1f1a379036932507f97bde02ee"><td class="memItemLeft" align="right" valign="top">Term&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#aa03fdb1f1a379036932507f97bde02ee">qbpp::operator*</a> (Term &amp;&amp;term, Var var)</td></tr>
<tr class="separator:aa03fdb1f1a379036932507f97bde02ee"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a423468e910147b613228272973feae2f"><td class="memItemLeft" align="right" valign="top">Term&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a423468e910147b613228272973feae2f">qbpp::operator*</a> (Var var, Term &amp;&amp;term)</td></tr>
<tr class="separator:a423468e910147b613228272973feae2f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7d8fdbb9321686d9738721603b60a1b0"><td class="memItemLeft" align="right" valign="top">Term&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a7d8fdbb9321686d9738721603b60a1b0">qbpp::operator*</a> (const Term &amp;term1, const Term &amp;term2)</td></tr>
<tr class="separator:a7d8fdbb9321686d9738721603b60a1b0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae86b346b13e95536400466d0c1a8a7e7"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:ae86b346b13e95536400466d0c1a8a7e7"><td class="memTemplItemLeft" align="right" valign="top">Terms&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#ae86b346b13e95536400466d0c1a8a7e7">qbpp::operator+</a> (const Terms &amp;lhs, T &amp;&amp;rhs)</td></tr>
<tr class="separator:ae86b346b13e95536400466d0c1a8a7e7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa3a07f15a9dbedd3df1ef2afd14fa0b3"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:aa3a07f15a9dbedd3df1ef2afd14fa0b3"><td class="memTemplItemLeft" align="right" valign="top">Terms&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#aa3a07f15a9dbedd3df1ef2afd14fa0b3">qbpp::operator+</a> (Terms &amp;&amp;lhs, T &amp;&amp;rhs)</td></tr>
<tr class="separator:aa3a07f15a9dbedd3df1ef2afd14fa0b3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab77d778432a12924466197d80ba7aea7"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:ab77d778432a12924466197d80ba7aea7"><td class="memTemplItemLeft" align="right" valign="top">Terms&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#ab77d778432a12924466197d80ba7aea7">qbpp::operator-</a> (const Terms &amp;lhs, T &amp;&amp;rhs)</td></tr>
<tr class="separator:ab77d778432a12924466197d80ba7aea7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a4161e5b2b716cf5bd4fbd478e4d3a3e3"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:a4161e5b2b716cf5bd4fbd478e4d3a3e3"><td class="memTemplItemLeft" align="right" valign="top">Terms&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a4161e5b2b716cf5bd4fbd478e4d3a3e3">qbpp::operator-</a> (Terms &amp;&amp;lhs, T &amp;&amp;rhs)</td></tr>
<tr class="separator:a4161e5b2b716cf5bd4fbd478e4d3a3e3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0175f34e085bee1698b7912d8b93635e"><td class="memTemplParams" colspan="2">template&lt;typename T , typename std::enable_if&lt; std::is_convertible&lt; T, coeff_t &gt;::value, int &gt;::type  = 0&gt; </td></tr>
<tr class="memitem:a0175f34e085bee1698b7912d8b93635e"><td class="memTemplItemLeft" align="right" valign="top">Terms&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a0175f34e085bee1698b7912d8b93635e">qbpp::operator*</a> (const Terms &amp;lhs, T rhs)</td></tr>
<tr class="separator:a0175f34e085bee1698b7912d8b93635e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa75db900b757b417db92c376cd818881"><td class="memTemplParams" colspan="2">template&lt;typename T , typename std::enable_if&lt; std::is_convertible&lt; T, coeff_t &gt;::value, int &gt;::type  = 0&gt; </td></tr>
<tr class="memitem:aa75db900b757b417db92c376cd818881"><td class="memTemplItemLeft" align="right" valign="top">Terms&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#aa75db900b757b417db92c376cd818881">qbpp::operator*</a> (Terms &amp;&amp;lhs, T rhs)</td></tr>
<tr class="separator:aa75db900b757b417db92c376cd818881"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a84aad0c0af7309b5e8cd7b682730541c"><td class="memTemplParams" colspan="2">template&lt;typename T , typename std::enable_if&lt; std::is_convertible&lt; T, coeff_t &gt;::value, int &gt;::type  = 0&gt; </td></tr>
<tr class="memitem:a84aad0c0af7309b5e8cd7b682730541c"><td class="memTemplItemLeft" align="right" valign="top">Terms&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a84aad0c0af7309b5e8cd7b682730541c">qbpp::operator*</a> (T rhs, const Terms &amp;lhs)</td></tr>
<tr class="separator:a84aad0c0af7309b5e8cd7b682730541c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a4bceb9344b7082519e82882b129557d1"><td class="memTemplParams" colspan="2">template&lt;typename T , typename std::enable_if&lt; std::is_convertible&lt; T, coeff_t &gt;::value, int &gt;::type  = 0&gt; </td></tr>
<tr class="memitem:a4bceb9344b7082519e82882b129557d1"><td class="memTemplItemLeft" align="right" valign="top">Terms&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a4bceb9344b7082519e82882b129557d1">qbpp::operator*</a> (T rhs, Terms &amp;&amp;lhs)</td></tr>
<tr class="separator:a4bceb9344b7082519e82882b129557d1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a182f3427c90e143bd5d89394e5d9a208"><td class="memItemLeft" align="right" valign="top">Terms&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a182f3427c90e143bd5d89394e5d9a208">qbpp::operator*</a> (const Terms &amp;lhs, const Term &amp;rhs)</td></tr>
<tr class="separator:a182f3427c90e143bd5d89394e5d9a208"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aaef8f8c2b14dc7df24c4f1b2f77a0102"><td class="memItemLeft" align="right" valign="top">Terms&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#aaef8f8c2b14dc7df24c4f1b2f77a0102">qbpp::operator*</a> (const Term &amp;rhs, const Terms &amp;lhs)</td></tr>
<tr class="separator:aaef8f8c2b14dc7df24c4f1b2f77a0102"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0d0a80b398619f86c2bb3d492d238995"><td class="memItemLeft" align="right" valign="top">Terms&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a0d0a80b398619f86c2bb3d492d238995">qbpp::operator*</a> (Terms &amp;&amp;lhs, const Term &amp;rhs)</td></tr>
<tr class="separator:a0d0a80b398619f86c2bb3d492d238995"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a4a1a7841bb2b7baab6bca7ff3398cabb"><td class="memItemLeft" align="right" valign="top">Terms&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a4a1a7841bb2b7baab6bca7ff3398cabb">qbpp::operator*</a> (const Term &amp;lhs, Terms &amp;&amp;rhs)</td></tr>
<tr class="separator:a4a1a7841bb2b7baab6bca7ff3398cabb"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aac55eacb0abdb7bdaaaec0e52ed9a2b2"><td class="memItemLeft" align="right" valign="top">Terms&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#aac55eacb0abdb7bdaaaec0e52ed9a2b2">qbpp::operator*</a> (const Terms &amp;lhs, const Terms &amp;rhs)</td></tr>
<tr class="separator:aac55eacb0abdb7bdaaaec0e52ed9a2b2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a233fc67d9e9c0851cf41f2cf0e8e196f"><td class="memTemplParams" colspan="2">template&lt;typename T , typename std::enable_if&lt; std::is_convertible&lt; T, coeff_t &gt;::value, int &gt;::type  = 0&gt; </td></tr>
<tr class="memitem:a233fc67d9e9c0851cf41f2cf0e8e196f"><td class="memTemplItemLeft" align="right" valign="top">Expr&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a233fc67d9e9c0851cf41f2cf0e8e196f">qbpp::operator*</a> (const Expr &amp;expr, T val)</td></tr>
<tr class="separator:a233fc67d9e9c0851cf41f2cf0e8e196f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a38c8c240932ceed7c29015b3395855c2"><td class="memTemplParams" colspan="2">template&lt;typename T , typename std::enable_if&lt; std::is_convertible&lt; T, coeff_t &gt;::value, int &gt;::type  = 0&gt; </td></tr>
<tr class="memitem:a38c8c240932ceed7c29015b3395855c2"><td class="memTemplItemLeft" align="right" valign="top">Expr&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a38c8c240932ceed7c29015b3395855c2">qbpp::operator*</a> (T al, const Expr &amp;expr)</td></tr>
<tr class="separator:a38c8c240932ceed7c29015b3395855c2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5a4b174c83ad97840836e6e9b0e0c53f"><td class="memTemplParams" colspan="2">template&lt;typename T , typename std::enable_if&lt; std::is_convertible&lt; T, coeff_t &gt;::value, int &gt;::type  = 0&gt; </td></tr>
<tr class="memitem:a5a4b174c83ad97840836e6e9b0e0c53f"><td class="memTemplItemLeft" align="right" valign="top">Expr&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a5a4b174c83ad97840836e6e9b0e0c53f">qbpp::operator*</a> (Expr &amp;&amp;expr, T val)</td></tr>
<tr class="separator:a5a4b174c83ad97840836e6e9b0e0c53f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab75dceb045351ca95606dd51d5751b67"><td class="memTemplParams" colspan="2">template&lt;typename T , typename std::enable_if&lt; std::is_convertible&lt; T, coeff_t &gt;::value, int &gt;::type  = 0&gt; </td></tr>
<tr class="memitem:ab75dceb045351ca95606dd51d5751b67"><td class="memTemplItemLeft" align="right" valign="top">Expr&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#ab75dceb045351ca95606dd51d5751b67">qbpp::operator*</a> (T val, Expr &amp;&amp;expr)</td></tr>
<tr class="separator:ab75dceb045351ca95606dd51d5751b67"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a83c54e9a6e3ec459838cbf27ba9b33e1"><td class="memItemLeft" align="right" valign="top">Expr&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a83c54e9a6e3ec459838cbf27ba9b33e1">qbpp::operator*</a> (const Expr &amp;expr, const Term &amp;term)</td></tr>
<tr class="separator:a83c54e9a6e3ec459838cbf27ba9b33e1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a63dea7f50167bbb932bdd39354544a3a"><td class="memItemLeft" align="right" valign="top">Expr&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a63dea7f50167bbb932bdd39354544a3a">qbpp::operator*</a> (const Term &amp;term, const Expr &amp;expr)</td></tr>
<tr class="separator:a63dea7f50167bbb932bdd39354544a3a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a81b550684c67606459ecc4f9dcfcd74a"><td class="memItemLeft" align="right" valign="top">Expr&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a81b550684c67606459ecc4f9dcfcd74a">qbpp::operator*</a> (const Expr &amp;expr, Term &amp;&amp;term)</td></tr>
<tr class="separator:a81b550684c67606459ecc4f9dcfcd74a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a25c2ff85fab553982151abb87c2b9ce3"><td class="memItemLeft" align="right" valign="top">Expr&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a25c2ff85fab553982151abb87c2b9ce3">qbpp::operator*</a> (Term &amp;&amp;term, const Expr &amp;expr)</td></tr>
<tr class="separator:a25c2ff85fab553982151abb87c2b9ce3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa04f4e307b81ca2a4af5e2621d0298e1"><td class="memItemLeft" align="right" valign="top">Expr &amp;&amp;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#aa04f4e307b81ca2a4af5e2621d0298e1">qbpp::operator*</a> (Expr &amp;&amp;expr, const Term &amp;term)</td></tr>
<tr class="separator:aa04f4e307b81ca2a4af5e2621d0298e1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7ea9d780fbe47ddb287a68142346b4b8"><td class="memItemLeft" align="right" valign="top">Expr &amp;&amp;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a7ea9d780fbe47ddb287a68142346b4b8">qbpp::operator*</a> (const Term &amp;term, Expr &amp;&amp;expr)</td></tr>
<tr class="separator:a7ea9d780fbe47ddb287a68142346b4b8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:abcfa27bfe44a375beb985a9b3a42b646"><td class="memItemLeft" align="right" valign="top">Expr &amp;&amp;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#abcfa27bfe44a375beb985a9b3a42b646">qbpp::operator*</a> (Expr &amp;&amp;expr, Term &amp;&amp;rhs)</td></tr>
<tr class="separator:abcfa27bfe44a375beb985a9b3a42b646"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad7983b7a7e99cb6a37300a369459e844"><td class="memItemLeft" align="right" valign="top">Expr &amp;&amp;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#ad7983b7a7e99cb6a37300a369459e844">qbpp::operator*</a> (Term &amp;&amp;rhs, Expr &amp;&amp;expr)</td></tr>
<tr class="separator:ad7983b7a7e99cb6a37300a369459e844"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a415a7bec3cb602c136dcbf5e5b308a31"><td class="memItemLeft" align="right" valign="top">Expr &amp;&amp;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a415a7bec3cb602c136dcbf5e5b308a31">qbpp::operator*</a> (Expr &amp;&amp;lhs, Expr &amp;&amp;rhs)</td></tr>
<tr class="separator:a415a7bec3cb602c136dcbf5e5b308a31"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a40de14b387586e8f96e89b55c236cdc8"><td class="memItemLeft" align="right" valign="top">Expr &amp;&amp;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a40de14b387586e8f96e89b55c236cdc8">qbpp::operator*</a> (Expr &amp;&amp;lhs, const Expr &amp;rhs)</td></tr>
<tr class="separator:a40de14b387586e8f96e89b55c236cdc8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5c227bce7618b6ec0a118d0b310052f7"><td class="memItemLeft" align="right" valign="top">Expr &amp;&amp;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a5c227bce7618b6ec0a118d0b310052f7">qbpp::operator*</a> (const Expr &amp;lhs, Expr &amp;&amp;rhs)</td></tr>
<tr class="separator:a5c227bce7618b6ec0a118d0b310052f7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad873b68d76b9eb9b1c5465dc364c9bf4"><td class="memItemLeft" align="right" valign="top">Expr&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#ad873b68d76b9eb9b1c5465dc364c9bf4">qbpp::operator*</a> (const Expr &amp;lhs, const Expr &amp;rhs)</td></tr>
<tr class="separator:ad873b68d76b9eb9b1c5465dc364c9bf4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a67a6bafa98304580e3a07545e2072836"><td class="memItemLeft" align="right" valign="top">Expr &amp;&amp;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a67a6bafa98304580e3a07545e2072836">qbpp::operator+</a> (Expr &amp;&amp;lhs, Expr &amp;&amp;rhs)</td></tr>
<tr class="separator:a67a6bafa98304580e3a07545e2072836"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab65597dff37a1f49408f54984acb740a"><td class="memItemLeft" align="right" valign="top">Expr &amp;&amp;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#ab65597dff37a1f49408f54984acb740a">qbpp::operator+</a> (Expr &amp;&amp;lhs, const Expr &amp;rhs)</td></tr>
<tr class="separator:ab65597dff37a1f49408f54984acb740a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a351d09f34de7b6e2f6a9c46411f93899"><td class="memItemLeft" align="right" valign="top">Expr &amp;&amp;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a351d09f34de7b6e2f6a9c46411f93899">qbpp::operator+</a> (const Expr &amp;lhs, Expr &amp;&amp;rhs)</td></tr>
<tr class="separator:a351d09f34de7b6e2f6a9c46411f93899"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:adfcbe6bb658e5b06f31350f2c9fd5a39"><td class="memItemLeft" align="right" valign="top">Expr&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#adfcbe6bb658e5b06f31350f2c9fd5a39">qbpp::operator+</a> (const Expr &amp;lhs, const Expr &amp;rhs)</td></tr>
<tr class="separator:adfcbe6bb658e5b06f31350f2c9fd5a39"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a37732a70b2fe3e12d30230839196282d"><td class="memItemLeft" align="right" valign="top">Expr &amp;&amp;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a37732a70b2fe3e12d30230839196282d">qbpp::operator-</a> (Expr &amp;&amp;lhs, const Expr &amp;rhs)</td></tr>
<tr class="separator:a37732a70b2fe3e12d30230839196282d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a9ab2e97024ebbd25a97c1c872624c2da"><td class="memItemLeft" align="right" valign="top">Expr &amp;&amp;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a9ab2e97024ebbd25a97c1c872624c2da">qbpp::operator-</a> (const Expr &amp;lhs, Expr &amp;&amp;rhs)</td></tr>
<tr class="separator:a9ab2e97024ebbd25a97c1c872624c2da"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a931936508e79a8100f20b0ebb2f40931"><td class="memItemLeft" align="right" valign="top">Expr&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a931936508e79a8100f20b0ebb2f40931">qbpp::operator-</a> (const Expr &amp;lhs, const Expr &amp;rhs)</td></tr>
<tr class="separator:a931936508e79a8100f20b0ebb2f40931"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3f213808de39398c9c1bad29c727fca2"><td class="memTemplParams" colspan="2">template&lt;typename T , typename std::enable_if&lt; std::is_convertible&lt; T, coeff_t &gt;::value, int &gt;::type  = 0&gt; </td></tr>
<tr class="memitem:a3f213808de39398c9c1bad29c727fca2"><td class="memTemplItemLeft" align="right" valign="top">Expr &amp;&amp;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a3f213808de39398c9c1bad29c727fca2">qbpp::operator/</a> (Expr &amp;&amp;expr, T val)</td></tr>
<tr class="separator:a3f213808de39398c9c1bad29c727fca2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aef7dc8cb3e2797a05cda9ef1318c8126"><td class="memTemplParams" colspan="2">template&lt;typename T , typename std::enable_if&lt; std::is_convertible&lt; T, coeff_t &gt;::value, int &gt;::type  = 0&gt; </td></tr>
<tr class="memitem:aef7dc8cb3e2797a05cda9ef1318c8126"><td class="memTemplItemLeft" align="right" valign="top">Expr&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#aef7dc8cb3e2797a05cda9ef1318c8126">qbpp::operator/</a> (const Expr &amp;expr, T val)</td></tr>
<tr class="separator:aef7dc8cb3e2797a05cda9ef1318c8126"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2fe367f281dd9a023e30260147840966"><td class="memItemLeft" align="right" valign="top">Expr&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a2fe367f281dd9a023e30260147840966">qbpp::operator+</a> (Expr &amp;&amp;expr)</td></tr>
<tr class="separator:a2fe367f281dd9a023e30260147840966"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6a3973888ef2bf5e5ad84c2c251d6032"><td class="memItemLeft" align="right" valign="top">const Expr &amp;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a6a3973888ef2bf5e5ad84c2c251d6032">qbpp::operator+</a> (const Expr &amp;expr)</td></tr>
<tr class="separator:a6a3973888ef2bf5e5ad84c2c251d6032"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af477c93741540b199e681537ba24522b"><td class="memTemplParams" colspan="2">template&lt;typename T , typename std::enable_if&lt; std::is_integral&lt; T &gt;::value, int &gt;::type  = 0&gt; </td></tr>
<tr class="memitem:af477c93741540b199e681537ba24522b"><td class="memTemplItemLeft" align="right" valign="top">Expr&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#af477c93741540b199e681537ba24522b">qbpp::operator+</a> (const Term &amp;term, T val)</td></tr>
<tr class="separator:af477c93741540b199e681537ba24522b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0f1a231c3e821433f189d293a3366130"><td class="memTemplParams" colspan="2">template&lt;typename T , typename std::enable_if&lt; std::is_integral&lt; T &gt;::value, int &gt;::type  = 0&gt; </td></tr>
<tr class="memitem:a0f1a231c3e821433f189d293a3366130"><td class="memTemplItemLeft" align="right" valign="top">Expr&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a0f1a231c3e821433f189d293a3366130">qbpp::operator+</a> (Term &amp;&amp;term, T val)</td></tr>
<tr class="separator:a0f1a231c3e821433f189d293a3366130"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a9d1a9d429da6c2b0e3e40016370856fc"><td class="memTemplParams" colspan="2">template&lt;typename T , typename std::enable_if&lt; std::is_integral&lt; T &gt;::value, int &gt;::type  = 0&gt; </td></tr>
<tr class="memitem:a9d1a9d429da6c2b0e3e40016370856fc"><td class="memTemplItemLeft" align="right" valign="top">Expr&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a9d1a9d429da6c2b0e3e40016370856fc">qbpp::operator+</a> (T val, Term &amp;&amp;term)</td></tr>
<tr class="separator:a9d1a9d429da6c2b0e3e40016370856fc"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a15a0b4154f76ad5f85558cba78193673"><td class="memTemplParams" colspan="2">template&lt;typename T , typename std::enable_if&lt; std::is_integral&lt; T &gt;::value, int &gt;::type  = 0&gt; </td></tr>
<tr class="memitem:a15a0b4154f76ad5f85558cba78193673"><td class="memTemplItemLeft" align="right" valign="top">Expr&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a15a0b4154f76ad5f85558cba78193673">qbpp::operator+</a> (T val, const Term &amp;term)</td></tr>
<tr class="separator:a15a0b4154f76ad5f85558cba78193673"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aef1c668aa5099291a8294ba9461295b1"><td class="memTemplParams" colspan="2">template&lt;typename T , typename std::enable_if&lt; std::is_integral&lt; T &gt;::value, int &gt;::type  = 0&gt; </td></tr>
<tr class="memitem:aef1c668aa5099291a8294ba9461295b1"><td class="memTemplItemLeft" align="right" valign="top">Expr&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#aef1c668aa5099291a8294ba9461295b1">qbpp::operator+</a> (Var var, T val)</td></tr>
<tr class="separator:aef1c668aa5099291a8294ba9461295b1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aea537334f95ca900d0f414095bfafc5d"><td class="memTemplParams" colspan="2">template&lt;typename T , typename std::enable_if&lt; std::is_integral&lt; T &gt;::value, int &gt;::type  = 0&gt; </td></tr>
<tr class="memitem:aea537334f95ca900d0f414095bfafc5d"><td class="memTemplItemLeft" align="right" valign="top">Expr&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#aea537334f95ca900d0f414095bfafc5d">qbpp::operator+</a> (T val, Var var)</td></tr>
<tr class="separator:aea537334f95ca900d0f414095bfafc5d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a53b4f6c567d5d59b251727198239a8e9"><td class="memTemplParams" colspan="2">template&lt;typename T , typename std::enable_if&lt; std::is_integral&lt; T &gt;::value, int &gt;::type  = 0&gt; </td></tr>
<tr class="memitem:a53b4f6c567d5d59b251727198239a8e9"><td class="memTemplItemLeft" align="right" valign="top">Expr&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a53b4f6c567d5d59b251727198239a8e9">qbpp::operator-</a> (Var var, T val)</td></tr>
<tr class="separator:a53b4f6c567d5d59b251727198239a8e9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a4f795859ce59a2b91856b2b8f2d269fc"><td class="memTemplParams" colspan="2">template&lt;typename T , typename std::enable_if&lt; std::is_integral&lt; T &gt;::value, int &gt;::type  = 0&gt; </td></tr>
<tr class="memitem:a4f795859ce59a2b91856b2b8f2d269fc"><td class="memTemplItemLeft" align="right" valign="top">Expr&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a4f795859ce59a2b91856b2b8f2d269fc">qbpp::operator-</a> (T val, Var var)</td></tr>
<tr class="separator:a4f795859ce59a2b91856b2b8f2d269fc"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2cf57eaa3e7a52f736a8c1ac115d65c2"><td class="memItemLeft" align="right" valign="top">Expr&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a2cf57eaa3e7a52f736a8c1ac115d65c2">qbpp::operator+</a> (Var lhs, Var rhs)</td></tr>
<tr class="separator:a2cf57eaa3e7a52f736a8c1ac115d65c2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2d60c7bf6796c310a2d66ddd44dc9578"><td class="memItemLeft" align="right" valign="top">Expr&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a2d60c7bf6796c310a2d66ddd44dc9578">qbpp::operator+</a> (const Term &amp;lhs, const Term &amp;rhs)</td></tr>
<tr class="separator:a2d60c7bf6796c310a2d66ddd44dc9578"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a43c290c298deb750777d9fc56fd838b3"><td class="memItemLeft" align="right" valign="top">Expr&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a43c290c298deb750777d9fc56fd838b3">qbpp::operator+</a> (const Term &amp;lhs, Term &amp;&amp;rhs)</td></tr>
<tr class="separator:a43c290c298deb750777d9fc56fd838b3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a4c6678c3deedf0f6a8f5d8dd8c332665"><td class="memItemLeft" align="right" valign="top">Expr&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a4c6678c3deedf0f6a8f5d8dd8c332665">qbpp::operator+</a> (Term &amp;&amp;lhs, const Term &amp;rhs)</td></tr>
<tr class="separator:a4c6678c3deedf0f6a8f5d8dd8c332665"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a414cf5ea40662b91223e1eaa2f615418"><td class="memItemLeft" align="right" valign="top">Expr&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a414cf5ea40662b91223e1eaa2f615418">qbpp::operator+</a> (Term &amp;&amp;lhs, Term &amp;&amp;rhs)</td></tr>
<tr class="separator:a414cf5ea40662b91223e1eaa2f615418"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8f6b999809f238a18fa81839f33ea1de"><td class="memItemLeft" align="right" valign="top">Expr&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a8f6b999809f238a18fa81839f33ea1de">qbpp::operator-</a> (const Term &amp;lhs, const Term &amp;rhs)</td></tr>
<tr class="separator:a8f6b999809f238a18fa81839f33ea1de"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:acae7377a6db2bdc3a16aa89e5167b22f"><td class="memItemLeft" align="right" valign="top">Expr&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#acae7377a6db2bdc3a16aa89e5167b22f">qbpp::operator-</a> (const Term &amp;lhs, Term &amp;&amp;rhs)</td></tr>
<tr class="separator:acae7377a6db2bdc3a16aa89e5167b22f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac06a62cf5b881a260a6dd416b41924e6"><td class="memItemLeft" align="right" valign="top">Expr&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#ac06a62cf5b881a260a6dd416b41924e6">qbpp::operator-</a> (Term &amp;&amp;lhs, const Term &amp;rhs)</td></tr>
<tr class="separator:ac06a62cf5b881a260a6dd416b41924e6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8fa25a224e5494d284b840bc458b9c31"><td class="memItemLeft" align="right" valign="top">Expr&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a8fa25a224e5494d284b840bc458b9c31">qbpp::operator-</a> (Term &amp;&amp;lhs, Term &amp;&amp;rhs)</td></tr>
<tr class="separator:a8fa25a224e5494d284b840bc458b9c31"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0d991d7793566070f470e4bb21a247dd"><td class="memTemplParams" colspan="2">template&lt;typename T , typename std::enable_if&lt; std::is_integral&lt; T &gt;::value, int &gt;::type  = 0&gt; </td></tr>
<tr class="memitem:a0d991d7793566070f470e4bb21a247dd"><td class="memTemplItemLeft" align="right" valign="top">Expr&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a0d991d7793566070f470e4bb21a247dd">qbpp::operator-</a> (const Expr &amp;expr, T val)</td></tr>
<tr class="separator:a0d991d7793566070f470e4bb21a247dd"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a087913d3136c67eef2625ac29b0090a0"><td class="memTemplParams" colspan="2">template&lt;typename T , typename std::enable_if&lt; std::is_integral&lt; T &gt;::value, int &gt;::type  = 0&gt; </td></tr>
<tr class="memitem:a087913d3136c67eef2625ac29b0090a0"><td class="memTemplItemLeft" align="right" valign="top">Expr&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a087913d3136c67eef2625ac29b0090a0">qbpp::operator-</a> (Expr &amp;&amp;expr, T val)</td></tr>
<tr class="separator:a087913d3136c67eef2625ac29b0090a0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae843b2334810630c7991c5de6917def4"><td class="memItemLeft" align="right" valign="top">Expr&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#ae843b2334810630c7991c5de6917def4">qbpp::operator+</a> (const Expr &amp;expr, const Term &amp;term)</td></tr>
<tr class="separator:ae843b2334810630c7991c5de6917def4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa3d898fe8a38b23dd57f271ce4e6966f"><td class="memItemLeft" align="right" valign="top">Expr&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#aa3d898fe8a38b23dd57f271ce4e6966f">qbpp::operator+</a> (Expr &amp;&amp;expr, const Term &amp;term)</td></tr>
<tr class="separator:aa3d898fe8a38b23dd57f271ce4e6966f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a540929b0165d4a52f382e09cc8cfb450"><td class="memItemLeft" align="right" valign="top">Expr&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a540929b0165d4a52f382e09cc8cfb450">qbpp::operator+</a> (const Expr &amp;expr, Term &amp;&amp;term)</td></tr>
<tr class="separator:a540929b0165d4a52f382e09cc8cfb450"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7df13568272e2fd2e35edb28f7ba3ee2"><td class="memItemLeft" align="right" valign="top">Expr&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a7df13568272e2fd2e35edb28f7ba3ee2">qbpp::operator+</a> (Expr &amp;&amp;expr, Term &amp;&amp;term)</td></tr>
<tr class="separator:a7df13568272e2fd2e35edb28f7ba3ee2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a44b0530c927b7a8ae23850d11d3c89ac"><td class="memItemLeft" align="right" valign="top">Expr&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a44b0530c927b7a8ae23850d11d3c89ac">qbpp::operator+</a> (const Term &amp;term, const Expr &amp;expr)</td></tr>
<tr class="separator:a44b0530c927b7a8ae23850d11d3c89ac"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:afb6c89d2be538b1c6b9a24f01f22eba0"><td class="memItemLeft" align="right" valign="top">Expr&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#afb6c89d2be538b1c6b9a24f01f22eba0">qbpp::operator+</a> (const Term &amp;term, Expr &amp;&amp;expr)</td></tr>
<tr class="separator:afb6c89d2be538b1c6b9a24f01f22eba0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab1039d180ad70764e58f4782fcb11c9e"><td class="memItemLeft" align="right" valign="top">Expr&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#ab1039d180ad70764e58f4782fcb11c9e">qbpp::operator+</a> (Term &amp;&amp;term, const Expr &amp;expr)</td></tr>
<tr class="separator:ab1039d180ad70764e58f4782fcb11c9e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0cefbc66beb6fa587f27ec131032f3a5"><td class="memItemLeft" align="right" valign="top">Expr&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a0cefbc66beb6fa587f27ec131032f3a5">qbpp::operator+</a> (Term &amp;&amp;term, Expr &amp;&amp;expr)</td></tr>
<tr class="separator:a0cefbc66beb6fa587f27ec131032f3a5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a85e350f5fa3956a08dd1336afeff6687"><td class="memItemLeft" align="right" valign="top">Expr&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a85e350f5fa3956a08dd1336afeff6687">qbpp::operator-</a> (const Term &amp;term, const Expr &amp;expr)</td></tr>
<tr class="separator:a85e350f5fa3956a08dd1336afeff6687"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a58453737871175ab61f522e037bb0b33"><td class="memItemLeft" align="right" valign="top">Expr&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a58453737871175ab61f522e037bb0b33">qbpp::operator-</a> (Term &amp;&amp;term, const Expr &amp;expr)</td></tr>
<tr class="separator:a58453737871175ab61f522e037bb0b33"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:adbeffb3d997ed215c0968947900c36bc"><td class="memItemLeft" align="right" valign="top">Expr&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#adbeffb3d997ed215c0968947900c36bc">qbpp::operator-</a> (const Term &amp;term, Expr &amp;&amp;expr)</td></tr>
<tr class="separator:adbeffb3d997ed215c0968947900c36bc"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a863cda5e261445df1c2cf92359069aea"><td class="memItemLeft" align="right" valign="top">Expr&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a863cda5e261445df1c2cf92359069aea">qbpp::operator-</a> (Term &amp;&amp;term, Expr &amp;&amp;expr)</td></tr>
<tr class="separator:a863cda5e261445df1c2cf92359069aea"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa54ff9d011f4f1332f00667d6d1228e8"><td class="memItemLeft" align="right" valign="top">Expr&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#aa54ff9d011f4f1332f00667d6d1228e8">qbpp::operator-</a> (const Expr &amp;expr, const Term &amp;term)</td></tr>
<tr class="separator:aa54ff9d011f4f1332f00667d6d1228e8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a96da8d5e364cacf279e1cabd8de16fdc"><td class="memItemLeft" align="right" valign="top">Expr&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a96da8d5e364cacf279e1cabd8de16fdc">qbpp::operator-</a> (Expr &amp;&amp;expr, const Term &amp;term)</td></tr>
<tr class="separator:a96da8d5e364cacf279e1cabd8de16fdc"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab8555cd26e76a8e04aa84f2a8c4d53e7"><td class="memItemLeft" align="right" valign="top">Expr&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#ab8555cd26e76a8e04aa84f2a8c4d53e7">qbpp::operator-</a> (const Expr &amp;expr, Term &amp;&amp;term)</td></tr>
<tr class="separator:ab8555cd26e76a8e04aa84f2a8c4d53e7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a107cf524f830216f282b8ba06cfde494"><td class="memItemLeft" align="right" valign="top">Expr&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a107cf524f830216f282b8ba06cfde494">qbpp::operator-</a> (Expr &amp;&amp;expr, Term &amp;&amp;term)</td></tr>
<tr class="separator:a107cf524f830216f282b8ba06cfde494"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6b8a3b941d1962c59512643b6c30b8da"><td class="memItemLeft" align="right" valign="top">Expr&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a6b8a3b941d1962c59512643b6c30b8da">qbpp::replace</a> (const Term &amp;term, const MapDict &amp;map_dict)</td></tr>
<tr class="separator:a6b8a3b941d1962c59512643b6c30b8da"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad09e205acc6322f572528407e4f4a9b7"><td class="memItemLeft" align="right" valign="top">std::string&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#ad09e205acc6322f572528407e4f4a9b7">qbpp::str</a> (int8_t val)</td></tr>
<tr class="separator:ad09e205acc6322f572528407e4f4a9b7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3c1abcaeeb48eb53474f8f6203883ed2"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:a3c1abcaeeb48eb53474f8f6203883ed2"><td class="memTemplItemLeft" align="right" valign="top">auto&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a3c1abcaeeb48eb53474f8f6203883ed2">qbpp::str</a> (const T &amp;val) -&gt; decltype(std::to_string(val))</td></tr>
<tr class="separator:a3c1abcaeeb48eb53474f8f6203883ed2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aab442c5b6a214c5886d4dfdee199530a"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:aab442c5b6a214c5886d4dfdee199530a"><td class="memTemplItemLeft" align="right" valign="top">auto&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#aab442c5b6a214c5886d4dfdee199530a">qbpp::str</a> (T val) -&gt; decltype(val.str())</td></tr>
<tr class="separator:aab442c5b6a214c5886d4dfdee199530a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a9897ad8c594d3572669f7e64cb73c6d6"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:a9897ad8c594d3572669f7e64cb73c6d6"><td class="memTemplItemLeft" align="right" valign="top">T&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a9897ad8c594d3572669f7e64cb73c6d6">qbpp::abs</a> (T val)</td></tr>
<tr class="separator:a9897ad8c594d3572669f7e64cb73c6d6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a066e8101843403f3b7ca248dc98606fa"><td class="memItemLeft" align="right" valign="top">energy_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a066e8101843403f3b7ca248dc98606fa">qbpp::gcd</a> (energy_t a, energy_t b)</td></tr>
<tr class="separator:a066e8101843403f3b7ca248dc98606fa"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a717e98de5c1866aeef5498c1fd6e48b9"><td class="memItemLeft" align="right" valign="top">std::string&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a717e98de5c1866aeef5498c1fd6e48b9">qbpp::str_impl</a> (const Vars &amp;vars, std::function&lt; std::string(Var)&gt; str)</td></tr>
<tr class="separator:a717e98de5c1866aeef5498c1fd6e48b9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:add12c2325f44f6bb0fd5c9921462c8cc"><td class="memItemLeft" align="right" valign="top">std::string&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#add12c2325f44f6bb0fd5c9921462c8cc">qbpp::str_impl</a> (const Term &amp;term, std::function&lt; std::string(Var)&gt; str)</td></tr>
<tr class="separator:add12c2325f44f6bb0fd5c9921462c8cc"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3c27b7667bb88c80b66822f59aef9241"><td class="memItemLeft" align="right" valign="top">std::string&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a3c27b7667bb88c80b66822f59aef9241">qbpp::str_impl</a> (const Terms &amp;terms, std::function&lt; std::string(Var)&gt; str)</td></tr>
<tr class="separator:a3c27b7667bb88c80b66822f59aef9241"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a375a2df55223523ba27620ac306cf598"><td class="memItemLeft" align="right" valign="top">std::string&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a375a2df55223523ba27620ac306cf598">qbpp::str_impl</a> (const Expr &amp;expr, std::function&lt; std::string(Var)&gt; str)</td></tr>
<tr class="separator:a375a2df55223523ba27620ac306cf598"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a14c16118187b4556c1a59a34d5a9d4fa"><td class="memItemLeft" align="right" valign="top">std::string&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a14c16118187b4556c1a59a34d5a9d4fa">qbpp::str</a> (const impl::BitVector &amp;bit_vector)</td></tr>
<tr class="separator:a14c16118187b4556c1a59a34d5a9d4fa"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:acced023ee901b8bc9a1a2968ec852ab4"><td class="memItemLeft" align="right" valign="top">std::string&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#acced023ee901b8bc9a1a2968ec852ab4">qbpp::str</a> (const Expr &amp;expr)</td></tr>
<tr class="separator:acced023ee901b8bc9a1a2968ec852ab4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a9ddf64f23c738a48e826d286d24716a1"><td class="memItemLeft" align="right" valign="top">std::string&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a9ddf64f23c738a48e826d286d24716a1">qbpp::str</a> (const Terms &amp;terms)</td></tr>
<tr class="separator:a9ddf64f23c738a48e826d286d24716a1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8e549a11c0fee368294581bdf7d66958"><td class="memItemLeft" align="right" valign="top">std::string&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a8e549a11c0fee368294581bdf7d66958">qbpp::str</a> (const Expr &amp;expr, const std::string &amp;prefix, const std::string &amp;separator[[maybe_unused]]=&quot;,&quot;)</td></tr>
<tr class="separator:a8e549a11c0fee368294581bdf7d66958"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a18a943c23db4a5d7b987636d75766548"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:a18a943c23db4a5d7b987636d75766548"><td class="memTemplItemLeft" align="right" valign="top">std::string&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a18a943c23db4a5d7b987636d75766548">qbpp::str</a> (const Vector&lt; T &gt; &amp;vec, const std::string &amp;prefix=&quot;&quot;, const std::string &amp;separator=&quot;,&quot;)</td></tr>
<tr class="separator:a18a943c23db4a5d7b987636d75766548"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab6c438eb93ad97907172c9283f965896"><td class="memItemLeft" align="right" valign="top">std::ostream &amp;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#ab6c438eb93ad97907172c9283f965896">qbpp::operator&lt;&lt;</a> (std::ostream &amp;os, const VarInt &amp;var_int)</td></tr>
<tr class="separator:ab6c438eb93ad97907172c9283f965896"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad64686fafdfae06f36e558781820b108"><td class="memItemLeft" align="right" valign="top">std::ostream &amp;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#ad64686fafdfae06f36e558781820b108">qbpp::operator&lt;&lt;</a> (std::ostream &amp;os, const MapList &amp;map_list)</td></tr>
<tr class="separator:ad64686fafdfae06f36e558781820b108"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a4e719c2d1c3aa91cf3eb28cdc06f7f97"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:a4e719c2d1c3aa91cf3eb28cdc06f7f97"><td class="memTemplItemLeft" align="right" valign="top">std::ostream &amp;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a4e719c2d1c3aa91cf3eb28cdc06f7f97">qbpp::operator&lt;&lt;</a> (std::ostream &amp;os, const Vector&lt; T &gt; &amp;vec)</td></tr>
<tr class="separator:a4e719c2d1c3aa91cf3eb28cdc06f7f97"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a15ffad9a897b169b0a3cf63f30ff068b"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:a15ffad9a897b169b0a3cf63f30ff068b"><td class="memTemplItemLeft" align="right" valign="top">std::ostream &amp;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a15ffad9a897b169b0a3cf63f30ff068b">qbpp::operator&lt;&lt;</a> (std::ostream &amp;os, const std::pair&lt; std::string, Vector&lt; T &gt;&gt; &amp;vec)</td></tr>
<tr class="separator:a15ffad9a897b169b0a3cf63f30ff068b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa65f5014a29d3fd4d82f5f23dc6e17bb"><td class="memItemLeft" align="right" valign="top">std::ostream &amp;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp_1_1impl.html#aa65f5014a29d3fd4d82f5f23dc6e17bb">qbpp::impl::operator&lt;&lt;</a> (std::ostream &amp;os, const <a class="el" href="classqbpp_1_1impl_1_1IndexVarMapper.html">qbpp::impl::IndexVarMapper</a> &amp;mapper)</td></tr>
<tr class="separator:aa65f5014a29d3fd4d82f5f23dc6e17bb"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac5dbf5328b36f4ea4e4e07383cf921ac"><td class="memItemLeft" align="right" valign="top">std::ostream &amp;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp_1_1impl.html#ac5dbf5328b36f4ea4e4e07383cf921ac">qbpp::impl::operator&lt;&lt;</a> (std::ostream &amp;os, const <a class="el" href="classqbpp_1_1impl_1_1BitVector.html">qbpp::impl::BitVector</a> &amp;bit_vector)</td></tr>
<tr class="separator:ac5dbf5328b36f4ea4e4e07383cf921ac"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aedf0a4e1866526c2184c9b8b030e17c7"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:aedf0a4e1866526c2184c9b8b030e17c7"><td class="memTemplItemLeft" align="right" valign="top">Expr&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="namespaceqbpp_1_1impl.html#aedf0a4e1866526c2184c9b8b030e17c7">qbpp::impl::equal</a> (const T &amp;lhs, const T &amp;rhs)</td></tr>
<tr class="separator:aedf0a4e1866526c2184c9b8b030e17c7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aeaa6e805763b1a5d1644864e12bf8fcf"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:aeaa6e805763b1a5d1644864e12bf8fcf"><td class="memTemplItemLeft" align="right" valign="top">Expr&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="namespaceqbpp_1_1impl.html#aeaa6e805763b1a5d1644864e12bf8fcf">qbpp::impl::equal</a> (const std::vector&lt; T &gt; &amp;lhs, const std::vector&lt; T &gt; &amp;rhs)</td></tr>
<tr class="separator:aeaa6e805763b1a5d1644864e12bf8fcf"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a535714c670e24977e9ca43d862e313e1"><td class="memItemLeft" align="right" valign="top">MapDict&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a535714c670e24977e9ca43d862e313e1">qbpp::list_to_dict</a> (const MapList &amp;map_list)</td></tr>
<tr class="separator:a535714c670e24977e9ca43d862e313e1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a991fa31dc3fd35243afb4ed139ba764d"><td class="memItemLeft" align="right" valign="top">energy_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a991fa31dc3fd35243afb4ed139ba764d">qbpp::eval</a> (const Term &amp;term, const VarValMap &amp;var_val_map)</td></tr>
<tr class="separator:a991fa31dc3fd35243afb4ed139ba764d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac429a7530957802c68d0ba6188daad94"><td class="memItemLeft" align="right" valign="top">energy_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#ac429a7530957802c68d0ba6188daad94">qbpp::eval</a> (const Term &amp;term, const Sol &amp;sol)</td></tr>
<tr class="separator:ac429a7530957802c68d0ba6188daad94"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab3efb11e8167e5a1b4ec8cc2f5a98da3"><td class="memItemLeft" align="right" valign="top">Expr&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#ab3efb11e8167e5a1b4ec8cc2f5a98da3">qbpp::replace</a> (const Expr &amp;expr, const MapList &amp;map_list)</td></tr>
<tr class="separator:ab3efb11e8167e5a1b4ec8cc2f5a98da3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a4debed5e5fafbaf22768687291462387"><td class="memItemLeft" align="right" valign="top">energy_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a4debed5e5fafbaf22768687291462387">qbpp::eval_var_val_map</a> (const Expr &amp;expr, const VarValMap &amp;var_val_map)</td></tr>
<tr class="separator:a4debed5e5fafbaf22768687291462387"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6374dcd91f578f345c65c7bbd855d05d"><td class="memTemplParams" colspan="2">template&lt;typename T , typename... Args, typename  = typename std::enable_if&lt;std::is_integral&lt;T&gt;::value&gt;::type&gt; </td></tr>
<tr class="memitem:a6374dcd91f578f345c65c7bbd855d05d"><td class="memTemplItemLeft" align="right" valign="top">auto&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a6374dcd91f578f345c65c7bbd855d05d">qbpp::var</a> (const std::string &amp;var_str, T size, Args... args)</td></tr>
<tr class="separator:a6374dcd91f578f345c65c7bbd855d05d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a844402a465157e00bca032af58c0a795"><td class="memTemplParams" colspan="2">template&lt;typename T , typename... Args, typename  = typename std::enable_if&lt;std::is_integral&lt;T&gt;::value&gt;::type&gt; </td></tr>
<tr class="memitem:a844402a465157e00bca032af58c0a795"><td class="memTemplItemLeft" align="right" valign="top">auto&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a844402a465157e00bca032af58c0a795">qbpp::var</a> (T size, Args... args)</td></tr>
<tr class="separator:a844402a465157e00bca032af58c0a795"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a34b0039c6486ef9f1373775d5a897bae"><td class="memItemLeft" align="right" valign="top">VarIntCore&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a34b0039c6486ef9f1373775d5a897bae">qbpp::var_int</a> (const std::string &amp;var_str)</td></tr>
<tr class="separator:a34b0039c6486ef9f1373775d5a897bae"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a624463d035658e5f9fd9ec1ab647d226"><td class="memItemLeft" align="right" valign="top">VarIntCore&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a624463d035658e5f9fd9ec1ab647d226">qbpp::var_int</a> ()</td></tr>
<tr class="separator:a624463d035658e5f9fd9ec1ab647d226"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a75237ac2fece7216a9673897c8dabdbd"><td class="memTemplParams" colspan="2">template&lt;typename T , typename... Args&gt; </td></tr>
<tr class="memitem:a75237ac2fece7216a9673897c8dabdbd"><td class="memTemplItemLeft" align="right" valign="top">auto&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a75237ac2fece7216a9673897c8dabdbd">qbpp::var_int</a> (const std::string &amp;var_str, T size, Args... args)</td></tr>
<tr class="separator:a75237ac2fece7216a9673897c8dabdbd"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a06a5a1cc0d304f806669bb1cde4819ae"><td class="memItemLeft" align="right" valign="top">VarOnehotCore&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a06a5a1cc0d304f806669bb1cde4819ae">qbpp::var_onehot</a> (const std::string &amp;var_str)</td></tr>
<tr class="separator:a06a5a1cc0d304f806669bb1cde4819ae"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1a01ae748547e1c34071094bf76d2ce0"><td class="memItemLeft" align="right" valign="top">VarOnehotCore&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a1a01ae748547e1c34071094bf76d2ce0">qbpp::var_onehot</a> ()</td></tr>
<tr class="separator:a1a01ae748547e1c34071094bf76d2ce0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad239444e6826f97e56001b2bf41a6c7c"><td class="memTemplParams" colspan="2">template&lt;typename T , typename... Args&gt; </td></tr>
<tr class="memitem:ad239444e6826f97e56001b2bf41a6c7c"><td class="memTemplItemLeft" align="right" valign="top">auto&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#ad239444e6826f97e56001b2bf41a6c7c">qbpp::var_onehot</a> (const std::string &amp;var_str, T size, Args... args)</td></tr>
<tr class="separator:ad239444e6826f97e56001b2bf41a6c7c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a9e9e36efe70c5550c01ae98a8310afa0"><td class="memItemLeft" align="right" valign="top">auto&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a9e9e36efe70c5550c01ae98a8310afa0">qbpp::operator&lt;=</a> (energy_t lhs, VarOnehotCore &amp;&amp;rhs)</td></tr>
<tr class="separator:a9e9e36efe70c5550c01ae98a8310afa0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a360c87be4639072ec2ed4e680a65730f"><td class="memItemLeft" align="right" valign="top">auto&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a360c87be4639072ec2ed4e680a65730f">qbpp::operator&lt;=</a> (const std::pair&lt; energy_t, VarOnehotCore &gt; lhs, energy_t rhs)</td></tr>
<tr class="separator:a360c87be4639072ec2ed4e680a65730f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:acc035a0d943547028de5df9729398c18"><td class="memItemLeft" align="right" valign="top">std::vector&lt; coeff_t &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#acc035a0d943547028de5df9729398c18">qbpp::comp_coeffs</a> (energy_t min_val, energy_t max_val, energy_t base_coeff=1)</td></tr>
<tr class="separator:acc035a0d943547028de5df9729398c18"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2c5c84c1253682ac44a56c0badbaeebb"><td class="memItemLeft" align="right" valign="top">VarInt&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a2c5c84c1253682ac44a56c0badbaeebb">qbpp::new_var_int</a> (const std::string &amp;var_str, energy_t min_val, energy_t max_val, energy_t base_coeff=1)</td></tr>
<tr class="separator:a2c5c84c1253682ac44a56c0badbaeebb"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad67847f7e95244f0da53a9d4eb5de9e9"><td class="memItemLeft" align="right" valign="top">VarInt&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#ad67847f7e95244f0da53a9d4eb5de9e9">qbpp::new_var_int</a> (const VarIntCore &amp;var_int_core, energy_t min_val, energy_t max_val, energy_t base_coeff=1)</td></tr>
<tr class="separator:ad67847f7e95244f0da53a9d4eb5de9e9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a239178b366326866584c3162d46551c9"><td class="memItemLeft" align="right" valign="top">auto&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a239178b366326866584c3162d46551c9">qbpp::operator&lt;=</a> (energy_t lhs, VarIntCore &amp;&amp;rhs)</td></tr>
<tr class="separator:a239178b366326866584c3162d46551c9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a13e21162b83906d0559743650e03d7cb"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:a13e21162b83906d0559743650e03d7cb"><td class="memTemplItemLeft" align="right" valign="top">auto&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a13e21162b83906d0559743650e03d7cb">qbpp::operator&lt;=</a> (energy_t lhs, Vector&lt; T &gt; &amp;&amp;rhs)</td></tr>
<tr class="separator:a13e21162b83906d0559743650e03d7cb"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2d1220f01c3a9dd06880c49cfd635d57"><td class="memItemLeft" align="right" valign="top">auto&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a2d1220f01c3a9dd06880c49cfd635d57">qbpp::operator&lt;=</a> (const std::pair&lt; energy_t, VarIntCore &gt; lhs, energy_t rhs)</td></tr>
<tr class="separator:a2d1220f01c3a9dd06880c49cfd635d57"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad5189d033078cdf8d3f02d1d762cdb0e"><td class="memItemLeft" align="right" valign="top">std::pair&lt; energy_t, Vector&lt; VarIntCore &gt; &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#ad5189d033078cdf8d3f02d1d762cdb0e">qbpp::operator&lt;=</a> (energy_t lhs, const Vector&lt; VarIntCore &gt; &amp;rhs)</td></tr>
<tr class="separator:ad5189d033078cdf8d3f02d1d762cdb0e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:acbcf2054ff2c30f4161b547fe00163b2"><td class="memItemLeft" align="right" valign="top">auto&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#acbcf2054ff2c30f4161b547fe00163b2">qbpp::operator&lt;=</a> (const std::pair&lt; energy_t, Vector&lt; VarIntCore &gt;&gt; lhs, energy_t rhs)</td></tr>
<tr class="separator:acbcf2054ff2c30f4161b547fe00163b2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a11f992a6e65a3a32fd97ce9ec7dce387"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a11f992a6e65a3a32fd97ce9ec7dce387">qbpp::operator&lt;=</a> (energy_t, Var)=delete</td></tr>
<tr class="separator:a11f992a6e65a3a32fd97ce9ec7dce387"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af01c50694b44a6c2407bc4507fa06648"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#af01c50694b44a6c2407bc4507fa06648">qbpp::operator&lt;=</a> (Var, energy_t)=delete</td></tr>
<tr class="separator:af01c50694b44a6c2407bc4507fa06648"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac1b198c35eb8831b6ac3ad2048caee66"><td class="memItemLeft" align="right" valign="top">Expr&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#ac1b198c35eb8831b6ac3ad2048caee66">qbpp::expr</a> ()</td></tr>
<tr class="separator:ac1b198c35eb8831b6ac3ad2048caee66"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a832d4da94733bf0e3e2ba611da86f442"><td class="memItemLeft" align="right" valign="top">Vector&lt; Expr &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a832d4da94733bf0e3e2ba611da86f442">qbpp::expr</a> (vindex_t size)</td></tr>
<tr class="separator:a832d4da94733bf0e3e2ba611da86f442"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:add51cb422256f9e89483b8cd0deb4721"><td class="memTemplParams" colspan="2">template&lt;typename T , typename... Args&gt; </td></tr>
<tr class="memitem:add51cb422256f9e89483b8cd0deb4721"><td class="memTemplItemLeft" align="right" valign="top">auto&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#add51cb422256f9e89483b8cd0deb4721">qbpp::expr</a> (T size, Args... args)</td></tr>
<tr class="separator:add51cb422256f9e89483b8cd0deb4721"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a56a6b58eb5025738b3d0ea99718917e0"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:a56a6b58eb5025738b3d0ea99718917e0"><td class="memTemplItemLeft" align="right" valign="top">Expr&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a56a6b58eb5025738b3d0ea99718917e0">qbpp::toExpr</a> (const T &amp;arg)</td></tr>
<tr class="separator:a56a6b58eb5025738b3d0ea99718917e0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aaf3ddb99a2a36e2a6bd70bc775d6fada"><td class="memItemLeft" align="right" valign="top">Expr&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#aaf3ddb99a2a36e2a6bd70bc775d6fada">qbpp::toExpr</a> (const Expr &amp;arg)</td></tr>
<tr class="separator:aaf3ddb99a2a36e2a6bd70bc775d6fada"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:afee14d78f20b8b693bf97bfa56a06123"><td class="memItemLeft" align="right" valign="top">Vector&lt; Expr &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#afee14d78f20b8b693bf97bfa56a06123">qbpp::toExpr</a> (const Vector&lt; Expr &gt; &amp;arg)</td></tr>
<tr class="separator:afee14d78f20b8b693bf97bfa56a06123"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a771f1a64025858d915df0123983aa772"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:a771f1a64025858d915df0123983aa772"><td class="memTemplItemLeft" align="right" valign="top">auto&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a771f1a64025858d915df0123983aa772">qbpp::toExpr</a> (const Vector&lt; T &gt; &amp;arg)</td></tr>
<tr class="separator:a771f1a64025858d915df0123983aa772"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a272d30a9680bd1ef3e63e06ef7cdf01b"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:a272d30a9680bd1ef3e63e06ef7cdf01b"><td class="memTemplItemLeft" align="right" valign="top">auto&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a272d30a9680bd1ef3e63e06ef7cdf01b">qbpp::toExpr</a> (const std::vector&lt; T &gt; &amp;arg)</td></tr>
<tr class="separator:a272d30a9680bd1ef3e63e06ef7cdf01b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5b024ae1db7da6bff011ad43b14fb8ec"><td class="memItemLeft" align="right" valign="top">Vector&lt; Expr &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a5b024ae1db7da6bff011ad43b14fb8ec">qbpp::toExpr</a> (const std::initializer_list&lt; Expr &gt; &amp;list)</td></tr>
<tr class="separator:a5b024ae1db7da6bff011ad43b14fb8ec"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a054ed56666c40a3b5d948d4292c91496"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:a054ed56666c40a3b5d948d4292c91496"><td class="memTemplItemLeft" align="right" valign="top">auto&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a054ed56666c40a3b5d948d4292c91496">qbpp::toExpr</a> (const std::initializer_list&lt; T &gt; &amp;list)</td></tr>
<tr class="separator:a054ed56666c40a3b5d948d4292c91496"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac60fbc687c985a61cd3c755bc8ad2f45"><td class="memItemLeft" align="right" valign="top">Vector&lt; Vector&lt; Expr &gt; &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#ac60fbc687c985a61cd3c755bc8ad2f45">qbpp::toExpr</a> (const std::initializer_list&lt; std::initializer_list&lt; Expr &gt;&gt; &amp;list)</td></tr>
<tr class="separator:ac60fbc687c985a61cd3c755bc8ad2f45"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a08b806375d2c80f115a165e2dcb36cb9"><td class="memItemLeft" align="right" valign="top">Vector&lt; Vector&lt; Vector&lt; Expr &gt; &gt; &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a08b806375d2c80f115a165e2dcb36cb9">qbpp::toExpr</a> (const std::initializer_list&lt; std::initializer_list&lt; std::initializer_list&lt; Expr &gt;&gt;&gt; &amp;list)</td></tr>
<tr class="separator:a08b806375d2c80f115a165e2dcb36cb9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae06a111bb3040263deff02c501635c10"><td class="memItemLeft" align="right" valign="top">Vector&lt; Vector&lt; Vector&lt; Vector&lt; Expr &gt; &gt; &gt; &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#ae06a111bb3040263deff02c501635c10">qbpp::toExpr</a> (const std::initializer_list&lt; std::initializer_list&lt; std::initializer_list&lt; std::initializer_list&lt; Expr &gt;&gt;&gt;&gt; &amp;list)</td></tr>
<tr class="separator:ae06a111bb3040263deff02c501635c10"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:adf383c43f74885fa9316f6ed31c06008"><td class="memItemLeft" align="right" valign="top">coeff_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#adf383c43f74885fa9316f6ed31c06008">qbpp::gcd</a> (const Expr &amp;expr)</td></tr>
<tr class="separator:adf383c43f74885fa9316f6ed31c06008"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:afd23f996df8c2cae65e6f7088c3b7425"><td class="memItemLeft" align="right" valign="top">ExprExpr&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#afd23f996df8c2cae65e6f7088c3b7425">qbpp::operator==</a> (const Expr &amp;expr, energy_t val)</td></tr>
<tr class="separator:afd23f996df8c2cae65e6f7088c3b7425"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a370670ee4b0902d6ef81142eebd6fdeb"><td class="memItemLeft" align="right" valign="top">ExprExpr&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a370670ee4b0902d6ef81142eebd6fdeb">qbpp::comparison</a> (const Expr &amp;expr, energy_t minimum, energy_t maximum)</td></tr>
<tr class="separator:a370670ee4b0902d6ef81142eebd6fdeb"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a20f18e97069a78ff709eca10523114ab"><td class="memItemLeft" align="right" valign="top">std::pair&lt; energy_t, Expr &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a20f18e97069a78ff709eca10523114ab">qbpp::operator&lt;=</a> (energy_t min_val, const Expr &amp;expr)</td></tr>
<tr class="separator:a20f18e97069a78ff709eca10523114ab"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a09f90db0eadadb2d20f60fb87012ecc8"><td class="memItemLeft" align="right" valign="top">ExprExpr&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a09f90db0eadadb2d20f60fb87012ecc8">qbpp::operator&lt;=</a> (const std::pair&lt; energy_t, Expr &gt; &amp;pair, energy_t max_val)</td></tr>
<tr class="separator:a09f90db0eadadb2d20f60fb87012ecc8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a23e20254bb8ddd83cf2673a8ea204a94"><td class="memItemLeft" align="right" valign="top">ExprExpr&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a23e20254bb8ddd83cf2673a8ea204a94">qbpp::operator&lt;=</a> (const std::pair&lt; energy_t, Expr &gt; &amp;pair, Inf val)</td></tr>
<tr class="separator:a23e20254bb8ddd83cf2673a8ea204a94"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a023273dcba72fa9cef4128de93d12b43"><td class="memItemLeft" align="right" valign="top">std::pair&lt; energy_t, Expr &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a023273dcba72fa9cef4128de93d12b43">qbpp::operator&lt;=</a> (Inf val, const Expr &amp;expr)</td></tr>
<tr class="separator:a023273dcba72fa9cef4128de93d12b43"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:abbed8c57213e7a8eefe7aa9fd60281ac"><td class="memItemLeft" align="right" valign="top">Expr&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#abbed8c57213e7a8eefe7aa9fd60281ac">qbpp::simplify_seq</a> (const Expr &amp;expr, Vars(*sort_vars_func)(const Vars &amp;)=sort_vars)</td></tr>
<tr class="separator:abbed8c57213e7a8eefe7aa9fd60281ac"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa7464c250db0ba827d6582d3efa9a272"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:aa7464c250db0ba827d6582d3efa9a272"><td class="memTemplItemLeft" align="right" valign="top">Vector&lt; T &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#aa7464c250db0ba827d6582d3efa9a272">qbpp::simplify</a> (const Vector&lt; T &gt; &amp;vec, Vars(*sort_vars_func)(const Vars &amp;)=sort_vars)</td></tr>
<tr class="separator:aa7464c250db0ba827d6582d3efa9a272"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2d100408fb06c3d1530caff5553bb290"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:a2d100408fb06c3d1530caff5553bb290"><td class="memTemplItemLeft" align="right" valign="top">auto&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a2d100408fb06c3d1530caff5553bb290">qbpp::simplify_as_binary</a> (const Vector&lt; T &gt; &amp;arg)</td></tr>
<tr class="separator:a2d100408fb06c3d1530caff5553bb290"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af325ae9d6616b9bcb16dd6ded7fd9b12"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:af325ae9d6616b9bcb16dd6ded7fd9b12"><td class="memTemplItemLeft" align="right" valign="top">auto&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#af325ae9d6616b9bcb16dd6ded7fd9b12">qbpp::simplify_as_spin</a> (const Vector&lt; T &gt; &amp;arg)</td></tr>
<tr class="separator:af325ae9d6616b9bcb16dd6ded7fd9b12"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aebff5c6be40335125221f44731cee406"><td class="memItemLeft" align="right" valign="top">Expr&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#aebff5c6be40335125221f44731cee406">qbpp::reduce_sum</a> (const Term &amp;term)</td></tr>
<tr class="separator:aebff5c6be40335125221f44731cee406"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aba363d0725955ba9ec04d0f955a034d8"><td class="memItemLeft" align="right" valign="top">Expr&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#aba363d0725955ba9ec04d0f955a034d8">qbpp::reduce_cascade</a> (const Term &amp;term)</td></tr>
<tr class="separator:aba363d0725955ba9ec04d0f955a034d8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a37ffb81dbccb667862b9b0a495fb4914"><td class="memTemplParams" colspan="2">template&lt;typename T , typename U &gt; </td></tr>
<tr class="memitem:a37ffb81dbccb667862b9b0a495fb4914"><td class="memTemplItemLeft" align="right" valign="top">Vector&lt; Expr &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a37ffb81dbccb667862b9b0a495fb4914">qbpp::operator+</a> (const Vector&lt; T &gt; &amp;lhs, const Vector&lt; U &gt; &amp;rhs)</td></tr>
<tr class="separator:a37ffb81dbccb667862b9b0a495fb4914"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a083d9e1d43bf815bf2e8839701ed9fe7"><td class="memTemplParams" colspan="2">template&lt;typename T , typename U &gt; </td></tr>
<tr class="memitem:a083d9e1d43bf815bf2e8839701ed9fe7"><td class="memTemplItemLeft" align="right" valign="top">auto&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a083d9e1d43bf815bf2e8839701ed9fe7">qbpp::operator+</a> (const Vector&lt; Vector&lt; T &gt;&gt; &amp;lhs, const Vector&lt; Vector&lt; U &gt;&gt; &amp;rhs)</td></tr>
<tr class="separator:a083d9e1d43bf815bf2e8839701ed9fe7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0cf70a04677ea43c68d2668dc5537a06"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:a0cf70a04677ea43c68d2668dc5537a06"><td class="memTemplItemLeft" align="right" valign="top">Vector&lt; Expr &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a0cf70a04677ea43c68d2668dc5537a06">qbpp::operator+</a> (const Vector&lt; T &gt; &amp;lhs, const Expr &amp;rhs)</td></tr>
<tr class="separator:a0cf70a04677ea43c68d2668dc5537a06"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2a542219601eed30403575b279a6d633"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:a2a542219601eed30403575b279a6d633"><td class="memTemplItemLeft" align="right" valign="top">Vector&lt; Expr &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a2a542219601eed30403575b279a6d633">qbpp::operator+</a> (Vector&lt; T &gt; &amp;&amp;lhs, const Expr &amp;rhs)</td></tr>
<tr class="separator:a2a542219601eed30403575b279a6d633"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aafd565909b0343043aa9d96c33344ec1"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:aafd565909b0343043aa9d96c33344ec1"><td class="memTemplItemLeft" align="right" valign="top">auto&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#aafd565909b0343043aa9d96c33344ec1">qbpp::operator+</a> (const Vector&lt; Vector&lt; T &gt;&gt; &amp;lhs, const Expr &amp;rhs)</td></tr>
<tr class="separator:aafd565909b0343043aa9d96c33344ec1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a9893cea2de97b1ef46221d724ed0b518"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:a9893cea2de97b1ef46221d724ed0b518"><td class="memTemplItemLeft" align="right" valign="top">auto&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a9893cea2de97b1ef46221d724ed0b518">qbpp::operator+</a> (Vector&lt; Vector&lt; T &gt;&gt; &amp;&amp;lhs, const Expr &amp;rhs)</td></tr>
<tr class="separator:a9893cea2de97b1ef46221d724ed0b518"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1f232dd7584838aeea0d4a93528c55b2"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:a1f232dd7584838aeea0d4a93528c55b2"><td class="memTemplItemLeft" align="right" valign="top">auto&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a1f232dd7584838aeea0d4a93528c55b2">qbpp::operator+</a> (const Expr &amp;lhs, const Vector&lt; T &gt; &amp;rhs)</td></tr>
<tr class="separator:a1f232dd7584838aeea0d4a93528c55b2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7922b6627a5c3627350cc99c8b82594b"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:a7922b6627a5c3627350cc99c8b82594b"><td class="memTemplItemLeft" align="right" valign="top">auto&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a7922b6627a5c3627350cc99c8b82594b">qbpp::operator+</a> (const Expr &amp;lhs, Vector&lt; T &gt; &amp;&amp;rhs)</td></tr>
<tr class="separator:a7922b6627a5c3627350cc99c8b82594b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a144a520128475ade9af839aecf39f9d5"><td class="memTemplParams" colspan="2">template&lt;typename T , typename U &gt; </td></tr>
<tr class="memitem:a144a520128475ade9af839aecf39f9d5"><td class="memTemplItemLeft" align="right" valign="top">Vector&lt; Expr &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a144a520128475ade9af839aecf39f9d5">qbpp::operator*</a> (const Vector&lt; T &gt; &amp;lhs, const Vector&lt; U &gt; &amp;rhs)</td></tr>
<tr class="separator:a144a520128475ade9af839aecf39f9d5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a97371a7ec2464f30f371734c16fa7376"><td class="memTemplParams" colspan="2">template&lt;typename T , typename U &gt; </td></tr>
<tr class="memitem:a97371a7ec2464f30f371734c16fa7376"><td class="memTemplItemLeft" align="right" valign="top">auto&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a97371a7ec2464f30f371734c16fa7376">qbpp::operator*</a> (const Vector&lt; Vector&lt; T &gt;&gt; &amp;lhs, const Vector&lt; Vector&lt; U &gt;&gt; &amp;rhs)</td></tr>
<tr class="separator:a97371a7ec2464f30f371734c16fa7376"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8841feec80064cab49a5e40b1216e19a"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:a8841feec80064cab49a5e40b1216e19a"><td class="memTemplItemLeft" align="right" valign="top">Vector&lt; Expr &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a8841feec80064cab49a5e40b1216e19a">qbpp::operator*</a> (const Vector&lt; T &gt; &amp;lhs, const Expr &amp;rhs)</td></tr>
<tr class="separator:a8841feec80064cab49a5e40b1216e19a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa1856d5e7b36adf94bee9a1633ceab24"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:aa1856d5e7b36adf94bee9a1633ceab24"><td class="memTemplItemLeft" align="right" valign="top">Vector&lt; Expr &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#aa1856d5e7b36adf94bee9a1633ceab24">qbpp::operator*</a> (Vector&lt; T &gt; &amp;&amp;lhs, const Expr &amp;rhs)</td></tr>
<tr class="separator:aa1856d5e7b36adf94bee9a1633ceab24"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad1b72958336afdcdfdf7a8fac86586fa"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:ad1b72958336afdcdfdf7a8fac86586fa"><td class="memTemplItemLeft" align="right" valign="top">auto&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#ad1b72958336afdcdfdf7a8fac86586fa">qbpp::operator*</a> (const Vector&lt; Vector&lt; T &gt;&gt; &amp;lhs, const Expr &amp;rhs)</td></tr>
<tr class="separator:ad1b72958336afdcdfdf7a8fac86586fa"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:afd452169d1fe7f5e0d42b34b09bd563f"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:afd452169d1fe7f5e0d42b34b09bd563f"><td class="memTemplItemLeft" align="right" valign="top">auto&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#afd452169d1fe7f5e0d42b34b09bd563f">qbpp::operator*</a> (Vector&lt; Vector&lt; T &gt;&gt; &amp;&amp;lhs, const Expr &amp;rhs)</td></tr>
<tr class="separator:afd452169d1fe7f5e0d42b34b09bd563f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:abf1d343783f6b4f514f39cbd09bf0aa8"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:abf1d343783f6b4f514f39cbd09bf0aa8"><td class="memTemplItemLeft" align="right" valign="top">auto&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#abf1d343783f6b4f514f39cbd09bf0aa8">qbpp::operator*</a> (const Expr &amp;lhs, const Vector&lt; T &gt; &amp;rhs)</td></tr>
<tr class="separator:abf1d343783f6b4f514f39cbd09bf0aa8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af974e8c997df9d2954286074afae9b26"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:af974e8c997df9d2954286074afae9b26"><td class="memTemplItemLeft" align="right" valign="top">auto&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#af974e8c997df9d2954286074afae9b26">qbpp::operator*</a> (const Expr &amp;lhs, Vector&lt; T &gt; &amp;&amp;rhs)</td></tr>
<tr class="separator:af974e8c997df9d2954286074afae9b26"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:abd7c3d948e14c6adf6329f937b57990b"><td class="memTemplParams" colspan="2">template&lt;typename T , typename U &gt; </td></tr>
<tr class="memitem:abd7c3d948e14c6adf6329f937b57990b"><td class="memTemplItemLeft" align="right" valign="top">Vector&lt; Expr &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#abd7c3d948e14c6adf6329f937b57990b">qbpp::operator-</a> (const Vector&lt; T &gt; &amp;lhs, const Vector&lt; U &gt; &amp;rhs)</td></tr>
<tr class="separator:abd7c3d948e14c6adf6329f937b57990b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab5b0bf378a4a3d21f0a8f2992e7ee22a"><td class="memTemplParams" colspan="2">template&lt;typename T , typename U &gt; </td></tr>
<tr class="memitem:ab5b0bf378a4a3d21f0a8f2992e7ee22a"><td class="memTemplItemLeft" align="right" valign="top">auto&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#ab5b0bf378a4a3d21f0a8f2992e7ee22a">qbpp::operator-</a> (const Vector&lt; Vector&lt; T &gt;&gt; &amp;lhs, const Vector&lt; Vector&lt; U &gt;&gt; &amp;rhs)</td></tr>
<tr class="separator:ab5b0bf378a4a3d21f0a8f2992e7ee22a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad7f6ba4e83ad0bd4855e1717d0b8bb7e"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:ad7f6ba4e83ad0bd4855e1717d0b8bb7e"><td class="memTemplItemLeft" align="right" valign="top">Vector&lt; Expr &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#ad7f6ba4e83ad0bd4855e1717d0b8bb7e">qbpp::operator-</a> (const Vector&lt; T &gt; &amp;lhs, const Expr &amp;rhs)</td></tr>
<tr class="separator:ad7f6ba4e83ad0bd4855e1717d0b8bb7e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a62019ec8932c66515d0a6cfc29574c89"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:a62019ec8932c66515d0a6cfc29574c89"><td class="memTemplItemLeft" align="right" valign="top">Vector&lt; Expr &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a62019ec8932c66515d0a6cfc29574c89">qbpp::operator-</a> (Vector&lt; T &gt; &amp;&amp;lhs, const Expr &amp;rhs)</td></tr>
<tr class="separator:a62019ec8932c66515d0a6cfc29574c89"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac92ccdd64906f0ccf399157a5105eeed"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:ac92ccdd64906f0ccf399157a5105eeed"><td class="memTemplItemLeft" align="right" valign="top">auto&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#ac92ccdd64906f0ccf399157a5105eeed">qbpp::operator-</a> (const Vector&lt; Vector&lt; T &gt;&gt; &amp;lhs, const Expr &amp;rhs)</td></tr>
<tr class="separator:ac92ccdd64906f0ccf399157a5105eeed"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aacee764ce4bd6b5bd3acb9849dae5f11"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:aacee764ce4bd6b5bd3acb9849dae5f11"><td class="memTemplItemLeft" align="right" valign="top">auto&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#aacee764ce4bd6b5bd3acb9849dae5f11">qbpp::operator-</a> (Vector&lt; Vector&lt; T &gt;&gt; &amp;&amp;lhs, const Expr &amp;rhs)</td></tr>
<tr class="separator:aacee764ce4bd6b5bd3acb9849dae5f11"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a24ce6e0871802b4a7766c11656fa9631"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:a24ce6e0871802b4a7766c11656fa9631"><td class="memTemplItemLeft" align="right" valign="top">auto&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a24ce6e0871802b4a7766c11656fa9631">qbpp::operator-</a> (const Expr &amp;lhs, const Vector&lt; T &gt; &amp;rhs)</td></tr>
<tr class="separator:a24ce6e0871802b4a7766c11656fa9631"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab890feb0f35fb223c635edcda6db251c"><td class="memTemplParams" colspan="2">template&lt;typename T , class U , typename std::enable_if&lt; std::is_convertible&lt; U, coeff_t &gt;::value, int &gt;::type  = 0&gt; </td></tr>
<tr class="memitem:ab890feb0f35fb223c635edcda6db251c"><td class="memTemplItemLeft" align="right" valign="top">auto&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#ab890feb0f35fb223c635edcda6db251c">qbpp::operator/</a> (const Vector&lt; T &gt; &amp;lhs, U rhs)</td></tr>
<tr class="separator:ab890feb0f35fb223c635edcda6db251c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8e946624e4655cb0cc133ad0e684c70c"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:a8e946624e4655cb0cc133ad0e684c70c"><td class="memTemplItemLeft" align="right" valign="top">auto&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a8e946624e4655cb0cc133ad0e684c70c">qbpp::operator+</a> (const Vector&lt; T &gt; &amp;lhs)</td></tr>
<tr class="separator:a8e946624e4655cb0cc133ad0e684c70c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:adfc10143e432cc4ece745f944ba2a137"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:adfc10143e432cc4ece745f944ba2a137"><td class="memTemplItemLeft" align="right" valign="top">auto&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#adfc10143e432cc4ece745f944ba2a137">qbpp::operator-</a> (const Vector&lt; T &gt; &amp;lhs)</td></tr>
<tr class="separator:adfc10143e432cc4ece745f944ba2a137"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a444e1f7dfbf971dc812d41a99f849ca0"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:a444e1f7dfbf971dc812d41a99f849ca0"><td class="memTemplItemLeft" align="right" valign="top">auto&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a444e1f7dfbf971dc812d41a99f849ca0">qbpp::operator==</a> (const Vector&lt; T &gt; &amp;lhs, energy_t rhs)</td></tr>
<tr class="separator:a444e1f7dfbf971dc812d41a99f849ca0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad4f17221a2d801ebd24a43384823c61e"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:ad4f17221a2d801ebd24a43384823c61e"><td class="memTemplItemLeft" align="right" valign="top">auto&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#ad4f17221a2d801ebd24a43384823c61e">qbpp::operator&lt;=</a> (energy_t lhs, const Vector&lt; T &gt; &amp;rhs)</td></tr>
<tr class="separator:ad4f17221a2d801ebd24a43384823c61e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a30a00d8df331b8dc8b0cab12a6d660cf"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:a30a00d8df331b8dc8b0cab12a6d660cf"><td class="memTemplItemLeft" align="right" valign="top">auto&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a30a00d8df331b8dc8b0cab12a6d660cf">qbpp::operator&lt;=</a> (Inf lhs, const Vector&lt; T &gt; &amp;rhs)</td></tr>
<tr class="separator:a30a00d8df331b8dc8b0cab12a6d660cf"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a42c498f2f7edcdea158abc9612db95de"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:a42c498f2f7edcdea158abc9612db95de"><td class="memTemplItemLeft" align="right" valign="top">auto&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a42c498f2f7edcdea158abc9612db95de">qbpp::operator&lt;=</a> (const std::pair&lt; energy_t, Vector&lt; T &gt;&gt; &amp;lhs, energy_t rhs)</td></tr>
<tr class="separator:a42c498f2f7edcdea158abc9612db95de"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a36a7b0cba4c2ab36b14e103822870e83"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:a36a7b0cba4c2ab36b14e103822870e83"><td class="memTemplItemLeft" align="right" valign="top">auto&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a36a7b0cba4c2ab36b14e103822870e83">qbpp::operator&lt;=</a> (const std::pair&lt; energy_t, Vector&lt; T &gt;&gt; &amp;lhs, Inf rhs)</td></tr>
<tr class="separator:a36a7b0cba4c2ab36b14e103822870e83"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a62a69b4c4ed9266dacf2b7bc00724d09"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:a62a69b4c4ed9266dacf2b7bc00724d09"><td class="memTemplItemLeft" align="right" valign="top">auto&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a62a69b4c4ed9266dacf2b7bc00724d09">qbpp::operator&lt;=</a> (const std::pair&lt; Inf, Vector&lt; T &gt;&gt; &amp;lhs, energy_t rhs)</td></tr>
<tr class="separator:a62a69b4c4ed9266dacf2b7bc00724d09"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:afd211eae73bd78e4bc9ae97fc327495e"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:afd211eae73bd78e4bc9ae97fc327495e"><td class="memTemplItemLeft" align="right" valign="top">auto&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#afd211eae73bd78e4bc9ae97fc327495e">qbpp::operator&lt;=</a> (energy_t lhs, const Vector&lt; Vector&lt; T &gt;&gt; &amp;rhs)</td></tr>
<tr class="separator:afd211eae73bd78e4bc9ae97fc327495e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac116327a1209437dec96c323baf2b0c0"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:ac116327a1209437dec96c323baf2b0c0"><td class="memTemplItemLeft" align="right" valign="top">auto&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#ac116327a1209437dec96c323baf2b0c0">qbpp::operator&lt;=</a> (const std::pair&lt; energy_t, Vector&lt; Vector&lt; T &gt;&gt;&gt; &amp;lhs, energy_t rhs)</td></tr>
<tr class="separator:ac116327a1209437dec96c323baf2b0c0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2e39c3f450de79858df557a628b0e8bf"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:a2e39c3f450de79858df557a628b0e8bf"><td class="memTemplItemLeft" align="right" valign="top">auto&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a2e39c3f450de79858df557a628b0e8bf">qbpp::operator&lt;=</a> (const std::pair&lt; energy_t, Vector&lt; Vector&lt; T &gt;&gt;&gt; &amp;lhs, Inf rhs)</td></tr>
<tr class="separator:a2e39c3f450de79858df557a628b0e8bf"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac332a5446690e71b3f23d4990fb46110"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:ac332a5446690e71b3f23d4990fb46110"><td class="memTemplItemLeft" align="right" valign="top">auto&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#ac332a5446690e71b3f23d4990fb46110">qbpp::operator&lt;=</a> (const std::pair&lt; Inf, Vector&lt; Vector&lt; T &gt;&gt;&gt; &amp;lhs, energy_t rhs)</td></tr>
<tr class="separator:ac332a5446690e71b3f23d4990fb46110"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa7311e85eb4a80742f90b6225a5ebde6"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:aa7311e85eb4a80742f90b6225a5ebde6"><td class="memTemplItemLeft" align="right" valign="top">Expr&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#aa7311e85eb4a80742f90b6225a5ebde6">qbpp::sum</a> (const T &amp;arg[[maybe_unused]])</td></tr>
<tr class="separator:aa7311e85eb4a80742f90b6225a5ebde6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:acd23df7887fd6024222eaf8ac8d3e84a"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:acd23df7887fd6024222eaf8ac8d3e84a"><td class="memTemplItemLeft" align="right" valign="top">Expr&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#acd23df7887fd6024222eaf8ac8d3e84a">qbpp::sum</a> (const Vector&lt; Vector&lt; T &gt;&gt; &amp;arg[[maybe_unused]])</td></tr>
<tr class="separator:acd23df7887fd6024222eaf8ac8d3e84a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a53957f0090d3aab0d81c3141c321ad2a"><td class="memItemLeft" align="right" valign="top">Expr&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a53957f0090d3aab0d81c3141c321ad2a">qbpp::sum</a> (const Vector&lt; Var &gt; &amp;vars)</td></tr>
<tr class="separator:a53957f0090d3aab0d81c3141c321ad2a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a852d3ca4caf367d8475ccb839f021557"><td class="memItemLeft" align="right" valign="top">Expr&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a852d3ca4caf367d8475ccb839f021557">qbpp::sum</a> (const Vector&lt; Expr &gt; &amp;expr)</td></tr>
<tr class="separator:a852d3ca4caf367d8475ccb839f021557"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae7d9881501e697d70678c1782afccaea"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:ae7d9881501e697d70678c1782afccaea"><td class="memTemplItemLeft" align="right" valign="top">Expr&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#ae7d9881501e697d70678c1782afccaea">qbpp::total_sum_impl</a> (const T &amp;item)</td></tr>
<tr class="separator:ae7d9881501e697d70678c1782afccaea"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad2b6fc4c0549f9535eaad97fe6c40e4e"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:ad2b6fc4c0549f9535eaad97fe6c40e4e"><td class="memTemplItemLeft" align="right" valign="top">Expr&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#ad2b6fc4c0549f9535eaad97fe6c40e4e">qbpp::total_sum_impl</a> (const Vector&lt; T &gt; &amp;items)</td></tr>
<tr class="separator:ad2b6fc4c0549f9535eaad97fe6c40e4e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2733233894731d32bf82bd70ed665a2a"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:a2733233894731d32bf82bd70ed665a2a"><td class="memTemplItemLeft" align="right" valign="top"><a class="el" href="classqbpp_1_1Expr.html">qbpp::Expr</a>&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a2733233894731d32bf82bd70ed665a2a">qbpp::total_sum</a> (const T &amp;arg[[maybe_unused]])</td></tr>
<tr class="separator:a2733233894731d32bf82bd70ed665a2a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ada72da121769827fbb6febcd7e42e391"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:ada72da121769827fbb6febcd7e42e391"><td class="memTemplItemLeft" align="right" valign="top"><a class="el" href="classqbpp_1_1Expr.html">qbpp::Expr</a>&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#ada72da121769827fbb6febcd7e42e391">qbpp::total_sum</a> (const Vector&lt; T &gt; &amp;arg[[maybe_unused]])</td></tr>
<tr class="separator:ada72da121769827fbb6febcd7e42e391"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a253092d9eb405392a07ad60df7c26be4"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:a253092d9eb405392a07ad60df7c26be4"><td class="memTemplItemLeft" align="right" valign="top">Expr&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a253092d9eb405392a07ad60df7c26be4">qbpp::total_sum</a> (const Vector&lt; Vector&lt; T &gt;&gt; &amp;items)</td></tr>
<tr class="separator:a253092d9eb405392a07ad60df7c26be4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a008af6dfb07e3e463ee6d83c547c3ff0"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:a008af6dfb07e3e463ee6d83c547c3ff0"><td class="memTemplItemLeft" align="right" valign="top">Expr&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a008af6dfb07e3e463ee6d83c547c3ff0">qbpp::vector_sum</a> (const T &amp;items[[maybe_unused]])</td></tr>
<tr class="separator:a008af6dfb07e3e463ee6d83c547c3ff0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5d8611ddfe317bdef96ec2f8bb42fcf3"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:a5d8611ddfe317bdef96ec2f8bb42fcf3"><td class="memTemplItemLeft" align="right" valign="top">Expr&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a5d8611ddfe317bdef96ec2f8bb42fcf3">qbpp::vector_sum</a> (const Vector&lt; T &gt; &amp;items[[maybe_unused]])</td></tr>
<tr class="separator:a5d8611ddfe317bdef96ec2f8bb42fcf3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a528c53cddc5cd6fca5f732a565121fa9"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:a528c53cddc5cd6fca5f732a565121fa9"><td class="memTemplItemLeft" align="right" valign="top">auto&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a528c53cddc5cd6fca5f732a565121fa9">qbpp::vector_sum_impl</a> (const T &amp;items)</td></tr>
<tr class="separator:a528c53cddc5cd6fca5f732a565121fa9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aabb752897db4cf9a74a0642a55ea30be"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:aabb752897db4cf9a74a0642a55ea30be"><td class="memTemplItemLeft" align="right" valign="top">auto&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#aabb752897db4cf9a74a0642a55ea30be">qbpp::vector_sum_impl</a> (const Vector&lt; T &gt; &amp;items)</td></tr>
<tr class="separator:aabb752897db4cf9a74a0642a55ea30be"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab0c5a61b184429e490214e35c089b1e9"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:ab0c5a61b184429e490214e35c089b1e9"><td class="memTemplItemLeft" align="right" valign="top">auto&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#ab0c5a61b184429e490214e35c089b1e9">qbpp::vector_sum_impl</a> (const Vector&lt; Vector&lt; T &gt;&gt; &amp;items)</td></tr>
<tr class="separator:ab0c5a61b184429e490214e35c089b1e9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a90d9c1b64e1512ed11d17fe052b89115"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:a90d9c1b64e1512ed11d17fe052b89115"><td class="memTemplItemLeft" align="right" valign="top">auto&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a90d9c1b64e1512ed11d17fe052b89115">qbpp::vector_sum</a> (const Vector&lt; Vector&lt; T &gt;&gt; &amp;items)</td></tr>
<tr class="separator:a90d9c1b64e1512ed11d17fe052b89115"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad0eb856966f34efefd49d26a089f5a4d"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:ad0eb856966f34efefd49d26a089f5a4d"><td class="memTemplItemLeft" align="right" valign="top">Expr&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#ad0eb856966f34efefd49d26a089f5a4d">qbpp::product</a> (const T &amp;arg[[maybe_unused]])</td></tr>
<tr class="separator:ad0eb856966f34efefd49d26a089f5a4d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a925544a45e8ba58c721dd48ab7b966ad"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:a925544a45e8ba58c721dd48ab7b966ad"><td class="memTemplItemLeft" align="right" valign="top">Expr&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a925544a45e8ba58c721dd48ab7b966ad">qbpp::product</a> (const Vector&lt; Vector&lt; T &gt;&gt; &amp;arg[[maybe_unused]])</td></tr>
<tr class="separator:a925544a45e8ba58c721dd48ab7b966ad"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac7e482be6e10c77f2c6734c5c77337a5"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:ac7e482be6e10c77f2c6734c5c77337a5"><td class="memTemplItemLeft" align="right" valign="top">Expr&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#ac7e482be6e10c77f2c6734c5c77337a5">qbpp::product</a> (const Vector&lt; T &gt; &amp;items)</td></tr>
<tr class="separator:ac7e482be6e10c77f2c6734c5c77337a5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:add07cae928b6584059b1276e52821836"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:add07cae928b6584059b1276e52821836"><td class="memTemplItemLeft" align="right" valign="top">Expr&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#add07cae928b6584059b1276e52821836">qbpp::total_product_impl</a> (const T &amp;item)</td></tr>
<tr class="separator:add07cae928b6584059b1276e52821836"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a01a03d608bbf2ba6be63203f0a3263b1"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:a01a03d608bbf2ba6be63203f0a3263b1"><td class="memTemplItemLeft" align="right" valign="top">Expr&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a01a03d608bbf2ba6be63203f0a3263b1">qbpp::total_product_impl</a> (const Vector&lt; T &gt; &amp;items)</td></tr>
<tr class="separator:a01a03d608bbf2ba6be63203f0a3263b1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab7c36bf877df8c8164450e96d5c4a2a4"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:ab7c36bf877df8c8164450e96d5c4a2a4"><td class="memTemplItemLeft" align="right" valign="top">Expr&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#ab7c36bf877df8c8164450e96d5c4a2a4">qbpp::total_product</a> (const Vector&lt; Vector&lt; T &gt;&gt; &amp;items)</td></tr>
<tr class="separator:ab7c36bf877df8c8164450e96d5c4a2a4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7c589f0c4bd359baa6ebaf0ad135a9f6"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:a7c589f0c4bd359baa6ebaf0ad135a9f6"><td class="memTemplItemLeft" align="right" valign="top">Expr&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a7c589f0c4bd359baa6ebaf0ad135a9f6">qbpp::total_product</a> (const T &amp;arg[[maybe_unused]])</td></tr>
<tr class="separator:a7c589f0c4bd359baa6ebaf0ad135a9f6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae232eee49d6bc829974d79d90845d58d"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:ae232eee49d6bc829974d79d90845d58d"><td class="memTemplItemLeft" align="right" valign="top">Expr&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#ae232eee49d6bc829974d79d90845d58d">qbpp::total_product</a> (const Vector&lt; T &gt; &amp;arg[[maybe_unused]])</td></tr>
<tr class="separator:ae232eee49d6bc829974d79d90845d58d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5d07d485d2f6f80cb1430593dbcccafb"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:a5d07d485d2f6f80cb1430593dbcccafb"><td class="memTemplItemLeft" align="right" valign="top">Expr&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a5d07d485d2f6f80cb1430593dbcccafb">qbpp::vector_product</a> (const T &amp;items[[maybe_unused]])</td></tr>
<tr class="separator:a5d07d485d2f6f80cb1430593dbcccafb"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac5648444b926fefdefc5a3401bb7835a"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:ac5648444b926fefdefc5a3401bb7835a"><td class="memTemplItemLeft" align="right" valign="top">Expr&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#ac5648444b926fefdefc5a3401bb7835a">qbpp::vector_product</a> (const Vector&lt; T &gt; &amp;items[[maybe_unused]])</td></tr>
<tr class="separator:ac5648444b926fefdefc5a3401bb7835a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2e64e181c4cb360a3ee45c055802e029"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:a2e64e181c4cb360a3ee45c055802e029"><td class="memTemplItemLeft" align="right" valign="top">auto&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a2e64e181c4cb360a3ee45c055802e029">qbpp::vector_product_impl</a> (const T &amp;items)</td></tr>
<tr class="separator:a2e64e181c4cb360a3ee45c055802e029"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac042ddd28120930edb2d42bf33787109"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:ac042ddd28120930edb2d42bf33787109"><td class="memTemplItemLeft" align="right" valign="top">auto&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#ac042ddd28120930edb2d42bf33787109">qbpp::vector_product_impl</a> (const Vector&lt; T &gt; &amp;items)</td></tr>
<tr class="separator:ac042ddd28120930edb2d42bf33787109"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aac375cd21a0d470656dd274c14e154cd"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:aac375cd21a0d470656dd274c14e154cd"><td class="memTemplItemLeft" align="right" valign="top">auto&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#aac375cd21a0d470656dd274c14e154cd">qbpp::vector_product_impl</a> (const Vector&lt; Vector&lt; T &gt;&gt; &amp;items)</td></tr>
<tr class="separator:aac375cd21a0d470656dd274c14e154cd"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2290f2941de6dca0071a113ebd245145"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:a2290f2941de6dca0071a113ebd245145"><td class="memTemplItemLeft" align="right" valign="top">auto&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a2290f2941de6dca0071a113ebd245145">qbpp::vector_product</a> (const Vector&lt; Vector&lt; T &gt;&gt; &amp;items)</td></tr>
<tr class="separator:a2290f2941de6dca0071a113ebd245145"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a52854218b14a58db1c7a1660137c5375"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:a52854218b14a58db1c7a1660137c5375"><td class="memTemplItemLeft" align="right" valign="top">Expr&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a52854218b14a58db1c7a1660137c5375">qbpp::diff</a> (const Vector&lt; T &gt; &amp;items, energy_t head=0, energy_t tail=0)</td></tr>
<tr class="separator:a52854218b14a58db1c7a1660137c5375"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7acdf54490c098f0a92aa2884a4fe310"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:a7acdf54490c098f0a92aa2884a4fe310"><td class="memTemplItemLeft" align="right" valign="top">auto&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a7acdf54490c098f0a92aa2884a4fe310">qbpp::diff</a> (const Vector&lt; Vector&lt; T &gt;&gt; &amp;items, energy_t head=0, energy_t tail=0)</td></tr>
<tr class="separator:a7acdf54490c098f0a92aa2884a4fe310"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae112d85a9bed2bf373934acae380c874"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:ae112d85a9bed2bf373934acae380c874"><td class="memTemplItemLeft" align="right" valign="top">auto&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#ae112d85a9bed2bf373934acae380c874">qbpp::toInt</a> (const Vector&lt; T &gt; &amp;arg)</td></tr>
<tr class="separator:ae112d85a9bed2bf373934acae380c874"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8d661583b47e9b7925281d8e1c7c34e2"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:a8d661583b47e9b7925281d8e1c7c34e2"><td class="memTemplItemLeft" align="right" valign="top">Vector&lt; energy_t &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a8d661583b47e9b7925281d8e1c7c34e2">qbpp::eval_var_val_map</a> (const Vector&lt; T &gt; &amp;arg, const VarValMap &amp;var_val_map)</td></tr>
<tr class="separator:a8d661583b47e9b7925281d8e1c7c34e2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af71d58fe757f0d11540bd0347e4f483c"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:af71d58fe757f0d11540bd0347e4f483c"><td class="memTemplItemLeft" align="right" valign="top">Vector&lt; energy_t &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#af71d58fe757f0d11540bd0347e4f483c">qbpp::eval</a> (const Vector&lt; T &gt; &amp;arg, const MapList &amp;map_list)</td></tr>
<tr class="separator:af71d58fe757f0d11540bd0347e4f483c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac9e3973b3491ef68740472a27b14def8"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:ac9e3973b3491ef68740472a27b14def8"><td class="memTemplItemLeft" align="right" valign="top">auto&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#ac9e3973b3491ef68740472a27b14def8">qbpp::eval_var_val_map</a> (const Vector&lt; Vector&lt; T &gt;&gt; &amp;arg, const VarValMap &amp;var_val_map)</td></tr>
<tr class="separator:ac9e3973b3491ef68740472a27b14def8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a4b293e37097f0bad091e7fcd76aa6016"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:a4b293e37097f0bad091e7fcd76aa6016"><td class="memTemplItemLeft" align="right" valign="top">auto&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a4b293e37097f0bad091e7fcd76aa6016">qbpp::eval</a> (const Vector&lt; Vector&lt; T &gt;&gt; &amp;arg, const MapList &amp;map_list)</td></tr>
<tr class="separator:a4b293e37097f0bad091e7fcd76aa6016"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7f95a13d75087651ef3476dd1712e255"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:a7f95a13d75087651ef3476dd1712e255"><td class="memTemplItemLeft" align="right" valign="top">Vector&lt; Expr &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a7f95a13d75087651ef3476dd1712e255">qbpp::replace</a> (const Vector&lt; T &gt; &amp;arg, const MapList &amp;map_list)</td></tr>
<tr class="separator:a7f95a13d75087651ef3476dd1712e255"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a45adc5201c42b24d17beffaf1531b3c0"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:a45adc5201c42b24d17beffaf1531b3c0"><td class="memTemplItemLeft" align="right" valign="top">auto&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a45adc5201c42b24d17beffaf1531b3c0">qbpp::replace</a> (const Vector&lt; Vector&lt; T &gt;&gt; &amp;arg, const MapList &amp;map_list)</td></tr>
<tr class="separator:a45adc5201c42b24d17beffaf1531b3c0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3ef6a17c9562b454351f51950aab4392"><td class="memTemplParams" colspan="2">template&lt;typename T , typename F &gt; </td></tr>
<tr class="memitem:a3ef6a17c9562b454351f51950aab4392"><td class="memTemplItemLeft" align="right" valign="top">auto&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a3ef6a17c9562b454351f51950aab4392">qbpp::element_wise</a> (const Vector&lt; T &gt; &amp;arg, F func)</td></tr>
<tr class="separator:a3ef6a17c9562b454351f51950aab4392"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6aba041bba1df7d99d41703115fc4da3"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:a6aba041bba1df7d99d41703115fc4da3"><td class="memTemplItemLeft" align="right" valign="top">Vector&lt; Expr &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a6aba041bba1df7d99d41703115fc4da3">qbpp::reduce</a> (const Vector&lt; T &gt; &amp;arg)</td></tr>
<tr class="separator:a6aba041bba1df7d99d41703115fc4da3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a22773d3d3891d1859780eb828735ea3d"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:a22773d3d3891d1859780eb828735ea3d"><td class="memTemplItemLeft" align="right" valign="top">auto&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a22773d3d3891d1859780eb828735ea3d">qbpp::reduce</a> (const Vector&lt; Vector&lt; T &gt;&gt; &amp;arg)</td></tr>
<tr class="separator:a22773d3d3891d1859780eb828735ea3d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a481905d7a1c024060ed5610590af9b9d"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:a481905d7a1c024060ed5610590af9b9d"><td class="memTemplItemLeft" align="right" valign="top">coeff_t&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a481905d7a1c024060ed5610590af9b9d">qbpp::gcd</a> (const Vector&lt; T &gt; &amp;vec)</td></tr>
<tr class="separator:a481905d7a1c024060ed5610590af9b9d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:afc344ea88cf7416a6827b5cec17838df"><td class="memItemLeft" align="right" valign="top">int32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#afc344ea88cf7416a6827b5cec17838df">qbpp::onehot_to_int</a> (const Vector&lt; var_val_t &gt; &amp;vec)</td></tr>
<tr class="separator:afc344ea88cf7416a6827b5cec17838df"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a050514e46b9d1b1a9e1cae4d37a8e63f"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:a050514e46b9d1b1a9e1cae4d37a8e63f"><td class="memTemplItemLeft" align="right" valign="top">auto&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a050514e46b9d1b1a9e1cae4d37a8e63f">qbpp::onehot_to_int</a> (const Vector&lt; Vector&lt; T &gt;&gt; &amp;vec)</td></tr>
<tr class="separator:a050514e46b9d1b1a9e1cae4d37a8e63f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a4c0219233e0bdbb8aa860943e5876004"><td class="memItemLeft" align="right" valign="top">Term&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a4c0219233e0bdbb8aa860943e5876004">qbpp::operator*</a> (Term &amp;&amp;term1, const Term &amp;term2)</td></tr>
<tr class="separator:a4c0219233e0bdbb8aa860943e5876004"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aeba24be34d3ade9c60d41911472ce4e6"><td class="memItemLeft" align="right" valign="top">Term&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#aeba24be34d3ade9c60d41911472ce4e6">qbpp::operator*</a> (const Term &amp;term1, Term &amp;&amp;term2)</td></tr>
<tr class="separator:aeba24be34d3ade9c60d41911472ce4e6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5e5ebf4f0a712dc724906a38b9ed1ed3"><td class="memItemLeft" align="right" valign="top">Term&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a5e5ebf4f0a712dc724906a38b9ed1ed3">qbpp::operator*</a> (Term &amp;&amp;term1, Term &amp;&amp;term2)</td></tr>
<tr class="separator:a5e5ebf4f0a712dc724906a38b9ed1ed3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad031a05cdcfce3df0ae410a6a89ef045"><td class="memItemLeft" align="right" valign="top">std::tuple&lt; bool, size_t, size_t, coeff_t, coeff_t &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#ad031a05cdcfce3df0ae410a6a89ef045">qbpp::check_if_simplified_as_binary</a> (const Expr &amp;expr)</td></tr>
<tr class="separator:ad031a05cdcfce3df0ae410a6a89ef045"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7f7f438ebf6b13425cb0e91fad6e7427"><td class="memItemLeft" align="right" valign="top">Vector&lt; Expr &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a7f7f438ebf6b13425cb0e91fad6e7427">qbpp::operator*</a> (const Vector&lt; ExprExpr &gt; &amp;arg)</td></tr>
<tr class="separator:a7f7f438ebf6b13425cb0e91fad6e7427"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a37a6d72d288955175d1290a07aaab790"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:a37a6d72d288955175d1290a07aaab790"><td class="memTemplItemLeft" align="right" valign="top">auto&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a37a6d72d288955175d1290a07aaab790">qbpp::operator*</a> (const Vector&lt; Vector&lt; T &gt;&gt; &amp;arg)</td></tr>
<tr class="separator:a37a6d72d288955175d1290a07aaab790"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a9be9a5a724868c774b476067f536b899"><td class="memItemLeft" align="right" valign="top">Vector&lt; Expr &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a9be9a5a724868c774b476067f536b899">qbpp::operator*</a> (const Vector&lt; Var &gt; &amp;lhs, const Vector&lt; Var &gt; &amp;rhs)</td></tr>
<tr class="separator:a9be9a5a724868c774b476067f536b899"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="var-members"></a>
Variables</h2></td></tr>
<tr class="memitem:a0a9efb4a220eaef22429d00be02aeee2"><td class="memItemLeft" align="right" valign="top">constexpr size_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a0a9efb4a220eaef22429d00be02aeee2">qbpp::TERM_CAPACITY</a> = 4</td></tr>
<tr class="separator:a0a9efb4a220eaef22429d00be02aeee2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac9ce864e91548a7dbda1d8928d117df2"><td class="memItemLeft" align="right" valign="top">constexpr size_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#ac9ce864e91548a7dbda1d8928d117df2">qbpp::SEQ_THRESHOLD</a> = 1000</td></tr>
<tr class="separator:ac9ce864e91548a7dbda1d8928d117df2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac0b50505a1738411ca7c96487b296234"><td class="memItemLeft" align="right" valign="top">const vindex_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#ac0b50505a1738411ca7c96487b296234">qbpp::vindex_limit</a> = std::numeric_limits&lt;vindex_t&gt;::max()</td></tr>
<tr class="separator:ac0b50505a1738411ca7c96487b296234"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a08fb38f7fe1cd148d022d85d35f6d2c0"><td class="memItemLeft" align="right" valign="top">const Inf&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html#a08fb38f7fe1cd148d022d85d35f6d2c0">qbpp::inf</a></td></tr>
<tr class="separator:a08fb38f7fe1cd148d022d85d35f6d2c0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6211ad95928770b578ee164151c3c4c3"><td class="memItemLeft" align="right" valign="top">const Var&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp_1_1impl.html#a6211ad95928770b578ee164151c3c4c3">qbpp::impl::VarVoid</a> {<a class="el" href="namespaceqbpp.html#ac0b50505a1738411ca7c96487b296234">qbpp::vindex_limit</a>}</td></tr>
<tr class="separator:a6211ad95928770b578ee164151c3c4c3"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p>QUBO++, a C++ library for generating expressions for binary and spin variables. </p>
<p>This library provides classes and functions for generating and manipulating expressions and polynomials for binary and spin variables. The library is designed to be simple and easy to use. </p><dl class="section note"><dt>Note</dt><dd>Only for non-commercial use, evaluation, and research purposes with no warranties. Redistribution is strictly prohibited. This software includes components from Intel® Threading Building Blocks (oneTBB), licensed under the Apache License 2.0. </dd></dl>
<dl class="section author"><dt>Author</dt><dd>Koji Nakano </dd></dl>
<dl class="section copyright"><dt>Copyright</dt><dd>2025, Koji Nakano </dd></dl>
<dl class="section version"><dt>Version</dt><dd>2025-05-12 </dd></dl>

<p class="definition">Definition in file <a class="el" href="qbpp_8hpp_source.html">qbpp.hpp</a>.</p>
</div><h2 class="groupheader">Macro Definition Documentation</h2>
<a id="a1c6d5de492ac61ad29aec7aa9a436bbf"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a1c6d5de492ac61ad29aec7aa9a436bbf">&#9670;&nbsp;</a></span>VERSION</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define VERSION&#160;&#160;&#160;&quot;2025.05.12&quot;</td>
        </tr>
      </table>
</div><div class="memdoc">

<p class="definition">Definition at line <a class="el" href="qbpp_8hpp_source.html#l00018">18</a> of file <a class="el" href="qbpp_8hpp_source.html">qbpp.hpp</a>.</p>

</div>
</div>
<a id="a265e0828fcea8e75fbc9c0be99f7156e"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a265e0828fcea8e75fbc9c0be99f7156e">&#9670;&nbsp;</a></span>THROW_MESSAGE</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define THROW_MESSAGE</td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>...</em></td><td>)</td>
          <td>&#160;&#160;&#160;file_line(__FILE__, __LINE__, __VA_ARGS__)</td>
        </tr>
      </table>
</div><div class="memdoc">

<p class="definition">Definition at line <a class="el" href="qbpp_8hpp_source.html#l00091">91</a> of file <a class="el" href="qbpp_8hpp_source.html">qbpp.hpp</a>.</p>

</div>
</div>
<a id="a8521f0d9a069c55226a07fc055408eed"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a8521f0d9a069c55226a07fc055408eed">&#9670;&nbsp;</a></span>MAXDEG</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define MAXDEG&#160;&#160;&#160;0</td>
        </tr>
      </table>
</div><div class="memdoc">

<p class="definition">Definition at line <a class="el" href="qbpp_8hpp_source.html#l00099">99</a> of file <a class="el" href="qbpp_8hpp_source.html">qbpp.hpp</a>.</p>

</div>
</div>
<a id="a3615f8dba24054e0aaa8964d31d2eb24"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a3615f8dba24054e0aaa8964d31d2eb24">&#9670;&nbsp;</a></span>COEFF_TYPE</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define COEFF_TYPE&#160;&#160;&#160;int32_t</td>
        </tr>
      </table>
</div><div class="memdoc">

<p class="definition">Definition at line <a class="el" href="qbpp_8hpp_source.html#l00122">122</a> of file <a class="el" href="qbpp_8hpp_source.html">qbpp.hpp</a>.</p>

</div>
</div>
<a id="a47975cf459e9fa687601c8f30c9848c0"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a47975cf459e9fa687601c8f30c9848c0">&#9670;&nbsp;</a></span>ENERGY_TYPE</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define ENERGY_TYPE&#160;&#160;&#160;int64_t</td>
        </tr>
      </table>
</div><div class="memdoc">

<p class="definition">Definition at line <a class="el" href="qbpp_8hpp_source.html#l00126">126</a> of file <a class="el" href="qbpp_8hpp_source.html">qbpp.hpp</a>.</p>

</div>
</div>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.17
</small></address>
</body>
</html>
