<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.17"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>QUBO++ Library with QUBO Solver APIs: qbpp::impl::var_hash Struct Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/x-mathjax-config">
  MathJax.Hub.Config({
    extensions: ["tex2jax.js", "TeX/AMSmath.js", "TeX/AMSsymbols.js"],
    jax: ["input/TeX","output/HTML-CSS"],
});
</script>
<script type="text/javascript" async="async" src="https://cdn.jsdelivr.net/npm/mathjax@2/MathJax.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">QUBO++ Library with QUBO Solver APIs
   </div>
   <div id="projectbrief">Author: Koji Nakano, License: Non-commercial research and evaluation purposes without any guarantees.</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.17 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="namespaceqbpp.html">qbpp</a></li><li class="navelem"><a class="el" href="namespaceqbpp_1_1impl.html">impl</a></li><li class="navelem"><a class="el" href="structqbpp_1_1impl_1_1var__hash.html">var_hash</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#pub-methods">Public Member Functions</a> &#124;
<a href="structqbpp_1_1impl_1_1var__hash-members.html">List of all members</a>  </div>
  <div class="headertitle">
<div class="title">qbpp::impl::var_hash Struct Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p><code>#include &lt;<a class="el" href="qbpp_8hpp_source.html">qbpp.hpp</a>&gt;</code></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-methods"></a>
Public Member Functions</h2></td></tr>
<tr class="memitem:aea5125d36f23522f113180453b2db3b8"><td class="memItemLeft" align="right" valign="top">size_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structqbpp_1_1impl_1_1var__hash.html#aea5125d36f23522f113180453b2db3b8">operator()</a> (const <a class="el" href="classqbpp_1_1Var.html">Var</a> <a class="el" href="namespaceqbpp.html#a1cf3f678bcc7eee089c75d4bc5a0b959">var</a>) const</td></tr>
<tr class="separator:aea5125d36f23522f113180453b2db3b8"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock">
<p class="definition">Definition at line <a class="el" href="qbpp_8hpp_source.html#l00153">153</a> of file <a class="el" href="qbpp_8hpp_source.html">qbpp.hpp</a>.</p>
</div><h2 class="groupheader">Member Function Documentation</h2>
<a id="aea5125d36f23522f113180453b2db3b8"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aea5125d36f23522f113180453b2db3b8">&#9670;&nbsp;</a></span>operator()()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">size_t qbpp::impl::var_hash::operator() </td>
          <td>(</td>
          <td class="paramtype">const <a class="el" href="classqbpp_1_1Var.html">Var</a>&#160;</td>
          <td class="paramname"><em>var</em></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p class="definition">Definition at line <a class="el" href="qbpp_8hpp_source.html#l02731">2731</a> of file <a class="el" href="qbpp_8hpp_source.html">qbpp.hpp</a>.</p>
<div class="dynheader">
Here is the call graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="structqbpp_1_1impl_1_1var__hash_aea5125d36f23522f113180453b2db3b8_cgraph.png" border="0" usemap="#structqbpp_1_1impl_1_1var__hash_aea5125d36f23522f113180453b2db3b8_cgraph" alt=""/></div>
<map name="structqbpp_1_1impl_1_1var__hash_aea5125d36f23522f113180453b2db3b8_cgraph" id="structqbpp_1_1impl_1_1var__hash_aea5125d36f23522f113180453b2db3b8_cgraph">
<area shape="rect" title=" " alt="" coords="5,23,148,65"/>
<area shape="rect" href="classqbpp_1_1Var.html#ac7b09bf9026a6082d8d6d63201610c24" title=" " alt="" coords="196,5,312,32"/>
<area shape="rect" href="namespaceqbpp.html#a1cf3f678bcc7eee089c75d4bc5a0b959" title=" " alt="" coords="216,56,292,83"/>
<area shape="rect" href="namespaceqbpp.html#a844402a465157e00bca032af58c0a795" title=" " alt="" coords="360,56,436,83"/>
</map>
</div>

</div>
</div>
<hr/>The documentation for this struct was generated from the following file:<ul>
<li>include/<a class="el" href="qbpp_8hpp_source.html">qbpp.hpp</a></li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.17
</small></address>
</body>
</html>
