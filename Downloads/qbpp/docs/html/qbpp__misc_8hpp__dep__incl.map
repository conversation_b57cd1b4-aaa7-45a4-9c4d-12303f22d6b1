<map id="include/qbpp_misc.hpp" name="include/qbpp_misc.hpp">
<area shape="rect" id="node1" title="A miscellaneous library used for sample programs of the QUBO++ library." alt="" coords="1227,5,1382,32"/>
<area shape="rect" id="node2" href="$qbpp__easy__solver_8hpp.html" title="Easy QUBO Solver for solving QUBO problems." alt="" coords="1027,80,1223,107"/>
<area shape="rect" id="node4" href="$partition__easy_8cpp.html" title="Solves the Partitioning problem using the QUBO++ Easy solver." alt="" coords="481,162,653,189"/>
<area shape="rect" id="node5" href="$graph__color__easy_8cpp.html" title="Solves randomly generated Graph Coloring Problem using QUBO++ Easy Solver." alt="" coords="323,155,457,196"/>
<area shape="rect" id="node7" href="$tsp__easy_8cpp.html" title="Solves randomly generated Traveling Salesman Problem (TSP) using the QUBO++ Easy solver." alt="" coords="1591,162,1735,189"/>
<area shape="rect" id="node9" href="$factorization_8cpp.html" title="Solves factorization and multiplication problems for two prime numbers using multiple QUBO solvers." alt="" coords="1403,162,1567,189"/>
<area shape="rect" id="node11" href="$partition__exhaustive_8cpp.html" title="Solves the Partitioning problem using the QUBO++ Exhaustive solver." alt="" coords="1863,80,2072,107"/>
<area shape="rect" id="node12" href="$qbpp__graph__color_8hpp.html" title=" " alt="" coords="166,80,361,107"/>
<area shape="rect" id="node13" href="$graph__color__abs2_8cpp.html" title="Solves randomly generated Graph Coloring Problem using ABS2 QUBO Solver." alt="" coords="5,155,140,196"/>
<area shape="rect" id="node14" href="$graph__color__grb_8cpp.html" title="Solves randomly generated Graph Coloring Problem using QUBO++ Gurobi Optimizer." alt="" coords="164,155,299,196"/>
<area shape="rect" id="node15" href="$qbpp__tsp_8hpp.html" title="Generates a QUBO Expression for the Traveling Salesman Problem (TSP) using QUBO++ library." alt="" coords="1590,80,1737,107"/>
<area shape="rect" id="node16" href="$tsp__abs2_8cpp.html" title="Solves randomly generated Traveling Salesman Problem (TSP) using the ABS2 QUBO solver." alt="" coords="1759,162,1906,189"/>
<area shape="rect" id="node17" href="$tsp__grb_8cpp.html" title="Solves randomly generated Traveling Salesman Problem (TSP) using the QUBO++ Easy solver." alt="" coords="1930,162,2066,189"/>
<area shape="rect" id="node3" href="$nqueen__easy_8cpp.html" title="Solves the N&#45;Queens problem using ABS2 QUBO solver from QUBO++ library." alt="" coords="678,162,846,189"/>
<area shape="rect" id="node6" href="$ilp__easy_8cpp.html" title="Solves an Integer Linear Programming (ILP) problem using EasySolver through QUBO++ library." alt="" coords="870,162,1009,189"/>
<area shape="rect" id="node8" href="$simple__factorization__easy_8cpp.html" title="Simple factorization example using EasySolver." alt="" coords="1033,155,1217,196"/>
<area shape="rect" id="node10" href="$bin__packing__easy_8cpp.html" title="This file contains an example of solving the bin packing problem using the EasySolver." alt="" coords="1241,155,1379,196"/>
</map>
