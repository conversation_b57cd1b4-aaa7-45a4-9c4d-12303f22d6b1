<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.17"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>QUBO++ Library with QUBO Solver APIs: Member List</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/x-mathjax-config">
  MathJax.Hub.Config({
    extensions: ["tex2jax.js", "TeX/AMSmath.js", "TeX/AMSsymbols.js"],
    jax: ["input/TeX","output/HTML-CSS"],
});
</script>
<script type="text/javascript" async="async" src="https://cdn.jsdelivr.net/npm/mathjax@2/MathJax.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">QUBO++ Library with QUBO Solver APIs
   </div>
   <div id="projectbrief">Author: Koji Nakano, License: Non-commercial research and evaluation purposes without any guarantees.</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.17 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="namespaceqbpp.html">qbpp</a></li><li class="navelem"><a class="el" href="classqbpp_1_1QuadModel.html">QuadModel</a></li><li class="navelem"><a class="el" href="structqbpp_1_1QuadModel_1_1Impl.html">Impl</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">qbpp::QuadModel::Impl Member List</div>  </div>
</div><!--header-->
<div class="contents">

<p>This is the complete list of members for <a class="el" href="structqbpp_1_1QuadModel_1_1Impl.html">qbpp::QuadModel::Impl</a>, including all inherited members.</p>
<table class="directory">
  <tr class="even"><td class="entry"><a class="el" href="structqbpp_1_1QuadModel_1_1Impl.html#a09edd4e6be81432397b42973a76dd7f3">degree_</a></td><td class="entry"><a class="el" href="structqbpp_1_1QuadModel_1_1Impl.html">qbpp::QuadModel::Impl</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="structqbpp_1_1QuadModel_1_1Impl.html#a17282586c31a1881bd2228a3111c2aa8">Impl</a>(const qbpp::Model &amp;model)</td><td class="entry"><a class="el" href="structqbpp_1_1QuadModel_1_1Impl.html">qbpp::QuadModel::Impl</a></td><td class="entry"><span class="mlabel">inline</span><span class="mlabel">explicit</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="structqbpp_1_1QuadModel_1_1Impl.html#a1e0ab4d676896f9c9c7cdee144e667be">Impl</a>(const qbpp::Model &amp;model, const impl::IndexVarMapper &amp;index_var_mapper)</td><td class="entry"><a class="el" href="structqbpp_1_1QuadModel_1_1Impl.html">qbpp::QuadModel::Impl</a></td><td class="entry"><span class="mlabel">explicit</span></td></tr>
  <tr><td class="entry"><a class="el" href="structqbpp_1_1QuadModel_1_1Impl.html#a9762212f8ea544e98de6c04ad7b99d6b">linear_</a></td><td class="entry"><a class="el" href="structqbpp_1_1QuadModel_1_1Impl.html">qbpp::QuadModel::Impl</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="structqbpp_1_1QuadModel_1_1Impl.html#a34749366bee5f8bf43ace5920da19118">max_coeff_</a></td><td class="entry"><a class="el" href="structqbpp_1_1QuadModel_1_1Impl.html">qbpp::QuadModel::Impl</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="structqbpp_1_1QuadModel_1_1Impl.html#a5f449e8313c39b1def37a82122b50301">min_coeff_</a></td><td class="entry"><a class="el" href="structqbpp_1_1QuadModel_1_1Impl.html">qbpp::QuadModel::Impl</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="structqbpp_1_1QuadModel_1_1Impl.html#acacff3c9bbfad326f53e895915f57c6b">quadratic_</a></td><td class="entry"><a class="el" href="structqbpp_1_1QuadModel_1_1Impl.html">qbpp::QuadModel::Impl</a></td><td class="entry"></td></tr>
</table></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.17
</small></address>
</body>
</html>
