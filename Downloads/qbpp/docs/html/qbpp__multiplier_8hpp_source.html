<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.17"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>QUBO++ Library with QUBO Solver APIs: sample/qbpp_multiplier.hpp Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/x-mathjax-config">
  MathJax.Hub.Config({
    extensions: ["tex2jax.js", "TeX/AMSmath.js", "TeX/AMSsymbols.js"],
    jax: ["input/TeX","output/HTML-CSS"],
});
</script>
<script type="text/javascript" async="async" src="https://cdn.jsdelivr.net/npm/mathjax@2/MathJax.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">QUBO++ Library with QUBO Solver APIs
   </div>
   <div id="projectbrief">Author: Koji Nakano, License: Non-commercial research and evaluation purposes without any guarantees.</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.17 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_c29eeb5af533606caea8a09e66794740.html">sample</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">qbpp_multiplier.hpp</div>  </div>
</div><!--header-->
<div class="contents">
<a href="qbpp__multiplier_8hpp.html">Go to the documentation of this file.</a><div class="fragment"><div class="line"><a name="l00001"></a><span class="lineno">    1</span>&#160; </div>
<div class="line"><a name="l00007"></a><span class="lineno">    7</span>&#160;<span class="preprocessor">#ifndef QBPP_MULTIPLIER_HPP</span></div>
<div class="line"><a name="l00008"></a><span class="lineno">    8</span>&#160;<span class="preprocessor">#define QBPP_MULTIPLIER_HPP</span></div>
<div class="line"><a name="l00009"></a><span class="lineno">    9</span>&#160; </div>
<div class="line"><a name="l00010"></a><span class="lineno">   10</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="qbpp_8hpp.html">qbpp.hpp</a>&quot;</span></div>
<div class="line"><a name="l00011"></a><span class="lineno">   11</span>&#160; </div>
<div class="line"><a name="l00012"></a><span class="lineno">   12</span>&#160;<span class="keyword">namespace </span><a class="code" href="namespaceqbpp.html">qbpp</a> {</div>
<div class="line"><a name="l00014"></a><span class="lineno"><a class="line" href="namespaceqbpp_1_1factorization.html">   14</a></span>&#160;<span class="keyword">namespace </span>factorization {</div>
<div class="line"><a name="l00015"></a><span class="lineno">   15</span>&#160; </div>
<div class="line"><a name="l00026"></a><span class="lineno"><a class="line" href="namespaceqbpp_1_1factorization.html#a02e68188402cc688e6ebe4cd9bff29f7">   26</a></span>&#160;<span class="keyword">inline</span> <a class="code" href="classqbpp_1_1Expr.html">Expr</a> <a class="code" href="namespaceqbpp_1_1factorization.html#a02e68188402cc688e6ebe4cd9bff29f7">and_gate</a>(<span class="keyword">const</span> <a class="code" href="classqbpp_1_1Expr.html">Expr</a> &amp;a, <span class="keyword">const</span> <a class="code" href="classqbpp_1_1Expr.html">Expr</a> &amp;b, <a class="code" href="classqbpp_1_1Var.html">Var</a> x, <a class="code" href="namespaceqbpp.html#aeea468454b74e8cf74539f77454bef6b">MapList</a> &amp;opt_sol) {</div>
<div class="line"><a name="l00027"></a><span class="lineno">   27</span>&#160;  opt_sol.push_back({x, <a class="code" href="namespaceqbpp.html#ab5da6d0960920098998bb7347082ee6b">eval</a>(a, opt_sol) &amp; <a class="code" href="namespaceqbpp.html#ab5da6d0960920098998bb7347082ee6b">eval</a>(b, opt_sol)});</div>
<div class="line"><a name="l00028"></a><span class="lineno">   28</span>&#160;  <span class="keywordflow">return</span> a * b - 2 * a * x - 2 * b * x + 3 * x;</div>
<div class="line"><a name="l00029"></a><span class="lineno">   29</span>&#160;}</div>
<div class="line"><a name="l00030"></a><span class="lineno">   30</span>&#160; </div>
<div class="line"><a name="l00043"></a><span class="lineno"><a class="line" href="namespaceqbpp_1_1factorization.html#aeb9af8fc3150bce913920c5dbbb83828">   43</a></span>&#160;<span class="keyword">inline</span> <a class="code" href="classqbpp_1_1Expr.html">Expr</a> <a class="code" href="namespaceqbpp_1_1factorization.html#aeb9af8fc3150bce913920c5dbbb83828">half_adder</a>(<span class="keyword">const</span> <a class="code" href="classqbpp_1_1Expr.html">Expr</a> &amp;x, <span class="keyword">const</span> <a class="code" href="classqbpp_1_1Expr.html">Expr</a> &amp;y, <a class="code" href="classqbpp_1_1Var.html">Var</a> s, <a class="code" href="classqbpp_1_1Var.html">Var</a> c,</div>
<div class="line"><a name="l00044"></a><span class="lineno">   44</span>&#160;                       <a class="code" href="namespaceqbpp.html#aeea468454b74e8cf74539f77454bef6b">MapList</a> &amp;opt_sol) {</div>
<div class="line"><a name="l00045"></a><span class="lineno">   45</span>&#160;  <span class="keyword">auto</span> val_x = <a class="code" href="namespaceqbpp.html#ab5da6d0960920098998bb7347082ee6b">eval</a>(x, opt_sol);</div>
<div class="line"><a name="l00046"></a><span class="lineno">   46</span>&#160;  <span class="keyword">auto</span> val_y = <a class="code" href="namespaceqbpp.html#ab5da6d0960920098998bb7347082ee6b">eval</a>(y, opt_sol);</div>
<div class="line"><a name="l00047"></a><span class="lineno">   47</span>&#160;  opt_sol.push_back({s, val_x ^ val_y});</div>
<div class="line"><a name="l00048"></a><span class="lineno">   48</span>&#160;  opt_sol.push_back({c, val_x &amp; val_y});</div>
<div class="line"><a name="l00049"></a><span class="lineno">   49</span>&#160;  <span class="keywordflow">return</span> s + x + y + 4 * c - 4 * c * x - 4 * c * y - 2 * s * x - 2 * s * y +</div>
<div class="line"><a name="l00050"></a><span class="lineno">   50</span>&#160;         2 * x * y + 4 * c * s;</div>
<div class="line"><a name="l00051"></a><span class="lineno">   51</span>&#160;}</div>
<div class="line"><a name="l00052"></a><span class="lineno">   52</span>&#160; </div>
<div class="line"><a name="l00066"></a><span class="lineno"><a class="line" href="namespaceqbpp_1_1factorization.html#af6b0ff4cf1a09dbca5f12e0e6f040023">   66</a></span>&#160;<span class="keyword">inline</span> <a class="code" href="classqbpp_1_1Expr.html">Expr</a> <a class="code" href="namespaceqbpp_1_1factorization.html#af6b0ff4cf1a09dbca5f12e0e6f040023">full_adder</a>(<span class="keyword">const</span> <a class="code" href="classqbpp_1_1Expr.html">Expr</a> &amp;x, <span class="keyword">const</span> <a class="code" href="classqbpp_1_1Expr.html">Expr</a> &amp;y, <span class="keyword">const</span> <a class="code" href="classqbpp_1_1Expr.html">Expr</a> &amp;z, <a class="code" href="classqbpp_1_1Var.html">Var</a> s,</div>
<div class="line"><a name="l00067"></a><span class="lineno">   67</span>&#160;                       <a class="code" href="classqbpp_1_1Var.html">Var</a> c, <a class="code" href="namespaceqbpp.html#aeea468454b74e8cf74539f77454bef6b">MapList</a> &amp;opt_sol) {</div>
<div class="line"><a name="l00068"></a><span class="lineno">   68</span>&#160;  <span class="keyword">auto</span> val_x = <a class="code" href="namespaceqbpp.html#ab5da6d0960920098998bb7347082ee6b">eval</a>(x, opt_sol);</div>
<div class="line"><a name="l00069"></a><span class="lineno">   69</span>&#160;  <span class="keyword">auto</span> val_y = <a class="code" href="namespaceqbpp.html#ab5da6d0960920098998bb7347082ee6b">eval</a>(y, opt_sol);</div>
<div class="line"><a name="l00070"></a><span class="lineno">   70</span>&#160;  <span class="keyword">auto</span> val_z = <a class="code" href="namespaceqbpp.html#ab5da6d0960920098998bb7347082ee6b">eval</a>(z, opt_sol);</div>
<div class="line"><a name="l00071"></a><span class="lineno">   71</span>&#160;  opt_sol.push_back({s, val_x ^ val_y ^ val_z});</div>
<div class="line"><a name="l00072"></a><span class="lineno">   72</span>&#160;  opt_sol.push_back({c, (val_x &amp; val_y) | (val_y &amp; val_z) | (val_z &amp; val_x)});</div>
<div class="line"><a name="l00073"></a><span class="lineno">   73</span>&#160;  <span class="keywordflow">return</span> s + x + y + z + 4 * c - 4 * c * x - 4 * c * y - 4 * c * z - 2 * s * x -</div>
<div class="line"><a name="l00074"></a><span class="lineno">   74</span>&#160;         2 * s * y - 2 * s * z + 2 * x * y + 2 * x * z + 2 * y * z + 4 * c * s;</div>
<div class="line"><a name="l00075"></a><span class="lineno">   75</span>&#160;}</div>
<div class="line"><a name="l00076"></a><span class="lineno">   76</span>&#160; </div>
<div class="line"><a name="l00094"></a><span class="lineno">   94</span>&#160;<span class="keyword">template</span> &lt;<span class="keyword">typename</span> T, <span class="keyword">typename</span> U&gt;</div>
<div class="line"><a name="l00095"></a><span class="lineno"><a class="line" href="namespaceqbpp_1_1factorization.html#a1e5c7d80129600dc5345d9e9dd5602d9">   95</a></span>&#160;<a class="code" href="classqbpp_1_1Expr.html">Expr</a> <a class="code" href="namespaceqbpp_1_1factorization.html#a1e5c7d80129600dc5345d9e9dd5602d9">adder</a>(<span class="keyword">const</span> <a class="code" href="classqbpp_1_1Vector.html">qbpp::Vector&lt;T&gt;</a> &amp;x, <span class="keyword">const</span> <a class="code" href="classqbpp_1_1Vector.html">qbpp::Vector&lt;U&gt;</a> &amp;y,</div>
<div class="line"><a name="l00096"></a><span class="lineno">   96</span>&#160;           <span class="keyword">const</span> <a class="code" href="classqbpp_1_1Vector.html">qbpp::Vector&lt;Var&gt;</a> &amp;s, <span class="keyword">const</span> <a class="code" href="classqbpp_1_1Vector.html">qbpp::Vector&lt;Var&gt;</a> &amp;c,</div>
<div class="line"><a name="l00097"></a><span class="lineno">   97</span>&#160;           <a class="code" href="namespaceqbpp.html#aeea468454b74e8cf74539f77454bef6b">MapList</a> &amp;opt_sol) {</div>
<div class="line"><a name="l00098"></a><span class="lineno">   98</span>&#160;  <span class="keywordtype">size_t</span> n = x.<a class="code" href="classqbpp_1_1Vector.html#af719287ecdb5f44bbd0aa5d9ba3aaea1">size</a>();</div>
<div class="line"><a name="l00099"></a><span class="lineno">   99</span>&#160;  <span class="keywordflow">if</span> (n != y.<a class="code" href="classqbpp_1_1Vector.html#af719287ecdb5f44bbd0aa5d9ba3aaea1">size</a>() || n != s.<a class="code" href="classqbpp_1_1Vector.html#af719287ecdb5f44bbd0aa5d9ba3aaea1">size</a>() || n != c.<a class="code" href="classqbpp_1_1Vector.html#af719287ecdb5f44bbd0aa5d9ba3aaea1">size</a>()) {</div>
<div class="line"><a name="l00100"></a><span class="lineno">  100</span>&#160;    <span class="keywordflow">throw</span> std::runtime_error(<span class="stringliteral">&quot;Invalid input size&quot;</span>);</div>
<div class="line"><a name="l00101"></a><span class="lineno">  101</span>&#160;  }</div>
<div class="line"><a name="l00102"></a><span class="lineno">  102</span>&#160;  <a class="code" href="classqbpp_1_1Expr.html">Expr</a> <a class="code" href="namespaceqbpp.html#ac1b198c35eb8831b6ac3ad2048caee66">expr</a> = <a class="code" href="namespaceqbpp_1_1factorization.html#aeb9af8fc3150bce913920c5dbbb83828">half_adder</a>(x[0], y[0], s[0], c[0], opt_sol);</div>
<div class="line"><a name="l00103"></a><span class="lineno">  103</span>&#160;  <span class="keywordflow">for</span> (<span class="keywordtype">size_t</span> i = 1; i &lt; x.<a class="code" href="classqbpp_1_1Vector.html#af719287ecdb5f44bbd0aa5d9ba3aaea1">size</a>(); i++) {</div>
<div class="line"><a name="l00104"></a><span class="lineno">  104</span>&#160;    <a class="code" href="namespaceqbpp.html#ac1b198c35eb8831b6ac3ad2048caee66">expr</a> += <a class="code" href="namespaceqbpp_1_1factorization.html#af6b0ff4cf1a09dbca5f12e0e6f040023">full_adder</a>(x[i], y[i], c[i - 1], s[i], c[i], opt_sol);</div>
<div class="line"><a name="l00105"></a><span class="lineno">  105</span>&#160;  }</div>
<div class="line"><a name="l00106"></a><span class="lineno">  106</span>&#160;  <span class="keywordflow">return</span> <a class="code" href="namespaceqbpp.html#ac1b198c35eb8831b6ac3ad2048caee66">expr</a>;</div>
<div class="line"><a name="l00107"></a><span class="lineno">  107</span>&#160;}</div>
<div class="line"><a name="l00108"></a><span class="lineno">  108</span>&#160; </div>
<div class="line"><a name="l00121"></a><span class="lineno">  121</span>&#160;<span class="keyword">template</span> &lt;<span class="keyword">typename</span> T, <span class="keyword">typename</span> U&gt;</div>
<div class="line"><a name="l00122"></a><span class="lineno"><a class="line" href="namespaceqbpp_1_1factorization.html#a0c2bd955d729e3d15ebad8da4835229f">  122</a></span>&#160;<a class="code" href="classqbpp_1_1Expr.html">Expr</a> <a class="code" href="namespaceqbpp_1_1factorization.html#a0c2bd955d729e3d15ebad8da4835229f">multiplier</a>(<span class="keyword">const</span> <a class="code" href="classqbpp_1_1Vector.html">qbpp::Vector&lt;T&gt;</a> &amp;x, <span class="keyword">const</span> <a class="code" href="classqbpp_1_1Vector.html">qbpp::Vector&lt;U&gt;</a> &amp;y,</div>
<div class="line"><a name="l00123"></a><span class="lineno">  123</span>&#160;                <span class="keyword">const</span> <a class="code" href="classqbpp_1_1Vector.html">qbpp::Vector&lt;Var&gt;</a> &amp;z, <a class="code" href="namespaceqbpp.html#aeea468454b74e8cf74539f77454bef6b">MapList</a> &amp;opt_sol) {</div>
<div class="line"><a name="l00124"></a><span class="lineno">  124</span>&#160;  <span class="keywordtype">int</span> Xsize = <span class="keyword">static_cast&lt;</span><span class="keywordtype">int</span><span class="keyword">&gt;</span>(x.<a class="code" href="classqbpp_1_1Vector.html#af719287ecdb5f44bbd0aa5d9ba3aaea1">size</a>());</div>
<div class="line"><a name="l00125"></a><span class="lineno">  125</span>&#160;  <span class="keywordtype">int</span> Ysize = <span class="keyword">static_cast&lt;</span><span class="keywordtype">int</span><span class="keyword">&gt;</span>(y.<a class="code" href="classqbpp_1_1Vector.html#af719287ecdb5f44bbd0aa5d9ba3aaea1">size</a>());</div>
<div class="line"><a name="l00126"></a><span class="lineno">  126</span>&#160;  <span class="keywordtype">int</span> Zsize = <span class="keyword">static_cast&lt;</span><span class="keywordtype">int</span><span class="keyword">&gt;</span>(z.<a class="code" href="classqbpp_1_1Vector.html#af719287ecdb5f44bbd0aa5d9ba3aaea1">size</a>());</div>
<div class="line"><a name="l00127"></a><span class="lineno">  127</span>&#160;  <span class="keywordflow">if</span> (Xsize + Ysize != Zsize) {</div>
<div class="line"><a name="l00128"></a><span class="lineno">  128</span>&#160;    <span class="keywordflow">throw</span> std::runtime_error(<span class="stringliteral">&quot;Invalid input size&quot;</span>);</div>
<div class="line"><a name="l00129"></a><span class="lineno">  129</span>&#160;  }</div>
<div class="line"><a name="l00130"></a><span class="lineno">  130</span>&#160; </div>
<div class="line"><a name="l00131"></a><span class="lineno">  131</span>&#160;  <span class="comment">// The resulting QUBO expression is stored in expr.</span></div>
<div class="line"><a name="l00132"></a><span class="lineno">  132</span>&#160;  <a class="code" href="classqbpp_1_1Expr.html">Expr</a> <a class="code" href="namespaceqbpp.html#ac1b198c35eb8831b6ac3ad2048caee66">expr</a>;</div>
<div class="line"><a name="l00133"></a><span class="lineno">  133</span>&#160; </div>
<div class="line"><a name="l00134"></a><span class="lineno">  134</span>&#160;  <span class="comment">// Creates variable array p of size Ysize * Xsize</span></div>
<div class="line"><a name="l00135"></a><span class="lineno">  135</span>&#160;  <span class="keyword">auto</span> p = <a class="code" href="namespaceqbpp.html#a1cf3f678bcc7eee089c75d4bc5a0b959">var</a>(<span class="stringliteral">&quot;p&quot;</span>, Ysize, Xsize);</div>
<div class="line"><a name="l00136"></a><span class="lineno">  136</span>&#160;  <span class="comment">// Takes minimum value 0 if p[i][j] = y[i] * x[j].</span></div>
<div class="line"><a name="l00137"></a><span class="lineno">  137</span>&#160;  <span class="keywordflow">for</span> (<span class="keywordtype">int</span> i = 0; i &lt; Ysize; i++)</div>
<div class="line"><a name="l00138"></a><span class="lineno">  138</span>&#160;    <span class="keywordflow">for</span> (<span class="keywordtype">int</span> j = 0; j &lt; Xsize; j++) {</div>
<div class="line"><a name="l00139"></a><span class="lineno">  139</span>&#160;      <a class="code" href="namespaceqbpp.html#ac1b198c35eb8831b6ac3ad2048caee66">expr</a> += <a class="code" href="namespaceqbpp_1_1factorization.html#a02e68188402cc688e6ebe4cd9bff29f7">and_gate</a>(y[i], x[j], p[i][j], opt_sol);</div>
<div class="line"><a name="l00140"></a><span class="lineno">  140</span>&#160;    }</div>
<div class="line"><a name="l00141"></a><span class="lineno">  141</span>&#160;  <span class="comment">// Creates variable array s of size Ysize - 1 * Xsize</span></div>
<div class="line"><a name="l00142"></a><span class="lineno">  142</span>&#160;  <span class="comment">// s is the output s of a half/full adder in an Xsize-bit adder</span></div>
<div class="line"><a name="l00143"></a><span class="lineno">  143</span>&#160;  <span class="comment">// Each s[i] is a vector of the output s of the i-th adder.</span></div>
<div class="line"><a name="l00144"></a><span class="lineno">  144</span>&#160;  <span class="keyword">auto</span> s = <a class="code" href="namespaceqbpp.html#a1cf3f678bcc7eee089c75d4bc5a0b959">var</a>(<span class="stringliteral">&quot;s&quot;</span>, Ysize - 1, Xsize);</div>
<div class="line"><a name="l00145"></a><span class="lineno">  145</span>&#160;  <span class="comment">// Creates variable array c of size Ysize * Xsize</span></div>
<div class="line"><a name="l00146"></a><span class="lineno">  146</span>&#160;  <span class="comment">// c is the output c of a half/full adder in an Xsize-bit adder</span></div>
<div class="line"><a name="l00147"></a><span class="lineno">  147</span>&#160;  <span class="comment">// Each c[i] is a vector of the output c of the i-th adder.</span></div>
<div class="line"><a name="l00148"></a><span class="lineno">  148</span>&#160;  <span class="keyword">auto</span> c = <a class="code" href="namespaceqbpp.html#a1cf3f678bcc7eee089c75d4bc5a0b959">var</a>(<span class="stringliteral">&quot;c&quot;</span>, Ysize, Xsize);</div>
<div class="line"><a name="l00149"></a><span class="lineno">  149</span>&#160; </div>
<div class="line"><a name="l00150"></a><span class="lineno">  150</span>&#160;  <span class="comment">// Temporary vector P of variables to be given to the 0-th adder</span></div>
<div class="line"><a name="l00151"></a><span class="lineno">  151</span>&#160;  <a class="code" href="classqbpp_1_1Vector.html">qbpp::Vector&lt;Expr&gt;</a> P(p[0].begin() + 1, p[0].end());</div>
<div class="line"><a name="l00152"></a><span class="lineno">  152</span>&#160;  P.<a class="code" href="classqbpp_1_1Vector.html#a05545411bd760deb7206b0079f4f5833">emplace_back</a>(0);</div>
<div class="line"><a name="l00153"></a><span class="lineno">  153</span>&#160; </div>
<div class="line"><a name="l00154"></a><span class="lineno">  154</span>&#160;  <span class="comment">// QUBO expression for the 0-th adder</span></div>
<div class="line"><a name="l00155"></a><span class="lineno">  155</span>&#160;  <a class="code" href="namespaceqbpp.html#ac1b198c35eb8831b6ac3ad2048caee66">expr</a> += <a class="code" href="namespaceqbpp_1_1factorization.html#a1e5c7d80129600dc5345d9e9dd5602d9">adder</a>(P, p[1], s[0], c[0], opt_sol);</div>
<div class="line"><a name="l00156"></a><span class="lineno">  156</span>&#160;  <span class="comment">// QUBO expression for the i-th adder (1 &lt;= i &lt; Ysize - 1)</span></div>
<div class="line"><a name="l00157"></a><span class="lineno">  157</span>&#160;  <span class="keywordflow">for</span> (<span class="keywordtype">int</span> i = 1; i &lt; Ysize - 1; ++i) {</div>
<div class="line"><a name="l00158"></a><span class="lineno">  158</span>&#160;    <a class="code" href="classqbpp_1_1Vector.html">qbpp::Vector&lt;Expr&gt;</a> S(s[i - 1].begin() + 1, s[i - 1].end());</div>
<div class="line"><a name="l00159"></a><span class="lineno">  159</span>&#160;    S.<a class="code" href="classqbpp_1_1Vector.html#a05545411bd760deb7206b0079f4f5833">emplace_back</a>(c[i - 1][Xsize - 1]);</div>
<div class="line"><a name="l00160"></a><span class="lineno">  160</span>&#160;    <a class="code" href="namespaceqbpp.html#ac1b198c35eb8831b6ac3ad2048caee66">expr</a> += <a class="code" href="namespaceqbpp_1_1factorization.html#a1e5c7d80129600dc5345d9e9dd5602d9">adder</a>(p[i + 1], S, s[i], c[i], opt_sol);</div>
<div class="line"><a name="l00161"></a><span class="lineno">  161</span>&#160;  }</div>
<div class="line"><a name="l00162"></a><span class="lineno">  162</span>&#160; </div>
<div class="line"><a name="l00163"></a><span class="lineno">  163</span>&#160;  <span class="comment">// p[0][0] is replaced by z[0]</span></div>
<div class="line"><a name="l00164"></a><span class="lineno">  164</span>&#160;  <a class="code" href="namespaceqbpp.html#aeea468454b74e8cf74539f77454bef6b">MapList</a> map = {{p[0][0], z[0]}};</div>
<div class="line"><a name="l00165"></a><span class="lineno">  165</span>&#160;  <span class="comment">// s[1][0], s[2][0],..., s[Ysize - 2][0] are replaced by z[1],</span></div>
<div class="line"><a name="l00166"></a><span class="lineno">  166</span>&#160;  <span class="comment">// z[2],...,z[Ysize - 2]</span></div>
<div class="line"><a name="l00167"></a><span class="lineno">  167</span>&#160;  <span class="keywordflow">for</span> (<span class="keywordtype">int</span> i = 1; i &lt; Ysize; ++i) {</div>
<div class="line"><a name="l00168"></a><span class="lineno">  168</span>&#160;    map.<a class="code" href="classqbpp_1_1Vector.html#a23f6c18870445548d8db321606a0a9fb">push_back</a>({s[i - 1][0], z[i]});</div>
<div class="line"><a name="l00169"></a><span class="lineno">  169</span>&#160;  }</div>
<div class="line"><a name="l00170"></a><span class="lineno">  170</span>&#160;  <span class="comment">// s[Ysize - 2][1], s[Ysize - 2][2],..., s[Ysize - 2][Xsize - 1] are replaced</span></div>
<div class="line"><a name="l00171"></a><span class="lineno">  171</span>&#160;  <span class="comment">// by z[Ysize - 1],  z[Ysize],...,z[Xsize + Ysize - 2]</span></div>
<div class="line"><a name="l00172"></a><span class="lineno">  172</span>&#160;  <span class="keywordflow">for</span> (<span class="keywordtype">int</span> j = 1; j &lt; Xsize; ++j) {</div>
<div class="line"><a name="l00173"></a><span class="lineno">  173</span>&#160;    map.push_back({s[Ysize - 2][j], z[j + Ysize - 1]});</div>
<div class="line"><a name="l00174"></a><span class="lineno">  174</span>&#160;  }</div>
<div class="line"><a name="l00175"></a><span class="lineno">  175</span>&#160;  <span class="comment">// c[Ysize - 2][Xsize - 1] is replaced by z[Xsize + Ysize - 1]</span></div>
<div class="line"><a name="l00176"></a><span class="lineno">  176</span>&#160;  map.push_back({c[Ysize - 2][Xsize - 1], z[Xsize + Ysize - 1]});</div>
<div class="line"><a name="l00177"></a><span class="lineno">  177</span>&#160; </div>
<div class="line"><a name="l00178"></a><span class="lineno">  178</span>&#160;  <span class="comment">// Replacement is performed and then simplified as a binary expression.</span></div>
<div class="line"><a name="l00179"></a><span class="lineno">  179</span>&#160;  <a class="code" href="namespaceqbpp.html#ac1b198c35eb8831b6ac3ad2048caee66">expr</a>.<a class="code" href="classqbpp_1_1Expr.html#a73c90484dacb24529c129a00da3d8611">replace</a>(map).<a class="code" href="classqbpp_1_1Expr.html#a21b1c63a0930ff6ff426d852b2ccd011">simplify_as_binary</a>();</div>
<div class="line"><a name="l00180"></a><span class="lineno">  180</span>&#160; </div>
<div class="line"><a name="l00181"></a><span class="lineno">  181</span>&#160;  <span class="keywordflow">return</span> <a class="code" href="namespaceqbpp.html#ac1b198c35eb8831b6ac3ad2048caee66">expr</a>;</div>
<div class="line"><a name="l00182"></a><span class="lineno">  182</span>&#160;}</div>
<div class="line"><a name="l00183"></a><span class="lineno">  183</span>&#160;}  <span class="comment">// namespace factorization</span></div>
<div class="line"><a name="l00184"></a><span class="lineno">  184</span>&#160;}  <span class="comment">// namespace qbpp</span></div>
<div class="line"><a name="l00185"></a><span class="lineno">  185</span>&#160; </div>
<div class="line"><a name="l00186"></a><span class="lineno">  186</span>&#160;<span class="preprocessor">#endif</span></div>
</div><!-- fragment --></div><!-- contents -->
<div class="ttc" id="aclassqbpp_1_1Vector_html_af719287ecdb5f44bbd0aa5d9ba3aaea1"><div class="ttname"><a href="classqbpp_1_1Vector.html#af719287ecdb5f44bbd0aa5d9ba3aaea1">qbpp::Vector::size</a></div><div class="ttdeci">size_t size() const</div><div class="ttdef"><b>Definition:</b> <a href="qbpp_8hpp_source.html#l00591">qbpp.hpp:591</a></div></div>
<div class="ttc" id="aclassqbpp_1_1Expr_html"><div class="ttname"><a href="classqbpp_1_1Expr.html">qbpp::Expr</a></div><div class="ttdef"><b>Definition:</b> <a href="qbpp_8hpp_source.html#l01309">qbpp.hpp:1309</a></div></div>
<div class="ttc" id="anamespaceqbpp_1_1factorization_html_a1e5c7d80129600dc5345d9e9dd5602d9"><div class="ttname"><a href="namespaceqbpp_1_1factorization.html#a1e5c7d80129600dc5345d9e9dd5602d9">qbpp::factorization::adder</a></div><div class="ttdeci">Expr adder(const qbpp::Vector&lt; T &gt; &amp;x, const qbpp::Vector&lt; U &gt; &amp;y, const qbpp::Vector&lt; Var &gt; &amp;s, const qbpp::Vector&lt; Var &gt; &amp;c, MapList &amp;opt_sol)</div><div class="ttdoc">function to generate QUBO expression for n-bit adder</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__multiplier_8hpp_source.html#l00095">qbpp_multiplier.hpp:95</a></div></div>
<div class="ttc" id="anamespaceqbpp_1_1factorization_html_af6b0ff4cf1a09dbca5f12e0e6f040023"><div class="ttname"><a href="namespaceqbpp_1_1factorization.html#af6b0ff4cf1a09dbca5f12e0e6f040023">qbpp::factorization::full_adder</a></div><div class="ttdeci">Expr full_adder(const Expr &amp;x, const Expr &amp;y, const Expr &amp;z, Var s, Var c, MapList &amp;opt_sol)</div><div class="ttdoc">Function to generate a QUBO expression Expr object for the Full Adder.</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__multiplier_8hpp_source.html#l00066">qbpp_multiplier.hpp:66</a></div></div>
<div class="ttc" id="anamespaceqbpp_html_a1cf3f678bcc7eee089c75d4bc5a0b959"><div class="ttname"><a href="namespaceqbpp.html#a1cf3f678bcc7eee089c75d4bc5a0b959">qbpp::var</a></div><div class="ttdeci">Var var(const std::string &amp;var_str)</div><div class="ttdef"><b>Definition:</b> <a href="qbpp_8hpp_source.html#l02887">qbpp.hpp:2887</a></div></div>
<div class="ttc" id="aclassqbpp_1_1Expr_html_a21b1c63a0930ff6ff426d852b2ccd011"><div class="ttname"><a href="classqbpp_1_1Expr.html#a21b1c63a0930ff6ff426d852b2ccd011">qbpp::Expr::simplify_as_binary</a></div><div class="ttdeci">Expr &amp; simplify_as_binary()</div><div class="ttdef"><b>Definition:</b> <a href="qbpp_8hpp_source.html#l04526">qbpp.hpp:4526</a></div></div>
<div class="ttc" id="aclassqbpp_1_1Var_html"><div class="ttname"><a href="classqbpp_1_1Var.html">qbpp::Var</a></div><div class="ttdef"><b>Definition:</b> <a href="qbpp_8hpp_source.html#l00729">qbpp.hpp:729</a></div></div>
<div class="ttc" id="anamespaceqbpp_html_aeea468454b74e8cf74539f77454bef6b"><div class="ttname"><a href="namespaceqbpp.html#aeea468454b74e8cf74539f77454bef6b">qbpp::MapList</a></div><div class="ttdeci">std::list&lt; std::pair&lt; std::variant&lt; Var, VarInt &gt;, Expr &gt; &gt; MapList</div><div class="ttdef"><b>Definition:</b> <a href="qbpp_8hpp_source.html#l00141">qbpp.hpp:141</a></div></div>
<div class="ttc" id="aclassqbpp_1_1Vector_html"><div class="ttname"><a href="classqbpp_1_1Vector.html">qbpp::Vector</a></div><div class="ttdef"><b>Definition:</b> <a href="qbpp_8hpp_source.html#l00056">qbpp.hpp:56</a></div></div>
<div class="ttc" id="anamespaceqbpp_1_1factorization_html_aeb9af8fc3150bce913920c5dbbb83828"><div class="ttname"><a href="namespaceqbpp_1_1factorization.html#aeb9af8fc3150bce913920c5dbbb83828">qbpp::factorization::half_adder</a></div><div class="ttdeci">Expr half_adder(const Expr &amp;x, const Expr &amp;y, Var s, Var c, MapList &amp;opt_sol)</div><div class="ttdoc">Function to generates a QUBO expression Expr object for the Half Adder.</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__multiplier_8hpp_source.html#l00043">qbpp_multiplier.hpp:43</a></div></div>
<div class="ttc" id="aqbpp_8hpp_html"><div class="ttname"><a href="qbpp_8hpp.html">qbpp.hpp</a></div><div class="ttdoc">QUBO++, a C++ library for generating expressions for binary and spin variables.</div></div>
<div class="ttc" id="aclassqbpp_1_1Vector_html_a05545411bd760deb7206b0079f4f5833"><div class="ttname"><a href="classqbpp_1_1Vector.html#a05545411bd760deb7206b0079f4f5833">qbpp::Vector::emplace_back</a></div><div class="ttdeci">void emplace_back(T &amp;&amp;t)</div><div class="ttdef"><b>Definition:</b> <a href="qbpp_8hpp_source.html#l00583">qbpp.hpp:583</a></div></div>
<div class="ttc" id="anamespaceqbpp_html"><div class="ttname"><a href="namespaceqbpp.html">qbpp</a></div><div class="ttdoc">Generates a QUBO Expression for the Graph Coloring Problem using QUBO++ library.</div><div class="ttdef"><b>Definition:</b> <a href="qbpp_8hpp_source.html#l00052">qbpp.hpp:52</a></div></div>
<div class="ttc" id="anamespaceqbpp_html_ac1b198c35eb8831b6ac3ad2048caee66"><div class="ttname"><a href="namespaceqbpp.html#ac1b198c35eb8831b6ac3ad2048caee66">qbpp::expr</a></div><div class="ttdeci">Expr expr()</div><div class="ttdef"><b>Definition:</b> <a href="qbpp_8hpp_source.html#l03071">qbpp.hpp:3071</a></div></div>
<div class="ttc" id="aclassqbpp_1_1Expr_html_a73c90484dacb24529c129a00da3d8611"><div class="ttname"><a href="classqbpp_1_1Expr.html#a73c90484dacb24529c129a00da3d8611">qbpp::Expr::replace</a></div><div class="ttdeci">Expr &amp; replace(const MapList &amp;map_list)</div><div class="ttdef"><b>Definition:</b> <a href="qbpp_8hpp_source.html#l04550">qbpp.hpp:4550</a></div></div>
<div class="ttc" id="anamespaceqbpp_1_1factorization_html_a0c2bd955d729e3d15ebad8da4835229f"><div class="ttname"><a href="namespaceqbpp_1_1factorization.html#a0c2bd955d729e3d15ebad8da4835229f">qbpp::factorization::multiplier</a></div><div class="ttdeci">Expr multiplier(const qbpp::Vector&lt; T &gt; &amp;x, const qbpp::Vector&lt; U &gt; &amp;y, const qbpp::Vector&lt; Var &gt; &amp;z, MapList &amp;opt_sol)</div><div class="ttdoc">Function to generate QUBO expression for multiplier.</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__multiplier_8hpp_source.html#l00122">qbpp_multiplier.hpp:122</a></div></div>
<div class="ttc" id="aclassqbpp_1_1Vector_html_a23f6c18870445548d8db321606a0a9fb"><div class="ttname"><a href="classqbpp_1_1Vector.html#a23f6c18870445548d8db321606a0a9fb">qbpp::Vector::push_back</a></div><div class="ttdeci">void push_back(const T &amp;t)</div><div class="ttdef"><b>Definition:</b> <a href="qbpp_8hpp_source.html#l00581">qbpp.hpp:581</a></div></div>
<div class="ttc" id="anamespaceqbpp_html_ab5da6d0960920098998bb7347082ee6b"><div class="ttname"><a href="namespaceqbpp.html#ab5da6d0960920098998bb7347082ee6b">qbpp::eval</a></div><div class="ttdeci">energy_t eval(const Expr &amp;expr, const Sol &amp;sol)</div><div class="ttdef"><b>Definition:</b> <a href="qbpp_8hpp_source.html#l02868">qbpp.hpp:2868</a></div></div>
<div class="ttc" id="anamespaceqbpp_1_1factorization_html_a02e68188402cc688e6ebe4cd9bff29f7"><div class="ttname"><a href="namespaceqbpp_1_1factorization.html#a02e68188402cc688e6ebe4cd9bff29f7">qbpp::factorization::and_gate</a></div><div class="ttdeci">Expr and_gate(const Expr &amp;a, const Expr &amp;b, Var x, MapList &amp;opt_sol)</div><div class="ttdoc">Function to generate a QUBO expression Expr object for the AND gate.</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__multiplier_8hpp_source.html#l00026">qbpp_multiplier.hpp:26</a></div></div>
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.17
</small></address>
</body>
</html>
