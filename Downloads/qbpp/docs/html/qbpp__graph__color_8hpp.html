<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.17"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>QUBO++ Library with QUBO Solver APIs: sample/qbpp_graph_color.hpp File Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/x-mathjax-config">
  MathJax.Hub.Config({
    extensions: ["tex2jax.js", "TeX/AMSmath.js", "TeX/AMSsymbols.js"],
    jax: ["input/TeX","output/HTML-CSS"],
});
</script>
<script type="text/javascript" async="async" src="https://cdn.jsdelivr.net/npm/mathjax@2/MathJax.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">QUBO++ Library with QUBO Solver APIs
   </div>
   <div id="projectbrief">Author: Koji Nakano, License: Non-commercial research and evaluation purposes without any guarantees.</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.17 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_c29eeb5af533606caea8a09e66794740.html">sample</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#nested-classes">Classes</a> &#124;
<a href="#namespaces">Namespaces</a>  </div>
  <div class="headertitle">
<div class="title">qbpp_graph_color.hpp File Reference</div>  </div>
</div><!--header-->
<div class="contents">
<div class="textblock"><code>#include &lt;boost/polygon/voronoi.hpp&gt;</code><br />
<code>#include &lt;iostream&gt;</code><br />
<code>#include &lt;limits&gt;</code><br />
<code>#include &lt;memory&gt;</code><br />
<code>#include &lt;queue&gt;</code><br />
<code>#include &lt;random&gt;</code><br />
<code>#include &lt;sstream&gt;</code><br />
<code>#include &lt;string&gt;</code><br />
<code>#include &lt;utility&gt;</code><br />
<code>#include &lt;vector&gt;</code><br />
<code>#include &quot;<a class="el" href="qbpp_8hpp_source.html">qbpp.hpp</a>&quot;</code><br />
<code>#include &quot;<a class="el" href="qbpp__misc_8hpp_source.html">qbpp_misc.hpp</a>&quot;</code><br />
</div><div class="textblock"><div class="dynheader">
Include dependency graph for qbpp_graph_color.hpp:</div>
<div class="dyncontent">
<div class="center"><img src="qbpp__graph__color_8hpp__incl.png" border="0" usemap="#sample_2qbpp__graph__color_8hpp" alt=""/></div>
<map name="sample_2qbpp__graph__color_8hpp" id="sample_2qbpp__graph__color_8hpp">
<area shape="rect" title=" " alt="" coords="1772,5,1967,32"/>
<area shape="rect" title=" " alt="" coords="1417,80,1591,107"/>
<area shape="rect" title=" " alt="" coords="3008,251,3080,278"/>
<area shape="rect" title=" " alt="" coords="5,251,59,278"/>
<area shape="rect" title=" " alt="" coords="83,251,152,278"/>
<area shape="rect" title=" " alt="" coords="2771,80,2829,107"/>
<area shape="rect" title=" " alt="" coords="2094,162,2159,189"/>
<area shape="rect" title=" " alt="" coords="176,251,245,278"/>
<area shape="rect" title=" " alt="" coords="270,251,325,278"/>
<area shape="rect" title=" " alt="" coords="349,251,403,278"/>
<area shape="rect" title=" " alt="" coords="3104,251,3163,278"/>
<area shape="rect" href="qbpp_8hpp.html" title="QUBO++, a C++ library for generating expressions for binary and spin variables." alt="" coords="1869,162,1944,189"/>
<area shape="rect" href="qbpp__misc_8hpp.html" title="A miscellaneous library used for sample programs of the QUBO++ library." alt="" coords="2637,80,2747,107"/>
<area shape="rect" title=" " alt="" coords="2845,251,2984,278"/>
<area shape="rect" title=" " alt="" coords="427,251,544,278"/>
<area shape="rect" title=" " alt="" coords="567,251,710,278"/>
<area shape="rect" title=" " alt="" coords="735,251,860,278"/>
<area shape="rect" title=" " alt="" coords="3187,251,3261,278"/>
<area shape="rect" title=" " alt="" coords="884,244,1031,285"/>
<area shape="rect" title=" " alt="" coords="1055,244,1201,285"/>
<area shape="rect" title=" " alt="" coords="1226,251,1414,278"/>
<area shape="rect" title=" " alt="" coords="1438,251,1583,278"/>
<area shape="rect" title=" " alt="" coords="1608,244,1747,285"/>
<area shape="rect" title=" " alt="" coords="1771,251,1832,278"/>
<area shape="rect" title=" " alt="" coords="3285,251,3344,278"/>
<area shape="rect" title=" " alt="" coords="1857,251,1954,278"/>
<area shape="rect" title=" " alt="" coords="1979,251,2045,278"/>
<area shape="rect" title=" " alt="" coords="2070,251,2109,278"/>
<area shape="rect" title=" " alt="" coords="2133,251,2192,278"/>
<area shape="rect" title=" " alt="" coords="2216,251,2283,278"/>
<area shape="rect" title=" " alt="" coords="2307,251,2365,278"/>
<area shape="rect" title=" " alt="" coords="2389,251,2472,278"/>
<area shape="rect" title=" " alt="" coords="2497,251,2607,278"/>
<area shape="rect" title=" " alt="" coords="2632,251,2736,278"/>
<area shape="rect" title=" " alt="" coords="2760,251,2821,278"/>
<area shape="rect" title=" " alt="" coords="3271,162,3332,189"/>
<area shape="rect" title=" " alt="" coords="2237,162,2401,189"/>
<area shape="rect" title=" " alt="" coords="2425,162,2594,189"/>
<area shape="rect" title=" " alt="" coords="2618,155,2766,196"/>
<area shape="rect" title=" " alt="" coords="2790,155,2938,196"/>
<area shape="rect" title=" " alt="" coords="2962,162,3030,189"/>
<area shape="rect" title=" " alt="" coords="3055,162,3095,189"/>
</map>
</div>
</div><div class="textblock"><div class="dynheader">
This graph shows which files directly or indirectly include this file:</div>
<div class="dyncontent">
<div class="center"><img src="qbpp__graph__color_8hpp__dep__incl.png" border="0" usemap="#sample_2qbpp__graph__color_8hppdep" alt=""/></div>
<map name="sample_2qbpp__graph__color_8hppdep" id="sample_2qbpp__graph__color_8hppdep">
<area shape="rect" title=" " alt="" coords="134,5,329,32"/>
<area shape="rect" href="graph__color__easy_8cpp.html" title="Solves randomly generated Graph Coloring Problem using QUBO++ Easy Solver." alt="" coords="5,80,140,121"/>
<area shape="rect" href="graph__color__abs2_8cpp.html" title="Solves randomly generated Graph Coloring Problem using ABS2 QUBO Solver." alt="" coords="164,80,299,121"/>
<area shape="rect" href="graph__color__grb_8cpp.html" title="Solves randomly generated Graph Coloring Problem using QUBO++ Gurobi Optimizer." alt="" coords="323,80,457,121"/>
</map>
</div>
</div>
<p><a href="qbpp__graph__color_8hpp_source.html">Go to the source code of this file.</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="nested-classes"></a>
Classes</h2></td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">class &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classqbpp_1_1graph__color_1_1GraphColorMap.html">qbpp::graph_color::GraphColorMap</a></td></tr>
<tr class="memdesc:"><td class="mdescLeft">&#160;</td><td class="mdescRight">Class to store a graph with node coloring and related information.  <a href="classqbpp_1_1graph__color_1_1GraphColorMap.html#details">More...</a><br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">class &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classqbpp_1_1graph__color_1_1GraphColorQuadModel.html">qbpp::graph_color::GraphColorQuadModel</a></td></tr>
<tr class="memdesc:"><td class="mdescLeft">&#160;</td><td class="mdescRight">Class to store the QUBO expression with variables for the Graph Coloring Problem.  <a href="classqbpp_1_1graph__color_1_1GraphColorQuadModel.html#details">More...</a><br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="namespaces"></a>
Namespaces</h2></td></tr>
<tr class="memitem:namespaceqbpp"><td class="memItemLeft" align="right" valign="top"> &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html">qbpp</a></td></tr>
<tr class="memdesc:namespaceqbpp"><td class="mdescLeft">&#160;</td><td class="mdescRight">Generates a QUBO Expression for the Graph Coloring Problem using QUBO++ library. <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:namespaceqbpp_1_1graph__color"><td class="memItemLeft" align="right" valign="top"> &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp_1_1graph__color.html">qbpp::graph_color</a></td></tr>
<tr class="memdesc:namespaceqbpp_1_1graph__color"><td class="mdescLeft">&#160;</td><td class="mdescRight">Namespace for the Graph Node Coloring using QUBO++. <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.17
</small></address>
</body>
</html>
