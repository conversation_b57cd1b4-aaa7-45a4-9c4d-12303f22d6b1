<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.17"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>QUBO++ Library with QUBO Solver APIs: sample/nqueen_easy.cpp File Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/x-mathjax-config">
  MathJax.Hub.Config({
    extensions: ["tex2jax.js", "TeX/AMSmath.js", "TeX/AMSsymbols.js"],
    jax: ["input/TeX","output/HTML-CSS"],
});
</script>
<script type="text/javascript" async="async" src="https://cdn.jsdelivr.net/npm/mathjax@2/MathJax.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">QUBO++ Library with QUBO Solver APIs
   </div>
   <div id="projectbrief">Author: Koji Nakano, License: Non-commercial research and evaluation purposes without any guarantees.</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.17 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_c29eeb5af533606caea8a09e66794740.html">sample</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#func-members">Functions</a>  </div>
  <div class="headertitle">
<div class="title">nqueen_easy.cpp File Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p>Solves the N-Queens problem using ABS2 QUBO solver from QUBO++ library.  
<a href="#details">More...</a></p>
<div class="textblock"><code>#include &lt;boost/program_options.hpp&gt;</code><br />
<code>#include &quot;<a class="el" href="qbpp_8hpp_source.html">qbpp.hpp</a>&quot;</code><br />
<code>#include &quot;<a class="el" href="qbpp__easy__solver_8hpp_source.html">qbpp_easy_solver.hpp</a>&quot;</code><br />
<code>#include &quot;<a class="el" href="qbpp__nqueen_8hpp_source.html">qbpp_nqueen.hpp</a>&quot;</code><br />
</div><div class="textblock"><div class="dynheader">
Include dependency graph for nqueen_easy.cpp:</div>
<div class="dyncontent">
<div class="center"><img src="nqueen__easy_8cpp__incl.png" border="0" usemap="#sample_2nqueen__easy_8cpp" alt=""/></div>
<map name="sample_2nqueen__easy_8cpp" id="sample_2nqueen__easy_8cpp">
<area shape="rect" title="Solves the N&#45;Queens problem using ABS2 QUBO solver from QUBO++ library." alt="" coords="1844,5,2012,32"/>
<area shape="rect" title=" " alt="" coords="1663,80,1841,107"/>
<area shape="rect" href="qbpp_8hpp.html" title="QUBO++, a C++ library for generating expressions for binary and spin variables." alt="" coords="1841,237,1916,263"/>
<area shape="rect" href="qbpp__easy__solver_8hpp.html" title="Easy QUBO Solver for solving QUBO problems." alt="" coords="2746,80,2897,107"/>
<area shape="rect" href="qbpp__nqueen_8hpp.html" title="Generates QUBO expression for the N&#45;Queens problem using the QUBO++ library." alt="" coords="1917,80,2041,107"/>
<area shape="rect" title=" " alt="" coords="3143,326,3281,353"/>
<area shape="rect" title=" " alt="" coords="3448,326,3565,353"/>
<area shape="rect" title=" " alt="" coords="2455,326,2598,353"/>
<area shape="rect" title=" " alt="" coords="83,326,208,353"/>
<area shape="rect" title=" " alt="" coords="2788,326,2863,353"/>
<area shape="rect" title=" " alt="" coords="232,319,379,360"/>
<area shape="rect" title=" " alt="" coords="403,319,549,360"/>
<area shape="rect" title=" " alt="" coords="574,326,762,353"/>
<area shape="rect" title=" " alt="" coords="786,326,931,353"/>
<area shape="rect" title=" " alt="" coords="956,319,1095,360"/>
<area shape="rect" title=" " alt="" coords="1119,326,1180,353"/>
<area shape="rect" title=" " alt="" coords="2887,326,2945,353"/>
<area shape="rect" title=" " alt="" coords="1205,326,1302,353"/>
<area shape="rect" title=" " alt="" coords="1327,326,1393,353"/>
<area shape="rect" title=" " alt="" coords="2969,326,3041,353"/>
<area shape="rect" title=" " alt="" coords="5,326,59,353"/>
<area shape="rect" title=" " alt="" coords="1418,326,1457,353"/>
<area shape="rect" title=" " alt="" coords="1481,326,1551,353"/>
<area shape="rect" title=" " alt="" coords="1575,326,1633,353"/>
<area shape="rect" title=" " alt="" coords="1657,326,1724,353"/>
<area shape="rect" title=" " alt="" coords="1748,326,1817,353"/>
<area shape="rect" title=" " alt="" coords="1842,326,1897,353"/>
<area shape="rect" title=" " alt="" coords="2623,326,2681,353"/>
<area shape="rect" title=" " alt="" coords="1921,326,2004,353"/>
<area shape="rect" title=" " alt="" coords="2029,326,2139,353"/>
<area shape="rect" title=" " alt="" coords="2164,326,2268,353"/>
<area shape="rect" title=" " alt="" coords="2292,326,2345,353"/>
<area shape="rect" title=" " alt="" coords="2369,326,2431,353"/>
<area shape="rect" title=" " alt="" coords="2705,326,2764,353"/>
<area shape="rect" title=" " alt="" coords="2258,237,2422,263"/>
<area shape="rect" title=" " alt="" coords="3365,237,3430,263"/>
<area shape="rect" title=" " alt="" coords="3455,237,3495,263"/>
<area shape="rect" href="qbpp__misc_8hpp.html" title="A miscellaneous library used for sample programs of the QUBO++ library." alt="" coords="2719,155,2830,181"/>
<area shape="rect" title=" " alt="" coords="2956,155,3031,181"/>
<area shape="rect" title=" " alt="" coords="3055,155,3124,181"/>
<area shape="rect" title=" " alt="" coords="3148,155,3212,181"/>
<area shape="rect" title=" " alt="" coords="2913,237,2975,263"/>
<area shape="rect" title=" " alt="" coords="2999,237,3169,263"/>
<area shape="rect" title=" " alt="" coords="3193,229,3341,271"/>
<area shape="rect" title=" " alt="" coords="2446,229,2594,271"/>
<area shape="rect" title=" " alt="" coords="2618,237,2686,263"/>
</map>
</div>
</div>
<p><a href="nqueen__easy_8cpp_source.html">Go to the source code of this file.</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:a0ddf1224851353fc92bfbff6f499fa97"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="nqueen__easy_8cpp.html#a0ddf1224851353fc92bfbff6f499fa97">main</a> (int argc, char *argv[])</td></tr>
<tr class="memdesc:a0ddf1224851353fc92bfbff6f499fa97"><td class="mdescLeft">&#160;</td><td class="mdescRight">Solves the N-Queens problem using EasySolver in the QUBO++ library. library.  <a href="nqueen__easy_8cpp.html#a0ddf1224851353fc92bfbff6f499fa97">More...</a><br /></td></tr>
<tr class="separator:a0ddf1224851353fc92bfbff6f499fa97"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p>Solves the N-Queens problem using ABS2 QUBO solver from QUBO++ library. </p>
<dl class="section author"><dt>Author</dt><dd>Koji Nakano </dd></dl>
<dl class="section version"><dt>Version</dt><dd>2024-11-23 </dd></dl>

<p class="definition">Definition in file <a class="el" href="nqueen__easy_8cpp_source.html">nqueen_easy.cpp</a>.</p>
</div><h2 class="groupheader">Function Documentation</h2>
<a id="a0ddf1224851353fc92bfbff6f499fa97"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a0ddf1224851353fc92bfbff6f499fa97">&#9670;&nbsp;</a></span>main()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int main </td>
          <td>(</td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>argc</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">char *&#160;</td>
          <td class="paramname"><em>argv</em>[]&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Solves the N-Queens problem using EasySolver in the QUBO++ library. library. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">argc</td><td>Number of command line arguments. </td></tr>
    <tr><td class="paramname">argv</td><td>Command line arguments. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>int Return code.</dd></dl>
<p>The dimension of the chessboard can be specified by the command line argument. </p>

<p class="definition">Definition at line <a class="el" href="nqueen__easy_8cpp_source.html#l00022">22</a> of file <a class="el" href="nqueen__easy_8cpp_source.html">nqueen_easy.cpp</a>.</p>
<div class="dynheader">
Here is the call graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="nqueen__easy_8cpp_a0ddf1224851353fc92bfbff6f499fa97_cgraph.png" border="0" usemap="#nqueen__easy_8cpp_a0ddf1224851353fc92bfbff6f499fa97_cgraph" alt=""/></div>
<map name="nqueen__easy_8cpp_a0ddf1224851353fc92bfbff6f499fa97_cgraph" id="nqueen__easy_8cpp_a0ddf1224851353fc92bfbff6f499fa97_cgraph">
<area shape="rect" title="Solves the N&#45;Queens problem using EasySolver in the QUBO++ library. library." alt="" coords="5,111,56,138"/>
<area shape="rect" href="classqbpp_1_1nqueen_1_1NQueenQuadModel.html#a17de6168be88796c8bd27ebaf33f10ea" title="Gets the variable at (i, j)" alt="" coords="111,5,297,47"/>
<area shape="rect" href="classqbpp_1_1misc_1_1RandomGenerator.html#a1f4f3d675aee33dce174c8222c3bc22a" title=" " alt="" coords="104,71,304,112"/>
<area shape="rect" href="classqbpp_1_1QuadModel.html#a16af851530b1f63aca9baa8ec67ed01f" title=" " alt="" coords="125,136,283,177"/>
<area shape="rect" href="classqbpp_1_1Model.html#a5cf2bb955c3aa25951ca499d7d8635d2" title=" " alt="" coords="125,202,283,229"/>
<area shape="rect" href="classqbpp_1_1misc_1_1RandomGenerator.html#a6b19f6bde3a3b6ff087a908d11ece3c1" title=" " alt="" coords="352,71,552,112"/>
</map>
</div>

</div>
</div>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.17
</small></address>
</body>
</html>
