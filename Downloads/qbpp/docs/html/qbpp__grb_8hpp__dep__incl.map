<map id="include/qbpp_grb.hpp" name="include/qbpp_grb.hpp">
<area shape="rect" id="node1" title="QUBO++ interface to call Gurobi Optimizer." alt="" coords="315,5,460,32"/>
<area shape="rect" id="node2" href="$graph__color__grb_8cpp.html" title="Solves randomly generated Graph Coloring Problem using QUBO++ Gurobi Optimizer." alt="" coords="5,80,140,121"/>
<area shape="rect" id="node3" href="$ilp__grb_8cpp.html" title="Solves an Integer Linear Programming (ILP) problem using Gurobi Optimizer through QUBO++ library." alt="" coords="165,87,295,114"/>
<area shape="rect" id="node4" href="$tsp__grb_8cpp.html" title="Solves randomly generated Traveling Salesman Problem (TSP) using the QUBO++ Easy solver." alt="" coords="319,87,455,114"/>
<area shape="rect" id="node5" href="$simple__factorization__grb_8cpp.html" title="Simple factorization example using Gurobi Optimizer." alt="" coords="479,80,663,121"/>
<area shape="rect" id="node6" href="$factorization_8cpp.html" title="Solves factorization and multiplication problems for two prime numbers using multiple QUBO solvers." alt="" coords="688,87,852,114"/>
</map>
