<map id="include/qbpp_abs2.hpp" name="include/qbpp_abs2.hpp">
<area shape="rect" id="node1" title="QUBO++ interface to call ABS2 GPU QUBO solver." alt="" coords="326,5,481,32"/>
<area shape="rect" id="node2" href="$graph__color__abs2_8cpp.html" title="Solves randomly generated Graph Coloring Problem using ABS2 QUBO Solver." alt="" coords="5,80,140,121"/>
<area shape="rect" id="node3" href="$ilp__abs2_8cpp.html" title="Solves an Integer Linear Programming (ILP) problem using ABS2 solver through QUBO++ library." alt="" coords="165,87,306,114"/>
<area shape="rect" id="node4" href="$tsp__abs2_8cpp.html" title="Solves randomly generated Traveling Salesman Problem (TSP) using the ABS2 QUBO solver." alt="" coords="330,87,477,114"/>
<area shape="rect" id="node5" href="$simple__factorization__abs2_8cpp.html" title="Simple factorization example using ABS2 QUBO Solver." alt="" coords="501,80,685,121"/>
<area shape="rect" id="node6" href="$factorization_8cpp.html" title="Solves factorization and multiplication problems for two prime numbers using multiple QUBO solvers." alt="" coords="709,87,873,114"/>
</map>
