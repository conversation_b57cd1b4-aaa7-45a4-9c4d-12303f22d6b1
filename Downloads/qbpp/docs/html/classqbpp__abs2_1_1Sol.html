<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.17"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>QUBO++ Library with QUBO Solver APIs: qbpp_abs2::Sol Class Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/x-mathjax-config">
  MathJax.Hub.Config({
    extensions: ["tex2jax.js", "TeX/AMSmath.js", "TeX/AMSsymbols.js"],
    jax: ["input/TeX","output/HTML-CSS"],
});
</script>
<script type="text/javascript" async="async" src="https://cdn.jsdelivr.net/npm/mathjax@2/MathJax.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">QUBO++ Library with QUBO Solver APIs
   </div>
   <div id="projectbrief">Author: Koji Nakano, License: Non-commercial research and evaluation purposes without any guarantees.</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.17 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="namespaceqbpp__abs2.html">qbpp_abs2</a></li><li class="navelem"><a class="el" href="classqbpp__abs2_1_1Sol.html">Sol</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#pub-methods">Public Member Functions</a> &#124;
<a href="#pro-methods">Protected Member Functions</a> &#124;
<a href="#pro-attribs">Protected Attributes</a> &#124;
<a href="#pri-attribs">Private Attributes</a> &#124;
<a href="classqbpp__abs2_1_1Sol-members.html">List of all members</a>  </div>
  <div class="headertitle">
<div class="title">qbpp_abs2::Sol Class Reference<div class="ingroups"><a class="el" href="group__qbpp__abs2.html">QUBO++ API for ABS2 QUBO Solver</a></div></div>  </div>
</div><!--header-->
<div class="contents">

<p>A class for storing the ABS2 QUBO solution.  
 <a href="classqbpp__abs2_1_1Sol.html#details">More...</a></p>

<p><code>#include &lt;<a class="el" href="qbpp__abs2_8hpp_source.html">qbpp_abs2.hpp</a>&gt;</code></p>
<div class="dynheader">
Inheritance diagram for qbpp_abs2::Sol:</div>
<div class="dyncontent">
<div class="center"><img src="classqbpp__abs2_1_1Sol__inherit__graph.png" border="0" usemap="#qbpp__abs2_1_1Sol_inherit__map" alt="Inheritance graph"/></div>
<map name="qbpp__abs2_1_1Sol_inherit__map" id="qbpp__abs2_1_1Sol_inherit__map">
<area shape="rect" title="A class for storing the ABS2 QUBO solution." alt="" coords="5,80,117,107"/>
<area shape="rect" href="classqbpp_1_1Sol.html" title=" " alt="" coords="23,5,100,32"/>
</map>
<center><span class="legend">[<a href="graph_legend.html">legend</a>]</span></center></div>
<div class="dynheader">
Collaboration diagram for qbpp_abs2::Sol:</div>
<div class="dyncontent">
<div class="center"><img src="classqbpp__abs2_1_1Sol__coll__graph.png" border="0" usemap="#qbpp__abs2_1_1Sol_coll__map" alt="Collaboration graph"/></div>
<map name="qbpp__abs2_1_1Sol_coll__map" id="qbpp__abs2_1_1Sol_coll__map">
<area shape="rect" title="A class for storing the ABS2 QUBO solution." alt="" coords="71,247,183,273"/>
<area shape="rect" href="classqbpp_1_1Sol.html" title=" " alt="" coords="88,171,165,197"/>
<area shape="rect" href="classqbpp_1_1impl_1_1BitVector.html" title=" " alt="" coords="5,81,149,108"/>
<area shape="rect" href="classqbpp_1_1QuadModel.html" title=" " alt="" coords="174,81,298,108"/>
<area shape="rect" href="classqbpp_1_1Model.html" title=" " alt="" coords="189,5,283,32"/>
</map>
<center><span class="legend">[<a href="graph_legend.html">legend</a>]</span></center></div>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-methods"></a>
Public Member Functions</h2></td></tr>
<tr class="memitem:gaba8e35f84b2f822cd2633b61358370db"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__qbpp__abs2.html#gaba8e35f84b2f822cd2633b61358370db">Sol</a> (const <a class="el" href="classqbpp__abs2_1_1QuadModel.html">QuadModel</a> &amp;quad_model, const <a class="el" href="classabs2_1_1Sol.html">abs2::Sol</a> &amp;sol)</td></tr>
<tr class="memdesc:gaba8e35f84b2f822cd2633b61358370db"><td class="mdescLeft">&#160;</td><td class="mdescRight">Construct a new <a class="el" href="classqbpp__abs2_1_1Sol.html" title="A class for storing the ABS2 QUBO solution.">Sol</a> object from an ABS2 solution.  <a href="group__qbpp__abs2.html#gaba8e35f84b2f822cd2633b61358370db">More...</a><br /></td></tr>
<tr class="separator:gaba8e35f84b2f822cd2633b61358370db"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gac27ae911e72fdeb424a950d41fbef95c"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__qbpp__abs2.html#gac27ae911e72fdeb424a950d41fbef95c">Sol</a> (const <a class="el" href="classqbpp_1_1Sol.html">qbpp::Sol</a> &amp;sol)</td></tr>
<tr class="memdesc:gac27ae911e72fdeb424a950d41fbef95c"><td class="mdescLeft">&#160;</td><td class="mdescRight">Construct a new <a class="el" href="classqbpp__abs2_1_1Sol.html" title="A class for storing the ABS2 QUBO solution.">Sol</a> object from a QUBO++ QUBO solution.  <a href="group__qbpp__abs2.html#gac27ae911e72fdeb424a950d41fbef95c">More...</a><br /></td></tr>
<tr class="separator:gac27ae911e72fdeb424a950d41fbef95c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac7b3defb0e488e8570785400889f4c77"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classqbpp__abs2_1_1Sol.html#ac7b3defb0e488e8570785400889f4c77">Sol</a> (const <a class="el" href="classqbpp__abs2_1_1Sol.html">Sol</a> &amp;sol)=default</td></tr>
<tr class="memdesc:ac7b3defb0e488e8570785400889f4c77"><td class="mdescLeft">&#160;</td><td class="mdescRight">Copy constructor.  <a href="classqbpp__abs2_1_1Sol.html#ac7b3defb0e488e8570785400889f4c77">More...</a><br /></td></tr>
<tr class="separator:ac7b3defb0e488e8570785400889f4c77"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a32d5e94f5ec2e824cbb8669e5bb1c69a"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classqbpp__abs2_1_1Sol.html#a32d5e94f5ec2e824cbb8669e5bb1c69a">print</a> (const std::string &amp;attrs) const</td></tr>
<tr class="memdesc:a32d5e94f5ec2e824cbb8669e5bb1c69a"><td class="mdescLeft">&#160;</td><td class="mdescRight">print the solution  <a href="classqbpp__abs2_1_1Sol.html#a32d5e94f5ec2e824cbb8669e5bb1c69a">More...</a><br /></td></tr>
<tr class="separator:a32d5e94f5ec2e824cbb8669e5bb1c69a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a48d9116f9fcbe00f84640847ee49df23"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classqbpp__abs2_1_1Sol.html#a48d9116f9fcbe00f84640847ee49df23">set</a> (int <a class="el" href="classqbpp_1_1Sol.html#a24f5a53fa68071dd9bcda910533bbad7">index</a>, bool value)</td></tr>
<tr class="memdesc:a48d9116f9fcbe00f84640847ee49df23"><td class="mdescLeft">&#160;</td><td class="mdescRight">Set the value of the variable with "index" to "value".  <a href="classqbpp__abs2_1_1Sol.html#a48d9116f9fcbe00f84640847ee49df23">More...</a><br /></td></tr>
<tr class="separator:a48d9116f9fcbe00f84640847ee49df23"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a04aabe5387a17b86a90acaa607175545"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classqbpp__abs2_1_1Sol.html#a04aabe5387a17b86a90acaa607175545">set</a> (<a class="el" href="classqbpp_1_1Var.html">qbpp::Var</a> var, bool value)</td></tr>
<tr class="memdesc:a04aabe5387a17b86a90acaa607175545"><td class="mdescLeft">&#160;</td><td class="mdescRight">Set the value of the variable "var" to "value".  <a href="classqbpp__abs2_1_1Sol.html#a04aabe5387a17b86a90acaa607175545">More...</a><br /></td></tr>
<tr class="separator:a04aabe5387a17b86a90acaa607175545"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae35741170c9c1e91c8c700221fb053e2"><td class="memItemLeft" align="right" valign="top">double&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classqbpp__abs2_1_1Sol.html#ae35741170c9c1e91c8c700221fb053e2">get_tts</a> () const</td></tr>
<tr class="separator:ae35741170c9c1e91c8c700221fb053e2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8de3bebb198a0820db0446f9686e914d"><td class="memItemLeft" align="right" valign="top">const std::shared_ptr&lt; <a class="el" href="classabs2_1_1Sol.html">abs2::Sol</a> &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classqbpp__abs2_1_1Sol.html#a8de3bebb198a0820db0446f9686e914d">get_abs2sol_ptr</a> () const</td></tr>
<tr class="memdesc:a8de3bebb198a0820db0446f9686e914d"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the reference to the ABS2 solution.  <a href="classqbpp__abs2_1_1Sol.html#a8de3bebb198a0820db0446f9686e914d">More...</a><br /></td></tr>
<tr class="separator:a8de3bebb198a0820db0446f9686e914d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae30384ae030eaf5b08209f857ae14e97"><td class="memItemLeft" align="right" valign="top">bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classqbpp_1_1Sol.html#ae30384ae030eaf5b08209f857ae14e97">operator==</a> (const <a class="el" href="classqbpp__abs2_1_1Sol.html">Sol</a> &amp;sol) const</td></tr>
<tr class="separator:ae30384ae030eaf5b08209f857ae14e97"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1325a24d1d0d1a51223f2ef02608cc3e"><td class="memItemLeft" align="right" valign="top">bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classqbpp_1_1Sol.html#a1325a24d1d0d1a51223f2ef02608cc3e">operator&lt;</a> (const <a class="el" href="classqbpp__abs2_1_1Sol.html">Sol</a> &amp;sol) const</td></tr>
<tr class="separator:a1325a24d1d0d1a51223f2ef02608cc3e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:abdb4f6b779678720715f7845f264440f"><td class="memItemLeft" align="right" valign="top">var_val_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classqbpp_1_1Sol.html#abdb4f6b779678720715f7845f264440f">get</a> (vindex_t <a class="el" href="classqbpp_1_1Sol.html#a24f5a53fa68071dd9bcda910533bbad7">index</a>) const</td></tr>
<tr class="separator:abdb4f6b779678720715f7845f264440f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6543e96e459b50574cb17643c961b93a"><td class="memItemLeft" align="right" valign="top">var_val_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classqbpp_1_1Sol.html#a6543e96e459b50574cb17643c961b93a">get</a> (Var var) const</td></tr>
<tr class="separator:a6543e96e459b50574cb17643c961b93a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a427f81eb5091b76b4f197f3c42b5a1c5"><td class="memItemLeft" align="right" valign="top">energy_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classqbpp_1_1Sol.html#a427f81eb5091b76b4f197f3c42b5a1c5">get</a> (const Expr &amp;expr) const</td></tr>
<tr class="separator:a427f81eb5091b76b4f197f3c42b5a1c5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a97b2220c9849ce534280e5a54944a54b"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:a97b2220c9849ce534280e5a54944a54b"><td class="memTemplItemLeft" align="right" valign="top">auto&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="classqbpp_1_1Sol.html#a97b2220c9849ce534280e5a54944a54b">get</a> (const Vector&lt; T &gt; &amp;vec) const</td></tr>
<tr class="separator:a97b2220c9849ce534280e5a54944a54b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a62ab1f54864dd39a79cfd1a3cb4a838c"><td class="memItemLeft" align="right" valign="top">bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classqbpp_1_1Sol.html#a62ab1f54864dd39a79cfd1a3cb4a838c">has</a> (Var var) const</td></tr>
<tr class="separator:a62ab1f54864dd39a79cfd1a3cb4a838c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8acf56be292028a9601a6d7a3517ba7b"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classqbpp_1_1Sol.html#a8acf56be292028a9601a6d7a3517ba7b">clear</a> ()</td></tr>
<tr class="separator:a8acf56be292028a9601a6d7a3517ba7b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a16e5cbcab9500ff5bf032b6cb5ec5e8d"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classqbpp_1_1Sol.html#a16e5cbcab9500ff5bf032b6cb5ec5e8d">nullify</a> ()</td></tr>
<tr class="separator:a16e5cbcab9500ff5bf032b6cb5ec5e8d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa8c0a0717fbba880622a517e80bb86c7"><td class="memItemLeft" align="right" valign="top">bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classqbpp_1_1Sol.html#aa8c0a0717fbba880622a517e80bb86c7">is_null</a> () const</td></tr>
<tr class="separator:aa8c0a0717fbba880622a517e80bb86c7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2f7494eb4a254dd19b0261e801bacffb"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classqbpp_1_1Sol.html#a2f7494eb4a254dd19b0261e801bacffb">set</a> (vindex_t <a class="el" href="classqbpp_1_1Sol.html#a24f5a53fa68071dd9bcda910533bbad7">index</a>, bool value)</td></tr>
<tr class="separator:a2f7494eb4a254dd19b0261e801bacffb"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0a976747032bda528cc78f2400f601d8"><td class="memItemLeft" align="right" valign="top"><a class="el" href="classqbpp__abs2_1_1Sol.html">Sol</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classqbpp_1_1Sol.html#a0a976747032bda528cc78f2400f601d8">set</a> (const <a class="el" href="classqbpp__abs2_1_1Sol.html">Sol</a> &amp;sol)</td></tr>
<tr class="separator:a0a976747032bda528cc78f2400f601d8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae753a79a37102490b4818c3e04d9348d"><td class="memItemLeft" align="right" valign="top"><a class="el" href="classqbpp__abs2_1_1Sol.html">Sol</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classqbpp_1_1Sol.html#ae753a79a37102490b4818c3e04d9348d">set</a> (const MapList &amp;map_list)</td></tr>
<tr class="separator:ae753a79a37102490b4818c3e04d9348d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a27f590351140f1f04049f1500b7ada31"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classqbpp_1_1Sol.html#a27f590351140f1f04049f1500b7ada31">set64</a> (vindex_t index64, uint64_t value)</td></tr>
<tr class="separator:a27f590351140f1f04049f1500b7ada31"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a78255413d025557d55c0c16f4076f9fe"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classqbpp_1_1Sol.html#a78255413d025557d55c0c16f4076f9fe">bit_vector_flip</a> (vindex_t <a class="el" href="classqbpp_1_1Sol.html#a24f5a53fa68071dd9bcda910533bbad7">index</a>)</td></tr>
<tr class="separator:a78255413d025557d55c0c16f4076f9fe"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a4f4c097837ad52f4fd368e048e79aa66"><td class="memItemLeft" align="right" valign="top">virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classqbpp_1_1Sol.html#a4f4c097837ad52f4fd368e048e79aa66">flip</a> (vindex_t <a class="el" href="classqbpp_1_1Sol.html#a24f5a53fa68071dd9bcda910533bbad7">index</a>)</td></tr>
<tr class="separator:a4f4c097837ad52f4fd368e048e79aa66"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aae6d875ede912bcbf6254b74ffc66018"><td class="memItemLeft" align="right" valign="top">virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classqbpp_1_1Sol.html#aae6d875ede912bcbf6254b74ffc66018">flip_bit_add_delta</a> (vindex_t <a class="el" href="classqbpp_1_1Sol.html#a24f5a53fa68071dd9bcda910533bbad7">index</a>, energy_t delta)</td></tr>
<tr class="separator:aae6d875ede912bcbf6254b74ffc66018"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a12f17cd3af83d8fab14667f103f5f21e"><td class="memItemLeft" align="right" valign="top">vindex_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classqbpp_1_1Sol.html#a12f17cd3af83d8fab14667f103f5f21e">popcount</a> () const</td></tr>
<tr class="separator:a12f17cd3af83d8fab14667f103f5f21e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3ad455870df155ffed4701ce070cf335"><td class="memItemLeft" align="right" valign="top">energy_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classqbpp_1_1Sol.html#a3ad455870df155ffed4701ce070cf335">energy</a> () const</td></tr>
<tr class="separator:a3ad455870df155ffed4701ce070cf335"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a16504ce467c6fbfcefea4171a492bef1"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classqbpp_1_1Sol.html#a16504ce467c6fbfcefea4171a492bef1">set_energy</a> (energy_t <a class="el" href="classqbpp_1_1Sol.html#a3ad455870df155ffed4701ce070cf335">energy</a>)</td></tr>
<tr class="separator:a16504ce467c6fbfcefea4171a492bef1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1407e31d02d063a89ea14c3872bdae26"><td class="memItemLeft" align="right" valign="top">bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classqbpp_1_1Sol.html#a1407e31d02d063a89ea14c3872bdae26">energy_has_value</a> () const</td></tr>
<tr class="separator:a1407e31d02d063a89ea14c3872bdae26"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa8b89bea21691be375e3e33d152df3b8"><td class="memItemLeft" align="right" valign="top">energy_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classqbpp_1_1Sol.html#aa8b89bea21691be375e3e33d152df3b8">add_energy</a> (energy_t <a class="el" href="classqbpp_1_1Sol.html#a3ad455870df155ffed4701ce070cf335">energy</a>)</td></tr>
<tr class="separator:aa8b89bea21691be375e3e33d152df3b8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aae14179040e70ddf134c560790b602aa"><td class="memItemLeft" align="right" valign="top">const impl::BitVector &amp;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classqbpp_1_1Sol.html#aae14179040e70ddf134c560790b602aa">bit_vector</a> () const</td></tr>
<tr class="separator:aae14179040e70ddf134c560790b602aa"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af2b8d3ee4c5097b2b44306c05dd0a6d1"><td class="memItemLeft" align="right" valign="top">impl::BitVector &amp;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classqbpp_1_1Sol.html#af2b8d3ee4c5097b2b44306c05dd0a6d1">bit_vector</a> ()</td></tr>
<tr class="separator:af2b8d3ee4c5097b2b44306c05dd0a6d1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2a8846783606a331e5892f4ae022bf86"><td class="memItemLeft" align="right" valign="top">uint64_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classqbpp_1_1Sol.html#a2a8846783606a331e5892f4ae022bf86">operator[]</a> (size_t i) const</td></tr>
<tr class="separator:a2a8846783606a331e5892f4ae022bf86"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aade76db5219653d4c8f89c8971dcf35c"><td class="memItemLeft" align="right" valign="top">uint64_t &amp;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classqbpp_1_1Sol.html#aade76db5219653d4c8f89c8971dcf35c">operator[]</a> (size_t i)</td></tr>
<tr class="separator:aade76db5219653d4c8f89c8971dcf35c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a06cbfd519670efbc74363592d5e826b2"><td class="memItemLeft" align="right" valign="top">vindex_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classqbpp_1_1Sol.html#a06cbfd519670efbc74363592d5e826b2">var_count</a> () const</td></tr>
<tr class="separator:a06cbfd519670efbc74363592d5e826b2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a776c4f721be7e67bc2c801c126989953"><td class="memItemLeft" align="right" valign="top">energy_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classqbpp_1_1Sol.html#a776c4f721be7e67bc2c801c126989953">constant</a> () const</td></tr>
<tr class="separator:a776c4f721be7e67bc2c801c126989953"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3db07b94f467d91e0be73e15fe5bd453"><td class="memItemLeft" align="right" valign="top">Var&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classqbpp_1_1Sol.html#a3db07b94f467d91e0be73e15fe5bd453">get_var</a> (vindex_t <a class="el" href="classqbpp_1_1Sol.html#a24f5a53fa68071dd9bcda910533bbad7">index</a>) const</td></tr>
<tr class="separator:a3db07b94f467d91e0be73e15fe5bd453"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a24f5a53fa68071dd9bcda910533bbad7"><td class="memItemLeft" align="right" valign="top">vindex_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classqbpp_1_1Sol.html#a24f5a53fa68071dd9bcda910533bbad7">index</a> (Var var) const</td></tr>
<tr class="separator:a24f5a53fa68071dd9bcda910533bbad7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3e3cf991914a1e6743a4e3e0b987f2ec"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classqbpp_1_1Sol.html#a3e3cf991914a1e6743a4e3e0b987f2ec">operator MapList</a> () const</td></tr>
<tr class="separator:a3e3cf991914a1e6743a4e3e0b987f2ec"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pro-methods"></a>
Protected Member Functions</h2></td></tr>
<tr class="memitem:a90c464b3df40d670646cb6f940462275"><td class="memItemLeft" align="right" valign="top">energy_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classqbpp_1_1Sol.html#a90c464b3df40d670646cb6f940462275">comp_energy</a> () const</td></tr>
<tr class="separator:a90c464b3df40d670646cb6f940462275"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8755c0c3c5f2da04d9f5fc7ad031d1d4"><td class="memItemLeft" align="right" valign="top">MapList&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classqbpp_1_1Sol.html#a8755c0c3c5f2da04d9f5fc7ad031d1d4">get_map_list</a> () const</td></tr>
<tr class="separator:a8755c0c3c5f2da04d9f5fc7ad031d1d4"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pro-attribs"></a>
Protected Attributes</h2></td></tr>
<tr class="memitem:a11752b0b3742bec8da842db87ba94230"><td class="memItemLeft" align="right" valign="top">const <a class="el" href="classqbpp__abs2_1_1QuadModel.html">QuadModel</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classqbpp_1_1Sol.html#a11752b0b3742bec8da842db87ba94230">quad_model_</a></td></tr>
<tr class="separator:a11752b0b3742bec8da842db87ba94230"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a97a3c5acd6aff38c15164146e453b2bf"><td class="memItemLeft" align="right" valign="top">impl::BitVector&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classqbpp_1_1Sol.html#a97a3c5acd6aff38c15164146e453b2bf">bit_vector_</a></td></tr>
<tr class="separator:a97a3c5acd6aff38c15164146e453b2bf"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5dc3ec6002a1d823ad1bd525e9f6fa3e"><td class="memItemLeft" align="right" valign="top">std::optional&lt; energy_t &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classqbpp_1_1Sol.html#a5dc3ec6002a1d823ad1bd525e9f6fa3e">energy_</a> = std::nullopt</td></tr>
<tr class="separator:a5dc3ec6002a1d823ad1bd525e9f6fa3e"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pri-attribs"></a>
Private Attributes</h2></td></tr>
<tr class="memitem:a2979356a3719cec1699dab0ba5c80ea7"><td class="memItemLeft" align="right" valign="top">const std::shared_ptr&lt; <a class="el" href="classabs2_1_1Sol.html">abs2::Sol</a> &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classqbpp__abs2_1_1Sol.html#a2979356a3719cec1699dab0ba5c80ea7">abs2sol_ptr</a></td></tr>
<tr class="memdesc:a2979356a3719cec1699dab0ba5c80ea7"><td class="mdescLeft">&#160;</td><td class="mdescRight"><a class="el" href="classqbpp__abs2_1_1Sol.html" title="A class for storing the ABS2 QUBO solution.">Sol</a> object created by ABS2 QUBO solver.  <a href="classqbpp__abs2_1_1Sol.html#a2979356a3719cec1699dab0ba5c80ea7">More...</a><br /></td></tr>
<tr class="separator:a2979356a3719cec1699dab0ba5c80ea7"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p>A class for storing the ABS2 QUBO solution. </p>
<p>This class is derived from <a class="el" href="classqbpp_1_1Sol.html">qbpp::Sol</a> and containing an <a class="el" href="classabs2_1_1Sol.html" title="Class to store a solution computed by ABS2 QUBO solver.">abs2::Sol</a> object. </p>

<p class="definition">Definition at line <a class="el" href="qbpp__abs2_8hpp_source.html#l00148">148</a> of file <a class="el" href="qbpp__abs2_8hpp_source.html">qbpp_abs2.hpp</a>.</p>
</div><h2 class="groupheader">Constructor &amp; Destructor Documentation</h2>
<a id="ac7b3defb0e488e8570785400889f4c77"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ac7b3defb0e488e8570785400889f4c77">&#9670;&nbsp;</a></span>Sol()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">qbpp_abs2::Sol::Sol </td>
          <td>(</td>
          <td class="paramtype">const <a class="el" href="classqbpp__abs2_1_1Sol.html">Sol</a> &amp;&#160;</td>
          <td class="paramname"><em>sol</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">default</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Copy constructor. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">sol</td><td><a class="el" href="classqbpp__abs2_1_1Sol.html" title="A class for storing the ABS2 QUBO solution.">Sol</a> object </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<h2 class="groupheader">Member Function Documentation</h2>
<a id="a32d5e94f5ec2e824cbb8669e5bb1c69a"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a32d5e94f5ec2e824cbb8669e5bb1c69a">&#9670;&nbsp;</a></span>print()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">void qbpp_abs2::Sol::print </td>
          <td>(</td>
          <td class="paramtype">const std::string &amp;&#160;</td>
          <td class="paramname"><em>attrs</em></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>print the solution </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">attrs</td><td>attributes to print </td></tr>
  </table>
  </dd>
</dl>

<p class="definition">Definition at line <a class="el" href="qbpp__abs2_8hpp_source.html#l00174">174</a> of file <a class="el" href="qbpp__abs2_8hpp_source.html">qbpp_abs2.hpp</a>.</p>

</div>
</div>
<a id="a48d9116f9fcbe00f84640847ee49df23"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a48d9116f9fcbe00f84640847ee49df23">&#9670;&nbsp;</a></span>set() <span class="overload">[1/5]</span></h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">void qbpp_abs2::Sol::set </td>
          <td>(</td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>index</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">bool&#160;</td>
          <td class="paramname"><em>value</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Set the value of the variable with "index" to "value". </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">index</td><td>Variable index </td></tr>
    <tr><td class="paramname">value</td><td>Value </td></tr>
  </table>
  </dd>
</dl>

<p class="definition">Definition at line <a class="el" href="qbpp__abs2_8hpp_source.html#l00179">179</a> of file <a class="el" href="qbpp__abs2_8hpp_source.html">qbpp_abs2.hpp</a>.</p>
<div class="dynheader">
Here is the call graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="classqbpp__abs2_1_1Sol_a48d9116f9fcbe00f84640847ee49df23_cgraph.png" border="0" usemap="#classqbpp__abs2_1_1Sol_a48d9116f9fcbe00f84640847ee49df23_cgraph" alt=""/></div>
<map name="classqbpp__abs2_1_1Sol_a48d9116f9fcbe00f84640847ee49df23_cgraph" id="classqbpp__abs2_1_1Sol_a48d9116f9fcbe00f84640847ee49df23_cgraph">
<area shape="rect" title="Set the value of the variable with &quot;index&quot; to &quot;value&quot;." alt="" coords="5,56,144,83"/>
<area shape="rect" href="classqbpp_1_1Sol.html#a24f5a53fa68071dd9bcda910533bbad7" title=" " alt="" coords="370,31,486,57"/>
<area shape="rect" href="classqbpp_1_1Sol.html#a2f7494eb4a254dd19b0261e801bacffb" title=" " alt="" coords="192,81,295,108"/>
<area shape="rect" href="classqbpp_1_1Model.html#a0712035184681aeb478c5e80bb5221bd" title=" " alt="" coords="561,5,693,32"/>
<area shape="rect" href="namespaceqbpp.html#a1cf3f678bcc7eee089c75d4bc5a0b959" title=" " alt="" coords="589,56,665,83"/>
<area shape="rect" href="classqbpp_1_1Model.html#a52da2204913c7f529be7e8db017aa14a" title=" " alt="" coords="741,5,860,32"/>
<area shape="rect" href="namespaceqbpp.html#a844402a465157e00bca032af58c0a795" title=" " alt="" coords="763,56,839,83"/>
<area shape="rect" href="classqbpp_1_1impl_1_1BitVector.html#a4694931fe6e873494026bbbe9334f848" title=" " alt="" coords="343,81,513,108"/>
</map>
</div>

</div>
</div>
<a id="a04aabe5387a17b86a90acaa607175545"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a04aabe5387a17b86a90acaa607175545">&#9670;&nbsp;</a></span>set() <span class="overload">[2/5]</span></h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">void qbpp_abs2::Sol::set </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="classqbpp_1_1Var.html">qbpp::Var</a>&#160;</td>
          <td class="paramname"><em>var</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">bool&#160;</td>
          <td class="paramname"><em>value</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Set the value of the variable "var" to "value". </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">var</td><td>Variable </td></tr>
    <tr><td class="paramname">value</td><td>Value </td></tr>
  </table>
  </dd>
</dl>

<p class="definition">Definition at line <a class="el" href="qbpp__abs2_8hpp_source.html#l00187">187</a> of file <a class="el" href="qbpp__abs2_8hpp_source.html">qbpp_abs2.hpp</a>.</p>
<div class="dynheader">
Here is the call graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="classqbpp__abs2_1_1Sol_a04aabe5387a17b86a90acaa607175545_cgraph.png" border="0" usemap="#classqbpp__abs2_1_1Sol_a04aabe5387a17b86a90acaa607175545_cgraph" alt=""/></div>
<map name="classqbpp__abs2_1_1Sol_a04aabe5387a17b86a90acaa607175545_cgraph" id="classqbpp__abs2_1_1Sol_a04aabe5387a17b86a90acaa607175545_cgraph">
<area shape="rect" title="Set the value of the variable &quot;var&quot; to &quot;value&quot;." alt="" coords="5,56,144,83"/>
<area shape="rect" href="classqbpp_1_1Sol.html#a24f5a53fa68071dd9bcda910533bbad7" title=" " alt="" coords="370,56,486,83"/>
<area shape="rect" href="namespaceqbpp.html#a1cf3f678bcc7eee089c75d4bc5a0b959" title=" " alt="" coords="589,5,665,32"/>
<area shape="rect" href="classqbpp_1_1Sol.html#a2f7494eb4a254dd19b0261e801bacffb" title=" " alt="" coords="192,107,295,133"/>
<area shape="rect" href="classqbpp_1_1Model.html#a0712035184681aeb478c5e80bb5221bd" title=" " alt="" coords="561,56,693,83"/>
<area shape="rect" href="classqbpp_1_1Model.html#a52da2204913c7f529be7e8db017aa14a" title=" " alt="" coords="741,56,860,83"/>
<area shape="rect" href="namespaceqbpp.html#a844402a465157e00bca032af58c0a795" title=" " alt="" coords="763,5,839,32"/>
<area shape="rect" href="classqbpp_1_1impl_1_1BitVector.html#a4694931fe6e873494026bbbe9334f848" title=" " alt="" coords="343,107,513,133"/>
</map>
</div>

</div>
</div>
<a id="ae35741170c9c1e91c8c700221fb053e2"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ae35741170c9c1e91c8c700221fb053e2">&#9670;&nbsp;</a></span>get_tts()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">double qbpp_abs2::Sol::get_tts </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p class="definition">Definition at line <a class="el" href="qbpp__abs2_8hpp_source.html#l00192">192</a> of file <a class="el" href="qbpp__abs2_8hpp_source.html">qbpp_abs2.hpp</a>.</p>

</div>
</div>
<a id="a8de3bebb198a0820db0446f9686e914d"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a8de3bebb198a0820db0446f9686e914d">&#9670;&nbsp;</a></span>get_abs2sol_ptr()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">const std::shared_ptr&lt;<a class="el" href="classabs2_1_1Sol.html">abs2::Sol</a>&gt; qbpp_abs2::Sol::get_abs2sol_ptr </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Returns the reference to the ABS2 solution. </p>
<dl class="section return"><dt>Returns</dt><dd>ABS2 solution </dd></dl>

<p class="definition">Definition at line <a class="el" href="qbpp__abs2_8hpp_source.html#l00196">196</a> of file <a class="el" href="qbpp__abs2_8hpp_source.html">qbpp_abs2.hpp</a>.</p>
<div class="dynheader">
Here is the caller graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="classqbpp__abs2_1_1Sol_a8de3bebb198a0820db0446f9686e914d_icgraph.png" border="0" usemap="#classqbpp__abs2_1_1Sol_a8de3bebb198a0820db0446f9686e914d_icgraph" alt=""/></div>
<map name="classqbpp__abs2_1_1Sol_a8de3bebb198a0820db0446f9686e914d_icgraph" id="classqbpp__abs2_1_1Sol_a8de3bebb198a0820db0446f9686e914d_icgraph">
<area shape="rect" title="Returns the reference to the ABS2 solution." alt="" coords="416,39,555,80"/>
<area shape="rect" href="group__qbpp__abs2.html#ga515435f4a02b9b0daba94b59e01d5556" title="Executes ABS2 for model with &quot;param&quot; and &quot;start&quot;, and returns &quot;sol&quot;." alt="" coords="227,5,365,47"/>
<area shape="rect" href="group__qbpp__abs2.html#gacde6f2007700c2ac188f3a88c5bba1d0" title="Provide a hint solution to the ABS2 solver." alt="" coords="224,71,368,112"/>
<area shape="rect" href="classqbpp_1_1factorization_1_1ABS2Callback.html#abf19e5e8a6b948d74128b06d35313098" title="The default callback function for ABS2 QUBO solver." alt="" coords="5,71,176,112"/>
</map>
</div>

</div>
</div>
<a id="a90c464b3df40d670646cb6f940462275"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a90c464b3df40d670646cb6f940462275">&#9670;&nbsp;</a></span>comp_energy()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">energy_t qbpp::Sol::comp_energy </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span><span class="mlabel">protected</span><span class="mlabel">inherited</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p class="definition">Definition at line <a class="el" href="qbpp_8hpp_source.html#l03492">3492</a> of file <a class="el" href="qbpp_8hpp_source.html">qbpp.hpp</a>.</p>
<div class="dynheader">
Here is the caller graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="classqbpp_1_1Sol_a90c464b3df40d670646cb6f940462275_icgraph.png" border="0" usemap="#classqbpp_1_1Sol_a90c464b3df40d670646cb6f940462275_icgraph" alt=""/></div>
<map name="classqbpp_1_1Sol_a90c464b3df40d670646cb6f940462275_icgraph" id="classqbpp_1_1Sol_a90c464b3df40d670646cb6f940462275_icgraph">
<area shape="rect" title=" " alt="" coords="1655,631,1817,658"/>
<area shape="rect" href="classqbpp_1_1Sol.html#a3ad455870df155ffed4701ce070cf335" title=" " alt="" coords="1483,465,1607,491"/>
<area shape="rect" href="classqbpp_1_1exhaustive__solver_1_1SolDelta.html#ae53a1eb415153c3d6e02794123a87b5a" title=" " alt="" coords="1265,824,1427,865"/>
<area shape="rect" href="classqbpp_1_1Sol.html#aa8b89bea21691be375e3e33d152df3b8" title=" " alt="" coords="1270,263,1422,290"/>
<area shape="rect" href="classMyEasySolver.html#a35d6dcb0df4a69bd5835c4044c822e98" title=" " alt="" coords="1047,162,1209,189"/>
<area shape="rect" href="classqbpp__grb_1_1Callback.html#a8b785f76ab9c73fa92b97bb8da6f5e37" title="Default callback function for Gurobi Optimizer." alt="" coords="845,489,979,531"/>
<area shape="rect" href="classqbpp_1_1easy__solver_1_1EasySolver.html#adbadd766f998ea7171e18493a3538d9d" title=" " alt="" coords="1273,96,1419,137"/>
<area shape="rect" href="classqbpp_1_1easy__solver_1_1BestSols.html#a50e4d669aace640e64c43cebac64dc78" title=" " alt="" coords="1278,482,1414,538"/>
<area shape="rect" href="classqbpp_1_1Sol.html#a1325a24d1d0d1a51223f2ef02608cc3e" title=" " alt="" coords="1276,562,1416,589"/>
<area shape="rect" href="classqbpp_1_1Sol.html#ae30384ae030eaf5b08209f857ae14e97" title=" " alt="" coords="1272,613,1420,639"/>
<area shape="rect" href="classqbpp_1_1tsp_1_1TSPSol.html#ab2abe5283ba0f9fefad71b1beee340e3" title="Print the tour." alt="" coords="1277,315,1415,356"/>
<area shape="rect" href="classqbpp_1_1exhaustive__solver_1_1SearchAlgorithm.html#a73cc42b1cd9e72aa4a56eb25354ca21e" title=" " alt="" coords="1257,743,1435,799"/>
<area shape="rect" href="classqbpp_1_1Sol.html#a16504ce467c6fbfcefea4171a492bef1" title=" " alt="" coords="1271,381,1421,407"/>
<area shape="rect" href="classqbpp_1_1factorization_1_1SolHolder.html#a46e8fb339ad5d7983403f1785f5c24e5" title="Sets new_sol as the best solution if it is better than the current best solution." alt="" coords="1281,663,1411,719"/>
<area shape="rect" href="namespaceqbpp.html#a0a822c8155843fe78c8ac4ddd95f1681" title=" " alt="" coords="1309,213,1383,239"/>
<area shape="rect" href="classqbpp_1_1easy__solver_1_1SolDelta.html#ac0f902682f6255272a79333a947e1a24" title=" " alt="" coords="1052,96,1204,137"/>
<area shape="rect" href="classqbpp_1_1easy__solver_1_1SolDelta.html#acf11d3f5b86a20077d4c20dc9c18e044" title=" " alt="" coords="844,125,980,167"/>
<area shape="rect" href="classqbpp_1_1easy__solver_1_1PosMinSolDelta.html#aff102bc17a0d06a78a285f1ab2c9632e" title=" " alt="" coords="607,136,769,177"/>
<area shape="rect" href="classqbpp_1_1easy__solver_1_1SolDelta.html#ae5e7dbc16325f4f3b26895fe8686afc5" title=" " alt="" coords="620,5,756,47"/>
<area shape="rect" href="classqbpp_1_1easy__solver_1_1SolDelta.html#ad0505230c61ae317b5414f06327daeb4" title=" " alt="" coords="615,71,761,112"/>
<area shape="rect" href="classqbpp_1_1easy__solver_1_1EasySolver.html#a329a03e4e5c401ec471e0f7f4c42cb51" title=" " alt="" coords="373,71,551,112"/>
<area shape="rect" href="classqbpp_1_1easy__solver_1_1EasySolver.html#a9a61ebfb6366c734e7af2b08890753f0" title=" " alt="" coords="189,71,325,112"/>
<area shape="rect" href="graph__color__easy_8cpp.html#a3c04138a5bfe5d72780bb7e82a18e627" title="Main function to generate a random map and solve the Graph Coloring Problem using the Easy Solver." alt="" coords="48,49,99,75"/>
<area shape="rect" href="classqbpp_1_1easy__solver_1_1EasySolver.html#a8d5bc85759b94bc1c1bf2db59fc323b0" title=" " alt="" coords="5,100,141,141"/>
<area shape="rect" href="classqbpp_1_1exhaustive__solver_1_1SearchAlgorithm.html#ad031941ba26d6f9e9262803068fbfc7e" title=" " alt="" coords="1047,846,1209,902"/>
<area shape="rect" href="classqbpp_1_1exhaustive__solver_1_1SolDelta.html#a926e9faffb265b7208b75663e76f77da" title=" " alt="" coords="1047,780,1209,821"/>
<area shape="rect" href="classqbpp_1_1exhaustive__solver_1_1SearchAlgorithm.html#a3a2b22a2908c11bf374b4f06b2ad1815" title=" " alt="" coords="825,780,999,821"/>
<area shape="rect" href="classqbpp_1_1exhaustive__solver_1_1ExhaustiveSolver.html#a967d19602b7de9fef4198508924c7a25" title=" " alt="" coords="599,780,777,821"/>
<area shape="rect" href="classABS2Callback.html#a51998226f55f98e4870d89d4d5bf4d29" title="Callback function for ABS2 solver." alt="" coords="1047,263,1209,290"/>
<area shape="rect" href="classGRB__Callback.html#ac08cbd22e13d589be7ca35178d3126d0" title="callback function for Gurobi optimizer." alt="" coords="829,318,995,345"/>
<area shape="rect" href="tsp__abs2_8cpp.html#a3c04138a5bfe5d72780bb7e82a18e627" title="Main function to generate a random map and solve the Traveling Salesman Problem (TSP) using the ABS2 ..." alt="" coords="1103,213,1153,239"/>
<area shape="rect" href="classqbpp__grb_1_1Callback.html#a6d111191d65a98194fec8779fac6dadd" title="Get the solution obtained by Gurobi Optimizer." alt="" coords="1061,431,1195,472"/>
<area shape="rect" href="classqbpp__grb_1_1QuadModel.html#ad3add2ab2e0d80e5d0d8123deebc6e98" title="Optimize the QUBO model." alt="" coords="1053,365,1203,407"/>
<area shape="rect" href="classqbpp_1_1factorization_1_1GRB__Callback.html#a5b00538ab4f4123134dacfaa79c3cd4c" title="callback function for Gurobi optimizer." alt="" coords="825,424,999,465"/>
<area shape="rect" href="tsp__grb_8cpp.html#a3c04138a5bfe5d72780bb7e82a18e627" title="Main function to generate a random map and solve the Traveling Salesman Problem (TSP) using the Gurob..." alt="" coords="887,373,937,399"/>
</map>
</div>

</div>
</div>
<a id="a8755c0c3c5f2da04d9f5fc7ad031d1d4"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a8755c0c3c5f2da04d9f5fc7ad031d1d4">&#9670;&nbsp;</a></span>get_map_list()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">MapList qbpp::Sol::get_map_list </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span><span class="mlabel">protected</span><span class="mlabel">inherited</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p class="definition">Definition at line <a class="el" href="qbpp_8hpp_source.html#l03517">3517</a> of file <a class="el" href="qbpp_8hpp_source.html">qbpp.hpp</a>.</p>
<div class="dynheader">
Here is the call graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="classqbpp_1_1Sol_a8755c0c3c5f2da04d9f5fc7ad031d1d4_cgraph.png" border="0" usemap="#classqbpp_1_1Sol_a8755c0c3c5f2da04d9f5fc7ad031d1d4_cgraph" alt=""/></div>
<map name="classqbpp_1_1Sol_a8755c0c3c5f2da04d9f5fc7ad031d1d4_cgraph" id="classqbpp_1_1Sol_a8755c0c3c5f2da04d9f5fc7ad031d1d4_cgraph">
<area shape="rect" title=" " alt="" coords="5,5,164,32"/>
<area shape="rect" href="namespaceqbpp.html#a844402a465157e00bca032af58c0a795" title=" " alt="" coords="212,5,288,32"/>
</map>
</div>
<div class="dynheader">
Here is the caller graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="classqbpp_1_1Sol_a8755c0c3c5f2da04d9f5fc7ad031d1d4_icgraph.png" border="0" usemap="#classqbpp_1_1Sol_a8755c0c3c5f2da04d9f5fc7ad031d1d4_icgraph" alt=""/></div>
<map name="classqbpp_1_1Sol_a8755c0c3c5f2da04d9f5fc7ad031d1d4_icgraph" id="classqbpp_1_1Sol_a8755c0c3c5f2da04d9f5fc7ad031d1d4_icgraph">
<area shape="rect" title=" " alt="" coords="185,13,344,39"/>
<area shape="rect" href="classqbpp_1_1Sol.html#a3e3cf991914a1e6743a4e3e0b987f2ec" title=" " alt="" coords="5,5,137,47"/>
</map>
</div>

</div>
</div>
<a id="ae30384ae030eaf5b08209f857ae14e97"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ae30384ae030eaf5b08209f857ae14e97">&#9670;&nbsp;</a></span>operator==()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">bool qbpp::Sol::operator== </td>
          <td>(</td>
          <td class="paramtype">const <a class="el" href="classqbpp_1_1Sol.html">Sol</a> &amp;&#160;</td>
          <td class="paramname"><em>sol</em></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span><span class="mlabel">inherited</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p class="definition">Definition at line <a class="el" href="qbpp_8hpp_source.html#l02230">2230</a> of file <a class="el" href="qbpp_8hpp_source.html">qbpp.hpp</a>.</p>
<div class="dynheader">
Here is the call graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="classqbpp_1_1Sol_ae30384ae030eaf5b08209f857ae14e97_cgraph.png" border="0" usemap="#classqbpp_1_1Sol_ae30384ae030eaf5b08209f857ae14e97_cgraph" alt=""/></div>
<map name="classqbpp_1_1Sol_ae30384ae030eaf5b08209f857ae14e97_cgraph" id="classqbpp_1_1Sol_ae30384ae030eaf5b08209f857ae14e97_cgraph">
<area shape="rect" title=" " alt="" coords="5,5,153,32"/>
<area shape="rect" href="classqbpp_1_1Sol.html#a3ad455870df155ffed4701ce070cf335" title=" " alt="" coords="201,5,325,32"/>
<area shape="rect" href="classqbpp_1_1Sol.html#a90c464b3df40d670646cb6f940462275" title=" " alt="" coords="373,5,536,32"/>
</map>
</div>

</div>
</div>
<a id="a1325a24d1d0d1a51223f2ef02608cc3e"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a1325a24d1d0d1a51223f2ef02608cc3e">&#9670;&nbsp;</a></span>operator&lt;()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">bool qbpp::Sol::operator&lt; </td>
          <td>(</td>
          <td class="paramtype">const <a class="el" href="classqbpp_1_1Sol.html">Sol</a> &amp;&#160;</td>
          <td class="paramname"><em>sol</em></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span><span class="mlabel">inherited</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p class="definition">Definition at line <a class="el" href="qbpp_8hpp_source.html#l02234">2234</a> of file <a class="el" href="qbpp_8hpp_source.html">qbpp.hpp</a>.</p>
<div class="dynheader">
Here is the call graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="classqbpp_1_1Sol_a1325a24d1d0d1a51223f2ef02608cc3e_cgraph.png" border="0" usemap="#classqbpp_1_1Sol_a1325a24d1d0d1a51223f2ef02608cc3e_cgraph" alt=""/></div>
<map name="classqbpp_1_1Sol_a1325a24d1d0d1a51223f2ef02608cc3e_cgraph" id="classqbpp_1_1Sol_a1325a24d1d0d1a51223f2ef02608cc3e_cgraph">
<area shape="rect" title=" " alt="" coords="5,5,145,32"/>
<area shape="rect" href="classqbpp_1_1Sol.html#a3ad455870df155ffed4701ce070cf335" title=" " alt="" coords="193,5,317,32"/>
<area shape="rect" href="classqbpp_1_1Sol.html#a90c464b3df40d670646cb6f940462275" title=" " alt="" coords="365,5,528,32"/>
</map>
</div>

</div>
</div>
<a id="abdb4f6b779678720715f7845f264440f"></a>
<h2 class="memtitle"><span class="permalink"><a href="#abdb4f6b779678720715f7845f264440f">&#9670;&nbsp;</a></span>get() <span class="overload">[1/4]</span></h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">var_val_t qbpp::Sol::get </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="namespaceqbpp.html#a5cf436100ede362797d0c0501ea50a5a">vindex_t</a>&#160;</td>
          <td class="paramname"><em>index</em></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span><span class="mlabel">inherited</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p class="definition">Definition at line <a class="el" href="qbpp_8hpp_source.html#l02243">2243</a> of file <a class="el" href="qbpp_8hpp_source.html">qbpp.hpp</a>.</p>
<div class="dynheader">
Here is the call graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="classqbpp_1_1Sol_abdb4f6b779678720715f7845f264440f_cgraph.png" border="0" usemap="#classqbpp_1_1Sol_abdb4f6b779678720715f7845f264440f_cgraph" alt=""/></div>
<map name="classqbpp_1_1Sol_abdb4f6b779678720715f7845f264440f_cgraph" id="classqbpp_1_1Sol_abdb4f6b779678720715f7845f264440f_cgraph">
<area shape="rect" title=" " alt="" coords="5,31,108,57"/>
<area shape="rect" href="classqbpp_1_1impl_1_1BitVector.html#a12302d4df0b9f233c0e93ae87f89d6da" title=" " alt="" coords="156,5,327,32"/>
<area shape="rect" href="classqbpp_1_1Sol.html#a24f5a53fa68071dd9bcda910533bbad7" title=" " alt="" coords="183,56,299,83"/>
<area shape="rect" href="classqbpp_1_1Model.html#a0712035184681aeb478c5e80bb5221bd" title=" " alt="" coords="375,31,507,57"/>
<area shape="rect" href="namespaceqbpp.html#a1cf3f678bcc7eee089c75d4bc5a0b959" title=" " alt="" coords="403,81,479,108"/>
<area shape="rect" href="classqbpp_1_1Model.html#a52da2204913c7f529be7e8db017aa14a" title=" " alt="" coords="555,31,673,57"/>
<area shape="rect" href="namespaceqbpp.html#a844402a465157e00bca032af58c0a795" title=" " alt="" coords="576,81,652,108"/>
</map>
</div>
<div class="dynheader">
Here is the caller graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="classqbpp_1_1Sol_abdb4f6b779678720715f7845f264440f_icgraph.png" border="0" usemap="#classqbpp_1_1Sol_abdb4f6b779678720715f7845f264440f_icgraph" alt=""/></div>
<map name="classqbpp_1_1Sol_abdb4f6b779678720715f7845f264440f_icgraph" id="classqbpp_1_1Sol_abdb4f6b779678720715f7845f264440f_icgraph">
<area shape="rect" title=" " alt="" coords="1255,495,1357,521"/>
<area shape="rect" href="namespaceqbpp_1_1factorization.html#a77868b746b7cb1a8a7e3707ddb5871f9" title="Converts a vector of Var in a solution to a 64&#45;bit unsigned integer." alt="" coords="1055,86,1184,127"/>
<area shape="rect" href="namespaceqbpp.html#ac429a7530957802c68d0ba6188daad94" title=" " alt="" coords="1078,224,1161,251"/>
<area shape="rect" href="classqbpp_1_1easy__solver_1_1SolDelta.html#a1a107d8974386a07d201e88fb2c9aed9" title=" " alt="" coords="1051,275,1187,317"/>
<area shape="rect" href="classqbpp_1_1easy__solver_1_1SolDelta.html#ae5e7dbc16325f4f3b26895fe8686afc5" title=" " alt="" coords="835,334,971,375"/>
<area shape="rect" href="classqbpp_1_1exhaustive__solver_1_1SolDelta.html#ae53a1eb415153c3d6e02794123a87b5a" title=" " alt="" coords="1038,393,1201,434"/>
<area shape="rect" href="classqbpp_1_1tsp_1_1TSPSol.html#a850469299d3a30962180e13657e16452" title="helper function to generate a tour from the solution." alt="" coords="1051,458,1188,499"/>
<area shape="rect" href="classqbpp_1_1Sol.html#a97b2220c9849ce534280e5a54944a54b" title=" " alt="" coords="1068,524,1171,551"/>
<area shape="rect" href="classqbpp_1_1Var.html#a73945543f5478d768f5a1ecbd3aaf31c" title=" " alt="" coords="1049,575,1189,601"/>
<area shape="rect" href="classqbpp_1_1tsp_1_1TSPSol.html#aeaddec0d0368bef74ebcbda49fe863fe" title=" " alt="" coords="1051,626,1188,667"/>
<area shape="rect" href="classqbpp_1_1Sol.html#a0a976747032bda528cc78f2400f601d8" title=" " alt="" coords="1068,692,1171,719"/>
<area shape="rect" href="classqbpp_1_1graph__color_1_1GraphColorMap.html#ad90af91eeca9f99c1a611d144cae1934" title="Set the color histogram of the nodes." alt="" coords="1032,743,1207,799"/>
<area shape="rect" href="group__qbpp__abs2.html#gac27ae911e72fdeb424a950d41fbef95c" title="Construct a new Sol object from a QUBO++ QUBO solution." alt="" coords="1050,823,1189,849"/>
<area shape="rect" href="namespaceqbpp_1_1factorization.html#a36079855e22273fe01e4c9eb34e57557" title="Converts a vector of Var in a solution holder to a 64&#45;bit unsigned integer." alt="" coords="838,86,967,127"/>
<area shape="rect" href="classqbpp_1_1factorization_1_1SolHolder.html#a46e8fb339ad5d7983403f1785f5c24e5" title="Sets new_sol as the best solution if it is better than the current best solution." alt="" coords="838,5,967,61"/>
<area shape="rect" href="classqbpp_1_1easy__solver_1_1TabuSolDelta.html#abddd3367c047935e82e517c421cb5a9a" title=" " alt="" coords="835,151,971,193"/>
<area shape="rect" href="classqbpp_1_1easy__solver_1_1SolDelta.html#acf11d3f5b86a20077d4c20dc9c18e044" title=" " alt="" coords="835,217,971,258"/>
<area shape="rect" href="classqbpp_1_1easy__solver_1_1SolDelta.html#ad0505230c61ae317b5414f06327daeb4" title=" " alt="" coords="613,275,760,317"/>
<area shape="rect" href="classqbpp_1_1easy__solver_1_1PosMinSolDelta.html#aff102bc17a0d06a78a285f1ab2c9632e" title=" " alt="" coords="605,210,768,251"/>
<area shape="rect" href="classqbpp_1_1easy__solver_1_1EasySolver.html#a329a03e4e5c401ec471e0f7f4c42cb51" title=" " alt="" coords="374,275,551,317"/>
<area shape="rect" href="classqbpp_1_1easy__solver_1_1EasySolver.html#a9a61ebfb6366c734e7af2b08890753f0" title=" " alt="" coords="189,275,325,317"/>
<area shape="rect" href="graph__color__easy_8cpp.html#a3c04138a5bfe5d72780bb7e82a18e627" title="Main function to generate a random map and solve the Graph Coloring Problem using the Easy Solver." alt="" coords="48,253,99,280"/>
<area shape="rect" href="classqbpp_1_1easy__solver_1_1EasySolver.html#a8d5bc85759b94bc1c1bf2db59fc323b0" title=" " alt="" coords="5,305,141,346"/>
<area shape="rect" href="classqbpp_1_1exhaustive__solver_1_1SearchAlgorithm.html#ad031941ba26d6f9e9262803068fbfc7e" title=" " alt="" coords="821,465,984,521"/>
<area shape="rect" href="classqbpp_1_1exhaustive__solver_1_1SolDelta.html#a926e9faffb265b7208b75663e76f77da" title=" " alt="" coords="821,399,984,441"/>
<area shape="rect" href="classqbpp_1_1exhaustive__solver_1_1SearchAlgorithm.html#a3a2b22a2908c11bf374b4f06b2ad1815" title=" " alt="" coords="600,435,773,477"/>
<area shape="rect" href="classqbpp_1_1exhaustive__solver_1_1ExhaustiveSolver.html#a967d19602b7de9fef4198508924c7a25" title=" " alt="" coords="373,435,552,477"/>
<area shape="rect" href="graph__color__abs2_8cpp.html#a3c04138a5bfe5d72780bb7e82a18e627" title="Main function to generate a random map and solve the Graph Coloring Problem using the ABS2 QUBO Solve..." alt="" coords="877,757,928,784"/>
</map>
</div>

</div>
</div>
<a id="a6543e96e459b50574cb17643c961b93a"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a6543e96e459b50574cb17643c961b93a">&#9670;&nbsp;</a></span>get() <span class="overload">[2/4]</span></h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">var_val_t qbpp::Sol::get </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="classqbpp_1_1Var.html">Var</a>&#160;</td>
          <td class="paramname"><em>var</em></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span><span class="mlabel">inherited</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p class="definition">Definition at line <a class="el" href="qbpp_8hpp_source.html#l02245">2245</a> of file <a class="el" href="qbpp_8hpp_source.html">qbpp.hpp</a>.</p>
<div class="dynheader">
Here is the call graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="classqbpp_1_1Sol_a6543e96e459b50574cb17643c961b93a_cgraph.png" border="0" usemap="#classqbpp_1_1Sol_a6543e96e459b50574cb17643c961b93a_cgraph" alt=""/></div>
<map name="classqbpp_1_1Sol_a6543e96e459b50574cb17643c961b93a_cgraph" id="classqbpp_1_1Sol_a6543e96e459b50574cb17643c961b93a_cgraph">
<area shape="rect" title=" " alt="" coords="5,31,108,57"/>
<area shape="rect" href="classqbpp_1_1Model.html#a0712035184681aeb478c5e80bb5221bd" title=" " alt="" coords="156,5,288,32"/>
<area shape="rect" href="namespaceqbpp.html#a1cf3f678bcc7eee089c75d4bc5a0b959" title=" " alt="" coords="184,56,260,83"/>
<area shape="rect" href="classqbpp_1_1Model.html#a52da2204913c7f529be7e8db017aa14a" title=" " alt="" coords="336,5,455,32"/>
<area shape="rect" href="namespaceqbpp.html#a844402a465157e00bca032af58c0a795" title=" " alt="" coords="357,56,433,83"/>
</map>
</div>
<div class="dynheader">
Here is the caller graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="classqbpp_1_1Sol_a6543e96e459b50574cb17643c961b93a_icgraph.png" border="0" usemap="#classqbpp_1_1Sol_a6543e96e459b50574cb17643c961b93a_icgraph" alt=""/></div>
<map name="classqbpp_1_1Sol_a6543e96e459b50574cb17643c961b93a_icgraph" id="classqbpp_1_1Sol_a6543e96e459b50574cb17643c961b93a_icgraph">
<area shape="rect" title=" " alt="" coords="5,29,108,56"/>
</map>
</div>

</div>
</div>
<a id="a427f81eb5091b76b4f197f3c42b5a1c5"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a427f81eb5091b76b4f197f3c42b5a1c5">&#9670;&nbsp;</a></span>get() <span class="overload">[3/4]</span></h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">energy_t qbpp::Sol::get </td>
          <td>(</td>
          <td class="paramtype">const <a class="el" href="classqbpp_1_1Expr.html">Expr</a> &amp;&#160;</td>
          <td class="paramname"><em>expr</em></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span><span class="mlabel">inherited</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p class="definition">Definition at line <a class="el" href="qbpp_8hpp_source.html#l02247">2247</a> of file <a class="el" href="qbpp_8hpp_source.html">qbpp.hpp</a>.</p>
<div class="dynheader">
Here is the call graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="classqbpp_1_1Sol_a427f81eb5091b76b4f197f3c42b5a1c5_cgraph.png" border="0" usemap="#classqbpp_1_1Sol_a427f81eb5091b76b4f197f3c42b5a1c5_cgraph" alt=""/></div>
<map name="classqbpp_1_1Sol_a427f81eb5091b76b4f197f3c42b5a1c5_cgraph" id="classqbpp_1_1Sol_a427f81eb5091b76b4f197f3c42b5a1c5_cgraph">
<area shape="rect" title=" " alt="" coords="5,31,108,57"/>
<area shape="rect" href="namespaceqbpp.html#ab5da6d0960920098998bb7347082ee6b" title=" " alt="" coords="156,5,239,32"/>
<area shape="rect" href="namespaceqbpp.html#ac1b198c35eb8831b6ac3ad2048caee66" title=" " alt="" coords="156,56,239,83"/>
<area shape="rect" href="namespaceqbpp.html#add51cb422256f9e89483b8cd0deb4721" title=" " alt="" coords="287,5,369,32"/>
</map>
</div>

</div>
</div>
<a id="a97b2220c9849ce534280e5a54944a54b"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a97b2220c9849ce534280e5a54944a54b">&#9670;&nbsp;</a></span>get() <span class="overload">[4/4]</span></h2>

<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename T &gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">auto qbpp::Sol::get </td>
          <td>(</td>
          <td class="paramtype">const <a class="el" href="classqbpp_1_1Vector.html">Vector</a>&lt; T &gt; &amp;&#160;</td>
          <td class="paramname"><em>vec</em></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span><span class="mlabel">inherited</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p class="definition">Definition at line <a class="el" href="qbpp_8hpp_source.html#l02261">2261</a> of file <a class="el" href="qbpp_8hpp_source.html">qbpp.hpp</a>.</p>
<div class="dynheader">
Here is the call graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="classqbpp_1_1Sol_a97b2220c9849ce534280e5a54944a54b_cgraph.png" border="0" usemap="#classqbpp_1_1Sol_a97b2220c9849ce534280e5a54944a54b_cgraph" alt=""/></div>
<map name="classqbpp_1_1Sol_a97b2220c9849ce534280e5a54944a54b_cgraph" id="classqbpp_1_1Sol_a97b2220c9849ce534280e5a54944a54b_cgraph">
<area shape="rect" title=" " alt="" coords="5,81,108,108"/>
<area shape="rect" href="classqbpp_1_1Sol.html#abdb4f6b779678720715f7845f264440f" title=" " alt="" coords="178,31,281,57"/>
<area shape="rect" href="classqbpp_1_1Vector.html#a5e8bb55fb9236e098401e23a758b5927" title=" " alt="" coords="156,81,303,108"/>
<area shape="rect" href="classqbpp_1_1Vector.html#af719287ecdb5f44bbd0aa5d9ba3aaea1" title=" " alt="" coords="165,132,293,159"/>
<area shape="rect" href="classqbpp_1_1impl_1_1BitVector.html#a12302d4df0b9f233c0e93ae87f89d6da" title=" " alt="" coords="351,5,521,32"/>
<area shape="rect" href="classqbpp_1_1Sol.html#a24f5a53fa68071dd9bcda910533bbad7" title=" " alt="" coords="378,56,494,83"/>
<area shape="rect" href="classqbpp_1_1Model.html#a0712035184681aeb478c5e80bb5221bd" title=" " alt="" coords="569,31,701,57"/>
<area shape="rect" href="namespaceqbpp.html#a1cf3f678bcc7eee089c75d4bc5a0b959" title=" " alt="" coords="597,81,673,108"/>
<area shape="rect" href="classqbpp_1_1Model.html#a52da2204913c7f529be7e8db017aa14a" title=" " alt="" coords="749,31,868,57"/>
<area shape="rect" href="namespaceqbpp.html#a844402a465157e00bca032af58c0a795" title=" " alt="" coords="771,81,847,108"/>
</map>
</div>

</div>
</div>
<a id="a62ab1f54864dd39a79cfd1a3cb4a838c"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a62ab1f54864dd39a79cfd1a3cb4a838c">&#9670;&nbsp;</a></span>has()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">bool qbpp::Sol::has </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="classqbpp_1_1Var.html">Var</a>&#160;</td>
          <td class="paramname"><em>var</em></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span><span class="mlabel">inherited</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p class="definition">Definition at line <a class="el" href="qbpp_8hpp_source.html#l02249">2249</a> of file <a class="el" href="qbpp_8hpp_source.html">qbpp.hpp</a>.</p>
<div class="dynheader">
Here is the call graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="classqbpp_1_1Sol_a62ab1f54864dd39a79cfd1a3cb4a838c_cgraph.png" border="0" usemap="#classqbpp_1_1Sol_a62ab1f54864dd39a79cfd1a3cb4a838c_cgraph" alt=""/></div>
<map name="classqbpp_1_1Sol_a62ab1f54864dd39a79cfd1a3cb4a838c_cgraph" id="classqbpp_1_1Sol_a62ab1f54864dd39a79cfd1a3cb4a838c_cgraph">
<area shape="rect" title=" " alt="" coords="5,31,112,57"/>
<area shape="rect" href="classqbpp_1_1Model.html#aed96d863b56813737a55c18402588719" title=" " alt="" coords="160,5,283,32"/>
<area shape="rect" href="namespaceqbpp.html#a1cf3f678bcc7eee089c75d4bc5a0b959" title=" " alt="" coords="183,56,259,83"/>
<area shape="rect" href="classqbpp_1_1Model.html#a52da2204913c7f529be7e8db017aa14a" title=" " alt="" coords="331,5,449,32"/>
<area shape="rect" href="classqbpp_1_1Model.html#a0712035184681aeb478c5e80bb5221bd" title=" " alt="" coords="497,5,629,32"/>
<area shape="rect" href="namespaceqbpp.html#a844402a465157e00bca032af58c0a795" title=" " alt="" coords="352,56,428,83"/>
</map>
</div>

</div>
</div>
<a id="a8acf56be292028a9601a6d7a3517ba7b"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a8acf56be292028a9601a6d7a3517ba7b">&#9670;&nbsp;</a></span>clear()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">void qbpp::Sol::clear </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span><span class="mlabel">inherited</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p class="definition">Definition at line <a class="el" href="qbpp_8hpp_source.html#l02251">2251</a> of file <a class="el" href="qbpp_8hpp_source.html">qbpp.hpp</a>.</p>
<div class="dynheader">
Here is the call graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="classqbpp_1_1Sol_a8acf56be292028a9601a6d7a3517ba7b_cgraph.png" border="0" usemap="#classqbpp_1_1Sol_a8acf56be292028a9601a6d7a3517ba7b_cgraph" alt=""/></div>
<map name="classqbpp_1_1Sol_a8acf56be292028a9601a6d7a3517ba7b_cgraph" id="classqbpp_1_1Sol_a8acf56be292028a9601a6d7a3517ba7b_cgraph">
<area shape="rect" title=" " alt="" coords="5,42,119,69"/>
<area shape="rect" href="classqbpp_1_1impl_1_1BitVector.html#a90946fde56ed3604b917ee1cad93c992" title=" " alt="" coords="170,5,314,47"/>
<area shape="rect" href="classqbpp_1_1Model.html#a3b0641c97573b248f1877c2826277102" title=" " alt="" coords="167,71,317,98"/>
</map>
</div>

</div>
</div>
<a id="a16e5cbcab9500ff5bf032b6cb5ec5e8d"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a16e5cbcab9500ff5bf032b6cb5ec5e8d">&#9670;&nbsp;</a></span>nullify()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">void qbpp::Sol::nullify </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span><span class="mlabel">inherited</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p class="definition">Definition at line <a class="el" href="qbpp_8hpp_source.html#l02256">2256</a> of file <a class="el" href="qbpp_8hpp_source.html">qbpp.hpp</a>.</p>

</div>
</div>
<a id="aa8c0a0717fbba880622a517e80bb86c7"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aa8c0a0717fbba880622a517e80bb86c7">&#9670;&nbsp;</a></span>is_null()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">bool qbpp::Sol::is_null </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span><span class="mlabel">inherited</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p class="definition">Definition at line <a class="el" href="qbpp_8hpp_source.html#l02258">2258</a> of file <a class="el" href="qbpp_8hpp_source.html">qbpp.hpp</a>.</p>

</div>
</div>
<a id="a2f7494eb4a254dd19b0261e801bacffb"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a2f7494eb4a254dd19b0261e801bacffb">&#9670;&nbsp;</a></span>set() <span class="overload">[3/5]</span></h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">void qbpp::Sol::set </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="namespaceqbpp.html#a5cf436100ede362797d0c0501ea50a5a">vindex_t</a>&#160;</td>
          <td class="paramname"><em>index</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">bool&#160;</td>
          <td class="paramname"><em>value</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span><span class="mlabel">inherited</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p class="definition">Definition at line <a class="el" href="qbpp_8hpp_source.html#l02270">2270</a> of file <a class="el" href="qbpp_8hpp_source.html">qbpp.hpp</a>.</p>
<div class="dynheader">
Here is the call graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="classqbpp_1_1Sol_a2f7494eb4a254dd19b0261e801bacffb_cgraph.png" border="0" usemap="#classqbpp_1_1Sol_a2f7494eb4a254dd19b0261e801bacffb_cgraph" alt=""/></div>
<map name="classqbpp_1_1Sol_a2f7494eb4a254dd19b0261e801bacffb_cgraph" id="classqbpp_1_1Sol_a2f7494eb4a254dd19b0261e801bacffb_cgraph">
<area shape="rect" title=" " alt="" coords="5,56,108,83"/>
<area shape="rect" href="classqbpp_1_1Sol.html#a24f5a53fa68071dd9bcda910533bbad7" title=" " alt="" coords="183,31,299,57"/>
<area shape="rect" href="classqbpp_1_1impl_1_1BitVector.html#a4694931fe6e873494026bbbe9334f848" title=" " alt="" coords="156,81,327,108"/>
<area shape="rect" href="classqbpp_1_1Model.html#a0712035184681aeb478c5e80bb5221bd" title=" " alt="" coords="375,5,507,32"/>
<area shape="rect" href="namespaceqbpp.html#a1cf3f678bcc7eee089c75d4bc5a0b959" title=" " alt="" coords="403,56,479,83"/>
<area shape="rect" href="classqbpp_1_1Model.html#a52da2204913c7f529be7e8db017aa14a" title=" " alt="" coords="555,5,673,32"/>
<area shape="rect" href="namespaceqbpp.html#a844402a465157e00bca032af58c0a795" title=" " alt="" coords="576,56,652,83"/>
</map>
</div>
<div class="dynheader">
Here is the caller graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="classqbpp_1_1Sol_a2f7494eb4a254dd19b0261e801bacffb_icgraph.png" border="0" usemap="#classqbpp_1_1Sol_a2f7494eb4a254dd19b0261e801bacffb_icgraph" alt=""/></div>
<map name="classqbpp_1_1Sol_a2f7494eb4a254dd19b0261e801bacffb_icgraph" id="classqbpp_1_1Sol_a2f7494eb4a254dd19b0261e801bacffb_icgraph">
<area shape="rect" title=" " alt="" coords="424,232,527,259"/>
<area shape="rect" href="classqbpp__grb_1_1Callback.html#a6d111191d65a98194fec8779fac6dadd" title="Get the solution obtained by Gurobi Optimizer." alt="" coords="234,71,369,113"/>
<area shape="rect" href="classqbpp__grb_1_1QuadModel.html#ad3add2ab2e0d80e5d0d8123deebc6e98" title="Optimize the QUBO model." alt="" coords="227,166,376,207"/>
<area shape="rect" href="classqbpp__abs2_1_1Sol.html#a48d9116f9fcbe00f84640847ee49df23" title="Set the value of the variable with &quot;index&quot; to &quot;value&quot;." alt="" coords="232,232,371,259"/>
<area shape="rect" href="classqbpp_1_1Sol.html#a171de5b36bce9d446849cde5b4324673" title=" " alt="" coords="250,283,353,309"/>
<area shape="rect" href="group__qbpp__abs2.html#gaba8e35f84b2f822cd2633b61358370db" title="Construct a new Sol object from an ABS2 solution." alt="" coords="232,333,371,360"/>
<area shape="rect" href="classGRB__Callback.html#ac08cbd22e13d589be7ca35178d3126d0" title="callback function for Gurobi optimizer." alt="" coords="9,5,175,32"/>
<area shape="rect" href="classqbpp__grb_1_1Callback.html#a8b785f76ab9c73fa92b97bb8da6f5e37" title="Default callback function for Gurobi Optimizer." alt="" coords="25,57,159,98"/>
<area shape="rect" href="classqbpp_1_1factorization_1_1GRB__Callback.html#a5b00538ab4f4123134dacfaa79c3cd4c" title="callback function for Gurobi optimizer." alt="" coords="5,122,179,163"/>
<area shape="rect" href="tsp__grb_8cpp.html#a3c04138a5bfe5d72780bb7e82a18e627" title="Main function to generate a random map and solve the Traveling Salesman Problem (TSP) using the Gurob..." alt="" coords="67,188,117,215"/>
</map>
</div>

</div>
</div>
<a id="a0a976747032bda528cc78f2400f601d8"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a0a976747032bda528cc78f2400f601d8">&#9670;&nbsp;</a></span>set() <span class="overload">[4/5]</span></h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="classqbpp__abs2_1_1Sol.html">Sol</a> qbpp::Sol::set </td>
          <td>(</td>
          <td class="paramtype">const <a class="el" href="classqbpp_1_1Sol.html">Sol</a> &amp;&#160;</td>
          <td class="paramname"><em>sol</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span><span class="mlabel">inherited</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p class="definition">Definition at line <a class="el" href="qbpp_8hpp_source.html#l02329">2329</a> of file <a class="el" href="qbpp_8hpp_source.html">qbpp.hpp</a>.</p>
<div class="dynheader">
Here is the call graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="classqbpp_1_1Sol_a0a976747032bda528cc78f2400f601d8_cgraph.png" border="0" usemap="#classqbpp_1_1Sol_a0a976747032bda528cc78f2400f601d8_cgraph" alt=""/></div>
<map name="classqbpp_1_1Sol_a0a976747032bda528cc78f2400f601d8_cgraph" id="classqbpp_1_1Sol_a0a976747032bda528cc78f2400f601d8_cgraph">
<area shape="rect" title=" " alt="" coords="5,66,108,93"/>
<area shape="rect" href="classqbpp_1_1Sol.html#abdb4f6b779678720715f7845f264440f" title=" " alt="" coords="183,41,286,67"/>
<area shape="rect" href="namespaceqbpp.html#a1cf3f678bcc7eee089c75d4bc5a0b959" title=" " alt="" coords="608,15,684,42"/>
<area shape="rect" href="classqbpp_1_1Model.html#a5ccc6ba4f93252db998f1ec2d50fe622" title=" " alt="" coords="156,91,313,118"/>
<area shape="rect" href="classqbpp_1_1Sol.html#a2f7494eb4a254dd19b0261e801bacffb" title=" " alt="" coords="183,142,286,169"/>
<area shape="rect" href="classqbpp_1_1impl_1_1BitVector.html#a12302d4df0b9f233c0e93ae87f89d6da" title=" " alt="" coords="361,41,532,67"/>
<area shape="rect" href="classqbpp_1_1Sol.html#a24f5a53fa68071dd9bcda910533bbad7" title=" " alt="" coords="389,91,505,118"/>
<area shape="rect" href="classqbpp_1_1Model.html#a0712035184681aeb478c5e80bb5221bd" title=" " alt="" coords="580,91,712,118"/>
<area shape="rect" href="classqbpp_1_1Model.html#a52da2204913c7f529be7e8db017aa14a" title=" " alt="" coords="760,91,879,118"/>
<area shape="rect" href="namespaceqbpp.html#a844402a465157e00bca032af58c0a795" title=" " alt="" coords="781,15,857,42"/>
<area shape="rect" href="classqbpp_1_1impl_1_1BitVector.html#a4694931fe6e873494026bbbe9334f848" title=" " alt="" coords="361,142,532,169"/>
</map>
</div>

</div>
</div>
<a id="ae753a79a37102490b4818c3e04d9348d"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ae753a79a37102490b4818c3e04d9348d">&#9670;&nbsp;</a></span>set() <span class="overload">[5/5]</span></h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="classqbpp__abs2_1_1Sol.html">Sol</a> qbpp::Sol::set </td>
          <td>(</td>
          <td class="paramtype">const <a class="el" href="namespaceqbpp.html#aeea468454b74e8cf74539f77454bef6b">MapList</a> &amp;&#160;</td>
          <td class="paramname"><em>map_list</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span><span class="mlabel">inherited</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p class="definition">Definition at line <a class="el" href="qbpp_8hpp_source.html#l02336">2336</a> of file <a class="el" href="qbpp_8hpp_source.html">qbpp.hpp</a>.</p>
<div class="dynheader">
Here is the call graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="classqbpp_1_1Sol_ae753a79a37102490b4818c3e04d9348d_cgraph.png" border="0" usemap="#classqbpp_1_1Sol_ae753a79a37102490b4818c3e04d9348d_cgraph" alt=""/></div>
<map name="classqbpp_1_1Sol_ae753a79a37102490b4818c3e04d9348d_cgraph" id="classqbpp_1_1Sol_ae753a79a37102490b4818c3e04d9348d_cgraph">
<area shape="rect" title=" " alt="" coords="5,56,108,83"/>
<area shape="rect" href="namespaceqbpp.html#a0b67fbbfcd3ee36f433d4a82616ddde4" title=" " alt="" coords="156,5,299,32"/>
<area shape="rect" href="namespaceqbpp.html#a1cf3f678bcc7eee089c75d4bc5a0b959" title=" " alt="" coords="593,56,669,83"/>
<area shape="rect" href="classqbpp_1_1Sol.html#a2f7494eb4a254dd19b0261e801bacffb" title=" " alt="" coords="176,107,279,133"/>
<area shape="rect" href="namespaceqbpp.html#a844402a465157e00bca032af58c0a795" title=" " alt="" coords="767,56,843,83"/>
<area shape="rect" href="classqbpp_1_1Sol.html#a24f5a53fa68071dd9bcda910533bbad7" title=" " alt="" coords="374,107,490,133"/>
<area shape="rect" href="classqbpp_1_1impl_1_1BitVector.html#a4694931fe6e873494026bbbe9334f848" title=" " alt="" coords="347,157,517,184"/>
<area shape="rect" href="classqbpp_1_1Model.html#a0712035184681aeb478c5e80bb5221bd" title=" " alt="" coords="565,107,697,133"/>
<area shape="rect" href="classqbpp_1_1Model.html#a52da2204913c7f529be7e8db017aa14a" title=" " alt="" coords="745,107,864,133"/>
</map>
</div>

</div>
</div>
<a id="a27f590351140f1f04049f1500b7ada31"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a27f590351140f1f04049f1500b7ada31">&#9670;&nbsp;</a></span>set64()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">void qbpp::Sol::set64 </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="namespaceqbpp.html#a5cf436100ede362797d0c0501ea50a5a">vindex_t</a>&#160;</td>
          <td class="paramname"><em>index64</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint64_t&#160;</td>
          <td class="paramname"><em>value</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span><span class="mlabel">inherited</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p class="definition">Definition at line <a class="el" href="qbpp_8hpp_source.html#l02280">2280</a> of file <a class="el" href="qbpp_8hpp_source.html">qbpp.hpp</a>.</p>
<div class="dynheader">
Here is the call graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="classqbpp_1_1Sol_a27f590351140f1f04049f1500b7ada31_cgraph.png" border="0" usemap="#classqbpp_1_1Sol_a27f590351140f1f04049f1500b7ada31_cgraph" alt=""/></div>
<map name="classqbpp_1_1Sol_a27f590351140f1f04049f1500b7ada31_cgraph" id="classqbpp_1_1Sol_a27f590351140f1f04049f1500b7ada31_cgraph">
<area shape="rect" title=" " alt="" coords="5,13,123,39"/>
<area shape="rect" href="classqbpp_1_1impl_1_1BitVector.html#afc8a61ad7129c36b0cc12165264b490d" title=" " alt="" coords="171,5,315,47"/>
</map>
</div>

</div>
</div>
<a id="a78255413d025557d55c0c16f4076f9fe"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a78255413d025557d55c0c16f4076f9fe">&#9670;&nbsp;</a></span>bit_vector_flip()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">void qbpp::Sol::bit_vector_flip </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="namespaceqbpp.html#a5cf436100ede362797d0c0501ea50a5a">vindex_t</a>&#160;</td>
          <td class="paramname"><em>index</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span><span class="mlabel">inherited</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p class="definition">Definition at line <a class="el" href="qbpp_8hpp_source.html#l02285">2285</a> of file <a class="el" href="qbpp_8hpp_source.html">qbpp.hpp</a>.</p>
<div class="dynheader">
Here is the call graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="classqbpp_1_1Sol_a78255413d025557d55c0c16f4076f9fe_cgraph.png" border="0" usemap="#classqbpp_1_1Sol_a78255413d025557d55c0c16f4076f9fe_cgraph" alt=""/></div>
<map name="classqbpp_1_1Sol_a78255413d025557d55c0c16f4076f9fe_cgraph" id="classqbpp_1_1Sol_a78255413d025557d55c0c16f4076f9fe_cgraph">
<area shape="rect" title=" " alt="" coords="5,42,172,69"/>
<area shape="rect" href="classqbpp_1_1impl_1_1BitVector.html#a1835e45be9c5473b9b1f2d9569be0945" title=" " alt="" coords="220,5,364,47"/>
<area shape="rect" href="classqbpp_1_1Sol.html#a24f5a53fa68071dd9bcda910533bbad7" title=" " alt="" coords="234,71,350,98"/>
<area shape="rect" href="classqbpp_1_1Model.html#a0712035184681aeb478c5e80bb5221bd" title=" " alt="" coords="412,46,544,73"/>
<area shape="rect" href="namespaceqbpp.html#a1cf3f678bcc7eee089c75d4bc5a0b959" title=" " alt="" coords="440,97,516,123"/>
<area shape="rect" href="classqbpp_1_1Model.html#a52da2204913c7f529be7e8db017aa14a" title=" " alt="" coords="592,46,711,73"/>
<area shape="rect" href="namespaceqbpp.html#a844402a465157e00bca032af58c0a795" title=" " alt="" coords="613,97,689,123"/>
</map>
</div>

</div>
</div>
<a id="a4f4c097837ad52f4fd368e048e79aa66"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a4f4c097837ad52f4fd368e048e79aa66">&#9670;&nbsp;</a></span>flip()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">virtual void qbpp::Sol::flip </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="namespaceqbpp.html#a5cf436100ede362797d0c0501ea50a5a">vindex_t</a>&#160;</td>
          <td class="paramname"><em>index</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span><span class="mlabel">virtual</span><span class="mlabel">inherited</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Reimplemented in <a class="el" href="classqbpp_1_1exhaustive__solver_1_1SolDelta.html#ae53a1eb415153c3d6e02794123a87b5a">qbpp::exhaustive_solver::SolDelta</a>, <a class="el" href="classqbpp_1_1easy__solver_1_1TabuSolDelta.html#abddd3367c047935e82e517c421cb5a9a">qbpp::easy_solver::TabuSolDelta</a>, and <a class="el" href="classqbpp_1_1easy__solver_1_1SolDelta.html#a1a107d8974386a07d201e88fb2c9aed9">qbpp::easy_solver::SolDelta</a>.</p>

<p class="definition">Definition at line <a class="el" href="qbpp_8hpp_source.html#l02287">2287</a> of file <a class="el" href="qbpp_8hpp_source.html">qbpp.hpp</a>.</p>
<div class="dynheader">
Here is the call graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="classqbpp_1_1Sol_a4f4c097837ad52f4fd368e048e79aa66_cgraph.png" border="0" usemap="#classqbpp_1_1Sol_a4f4c097837ad52f4fd368e048e79aa66_cgraph" alt=""/></div>
<map name="classqbpp_1_1Sol_a4f4c097837ad52f4fd368e048e79aa66_cgraph" id="classqbpp_1_1Sol_a4f4c097837ad52f4fd368e048e79aa66_cgraph">
<area shape="rect" title=" " alt="" coords="5,42,108,69"/>
<area shape="rect" href="classqbpp_1_1impl_1_1BitVector.html#a1835e45be9c5473b9b1f2d9569be0945" title=" " alt="" coords="156,5,300,47"/>
<area shape="rect" href="classqbpp_1_1Sol.html#a24f5a53fa68071dd9bcda910533bbad7" title=" " alt="" coords="170,71,286,98"/>
<area shape="rect" href="classqbpp_1_1Model.html#a0712035184681aeb478c5e80bb5221bd" title=" " alt="" coords="348,46,480,73"/>
<area shape="rect" href="namespaceqbpp.html#a1cf3f678bcc7eee089c75d4bc5a0b959" title=" " alt="" coords="376,97,452,123"/>
<area shape="rect" href="classqbpp_1_1Model.html#a52da2204913c7f529be7e8db017aa14a" title=" " alt="" coords="528,46,647,73"/>
<area shape="rect" href="namespaceqbpp.html#a844402a465157e00bca032af58c0a795" title=" " alt="" coords="549,97,625,123"/>
</map>
</div>

</div>
</div>
<a id="aae6d875ede912bcbf6254b74ffc66018"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aae6d875ede912bcbf6254b74ffc66018">&#9670;&nbsp;</a></span>flip_bit_add_delta()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">virtual void qbpp::Sol::flip_bit_add_delta </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="namespaceqbpp.html#a5cf436100ede362797d0c0501ea50a5a">vindex_t</a>&#160;</td>
          <td class="paramname"><em>index</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="namespaceqbpp.html#a9c324f641779d1a7a3c7723f28d8cff4">energy_t</a>&#160;</td>
          <td class="paramname"><em>delta</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span><span class="mlabel">virtual</span><span class="mlabel">inherited</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p class="definition">Definition at line <a class="el" href="qbpp_8hpp_source.html#l02292">2292</a> of file <a class="el" href="qbpp_8hpp_source.html">qbpp.hpp</a>.</p>
<div class="dynheader">
Here is the call graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="classqbpp_1_1Sol_aae6d875ede912bcbf6254b74ffc66018_cgraph.png" border="0" usemap="#classqbpp_1_1Sol_aae6d875ede912bcbf6254b74ffc66018_cgraph" alt=""/></div>
<map name="classqbpp_1_1Sol_aae6d875ede912bcbf6254b74ffc66018_cgraph" id="classqbpp_1_1Sol_aae6d875ede912bcbf6254b74ffc66018_cgraph">
<area shape="rect" title=" " alt="" coords="5,35,128,76"/>
<area shape="rect" href="classqbpp_1_1impl_1_1BitVector.html#a1835e45be9c5473b9b1f2d9569be0945" title=" " alt="" coords="176,5,320,47"/>
<area shape="rect" href="classqbpp_1_1Sol.html#a24f5a53fa68071dd9bcda910533bbad7" title=" " alt="" coords="190,71,306,98"/>
<area shape="rect" href="classqbpp_1_1Model.html#a0712035184681aeb478c5e80bb5221bd" title=" " alt="" coords="368,46,500,73"/>
<area shape="rect" href="namespaceqbpp.html#a1cf3f678bcc7eee089c75d4bc5a0b959" title=" " alt="" coords="396,97,472,123"/>
<area shape="rect" href="classqbpp_1_1Model.html#a52da2204913c7f529be7e8db017aa14a" title=" " alt="" coords="548,46,667,73"/>
<area shape="rect" href="namespaceqbpp.html#a844402a465157e00bca032af58c0a795" title=" " alt="" coords="569,97,645,123"/>
</map>
</div>
<div class="dynheader">
Here is the caller graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="classqbpp_1_1Sol_aae6d875ede912bcbf6254b74ffc66018_icgraph.png" border="0" usemap="#classqbpp_1_1Sol_aae6d875ede912bcbf6254b74ffc66018_icgraph" alt=""/></div>
<map name="classqbpp_1_1Sol_aae6d875ede912bcbf6254b74ffc66018_icgraph" id="classqbpp_1_1Sol_aae6d875ede912bcbf6254b74ffc66018_icgraph">
<area shape="rect" title=" " alt="" coords="1177,100,1300,141"/>
<area shape="rect" href="classqbpp_1_1easy__solver_1_1SolDelta.html#a1a107d8974386a07d201e88fb2c9aed9" title=" " alt="" coords="993,100,1129,141"/>
<area shape="rect" href="classqbpp_1_1easy__solver_1_1TabuSolDelta.html#abddd3367c047935e82e517c421cb5a9a" title=" " alt="" coords="809,5,945,47"/>
<area shape="rect" href="classqbpp_1_1easy__solver_1_1SolDelta.html#acf11d3f5b86a20077d4c20dc9c18e044" title=" " alt="" coords="809,71,945,112"/>
<area shape="rect" href="classqbpp_1_1easy__solver_1_1SolDelta.html#ae5e7dbc16325f4f3b26895fe8686afc5" title=" " alt="" coords="612,125,748,167"/>
<area shape="rect" href="classqbpp_1_1easy__solver_1_1SolDelta.html#ad0505230c61ae317b5414f06327daeb4" title=" " alt="" coords="607,191,753,232"/>
<area shape="rect" href="classqbpp_1_1easy__solver_1_1PosMinSolDelta.html#aff102bc17a0d06a78a285f1ab2c9632e" title=" " alt="" coords="599,60,761,101"/>
<area shape="rect" href="classqbpp_1_1easy__solver_1_1EasySolver.html#a329a03e4e5c401ec471e0f7f4c42cb51" title=" " alt="" coords="373,125,551,167"/>
<area shape="rect" href="classqbpp_1_1easy__solver_1_1EasySolver.html#a9a61ebfb6366c734e7af2b08890753f0" title=" " alt="" coords="189,125,325,167"/>
<area shape="rect" href="graph__color__easy_8cpp.html#a3c04138a5bfe5d72780bb7e82a18e627" title="Main function to generate a random map and solve the Graph Coloring Problem using the Easy Solver." alt="" coords="48,103,99,130"/>
<area shape="rect" href="classqbpp_1_1easy__solver_1_1EasySolver.html#a8d5bc85759b94bc1c1bf2db59fc323b0" title=" " alt="" coords="5,155,141,196"/>
</map>
</div>

</div>
</div>
<a id="a12f17cd3af83d8fab14667f103f5f21e"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a12f17cd3af83d8fab14667f103f5f21e">&#9670;&nbsp;</a></span>popcount()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">vindex_t qbpp::Sol::popcount </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span><span class="mlabel">inherited</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p class="definition">Definition at line <a class="el" href="qbpp_8hpp_source.html#l02297">2297</a> of file <a class="el" href="qbpp_8hpp_source.html">qbpp.hpp</a>.</p>
<div class="dynheader">
Here is the call graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="classqbpp_1_1Sol_a12f17cd3af83d8fab14667f103f5f21e_cgraph.png" border="0" usemap="#classqbpp_1_1Sol_a12f17cd3af83d8fab14667f103f5f21e_cgraph" alt=""/></div>
<map name="classqbpp_1_1Sol_a12f17cd3af83d8fab14667f103f5f21e_cgraph" id="classqbpp_1_1Sol_a12f17cd3af83d8fab14667f103f5f21e_cgraph">
<area shape="rect" title=" " alt="" coords="5,13,144,39"/>
<area shape="rect" href="classqbpp_1_1impl_1_1BitVector.html#a0dd9c8572940a9baa129b67c30528e38" title=" " alt="" coords="192,5,336,47"/>
</map>
</div>

</div>
</div>
<a id="a3ad455870df155ffed4701ce070cf335"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a3ad455870df155ffed4701ce070cf335">&#9670;&nbsp;</a></span>energy()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">energy_t qbpp::Sol::energy </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span><span class="mlabel">inherited</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p class="definition">Definition at line <a class="el" href="qbpp_8hpp_source.html#l02299">2299</a> of file <a class="el" href="qbpp_8hpp_source.html">qbpp.hpp</a>.</p>
<div class="dynheader">
Here is the call graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="classqbpp_1_1Sol_a3ad455870df155ffed4701ce070cf335_cgraph.png" border="0" usemap="#classqbpp_1_1Sol_a3ad455870df155ffed4701ce070cf335_cgraph" alt=""/></div>
<map name="classqbpp_1_1Sol_a3ad455870df155ffed4701ce070cf335_cgraph" id="classqbpp_1_1Sol_a3ad455870df155ffed4701ce070cf335_cgraph">
<area shape="rect" title=" " alt="" coords="5,5,129,32"/>
<area shape="rect" href="classqbpp_1_1Sol.html#a90c464b3df40d670646cb6f940462275" title=" " alt="" coords="177,5,340,32"/>
</map>
</div>
<div class="dynheader">
Here is the caller graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="classqbpp_1_1Sol_a3ad455870df155ffed4701ce070cf335_icgraph.png" border="0" usemap="#classqbpp_1_1Sol_a3ad455870df155ffed4701ce070cf335_icgraph" alt=""/></div>
<map name="classqbpp_1_1Sol_a3ad455870df155ffed4701ce070cf335_icgraph" id="classqbpp_1_1Sol_a3ad455870df155ffed4701ce070cf335_icgraph">
<area shape="rect" title=" " alt="" coords="1483,457,1607,484"/>
<area shape="rect" href="classqbpp_1_1Sol.html#aa8b89bea21691be375e3e33d152df3b8" title=" " alt="" coords="1270,23,1422,49"/>
<area shape="rect" href="classMyEasySolver.html#a35d6dcb0df4a69bd5835c4044c822e98" title=" " alt="" coords="1047,5,1209,32"/>
<area shape="rect" href="classqbpp__grb_1_1Callback.html#a8b785f76ab9c73fa92b97bb8da6f5e37" title="Default callback function for Gurobi Optimizer." alt="" coords="845,231,979,273"/>
<area shape="rect" href="classqbpp_1_1easy__solver_1_1EasySolver.html#adbadd766f998ea7171e18493a3538d9d" title=" " alt="" coords="1273,377,1419,418"/>
<area shape="rect" href="classqbpp_1_1exhaustive__solver_1_1SolDelta.html#ae53a1eb415153c3d6e02794123a87b5a" title=" " alt="" coords="1265,523,1427,565"/>
<area shape="rect" href="classqbpp_1_1easy__solver_1_1BestSols.html#a50e4d669aace640e64c43cebac64dc78" title=" " alt="" coords="1278,589,1414,645"/>
<area shape="rect" href="classqbpp_1_1Sol.html#a1325a24d1d0d1a51223f2ef02608cc3e" title=" " alt="" coords="1276,669,1416,696"/>
<area shape="rect" href="classqbpp_1_1Sol.html#ae30384ae030eaf5b08209f857ae14e97" title=" " alt="" coords="1272,720,1420,747"/>
<area shape="rect" href="classqbpp_1_1tsp_1_1TSPSol.html#ab2abe5283ba0f9fefad71b1beee340e3" title="Print the tour." alt="" coords="1277,125,1415,166"/>
<area shape="rect" href="classqbpp_1_1exhaustive__solver_1_1SearchAlgorithm.html#a73cc42b1cd9e72aa4a56eb25354ca21e" title=" " alt="" coords="1257,443,1435,499"/>
<area shape="rect" href="classqbpp_1_1Sol.html#a16504ce467c6fbfcefea4171a492bef1" title=" " alt="" coords="1271,325,1421,352"/>
<area shape="rect" href="classqbpp_1_1factorization_1_1SolHolder.html#a46e8fb339ad5d7983403f1785f5c24e5" title="Sets new_sol as the best solution if it is better than the current best solution." alt="" coords="1281,771,1411,827"/>
<area shape="rect" href="namespaceqbpp.html#a0a822c8155843fe78c8ac4ddd95f1681" title=" " alt="" coords="1309,851,1383,877"/>
<area shape="rect" href="classqbpp_1_1easy__solver_1_1SolDelta.html#ac0f902682f6255272a79333a947e1a24" title=" " alt="" coords="1052,391,1204,433"/>
<area shape="rect" href="classqbpp_1_1easy__solver_1_1SolDelta.html#acf11d3f5b86a20077d4c20dc9c18e044" title=" " alt="" coords="844,501,980,542"/>
<area shape="rect" href="classqbpp_1_1easy__solver_1_1PosMinSolDelta.html#aff102bc17a0d06a78a285f1ab2c9632e" title=" " alt="" coords="607,465,769,506"/>
<area shape="rect" href="classqbpp_1_1easy__solver_1_1SolDelta.html#ae5e7dbc16325f4f3b26895fe8686afc5" title=" " alt="" coords="620,334,756,375"/>
<area shape="rect" href="classqbpp_1_1easy__solver_1_1SolDelta.html#ad0505230c61ae317b5414f06327daeb4" title=" " alt="" coords="615,399,761,441"/>
<area shape="rect" href="classqbpp_1_1easy__solver_1_1EasySolver.html#a329a03e4e5c401ec471e0f7f4c42cb51" title=" " alt="" coords="373,399,551,441"/>
<area shape="rect" href="classqbpp_1_1easy__solver_1_1EasySolver.html#a9a61ebfb6366c734e7af2b08890753f0" title=" " alt="" coords="189,399,325,441"/>
<area shape="rect" href="graph__color__easy_8cpp.html#a3c04138a5bfe5d72780bb7e82a18e627" title="Main function to generate a random map and solve the Graph Coloring Problem using the Easy Solver." alt="" coords="48,377,99,404"/>
<area shape="rect" href="classqbpp_1_1easy__solver_1_1EasySolver.html#a8d5bc85759b94bc1c1bf2db59fc323b0" title=" " alt="" coords="5,429,141,470"/>
<area shape="rect" href="classqbpp_1_1exhaustive__solver_1_1SearchAlgorithm.html#ad031941ba26d6f9e9262803068fbfc7e" title=" " alt="" coords="1047,575,1209,631"/>
<area shape="rect" href="classqbpp_1_1exhaustive__solver_1_1SolDelta.html#a926e9faffb265b7208b75663e76f77da" title=" " alt="" coords="1047,509,1209,550"/>
<area shape="rect" href="classqbpp_1_1exhaustive__solver_1_1SearchAlgorithm.html#a3a2b22a2908c11bf374b4f06b2ad1815" title=" " alt="" coords="825,566,999,607"/>
<area shape="rect" href="classqbpp_1_1exhaustive__solver_1_1ExhaustiveSolver.html#a967d19602b7de9fef4198508924c7a25" title=" " alt="" coords="599,566,777,607"/>
<area shape="rect" href="classABS2Callback.html#a51998226f55f98e4870d89d4d5bf4d29" title="Callback function for ABS2 solver." alt="" coords="1047,157,1209,184"/>
<area shape="rect" href="classGRB__Callback.html#ac08cbd22e13d589be7ca35178d3126d0" title="callback function for Gurobi optimizer." alt="" coords="829,85,995,112"/>
<area shape="rect" href="tsp__abs2_8cpp.html#a3c04138a5bfe5d72780bb7e82a18e627" title="Main function to generate a random map and solve the Traveling Salesman Problem (TSP) using the ABS2 ..." alt="" coords="1103,107,1153,133"/>
<area shape="rect" href="classqbpp__grb_1_1Callback.html#a6d111191d65a98194fec8779fac6dadd" title="Get the solution obtained by Gurobi Optimizer." alt="" coords="1061,209,1195,250"/>
<area shape="rect" href="classqbpp__grb_1_1QuadModel.html#ad3add2ab2e0d80e5d0d8123deebc6e98" title="Optimize the QUBO model." alt="" coords="1053,326,1203,367"/>
<area shape="rect" href="classqbpp_1_1factorization_1_1GRB__Callback.html#a5b00538ab4f4123134dacfaa79c3cd4c" title="callback function for Gurobi optimizer." alt="" coords="825,166,999,207"/>
<area shape="rect" href="tsp__grb_8cpp.html#a3c04138a5bfe5d72780bb7e82a18e627" title="Main function to generate a random map and solve the Traveling Salesman Problem (TSP) using the Gurob..." alt="" coords="887,297,937,324"/>
</map>
</div>

</div>
</div>
<a id="a16504ce467c6fbfcefea4171a492bef1"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a16504ce467c6fbfcefea4171a492bef1">&#9670;&nbsp;</a></span>set_energy()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">void qbpp::Sol::set_energy </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="namespaceqbpp.html#a9c324f641779d1a7a3c7723f28d8cff4">energy_t</a>&#160;</td>
          <td class="paramname"><em>energy</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span><span class="mlabel">inherited</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p class="definition">Definition at line <a class="el" href="qbpp_8hpp_source.html#l02304">2304</a> of file <a class="el" href="qbpp_8hpp_source.html">qbpp.hpp</a>.</p>
<div class="dynheader">
Here is the call graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="classqbpp_1_1Sol_a16504ce467c6fbfcefea4171a492bef1_cgraph.png" border="0" usemap="#classqbpp_1_1Sol_a16504ce467c6fbfcefea4171a492bef1_cgraph" alt=""/></div>
<map name="classqbpp_1_1Sol_a16504ce467c6fbfcefea4171a492bef1_cgraph" id="classqbpp_1_1Sol_a16504ce467c6fbfcefea4171a492bef1_cgraph">
<area shape="rect" title=" " alt="" coords="5,5,155,32"/>
<area shape="rect" href="classqbpp_1_1Sol.html#a3ad455870df155ffed4701ce070cf335" title=" " alt="" coords="203,5,327,32"/>
<area shape="rect" href="classqbpp_1_1Sol.html#a90c464b3df40d670646cb6f940462275" title=" " alt="" coords="375,5,537,32"/>
</map>
</div>
<div class="dynheader">
Here is the caller graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="classqbpp_1_1Sol_a16504ce467c6fbfcefea4171a492bef1_icgraph.png" border="0" usemap="#classqbpp_1_1Sol_a16504ce467c6fbfcefea4171a492bef1_icgraph" alt=""/></div>
<map name="classqbpp_1_1Sol_a16504ce467c6fbfcefea4171a492bef1_icgraph" id="classqbpp_1_1Sol_a16504ce467c6fbfcefea4171a492bef1_icgraph">
<area shape="rect" title=" " alt="" coords="424,127,573,153"/>
<area shape="rect" href="classqbpp__grb_1_1Callback.html#a6d111191d65a98194fec8779fac6dadd" title="Get the solution obtained by Gurobi Optimizer." alt="" coords="234,71,369,113"/>
<area shape="rect" href="classqbpp__grb_1_1QuadModel.html#ad3add2ab2e0d80e5d0d8123deebc6e98" title="Optimize the QUBO model." alt="" coords="227,151,376,193"/>
<area shape="rect" href="classGRB__Callback.html#ac08cbd22e13d589be7ca35178d3126d0" title="callback function for Gurobi optimizer." alt="" coords="9,5,175,32"/>
<area shape="rect" href="classqbpp__grb_1_1Callback.html#a8b785f76ab9c73fa92b97bb8da6f5e37" title="Default callback function for Gurobi Optimizer." alt="" coords="25,57,159,98"/>
<area shape="rect" href="classqbpp_1_1factorization_1_1GRB__Callback.html#a5b00538ab4f4123134dacfaa79c3cd4c" title="callback function for Gurobi optimizer." alt="" coords="5,122,179,163"/>
<area shape="rect" href="tsp__grb_8cpp.html#a3c04138a5bfe5d72780bb7e82a18e627" title="Main function to generate a random map and solve the Traveling Salesman Problem (TSP) using the Gurob..." alt="" coords="67,188,117,215"/>
</map>
</div>

</div>
</div>
<a id="a1407e31d02d063a89ea14c3872bdae26"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a1407e31d02d063a89ea14c3872bdae26">&#9670;&nbsp;</a></span>energy_has_value()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">bool qbpp::Sol::energy_has_value </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span><span class="mlabel">inherited</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p class="definition">Definition at line <a class="el" href="qbpp_8hpp_source.html#l02306">2306</a> of file <a class="el" href="qbpp_8hpp_source.html">qbpp.hpp</a>.</p>

</div>
</div>
<a id="aa8b89bea21691be375e3e33d152df3b8"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aa8b89bea21691be375e3e33d152df3b8">&#9670;&nbsp;</a></span>add_energy()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">energy_t qbpp::Sol::add_energy </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="namespaceqbpp.html#a9c324f641779d1a7a3c7723f28d8cff4">energy_t</a>&#160;</td>
          <td class="paramname"><em>energy</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span><span class="mlabel">inherited</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p class="definition">Definition at line <a class="el" href="qbpp_8hpp_source.html#l02308">2308</a> of file <a class="el" href="qbpp_8hpp_source.html">qbpp.hpp</a>.</p>
<div class="dynheader">
Here is the call graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="classqbpp_1_1Sol_aa8b89bea21691be375e3e33d152df3b8_cgraph.png" border="0" usemap="#classqbpp_1_1Sol_aa8b89bea21691be375e3e33d152df3b8_cgraph" alt=""/></div>
<map name="classqbpp_1_1Sol_aa8b89bea21691be375e3e33d152df3b8_cgraph" id="classqbpp_1_1Sol_aa8b89bea21691be375e3e33d152df3b8_cgraph">
<area shape="rect" title=" " alt="" coords="5,5,157,32"/>
<area shape="rect" href="classqbpp_1_1Sol.html#a3ad455870df155ffed4701ce070cf335" title=" " alt="" coords="205,5,329,32"/>
<area shape="rect" href="classqbpp_1_1Sol.html#a90c464b3df40d670646cb6f940462275" title=" " alt="" coords="377,5,540,32"/>
</map>
</div>

</div>
</div>
<a id="aae14179040e70ddf134c560790b602aa"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aae14179040e70ddf134c560790b602aa">&#9670;&nbsp;</a></span>bit_vector() <span class="overload">[1/2]</span></h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">const impl::BitVector&amp; qbpp::Sol::bit_vector </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span><span class="mlabel">inherited</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p class="definition">Definition at line <a class="el" href="qbpp_8hpp_source.html#l02310">2310</a> of file <a class="el" href="qbpp_8hpp_source.html">qbpp.hpp</a>.</p>

</div>
</div>
<a id="af2b8d3ee4c5097b2b44306c05dd0a6d1"></a>
<h2 class="memtitle"><span class="permalink"><a href="#af2b8d3ee4c5097b2b44306c05dd0a6d1">&#9670;&nbsp;</a></span>bit_vector() <span class="overload">[2/2]</span></h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">impl::BitVector&amp; qbpp::Sol::bit_vector </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span><span class="mlabel">inherited</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p class="definition">Definition at line <a class="el" href="qbpp_8hpp_source.html#l02312">2312</a> of file <a class="el" href="qbpp_8hpp_source.html">qbpp.hpp</a>.</p>

</div>
</div>
<a id="a2a8846783606a331e5892f4ae022bf86"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a2a8846783606a331e5892f4ae022bf86">&#9670;&nbsp;</a></span>operator[]() <span class="overload">[1/2]</span></h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t qbpp::Sol::operator[] </td>
          <td>(</td>
          <td class="paramtype">size_t&#160;</td>
          <td class="paramname"><em>i</em></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span><span class="mlabel">inherited</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p class="definition">Definition at line <a class="el" href="qbpp_8hpp_source.html#l02314">2314</a> of file <a class="el" href="qbpp_8hpp_source.html">qbpp.hpp</a>.</p>

</div>
</div>
<a id="aade76db5219653d4c8f89c8971dcf35c"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aade76db5219653d4c8f89c8971dcf35c">&#9670;&nbsp;</a></span>operator[]() <span class="overload">[2/2]</span></h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t&amp; qbpp::Sol::operator[] </td>
          <td>(</td>
          <td class="paramtype">size_t&#160;</td>
          <td class="paramname"><em>i</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span><span class="mlabel">inherited</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p class="definition">Definition at line <a class="el" href="qbpp_8hpp_source.html#l02315">2315</a> of file <a class="el" href="qbpp_8hpp_source.html">qbpp.hpp</a>.</p>

</div>
</div>
<a id="a06cbfd519670efbc74363592d5e826b2"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a06cbfd519670efbc74363592d5e826b2">&#9670;&nbsp;</a></span>var_count()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">vindex_t qbpp::Sol::var_count </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span><span class="mlabel">inherited</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p class="definition">Definition at line <a class="el" href="qbpp_8hpp_source.html#l02319">2319</a> of file <a class="el" href="qbpp_8hpp_source.html">qbpp.hpp</a>.</p>
<div class="dynheader">
Here is the call graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="classqbpp_1_1Sol_a06cbfd519670efbc74363592d5e826b2_cgraph.png" border="0" usemap="#classqbpp_1_1Sol_a06cbfd519670efbc74363592d5e826b2_cgraph" alt=""/></div>
<map name="classqbpp_1_1Sol_a06cbfd519670efbc74363592d5e826b2_cgraph" id="classqbpp_1_1Sol_a06cbfd519670efbc74363592d5e826b2_cgraph">
<area shape="rect" title=" " alt="" coords="5,5,148,32"/>
<area shape="rect" href="classqbpp_1_1Model.html#a5cf2bb955c3aa25951ca499d7d8635d2" title=" " alt="" coords="196,5,355,32"/>
</map>
</div>
<div class="dynheader">
Here is the caller graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="classqbpp_1_1Sol_a06cbfd519670efbc74363592d5e826b2_icgraph.png" border="0" usemap="#classqbpp_1_1Sol_a06cbfd519670efbc74363592d5e826b2_icgraph" alt=""/></div>
<map name="classqbpp_1_1Sol_a06cbfd519670efbc74363592d5e826b2_icgraph" id="classqbpp_1_1Sol_a06cbfd519670efbc74363592d5e826b2_icgraph">
<area shape="rect" title=" " alt="" coords="192,5,335,32"/>
<area shape="rect" href="group__qbpp__abs2.html#gac27ae911e72fdeb424a950d41fbef95c" title="Construct a new Sol object from a QUBO++ QUBO solution." alt="" coords="5,5,144,32"/>
</map>
</div>

</div>
</div>
<a id="a776c4f721be7e67bc2c801c126989953"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a776c4f721be7e67bc2c801c126989953">&#9670;&nbsp;</a></span>constant()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">energy_t qbpp::Sol::constant </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span><span class="mlabel">inherited</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p class="definition">Definition at line <a class="el" href="qbpp_8hpp_source.html#l02321">2321</a> of file <a class="el" href="qbpp_8hpp_source.html">qbpp.hpp</a>.</p>
<div class="dynheader">
Here is the call graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="classqbpp_1_1Sol_a776c4f721be7e67bc2c801c126989953_cgraph.png" border="0" usemap="#classqbpp_1_1Sol_a776c4f721be7e67bc2c801c126989953_cgraph" alt=""/></div>
<map name="classqbpp_1_1Sol_a776c4f721be7e67bc2c801c126989953_cgraph" id="classqbpp_1_1Sol_a776c4f721be7e67bc2c801c126989953_cgraph">
<area shape="rect" title=" " alt="" coords="5,5,140,32"/>
<area shape="rect" href="classqbpp_1_1Model.html#a3b0641c97573b248f1877c2826277102" title=" " alt="" coords="188,5,339,32"/>
</map>
</div>

</div>
</div>
<a id="a3db07b94f467d91e0be73e15fe5bd453"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a3db07b94f467d91e0be73e15fe5bd453">&#9670;&nbsp;</a></span>get_var()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">Var qbpp::Sol::get_var </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="namespaceqbpp.html#a5cf436100ede362797d0c0501ea50a5a">vindex_t</a>&#160;</td>
          <td class="paramname"><em>index</em></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span><span class="mlabel">inherited</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p class="definition">Definition at line <a class="el" href="qbpp_8hpp_source.html#l02323">2323</a> of file <a class="el" href="qbpp_8hpp_source.html">qbpp.hpp</a>.</p>
<div class="dynheader">
Here is the call graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="classqbpp_1_1Sol_a3db07b94f467d91e0be73e15fe5bd453_cgraph.png" border="0" usemap="#classqbpp_1_1Sol_a3db07b94f467d91e0be73e15fe5bd453_cgraph" alt=""/></div>
<map name="classqbpp_1_1Sol_a3db07b94f467d91e0be73e15fe5bd453_cgraph" id="classqbpp_1_1Sol_a3db07b94f467d91e0be73e15fe5bd453_cgraph">
<area shape="rect" title=" " alt="" coords="5,81,133,108"/>
<area shape="rect" href="classqbpp_1_1Sol.html#a24f5a53fa68071dd9bcda910533bbad7" title=" " alt="" coords="181,56,297,83"/>
<area shape="rect" href="classqbpp_1_1Model.html#a52da2204913c7f529be7e8db017aa14a" title=" " alt="" coords="525,56,644,83"/>
<area shape="rect" href="classqbpp_1_1Model.html#a0712035184681aeb478c5e80bb5221bd" title=" " alt="" coords="345,56,477,83"/>
<area shape="rect" href="namespaceqbpp.html#a1cf3f678bcc7eee089c75d4bc5a0b959" title=" " alt="" coords="373,5,449,32"/>
<area shape="rect" href="namespaceqbpp.html#a844402a465157e00bca032af58c0a795" title=" " alt="" coords="547,5,623,32"/>
</map>
</div>

</div>
</div>
<a id="a24f5a53fa68071dd9bcda910533bbad7"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a24f5a53fa68071dd9bcda910533bbad7">&#9670;&nbsp;</a></span>index()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">vindex_t qbpp::Sol::index </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="classqbpp_1_1Var.html">Var</a>&#160;</td>
          <td class="paramname"><em>var</em></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span><span class="mlabel">inherited</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p class="definition">Definition at line <a class="el" href="qbpp_8hpp_source.html#l02325">2325</a> of file <a class="el" href="qbpp_8hpp_source.html">qbpp.hpp</a>.</p>
<div class="dynheader">
Here is the call graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="classqbpp_1_1Sol_a24f5a53fa68071dd9bcda910533bbad7_cgraph.png" border="0" usemap="#classqbpp_1_1Sol_a24f5a53fa68071dd9bcda910533bbad7_cgraph" alt=""/></div>
<map name="classqbpp_1_1Sol_a24f5a53fa68071dd9bcda910533bbad7_cgraph" id="classqbpp_1_1Sol_a24f5a53fa68071dd9bcda910533bbad7_cgraph">
<area shape="rect" title=" " alt="" coords="5,31,121,57"/>
<area shape="rect" href="classqbpp_1_1Model.html#a0712035184681aeb478c5e80bb5221bd" title=" " alt="" coords="169,5,301,32"/>
<area shape="rect" href="namespaceqbpp.html#a1cf3f678bcc7eee089c75d4bc5a0b959" title=" " alt="" coords="197,56,273,83"/>
<area shape="rect" href="classqbpp_1_1Model.html#a52da2204913c7f529be7e8db017aa14a" title=" " alt="" coords="349,5,468,32"/>
<area shape="rect" href="namespaceqbpp.html#a844402a465157e00bca032af58c0a795" title=" " alt="" coords="371,56,447,83"/>
</map>
</div>
<div class="dynheader">
Here is the caller graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="classqbpp_1_1Sol_a24f5a53fa68071dd9bcda910533bbad7_icgraph.png" border="0" usemap="#classqbpp_1_1Sol_a24f5a53fa68071dd9bcda910533bbad7_icgraph" alt=""/></div>
<map name="classqbpp_1_1Sol_a24f5a53fa68071dd9bcda910533bbad7_icgraph" id="classqbpp_1_1Sol_a24f5a53fa68071dd9bcda910533bbad7_icgraph">
<area shape="rect" title=" " alt="" coords="1480,720,1596,747"/>
<area shape="rect" href="classqbpp_1_1Sol.html#a78255413d025557d55c0c16f4076f9fe" title=" " alt="" coords="1265,5,1432,32"/>
<area shape="rect" href="classqbpp_1_1easy__solver_1_1SolDelta.html#a1a107d8974386a07d201e88fb2c9aed9" title=" " alt="" coords="1062,742,1198,783"/>
<area shape="rect" href="classqbpp_1_1easy__solver_1_1TabuSolDelta.html#abddd3367c047935e82e517c421cb5a9a" title=" " alt="" coords="840,811,976,853"/>
<area shape="rect" href="classqbpp_1_1exhaustive__solver_1_1SolDelta.html#ae53a1eb415153c3d6e02794123a87b5a" title=" " alt="" coords="1049,41,1211,82"/>
<area shape="rect" href="classqbpp_1_1exhaustive__solver_1_1SolDelta.html#a926e9faffb265b7208b75663e76f77da" title=" " alt="" coords="827,99,989,141"/>
<area shape="rect" href="classqbpp_1_1Sol.html#a4f4c097837ad52f4fd368e048e79aa66" title=" " alt="" coords="1297,691,1400,717"/>
<area shape="rect" href="classqbpp_1_1Sol.html#aae6d875ede912bcbf6254b74ffc66018" title=" " alt="" coords="1287,742,1410,783"/>
<area shape="rect" href="classqbpp_1_1Sol.html#abdb4f6b779678720715f7845f264440f" title=" " alt="" coords="1297,443,1400,469"/>
<area shape="rect" href="classqbpp_1_1Sol.html#a3db07b94f467d91e0be73e15fe5bd453" title=" " alt="" coords="1285,859,1413,885"/>
<area shape="rect" href="classqbpp__abs2_1_1Sol.html#a48d9116f9fcbe00f84640847ee49df23" title="Set the value of the variable with &quot;index&quot; to &quot;value&quot;." alt="" coords="1061,880,1199,907"/>
<area shape="rect" href="classqbpp_1_1Sol.html#a2f7494eb4a254dd19b0261e801bacffb" title=" " alt="" coords="1297,960,1400,987"/>
<area shape="rect" href="classqbpp_1_1easy__solver_1_1TabuSolDelta.html#a514b9fd7511de63ea113064e40c50862" title=" " alt="" coords="1268,1011,1429,1053"/>
<area shape="rect" href="classqbpp_1_1easy__solver_1_1SolDelta.html#acf11d3f5b86a20077d4c20dc9c18e044" title=" " alt="" coords="840,746,976,787"/>
<area shape="rect" href="classqbpp_1_1easy__solver_1_1SolDelta.html#ae5e7dbc16325f4f3b26895fe8686afc5" title=" " alt="" coords="840,629,976,670"/>
<area shape="rect" href="classqbpp_1_1easy__solver_1_1SolDelta.html#ad0505230c61ae317b5414f06327daeb4" title=" " alt="" coords="613,687,760,729"/>
<area shape="rect" href="classqbpp_1_1easy__solver_1_1PosMinSolDelta.html#aff102bc17a0d06a78a285f1ab2c9632e" title=" " alt="" coords="605,753,768,794"/>
<area shape="rect" href="classqbpp_1_1easy__solver_1_1EasySolver.html#a329a03e4e5c401ec471e0f7f4c42cb51" title=" " alt="" coords="374,687,551,729"/>
<area shape="rect" href="classqbpp_1_1easy__solver_1_1EasySolver.html#a9a61ebfb6366c734e7af2b08890753f0" title=" " alt="" coords="189,687,325,729"/>
<area shape="rect" href="graph__color__easy_8cpp.html#a3c04138a5bfe5d72780bb7e82a18e627" title="Main function to generate a random map and solve the Graph Coloring Problem using the Easy Solver." alt="" coords="48,665,99,692"/>
<area shape="rect" href="classqbpp_1_1easy__solver_1_1EasySolver.html#a8d5bc85759b94bc1c1bf2db59fc323b0" title=" " alt="" coords="5,717,141,758"/>
<area shape="rect" href="classqbpp_1_1exhaustive__solver_1_1SearchAlgorithm.html#ad031941ba26d6f9e9262803068fbfc7e" title=" " alt="" coords="827,19,989,75"/>
<area shape="rect" href="classqbpp_1_1exhaustive__solver_1_1SearchAlgorithm.html#a3a2b22a2908c11bf374b4f06b2ad1815" title=" " alt="" coords="600,63,773,105"/>
<area shape="rect" href="classqbpp_1_1exhaustive__solver_1_1ExhaustiveSolver.html#a967d19602b7de9fef4198508924c7a25" title=" " alt="" coords="373,63,552,105"/>
<area shape="rect" href="namespaceqbpp_1_1factorization.html#a77868b746b7cb1a8a7e3707ddb5871f9" title="Converts a vector of Var in a solution to a 64&#45;bit unsigned integer." alt="" coords="1065,494,1195,535"/>
<area shape="rect" href="namespaceqbpp.html#ac429a7530957802c68d0ba6188daad94" title=" " alt="" coords="1089,691,1171,717"/>
<area shape="rect" href="classqbpp_1_1tsp_1_1TSPSol.html#a850469299d3a30962180e13657e16452" title="helper function to generate a tour from the solution." alt="" coords="1061,158,1199,199"/>
<area shape="rect" href="classqbpp_1_1Sol.html#a97b2220c9849ce534280e5a54944a54b" title=" " alt="" coords="1079,224,1181,251"/>
<area shape="rect" href="classqbpp_1_1Var.html#a73945543f5478d768f5a1ecbd3aaf31c" title=" " alt="" coords="1060,275,1200,301"/>
<area shape="rect" href="classqbpp_1_1tsp_1_1TSPSol.html#aeaddec0d0368bef74ebcbda49fe863fe" title=" " alt="" coords="1061,326,1199,367"/>
<area shape="rect" href="classqbpp_1_1Sol.html#a0a976747032bda528cc78f2400f601d8" title=" " alt="" coords="1079,392,1181,419"/>
<area shape="rect" href="classqbpp_1_1graph__color_1_1GraphColorMap.html#ad90af91eeca9f99c1a611d144cae1934" title="Set the color histogram of the nodes." alt="" coords="1043,560,1217,616"/>
<area shape="rect" href="group__qbpp__abs2.html#gac27ae911e72fdeb424a950d41fbef95c" title="Construct a new Sol object from a QUBO++ QUBO solution." alt="" coords="1061,443,1199,469"/>
<area shape="rect" href="namespaceqbpp_1_1factorization.html#a36079855e22273fe01e4c9eb34e57557" title="Converts a vector of Var in a solution holder to a 64&#45;bit unsigned integer." alt="" coords="843,501,973,542"/>
<area shape="rect" href="classqbpp_1_1factorization_1_1SolHolder.html#a46e8fb339ad5d7983403f1785f5c24e5" title="Sets new_sol as the best solution if it is better than the current best solution." alt="" coords="843,420,973,476"/>
<area shape="rect" href="graph__color__abs2_8cpp.html#a3c04138a5bfe5d72780bb7e82a18e627" title="Main function to generate a random map and solve the Graph Coloring Problem using the ABS2 QUBO Solve..." alt="" coords="883,575,933,601"/>
<area shape="rect" href="classqbpp__grb_1_1Callback.html#a6d111191d65a98194fec8779fac6dadd" title="Get the solution obtained by Gurobi Optimizer." alt="" coords="1063,1099,1197,1141"/>
<area shape="rect" href="classqbpp__grb_1_1QuadModel.html#ad3add2ab2e0d80e5d0d8123deebc6e98" title="Optimize the QUBO model." alt="" coords="1055,931,1205,973"/>
<area shape="rect" href="classqbpp_1_1Sol.html#a171de5b36bce9d446849cde5b4324673" title=" " alt="" coords="1079,997,1181,1024"/>
<area shape="rect" href="group__qbpp__abs2.html#gaba8e35f84b2f822cd2633b61358370db" title="Construct a new Sol object from an ABS2 solution." alt="" coords="1061,1048,1199,1075"/>
<area shape="rect" href="classGRB__Callback.html#ac08cbd22e13d589be7ca35178d3126d0" title="callback function for Gurobi optimizer." alt="" coords="825,1048,991,1075"/>
<area shape="rect" href="classqbpp__grb_1_1Callback.html#a8b785f76ab9c73fa92b97bb8da6f5e37" title="Default callback function for Gurobi Optimizer." alt="" coords="841,1099,975,1141"/>
<area shape="rect" href="classqbpp_1_1factorization_1_1GRB__Callback.html#a5b00538ab4f4123134dacfaa79c3cd4c" title="callback function for Gurobi optimizer." alt="" coords="821,1165,995,1206"/>
<area shape="rect" href="tsp__grb_8cpp.html#a3c04138a5bfe5d72780bb7e82a18e627" title="Main function to generate a random map and solve the Traveling Salesman Problem (TSP) using the Gurob..." alt="" coords="883,939,933,965"/>
</map>
</div>

</div>
</div>
<a id="a3e3cf991914a1e6743a4e3e0b987f2ec"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a3e3cf991914a1e6743a4e3e0b987f2ec">&#9670;&nbsp;</a></span>operator MapList()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">qbpp::Sol::operator MapList </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span><span class="mlabel">inherited</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p class="definition">Definition at line <a class="el" href="qbpp_8hpp_source.html#l02327">2327</a> of file <a class="el" href="qbpp_8hpp_source.html">qbpp.hpp</a>.</p>
<div class="dynheader">
Here is the call graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="classqbpp_1_1Sol_a3e3cf991914a1e6743a4e3e0b987f2ec_cgraph.png" border="0" usemap="#classqbpp_1_1Sol_a3e3cf991914a1e6743a4e3e0b987f2ec_cgraph" alt=""/></div>
<map name="classqbpp_1_1Sol_a3e3cf991914a1e6743a4e3e0b987f2ec_cgraph" id="classqbpp_1_1Sol_a3e3cf991914a1e6743a4e3e0b987f2ec_cgraph">
<area shape="rect" title=" " alt="" coords="5,5,137,47"/>
<area shape="rect" href="classqbpp_1_1Sol.html#a8755c0c3c5f2da04d9f5fc7ad031d1d4" title=" " alt="" coords="185,13,344,39"/>
<area shape="rect" href="namespaceqbpp.html#a844402a465157e00bca032af58c0a795" title=" " alt="" coords="392,13,468,39"/>
</map>
</div>

</div>
</div>
<h2 class="groupheader">Member Data Documentation</h2>
<a id="a2979356a3719cec1699dab0ba5c80ea7"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a2979356a3719cec1699dab0ba5c80ea7">&#9670;&nbsp;</a></span>abs2sol_ptr</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">const std::shared_ptr&lt;<a class="el" href="classabs2_1_1Sol.html">abs2::Sol</a>&gt; qbpp_abs2::Sol::abs2sol_ptr</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">private</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p><a class="el" href="classqbpp__abs2_1_1Sol.html" title="A class for storing the ABS2 QUBO solution.">Sol</a> object created by ABS2 QUBO solver. </p>
<dl class="section note"><dt>Note</dt><dd>The shared_ptr is used to avoid copying the object. </dd></dl>

<p class="definition">Definition at line <a class="el" href="qbpp__abs2_8hpp_source.html#l00151">151</a> of file <a class="el" href="qbpp__abs2_8hpp_source.html">qbpp_abs2.hpp</a>.</p>

</div>
</div>
<a id="a11752b0b3742bec8da842db87ba94230"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a11752b0b3742bec8da842db87ba94230">&#9670;&nbsp;</a></span>quad_model_</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">const <a class="el" href="classqbpp__abs2_1_1QuadModel.html">QuadModel</a> qbpp::Sol::quad_model_</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">protected</span><span class="mlabel">inherited</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p class="definition">Definition at line <a class="el" href="qbpp_8hpp_source.html#l02184">2184</a> of file <a class="el" href="qbpp_8hpp_source.html">qbpp.hpp</a>.</p>

</div>
</div>
<a id="a97a3c5acd6aff38c15164146e453b2bf"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a97a3c5acd6aff38c15164146e453b2bf">&#9670;&nbsp;</a></span>bit_vector_</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">impl::BitVector qbpp::Sol::bit_vector_</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">protected</span><span class="mlabel">inherited</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p class="definition">Definition at line <a class="el" href="qbpp_8hpp_source.html#l02186">2186</a> of file <a class="el" href="qbpp_8hpp_source.html">qbpp.hpp</a>.</p>

</div>
</div>
<a id="a5dc3ec6002a1d823ad1bd525e9f6fa3e"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a5dc3ec6002a1d823ad1bd525e9f6fa3e">&#9670;&nbsp;</a></span>energy_</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">std::optional&lt;energy_t&gt; qbpp::Sol::energy_ = std::nullopt</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">mutable</span><span class="mlabel">protected</span><span class="mlabel">inherited</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p class="definition">Definition at line <a class="el" href="qbpp_8hpp_source.html#l02188">2188</a> of file <a class="el" href="qbpp_8hpp_source.html">qbpp.hpp</a>.</p>

</div>
</div>
<hr/>The documentation for this class was generated from the following file:<ul>
<li>include/<a class="el" href="qbpp__abs2_8hpp_source.html">qbpp_abs2.hpp</a></li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.17
</small></address>
</body>
</html>
