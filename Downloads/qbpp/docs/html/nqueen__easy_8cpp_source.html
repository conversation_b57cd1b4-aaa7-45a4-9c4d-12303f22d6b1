<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.17"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>QUBO++ Library with QUBO Solver APIs: sample/nqueen_easy.cpp Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/x-mathjax-config">
  MathJax.Hub.Config({
    extensions: ["tex2jax.js", "TeX/AMSmath.js", "TeX/AMSsymbols.js"],
    jax: ["input/TeX","output/HTML-CSS"],
});
</script>
<script type="text/javascript" async="async" src="https://cdn.jsdelivr.net/npm/mathjax@2/MathJax.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">QUBO++ Library with QUBO Solver APIs
   </div>
   <div id="projectbrief">Author: Koji Nakano, License: Non-commercial research and evaluation purposes without any guarantees.</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.17 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_c29eeb5af533606caea8a09e66794740.html">sample</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">nqueen_easy.cpp</div>  </div>
</div><!--header-->
<div class="contents">
<a href="nqueen__easy_8cpp.html">Go to the documentation of this file.</a><div class="fragment"><div class="line"><a name="l00001"></a><span class="lineno">    1</span>&#160; </div>
<div class="line"><a name="l00007"></a><span class="lineno">    7</span>&#160;<span class="preprocessor">#include &lt;boost/program_options.hpp&gt;</span></div>
<div class="line"><a name="l00008"></a><span class="lineno">    8</span>&#160; </div>
<div class="line"><a name="l00009"></a><span class="lineno">    9</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="qbpp_8hpp.html">qbpp.hpp</a>&quot;</span></div>
<div class="line"><a name="l00010"></a><span class="lineno">   10</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="qbpp__easy__solver_8hpp.html">qbpp_easy_solver.hpp</a>&quot;</span></div>
<div class="line"><a name="l00011"></a><span class="lineno">   11</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="qbpp__nqueen_8hpp.html">qbpp_nqueen.hpp</a>&quot;</span></div>
<div class="line"><a name="l00012"></a><span class="lineno">   12</span>&#160; </div>
<div class="line"><a name="l00013"></a><span class="lineno">   13</span>&#160;<span class="keyword">namespace </span>po = boost::program_options;</div>
<div class="line"><a name="l00014"></a><span class="lineno">   14</span>&#160; </div>
<div class="line"><a name="l00022"></a><span class="lineno"><a class="line" href="nqueen__easy_8cpp.html#a0ddf1224851353fc92bfbff6f499fa97">   22</a></span>&#160;<span class="keywordtype">int</span> <a class="code" href="nqueen__easy_8cpp.html#a0ddf1224851353fc92bfbff6f499fa97">main</a>(<span class="keywordtype">int</span> argc, <span class="keywordtype">char</span> *argv[]) {</div>
<div class="line"><a name="l00023"></a><span class="lineno">   23</span>&#160;  <span class="comment">// clang-format off</span></div>
<div class="line"><a name="l00024"></a><span class="lineno">   24</span>&#160;  po::options_description desc(</div>
<div class="line"><a name="l00025"></a><span class="lineno">   25</span>&#160;      <span class="stringliteral">&quot;N-Queens Problem Solver using QUBO++ Easy Solver&quot;</span>);</div>
<div class="line"><a name="l00026"></a><span class="lineno">   26</span>&#160;  desc.add_options()(<span class="stringliteral">&quot;help,h&quot;</span>, <span class="stringliteral">&quot;produce help message&quot;</span>)</div>
<div class="line"><a name="l00027"></a><span class="lineno">   27</span>&#160;     (<span class="stringliteral">&quot;dimension,d&quot;</span>, po::value&lt;int&gt;()-&gt;default_value(8), <span class="stringliteral">&quot;set dimension of the chessboard&quot;</span>)</div>
<div class="line"><a name="l00028"></a><span class="lineno">   28</span>&#160;     (<span class="stringliteral">&quot;time_limit,t&quot;</span>, po::value&lt;int&gt;()-&gt;default_value(10), <span class="stringliteral">&quot;set time limit in seconds&quot;</span>)</div>
<div class="line"><a name="l00029"></a><span class="lineno">   29</span>&#160;     (<span class="stringliteral">&quot;seed,s&quot;</span>, po::value&lt;int&gt;(), <span class="stringliteral">&quot;set random seed&quot;</span>)</div>
<div class="line"><a name="l00030"></a><span class="lineno">   30</span>&#160;     (<span class="stringliteral">&quot;expand,e&quot;</span>, <span class="stringliteral">&quot;expand the one-hot formula for QUBO model generation&quot;</span>)</div>
<div class="line"><a name="l00031"></a><span class="lineno">   31</span>&#160;     (<span class="stringliteral">&quot;fast,f&quot;</span>, <span class="stringliteral">&quot;fast mode for QUBO model generation&quot;</span>)</div>
<div class="line"><a name="l00032"></a><span class="lineno">   32</span>&#160;     (<span class="stringliteral">&quot;parallel,p&quot;</span>, <span class="stringliteral">&quot;parallel mode for QUBO model generation (default)&quot;</span>);</div>
<div class="line"><a name="l00033"></a><span class="lineno">   33</span>&#160;  <span class="comment">// clang-format on</span></div>
<div class="line"><a name="l00034"></a><span class="lineno">   34</span>&#160; </div>
<div class="line"><a name="l00035"></a><span class="lineno">   35</span>&#160;  po::variables_map vm;</div>
<div class="line"><a name="l00036"></a><span class="lineno">   36</span>&#160;  <span class="keywordflow">try</span> {</div>
<div class="line"><a name="l00037"></a><span class="lineno">   37</span>&#160;    po::store(po::parse_command_line(argc, argv, desc), vm);</div>
<div class="line"><a name="l00038"></a><span class="lineno">   38</span>&#160;  } <span class="keywordflow">catch</span> (<span class="keyword">const</span> std::exception &amp;e) {</div>
<div class="line"><a name="l00039"></a><span class="lineno">   39</span>&#160;    std::cout &lt;&lt; <span class="stringliteral">&quot;Wrong arguments. Please use -h/--help option to see the &quot;</span></div>
<div class="line"><a name="l00040"></a><span class="lineno">   40</span>&#160;                 <span class="stringliteral">&quot;usage.\n&quot;</span>;</div>
<div class="line"><a name="l00041"></a><span class="lineno">   41</span>&#160;    <span class="keywordflow">return</span> 1;</div>
<div class="line"><a name="l00042"></a><span class="lineno">   42</span>&#160;  }</div>
<div class="line"><a name="l00043"></a><span class="lineno">   43</span>&#160;  po::notify(vm);</div>
<div class="line"><a name="l00044"></a><span class="lineno">   44</span>&#160; </div>
<div class="line"><a name="l00045"></a><span class="lineno">   45</span>&#160;  <span class="keywordflow">if</span> (vm.count(<span class="stringliteral">&quot;help&quot;</span>)) {</div>
<div class="line"><a name="l00046"></a><span class="lineno">   46</span>&#160;    std::cout &lt;&lt; desc &lt;&lt; std::endl;</div>
<div class="line"><a name="l00047"></a><span class="lineno">   47</span>&#160;    <span class="keywordflow">return</span> 0;</div>
<div class="line"><a name="l00048"></a><span class="lineno">   48</span>&#160;  }</div>
<div class="line"><a name="l00049"></a><span class="lineno">   49</span>&#160; </div>
<div class="line"><a name="l00050"></a><span class="lineno">   50</span>&#160;  <span class="keywordtype">int</span> dimension = vm[<span class="stringliteral">&quot;dimension&quot;</span>].as&lt;<span class="keywordtype">int</span>&gt;();</div>
<div class="line"><a name="l00051"></a><span class="lineno">   51</span>&#160;  <span class="keywordtype">int</span> time_limit = vm[<span class="stringliteral">&quot;time_limit&quot;</span>].as&lt;<span class="keywordtype">int</span>&gt;();</div>
<div class="line"><a name="l00052"></a><span class="lineno">   52</span>&#160; </div>
<div class="line"><a name="l00053"></a><span class="lineno">   53</span>&#160;  <span class="comment">// Set the random seed for deterministic behavior if seed is provided.</span></div>
<div class="line"><a name="l00054"></a><span class="lineno">   54</span>&#160;  <span class="keywordflow">if</span> (vm.count(<span class="stringliteral">&quot;seed&quot;</span>)) {</div>
<div class="line"><a name="l00055"></a><span class="lineno">   55</span>&#160;    <a class="code" href="classqbpp_1_1misc_1_1RandomGenerator.html#a1f4f3d675aee33dce174c8222c3bc22a">qbpp::misc::RandomGenerator::set_seed</a>(vm[<span class="stringliteral">&quot;seed&quot;</span>].as&lt;int&gt;());</div>
<div class="line"><a name="l00056"></a><span class="lineno">   56</span>&#160;  }</div>
<div class="line"><a name="l00057"></a><span class="lineno">   57</span>&#160; </div>
<div class="line"><a name="l00058"></a><span class="lineno">   58</span>&#160;  <a class="code" href="classqbpp_1_1nqueen_1_1NQueenQuadModel.html#a3bf5a92cab22bb76ef6b4e20eabb4e82">qbpp::nqueen::NQueenQuadModel::Mode</a> mode;</div>
<div class="line"><a name="l00059"></a><span class="lineno">   59</span>&#160;  <span class="keywordflow">if</span> (vm.count(<span class="stringliteral">&quot;expand&quot;</span>)) {</div>
<div class="line"><a name="l00060"></a><span class="lineno">   60</span>&#160;    mode = <a class="code" href="classqbpp_1_1nqueen_1_1NQueenQuadModel.html#a3bf5a92cab22bb76ef6b4e20eabb4e82a8f9d21d221f9e47228ff3747ee2992d7">qbpp::nqueen::NQueenQuadModel::Mode::EXPAND</a>;</div>
<div class="line"><a name="l00061"></a><span class="lineno">   61</span>&#160;  } <span class="keywordflow">else</span> <span class="keywordflow">if</span> (vm.count(<span class="stringliteral">&quot;fast&quot;</span>)) {</div>
<div class="line"><a name="l00062"></a><span class="lineno">   62</span>&#160;    mode = <a class="code" href="classqbpp_1_1nqueen_1_1NQueenQuadModel.html#a3bf5a92cab22bb76ef6b4e20eabb4e82adca6e617f6fb54033deb311e7e7c93cc">qbpp::nqueen::NQueenQuadModel::Mode::FAST</a>;</div>
<div class="line"><a name="l00063"></a><span class="lineno">   63</span>&#160;  } <span class="keywordflow">else</span> <span class="keywordflow">if</span> (vm.count(<span class="stringliteral">&quot;parallel&quot;</span>)) {</div>
<div class="line"><a name="l00064"></a><span class="lineno">   64</span>&#160;    mode = <a class="code" href="classqbpp_1_1nqueen_1_1NQueenQuadModel.html#a3bf5a92cab22bb76ef6b4e20eabb4e82adf13a99b035d6f0bce4f44ab18eec8eb">qbpp::nqueen::NQueenQuadModel::Mode::PARALLEL</a>;</div>
<div class="line"><a name="l00065"></a><span class="lineno">   65</span>&#160;  } <span class="keywordflow">else</span> {</div>
<div class="line"><a name="l00066"></a><span class="lineno">   66</span>&#160;    mode = <a class="code" href="classqbpp_1_1nqueen_1_1NQueenQuadModel.html#a3bf5a92cab22bb76ef6b4e20eabb4e82adf13a99b035d6f0bce4f44ab18eec8eb">qbpp::nqueen::NQueenQuadModel::Mode::PARALLEL</a>;</div>
<div class="line"><a name="l00067"></a><span class="lineno">   67</span>&#160;  }</div>
<div class="line"><a name="l00068"></a><span class="lineno">   68</span>&#160; </div>
<div class="line"><a name="l00069"></a><span class="lineno">   69</span>&#160;  <a class="code" href="classqbpp_1_1nqueen_1_1NQueenQuadModel.html">qbpp::nqueen::NQueenQuadModel</a> nqueen_model(dimension, mode);</div>
<div class="line"><a name="l00070"></a><span class="lineno">   70</span>&#160; </div>
<div class="line"><a name="l00071"></a><span class="lineno">   71</span>&#160;  std::cout &lt;&lt; <span class="stringliteral">&quot;Generating the QUBO model.&quot;</span> &lt;&lt; std::endl;</div>
<div class="line"><a name="l00072"></a><span class="lineno">   72</span>&#160; </div>
<div class="line"><a name="l00073"></a><span class="lineno">   73</span>&#160;  std::cout &lt;&lt; <span class="stringliteral">&quot;Variables = &quot;</span> &lt;&lt; nqueen_model.<a class="code" href="classqbpp_1_1Model.html#a5cf2bb955c3aa25951ca499d7d8635d2">var_count</a>()</div>
<div class="line"><a name="l00074"></a><span class="lineno">   74</span>&#160;            &lt;&lt; <span class="stringliteral">&quot; Linear Terms = &quot;</span> &lt;&lt; nqueen_model.<a class="code" href="classqbpp_1_1QuadModel.html#a16af851530b1f63aca9baa8ec67ed01f">term_count</a>(1)</div>
<div class="line"><a name="l00075"></a><span class="lineno">   75</span>&#160;            &lt;&lt; <span class="stringliteral">&quot; Quadratic Terms = &quot;</span> &lt;&lt; nqueen_model.<a class="code" href="classqbpp_1_1QuadModel.html#a16af851530b1f63aca9baa8ec67ed01f">term_count</a>(2) &lt;&lt; std::endl;</div>
<div class="line"><a name="l00076"></a><span class="lineno">   76</span>&#160; </div>
<div class="line"><a name="l00077"></a><span class="lineno">   77</span>&#160;  std::cout &lt;&lt; <span class="stringliteral">&quot;Generating an EasySolver object for the QUBO model.&quot;</span></div>
<div class="line"><a name="l00078"></a><span class="lineno">   78</span>&#160;            &lt;&lt; std::endl;</div>
<div class="line"><a name="l00079"></a><span class="lineno">   79</span>&#160;  <span class="keyword">auto</span> solver = <a class="code" href="classqbpp_1_1easy__solver_1_1EasySolver.html">qbpp::easy_solver::EasySolver</a>(nqueen_model);</div>
<div class="line"><a name="l00080"></a><span class="lineno">   80</span>&#160;  solver.set_time_limit(time_limit);</div>
<div class="line"><a name="l00081"></a><span class="lineno">   81</span>&#160;  solver.set_target_energy(0);</div>
<div class="line"><a name="l00082"></a><span class="lineno">   82</span>&#160;  solver.enable_default_callback();</div>
<div class="line"><a name="l00083"></a><span class="lineno">   83</span>&#160; </div>
<div class="line"><a name="l00084"></a><span class="lineno">   84</span>&#160;  std::cout &lt;&lt; <span class="stringliteral">&quot;Executing the EasySolver to solve the QUBO model.&quot;</span> &lt;&lt; std::endl;</div>
<div class="line"><a name="l00085"></a><span class="lineno">   85</span>&#160;  <span class="keyword">auto</span> sol = solver.search();</div>
<div class="line"><a name="l00086"></a><span class="lineno">   86</span>&#160; </div>
<div class="line"><a name="l00087"></a><span class="lineno">   87</span>&#160;  <span class="comment">// Print the solution as a chessboard.</span></div>
<div class="line"><a name="l00088"></a><span class="lineno">   88</span>&#160;  <span class="keywordflow">for</span> (<span class="keywordtype">int</span> i = 0; i &lt; dimension; ++i) {</div>
<div class="line"><a name="l00089"></a><span class="lineno">   89</span>&#160;    <span class="keywordflow">for</span> (<span class="keywordtype">int</span> j = 0; j &lt; dimension; ++j)</div>
<div class="line"><a name="l00090"></a><span class="lineno">   90</span>&#160;      std::cout &lt;&lt; <span class="keyword">static_cast&lt;</span><span class="keywordtype">int</span><span class="keyword">&gt;</span>(sol.get(nqueen_model.<a class="code" href="classqbpp_1_1nqueen_1_1NQueenQuadModel.html#a17de6168be88796c8bd27ebaf33f10ea">get_var</a>(i, j)));</div>
<div class="line"><a name="l00091"></a><span class="lineno">   91</span>&#160;    std::cout &lt;&lt; std::endl;</div>
<div class="line"><a name="l00092"></a><span class="lineno">   92</span>&#160;  }</div>
<div class="line"><a name="l00093"></a><span class="lineno">   93</span>&#160; </div>
<div class="line"><a name="l00094"></a><span class="lineno">   94</span>&#160;  std::cout &lt;&lt; <span class="stringliteral">&quot;Dimension = &quot;</span> &lt;&lt; dimension &lt;&lt; <span class="stringliteral">&quot; TTS = &quot;</span> &lt;&lt; std::fixed</div>
<div class="line"><a name="l00095"></a><span class="lineno">   95</span>&#160;            &lt;&lt; std::setprecision(3) &lt;&lt; std::setfill(<span class="charliteral">&#39;0&#39;</span>) &lt;&lt; solver.get_tts()</div>
<div class="line"><a name="l00096"></a><span class="lineno">   96</span>&#160;            &lt;&lt; <span class="stringliteral">&quot;s Energy = &quot;</span> &lt;&lt; sol.energy() &lt;&lt; std::endl;</div>
<div class="line"><a name="l00097"></a><span class="lineno">   97</span>&#160; </div>
<div class="line"><a name="l00098"></a><span class="lineno">   98</span>&#160;  std::cout &lt;&lt; <span class="stringliteral">&quot;flip_count = &quot;</span> &lt;&lt; solver.get_flip_count() &lt;&lt; std::endl;</div>
<div class="line"><a name="l00099"></a><span class="lineno">   99</span>&#160;}</div>
</div><!-- fragment --></div><!-- contents -->
<div class="ttc" id="aqbpp__nqueen_8hpp_html"><div class="ttname"><a href="qbpp__nqueen_8hpp.html">qbpp_nqueen.hpp</a></div><div class="ttdoc">Generates QUBO expression for the N-Queens problem using the QUBO++ library.</div></div>
<div class="ttc" id="aclassqbpp_1_1nqueen_1_1NQueenQuadModel_html_a3bf5a92cab22bb76ef6b4e20eabb4e82a8f9d21d221f9e47228ff3747ee2992d7"><div class="ttname"><a href="classqbpp_1_1nqueen_1_1NQueenQuadModel.html#a3bf5a92cab22bb76ef6b4e20eabb4e82a8f9d21d221f9e47228ff3747ee2992d7">qbpp::nqueen::NQueenQuadModel::Mode::EXPAND</a></div><div class="ttdeci">@ EXPAND</div></div>
<div class="ttc" id="aclassqbpp_1_1nqueen_1_1NQueenQuadModel_html_a3bf5a92cab22bb76ef6b4e20eabb4e82adca6e617f6fb54033deb311e7e7c93cc"><div class="ttname"><a href="classqbpp_1_1nqueen_1_1NQueenQuadModel.html#a3bf5a92cab22bb76ef6b4e20eabb4e82adca6e617f6fb54033deb311e7e7c93cc">qbpp::nqueen::NQueenQuadModel::Mode::FAST</a></div><div class="ttdeci">@ FAST</div></div>
<div class="ttc" id="aclassqbpp_1_1QuadModel_html_a16af851530b1f63aca9baa8ec67ed01f"><div class="ttname"><a href="classqbpp_1_1QuadModel.html#a16af851530b1f63aca9baa8ec67ed01f">qbpp::QuadModel::term_count</a></div><div class="ttdeci">size_t term_count(vindex_t deg) const</div><div class="ttdef"><b>Definition:</b> <a href="qbpp_8hpp_source.html#l01992">qbpp.hpp:1992</a></div></div>
<div class="ttc" id="aqbpp_8hpp_html"><div class="ttname"><a href="qbpp_8hpp.html">qbpp.hpp</a></div><div class="ttdoc">QUBO++, a C++ library for generating expressions for binary and spin variables.</div></div>
<div class="ttc" id="aclassqbpp_1_1nqueen_1_1NQueenQuadModel_html_a3bf5a92cab22bb76ef6b4e20eabb4e82adf13a99b035d6f0bce4f44ab18eec8eb"><div class="ttname"><a href="classqbpp_1_1nqueen_1_1NQueenQuadModel.html#a3bf5a92cab22bb76ef6b4e20eabb4e82adf13a99b035d6f0bce4f44ab18eec8eb">qbpp::nqueen::NQueenQuadModel::Mode::PARALLEL</a></div><div class="ttdeci">@ PARALLEL</div></div>
<div class="ttc" id="aclassqbpp_1_1nqueen_1_1NQueenQuadModel_html"><div class="ttname"><a href="classqbpp_1_1nqueen_1_1NQueenQuadModel.html">qbpp::nqueen::NQueenQuadModel</a></div><div class="ttdoc">Class to generate a QUBO model for the N-Queens problem.</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__nqueen_8hpp_source.html#l00022">qbpp_nqueen.hpp:22</a></div></div>
<div class="ttc" id="aqbpp__easy__solver_8hpp_html"><div class="ttname"><a href="qbpp__easy__solver_8hpp.html">qbpp_easy_solver.hpp</a></div><div class="ttdoc">Easy QUBO Solver for solving QUBO problems.</div></div>
<div class="ttc" id="aclassqbpp_1_1misc_1_1RandomGenerator_html_a1f4f3d675aee33dce174c8222c3bc22a"><div class="ttname"><a href="classqbpp_1_1misc_1_1RandomGenerator.html#a1f4f3d675aee33dce174c8222c3bc22a">qbpp::misc::RandomGenerator::set_seed</a></div><div class="ttdeci">static void set_seed(uint32_t seed=1)</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__misc_8hpp_source.html#l00112">qbpp_misc.hpp:112</a></div></div>
<div class="ttc" id="aclassqbpp_1_1nqueen_1_1NQueenQuadModel_html_a17de6168be88796c8bd27ebaf33f10ea"><div class="ttname"><a href="classqbpp_1_1nqueen_1_1NQueenQuadModel.html#a17de6168be88796c8bd27ebaf33f10ea">qbpp::nqueen::NQueenQuadModel::get_var</a></div><div class="ttdeci">qbpp::Var get_var(int i, int j) const</div><div class="ttdoc">Gets the variable at (i, j)</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__nqueen_8hpp_source.html#l00177">qbpp_nqueen.hpp:177</a></div></div>
<div class="ttc" id="aclassqbpp_1_1easy__solver_1_1EasySolver_html"><div class="ttname"><a href="classqbpp_1_1easy__solver_1_1EasySolver.html">qbpp::easy_solver::EasySolver</a></div><div class="ttdef"><b>Definition:</b> <a href="qbpp__easy__solver_8hpp_source.html#l00225">qbpp_easy_solver.hpp:225</a></div></div>
<div class="ttc" id="aclassqbpp_1_1nqueen_1_1NQueenQuadModel_html_a3bf5a92cab22bb76ef6b4e20eabb4e82"><div class="ttname"><a href="classqbpp_1_1nqueen_1_1NQueenQuadModel.html#a3bf5a92cab22bb76ef6b4e20eabb4e82">qbpp::nqueen::NQueenQuadModel::Mode</a></div><div class="ttdeci">Mode</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__nqueen_8hpp_source.html#l00024">qbpp_nqueen.hpp:24</a></div></div>
<div class="ttc" id="anqueen__easy_8cpp_html_a0ddf1224851353fc92bfbff6f499fa97"><div class="ttname"><a href="nqueen__easy_8cpp.html#a0ddf1224851353fc92bfbff6f499fa97">main</a></div><div class="ttdeci">int main(int argc, char *argv[])</div><div class="ttdoc">Solves the N-Queens problem using EasySolver in the QUBO++ library. library.</div><div class="ttdef"><b>Definition:</b> <a href="nqueen__easy_8cpp_source.html#l00022">nqueen_easy.cpp:22</a></div></div>
<div class="ttc" id="aclassqbpp_1_1Model_html_a5cf2bb955c3aa25951ca499d7d8635d2"><div class="ttname"><a href="classqbpp_1_1Model.html#a5cf2bb955c3aa25951ca499d7d8635d2">qbpp::Model::var_count</a></div><div class="ttdeci">vindex_t var_count() const</div><div class="ttdef"><b>Definition:</b> <a href="qbpp_8hpp_source.html#l01859">qbpp.hpp:1859</a></div></div>
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.17
</small></address>
</body>
</html>
