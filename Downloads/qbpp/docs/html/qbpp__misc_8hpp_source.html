<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.17"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>QUBO++ Library with QUBO Solver APIs: include/qbpp_misc.hpp Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/x-mathjax-config">
  MathJax.Hub.Config({
    extensions: ["tex2jax.js", "TeX/AMSmath.js", "TeX/AMSsymbols.js"],
    jax: ["input/TeX","output/HTML-CSS"],
});
</script>
<script type="text/javascript" async="async" src="https://cdn.jsdelivr.net/npm/mathjax@2/MathJax.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">QUBO++ Library with QUBO Solver APIs
   </div>
   <div id="projectbrief">Author: Koji Nakano, License: Non-commercial research and evaluation purposes without any guarantees.</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.17 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_d44c64559bbebec7f509842c48db8b23.html">include</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">qbpp_misc.hpp</div>  </div>
</div><!--header-->
<div class="contents">
<a href="qbpp__misc_8hpp.html">Go to the documentation of this file.</a><div class="fragment"><div class="line"><a name="l00001"></a><span class="lineno">    1</span>&#160; </div>
<div class="line"><a name="l00011"></a><span class="lineno">   11</span>&#160;<span class="preprocessor">#ifndef QBPP_MISC_HPP</span></div>
<div class="line"><a name="l00012"></a><span class="lineno">   12</span>&#160;<span class="preprocessor">#define QBPP_MISC_HPP</span></div>
<div class="line"><a name="l00013"></a><span class="lineno">   13</span>&#160; </div>
<div class="line"><a name="l00014"></a><span class="lineno">   14</span>&#160;<span class="preprocessor">#include &lt;algorithm&gt;</span></div>
<div class="line"><a name="l00015"></a><span class="lineno">   15</span>&#160;<span class="preprocessor">#include &lt;atomic&gt;</span></div>
<div class="line"><a name="l00016"></a><span class="lineno">   16</span>&#160;<span class="preprocessor">#include &lt;boost/circular_buffer.hpp&gt;</span></div>
<div class="line"><a name="l00017"></a><span class="lineno">   17</span>&#160;<span class="preprocessor">#include &lt;boost/random/taus88.hpp&gt;</span></div>
<div class="line"><a name="l00018"></a><span class="lineno">   18</span>&#160;<span class="preprocessor">#include &lt;boost/random/uniform_int_distribution.hpp&gt;</span></div>
<div class="line"><a name="l00019"></a><span class="lineno">   19</span>&#160;<span class="preprocessor">#include &lt;boost/random/uniform_real_distribution.hpp&gt;</span></div>
<div class="line"><a name="l00020"></a><span class="lineno">   20</span>&#160;<span class="preprocessor">#include &lt;cmath&gt;</span></div>
<div class="line"><a name="l00021"></a><span class="lineno">   21</span>&#160;<span class="preprocessor">#include &lt;iostream&gt;</span></div>
<div class="line"><a name="l00022"></a><span class="lineno">   22</span>&#160;<span class="preprocessor">#include &lt;numeric&gt;</span></div>
<div class="line"><a name="l00023"></a><span class="lineno">   23</span>&#160;<span class="preprocessor">#include &lt;random&gt;</span></div>
<div class="line"><a name="l00024"></a><span class="lineno">   24</span>&#160;<span class="preprocessor">#include &lt;set&gt;</span></div>
<div class="line"><a name="l00025"></a><span class="lineno">   25</span>&#160;<span class="preprocessor">#include &lt;vector&gt;</span></div>
<div class="line"><a name="l00026"></a><span class="lineno">   26</span>&#160; </div>
<div class="line"><a name="l00027"></a><span class="lineno">   27</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="qbpp_8hpp.html">qbpp.hpp</a>&quot;</span></div>
<div class="line"><a name="l00028"></a><span class="lineno">   28</span>&#160; </div>
<div class="line"><a name="l00029"></a><span class="lineno">   29</span>&#160;<span class="keyword">namespace </span><a class="code" href="namespaceqbpp.html">qbpp</a> {</div>
<div class="line"><a name="l00030"></a><span class="lineno">   30</span>&#160; </div>
<div class="line"><a name="l00031"></a><span class="lineno"><a class="line" href="namespaceqbpp_1_1misc.html">   31</a></span>&#160;<span class="keyword">namespace </span>misc {</div>
<div class="line"><a name="l00032"></a><span class="lineno">   32</span>&#160; </div>
<div class="line"><a name="l00033"></a><span class="lineno">   33</span>&#160;<span class="preprocessor">#include &lt;boost/random/taus88.hpp&gt;</span></div>
<div class="line"><a name="l00034"></a><span class="lineno">   34</span>&#160;<span class="preprocessor">#include &lt;boost/random/uniform_int_distribution.hpp&gt;</span></div>
<div class="line"><a name="l00035"></a><span class="lineno">   35</span>&#160;<span class="preprocessor">#include &lt;boost/random/uniform_real_distribution.hpp&gt;</span></div>
<div class="line"><a name="l00036"></a><span class="lineno">   36</span>&#160; </div>
<div class="line"><a name="l00037"></a><span class="lineno"><a class="line" href="classqbpp_1_1misc_1_1RandomEngine.html">   37</a></span>&#160;<span class="keyword">class </span><a class="code" href="classqbpp_1_1misc_1_1RandomEngine.html">RandomEngine</a> {</div>
<div class="line"><a name="l00038"></a><span class="lineno"><a class="line" href="classqbpp_1_1misc_1_1RandomEngine.html#a5a5c90642189352ccd25ce46334be9d4">   38</a></span>&#160;  boost::random::taus88 <a class="code" href="classqbpp_1_1misc_1_1RandomEngine.html#a5a5c90642189352ccd25ce46334be9d4">rng</a>;</div>
<div class="line"><a name="l00039"></a><span class="lineno">   39</span>&#160; </div>
<div class="line"><a name="l00040"></a><span class="lineno"><a class="line" href="classqbpp_1_1misc_1_1RandomEngine.html#a425f33f093cefe100a65fa7c8f6a221f">   40</a></span>&#160;  <span class="keyword">explicit</span> <a class="code" href="classqbpp_1_1misc_1_1RandomEngine.html#a425f33f093cefe100a65fa7c8f6a221f">RandomEngine</a>(uint32_t base_seed = 0) {</div>
<div class="line"><a name="l00041"></a><span class="lineno">   41</span>&#160;    uint32_t rd_seed = <span class="keyword">static_cast&lt;</span>uint32_t<span class="keyword">&gt;</span>(std::random_device{}());</div>
<div class="line"><a name="l00042"></a><span class="lineno">   42</span>&#160;    <a class="code" href="classqbpp_1_1misc_1_1RandomEngine.html#a5a5c90642189352ccd25ce46334be9d4">rng</a>.seed(rd_seed ^ base_seed);</div>
<div class="line"><a name="l00043"></a><span class="lineno">   43</span>&#160;  }</div>
<div class="line"><a name="l00044"></a><span class="lineno">   44</span>&#160; </div>
<div class="line"><a name="l00045"></a><span class="lineno"><a class="line" href="classqbpp_1_1misc_1_1RandomEngine.html#a5b8a96fee1e7b4e1900d0d1eb9332a4b">   45</a></span>&#160;  uint32_t <a class="code" href="classqbpp_1_1misc_1_1RandomEngine.html#a5b8a96fee1e7b4e1900d0d1eb9332a4b">gen32_impl</a>() { <span class="keywordflow">return</span> <a class="code" href="classqbpp_1_1misc_1_1RandomEngine.html#a5a5c90642189352ccd25ce46334be9d4">rng</a>(); }</div>
<div class="line"><a name="l00046"></a><span class="lineno">   46</span>&#160; </div>
<div class="line"><a name="l00047"></a><span class="lineno"><a class="line" href="classqbpp_1_1misc_1_1RandomEngine.html#a9c5c1b9de3f73bd00a4444965021e39c">   47</a></span>&#160;  uint64_t <a class="code" href="classqbpp_1_1misc_1_1RandomEngine.html#a9c5c1b9de3f73bd00a4444965021e39c">gen64_impl</a>() { <span class="keywordflow">return</span> (<span class="keyword">static_cast&lt;</span>uint64_t<span class="keyword">&gt;</span>(<a class="code" href="classqbpp_1_1misc_1_1RandomEngine.html#a5a5c90642189352ccd25ce46334be9d4">rng</a>()) &lt;&lt; 32) | <a class="code" href="classqbpp_1_1misc_1_1RandomEngine.html#a5a5c90642189352ccd25ce46334be9d4">rng</a>(); }</div>
<div class="line"><a name="l00048"></a><span class="lineno">   48</span>&#160; </div>
<div class="line"><a name="l00049"></a><span class="lineno">   49</span>&#160;  <span class="keyword">template</span> &lt;<span class="keyword">typename</span> T&gt;</div>
<div class="line"><a name="l00050"></a><span class="lineno"><a class="line" href="classqbpp_1_1misc_1_1RandomEngine.html#af39f35312c1e432c923035679333f455">   50</a></span>&#160;  T <a class="code" href="classqbpp_1_1misc_1_1RandomEngine.html#af39f35312c1e432c923035679333f455">gen_impl</a>(T n) {</div>
<div class="line"><a name="l00051"></a><span class="lineno">   51</span>&#160;    <span class="keywordflow">if</span> constexpr (<span class="keyword">sizeof</span>(T) &lt;= 4) {</div>
<div class="line"><a name="l00052"></a><span class="lineno">   52</span>&#160;      boost::random::uniform_int_distribution&lt;T&gt; dist(0, n - 1);</div>
<div class="line"><a name="l00053"></a><span class="lineno">   53</span>&#160;      <span class="keywordflow">return</span> dist(<a class="code" href="classqbpp_1_1misc_1_1RandomEngine.html#a5a5c90642189352ccd25ce46334be9d4">rng</a>);</div>
<div class="line"><a name="l00054"></a><span class="lineno">   54</span>&#160;    } <span class="keywordflow">else</span> {</div>
<div class="line"><a name="l00055"></a><span class="lineno">   55</span>&#160;      <span class="keyword">struct </span>Engine64 {</div>
<div class="line"><a name="l00056"></a><span class="lineno">   56</span>&#160;        boost::random::taus88 &amp;base;</div>
<div class="line"><a name="l00057"></a><span class="lineno">   57</span>&#160;        <span class="keyword">using</span> result_type = uint64_t;</div>
<div class="line"><a name="l00058"></a><span class="lineno">   58</span>&#160;        <span class="keyword">static</span> constexpr result_type min() { <span class="keywordflow">return</span> 0; }</div>
<div class="line"><a name="l00059"></a><span class="lineno">   59</span>&#160;        <span class="keyword">static</span> constexpr result_type max() { <span class="keywordflow">return</span> UINT64_MAX; }</div>
<div class="line"><a name="l00060"></a><span class="lineno">   60</span>&#160;        result_type operator()() {</div>
<div class="line"><a name="l00061"></a><span class="lineno">   61</span>&#160;          <span class="keywordflow">return</span> (<span class="keyword">static_cast&lt;</span>uint64_t<span class="keyword">&gt;</span>(base()) &lt;&lt; 32) | base();</div>
<div class="line"><a name="l00062"></a><span class="lineno">   62</span>&#160;        }</div>
<div class="line"><a name="l00063"></a><span class="lineno">   63</span>&#160;      } engine64{<a class="code" href="classqbpp_1_1misc_1_1RandomEngine.html#a5a5c90642189352ccd25ce46334be9d4">rng</a>};</div>
<div class="line"><a name="l00064"></a><span class="lineno">   64</span>&#160;      boost::random::uniform_int_distribution&lt;T&gt; dist(0, n - 1);</div>
<div class="line"><a name="l00065"></a><span class="lineno">   65</span>&#160;      <span class="keywordflow">return</span> dist(engine64);</div>
<div class="line"><a name="l00066"></a><span class="lineno">   66</span>&#160;    }</div>
<div class="line"><a name="l00067"></a><span class="lineno">   67</span>&#160;  }</div>
<div class="line"><a name="l00068"></a><span class="lineno">   68</span>&#160; </div>
<div class="line"><a name="l00069"></a><span class="lineno"><a class="line" href="classqbpp_1_1misc_1_1RandomEngine.html#ae8c8cbd9cba067b7d5a50a82081b6afc">   69</a></span>&#160;  <span class="keywordtype">double</span> <a class="code" href="classqbpp_1_1misc_1_1RandomEngine.html#ae8c8cbd9cba067b7d5a50a82081b6afc">gen_double_impl</a>() {</div>
<div class="line"><a name="l00070"></a><span class="lineno">   70</span>&#160;    boost::random::uniform_real_distribution&lt;double&gt; dist(0.0, 1.0);</div>
<div class="line"><a name="l00071"></a><span class="lineno">   71</span>&#160;    <span class="keywordflow">return</span> dist(<a class="code" href="classqbpp_1_1misc_1_1RandomEngine.html#a5a5c90642189352ccd25ce46334be9d4">rng</a>);</div>
<div class="line"><a name="l00072"></a><span class="lineno">   72</span>&#160;  }</div>
<div class="line"><a name="l00073"></a><span class="lineno">   73</span>&#160; </div>
<div class="line"><a name="l00074"></a><span class="lineno"><a class="line" href="classqbpp_1_1misc_1_1RandomEngine.html#a6f2a7cd42073b86d7128dafa9cd529e1">   74</a></span>&#160;  <span class="keyword">static</span> <a class="code" href="classqbpp_1_1misc_1_1RandomEngine.html">RandomEngine</a> &amp;<a class="code" href="classqbpp_1_1misc_1_1RandomEngine.html#a6f2a7cd42073b86d7128dafa9cd529e1">instance</a>() {</div>
<div class="line"><a name="l00075"></a><span class="lineno">   75</span>&#160;    thread_local <span class="keyword">static</span> <a class="code" href="classqbpp_1_1misc_1_1RandomEngine.html">RandomEngine</a> engine(<a class="code" href="classqbpp_1_1misc_1_1RandomEngine.html#aaa825378e652a8d4b77b7ef5d8ee9f8b">assign_thread_id</a>());</div>
<div class="line"><a name="l00076"></a><span class="lineno">   76</span>&#160;    <span class="keywordflow">return</span> engine;</div>
<div class="line"><a name="l00077"></a><span class="lineno">   77</span>&#160;  }</div>
<div class="line"><a name="l00078"></a><span class="lineno">   78</span>&#160; </div>
<div class="line"><a name="l00079"></a><span class="lineno"><a class="line" href="classqbpp_1_1misc_1_1RandomEngine.html#aaa825378e652a8d4b77b7ef5d8ee9f8b">   79</a></span>&#160;  <span class="keyword">static</span> uint32_t <a class="code" href="classqbpp_1_1misc_1_1RandomEngine.html#aaa825378e652a8d4b77b7ef5d8ee9f8b">assign_thread_id</a>() {</div>
<div class="line"><a name="l00080"></a><span class="lineno">   80</span>&#160;    <span class="keyword">static</span> std::atomic&lt;uint32_t&gt; counter{0};</div>
<div class="line"><a name="l00081"></a><span class="lineno">   81</span>&#160;    <span class="keywordflow">return</span> counter++;</div>
<div class="line"><a name="l00082"></a><span class="lineno">   82</span>&#160;  }</div>
<div class="line"><a name="l00083"></a><span class="lineno">   83</span>&#160; </div>
<div class="line"><a name="l00084"></a><span class="lineno">   84</span>&#160; <span class="keyword">public</span>:</div>
<div class="line"><a name="l00085"></a><span class="lineno"><a class="line" href="classqbpp_1_1misc_1_1RandomEngine.html#a47e455a933a579a66e3ef26239cf8737">   85</a></span>&#160;  <span class="keyword">static</span> uint32_t <a class="code" href="classqbpp_1_1misc_1_1RandomEngine.html#a47e455a933a579a66e3ef26239cf8737">gen32</a>() { <span class="keywordflow">return</span> <a class="code" href="classqbpp_1_1misc_1_1RandomEngine.html#a6f2a7cd42073b86d7128dafa9cd529e1">instance</a>().<a class="code" href="classqbpp_1_1misc_1_1RandomEngine.html#a5b8a96fee1e7b4e1900d0d1eb9332a4b">gen32_impl</a>(); }</div>
<div class="line"><a name="l00086"></a><span class="lineno">   86</span>&#160; </div>
<div class="line"><a name="l00087"></a><span class="lineno"><a class="line" href="classqbpp_1_1misc_1_1RandomEngine.html#ae181ae930d1f490d6a57dc7ee1058a30">   87</a></span>&#160;  <span class="keyword">static</span> uint64_t <a class="code" href="classqbpp_1_1misc_1_1RandomEngine.html#ae181ae930d1f490d6a57dc7ee1058a30">gen64</a>() { <span class="keywordflow">return</span> <a class="code" href="classqbpp_1_1misc_1_1RandomEngine.html#a6f2a7cd42073b86d7128dafa9cd529e1">instance</a>().<a class="code" href="classqbpp_1_1misc_1_1RandomEngine.html#a9c5c1b9de3f73bd00a4444965021e39c">gen64_impl</a>(); }</div>
<div class="line"><a name="l00088"></a><span class="lineno">   88</span>&#160; </div>
<div class="line"><a name="l00089"></a><span class="lineno">   89</span>&#160;  <span class="keyword">template</span> &lt;<span class="keyword">typename</span> T&gt;</div>
<div class="line"><a name="l00090"></a><span class="lineno"><a class="line" href="classqbpp_1_1misc_1_1RandomEngine.html#ad836550b67c1c3784cf95a23d43bcd96">   90</a></span>&#160;  <span class="keyword">static</span> T <a class="code" href="classqbpp_1_1misc_1_1RandomEngine.html#ad836550b67c1c3784cf95a23d43bcd96">gen</a>(T n) {</div>
<div class="line"><a name="l00091"></a><span class="lineno">   91</span>&#160;    <span class="keywordflow">return</span> <a class="code" href="classqbpp_1_1misc_1_1RandomEngine.html#a6f2a7cd42073b86d7128dafa9cd529e1">instance</a>().<a class="code" href="classqbpp_1_1misc_1_1RandomEngine.html#af39f35312c1e432c923035679333f455">gen_impl</a>(n);</div>
<div class="line"><a name="l00092"></a><span class="lineno">   92</span>&#160;  }</div>
<div class="line"><a name="l00093"></a><span class="lineno">   93</span>&#160; </div>
<div class="line"><a name="l00094"></a><span class="lineno"><a class="line" href="classqbpp_1_1misc_1_1RandomEngine.html#a562e1b7897692b5591f9c61e89dcc404">   94</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">double</span> <a class="code" href="classqbpp_1_1misc_1_1RandomEngine.html#a562e1b7897692b5591f9c61e89dcc404">gen_double</a>() { <span class="keywordflow">return</span> <a class="code" href="classqbpp_1_1misc_1_1RandomEngine.html#a6f2a7cd42073b86d7128dafa9cd529e1">instance</a>().<a class="code" href="classqbpp_1_1misc_1_1RandomEngine.html#ae8c8cbd9cba067b7d5a50a82081b6afc">gen_double_impl</a>(); }</div>
<div class="line"><a name="l00095"></a><span class="lineno">   95</span>&#160;};</div>
<div class="line"><a name="l00096"></a><span class="lineno">   96</span>&#160; </div>
<div class="line"><a name="l00097"></a><span class="lineno"><a class="line" href="classqbpp_1_1misc_1_1RandomGenerator.html">   97</a></span>&#160;<span class="keyword">class </span><a class="code" href="classqbpp_1_1misc_1_1RandomGenerator.html">RandomGenerator</a> {</div>
<div class="line"><a name="l00098"></a><span class="lineno"><a class="line" href="classqbpp_1_1misc_1_1RandomGenerator.html#a904d0331b4fff9392c7d3e3d01b398b8">   98</a></span>&#160;  std::mt19937_64 <a class="code" href="classqbpp_1_1misc_1_1RandomGenerator.html#a904d0331b4fff9392c7d3e3d01b398b8">mt</a>;</div>
<div class="line"><a name="l00099"></a><span class="lineno">   99</span>&#160; </div>
<div class="line"><a name="l00100"></a><span class="lineno"><a class="line" href="classqbpp_1_1misc_1_1RandomGenerator.html#a6ab877662e0f56dad77f70ded6528ded">  100</a></span>&#160;  <a class="code" href="classqbpp_1_1misc_1_1RandomGenerator.html#a6ab877662e0f56dad77f70ded6528ded">RandomGenerator</a>() : <a class="code" href="classqbpp_1_1misc_1_1RandomGenerator.html#a904d0331b4fff9392c7d3e3d01b398b8">mt</a>(std::random_device{}()) {}</div>
<div class="line"><a name="l00101"></a><span class="lineno">  101</span>&#160; </div>
<div class="line"><a name="l00102"></a><span class="lineno">  102</span>&#160;  <a class="code" href="classqbpp_1_1misc_1_1RandomGenerator.html#a6ab877662e0f56dad77f70ded6528ded">RandomGenerator</a>(<span class="keyword">const</span> <a class="code" href="classqbpp_1_1misc_1_1RandomGenerator.html#a6ab877662e0f56dad77f70ded6528ded">RandomGenerator</a> &amp;) = <span class="keyword">delete</span>;</div>
<div class="line"><a name="l00103"></a><span class="lineno">  103</span>&#160; </div>
<div class="line"><a name="l00104"></a><span class="lineno">  104</span>&#160;  <a class="code" href="classqbpp_1_1misc_1_1RandomGenerator.html#a6ab877662e0f56dad77f70ded6528ded">RandomGenerator</a> &amp;<a class="code" href="classqbpp_1_1misc_1_1RandomGenerator.html#a0f1121b27e368bf92130ce84842758ff">operator=</a>(<span class="keyword">const</span> <a class="code" href="classqbpp_1_1misc_1_1RandomGenerator.html#a6ab877662e0f56dad77f70ded6528ded">RandomGenerator</a> &amp;) = <span class="keyword">delete</span>;</div>
<div class="line"><a name="l00105"></a><span class="lineno">  105</span>&#160; </div>
<div class="line"><a name="l00106"></a><span class="lineno"><a class="line" href="classqbpp_1_1misc_1_1RandomGenerator.html#a6b19f6bde3a3b6ff087a908d11ece3c1">  106</a></span>&#160;  <span class="keyword">static</span> <a class="code" href="classqbpp_1_1misc_1_1RandomGenerator.html">RandomGenerator</a> &amp;<a class="code" href="classqbpp_1_1misc_1_1RandomGenerator.html#a6b19f6bde3a3b6ff087a908d11ece3c1">get_instance</a>() {</div>
<div class="line"><a name="l00107"></a><span class="lineno">  107</span>&#160;    <span class="keyword">static</span> <a class="code" href="classqbpp_1_1misc_1_1RandomGenerator.html">RandomGenerator</a> instance;</div>
<div class="line"><a name="l00108"></a><span class="lineno">  108</span>&#160;    <span class="keywordflow">return</span> instance;</div>
<div class="line"><a name="l00109"></a><span class="lineno">  109</span>&#160;  }</div>
<div class="line"><a name="l00110"></a><span class="lineno">  110</span>&#160; </div>
<div class="line"><a name="l00111"></a><span class="lineno">  111</span>&#160; <span class="keyword">public</span>:</div>
<div class="line"><a name="l00112"></a><span class="lineno"><a class="line" href="classqbpp_1_1misc_1_1RandomGenerator.html#a1f4f3d675aee33dce174c8222c3bc22a">  112</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">void</span> <a class="code" href="classqbpp_1_1misc_1_1RandomGenerator.html#a1f4f3d675aee33dce174c8222c3bc22a">set_seed</a>(uint32_t seed = 1) { <a class="code" href="classqbpp_1_1misc_1_1RandomGenerator.html#a6b19f6bde3a3b6ff087a908d11ece3c1">get_instance</a>().<a class="code" href="classqbpp_1_1misc_1_1RandomGenerator.html#a904d0331b4fff9392c7d3e3d01b398b8">mt</a>.seed(seed); }</div>
<div class="line"><a name="l00113"></a><span class="lineno">  113</span>&#160; </div>
<div class="line"><a name="l00114"></a><span class="lineno"><a class="line" href="classqbpp_1_1misc_1_1RandomGenerator.html#a513a93d293d9fa08a7fee2bba9cb73ff">  114</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">void</span> <a class="code" href="classqbpp_1_1misc_1_1RandomGenerator.html#a513a93d293d9fa08a7fee2bba9cb73ff">rd_seed</a>() { <a class="code" href="classqbpp_1_1misc_1_1RandomGenerator.html#a6b19f6bde3a3b6ff087a908d11ece3c1">get_instance</a>().<a class="code" href="classqbpp_1_1misc_1_1RandomGenerator.html#a904d0331b4fff9392c7d3e3d01b398b8">mt</a>.seed(std::random_device{}()); }</div>
<div class="line"><a name="l00115"></a><span class="lineno">  115</span>&#160; </div>
<div class="line"><a name="l00116"></a><span class="lineno"><a class="line" href="classqbpp_1_1misc_1_1RandomGenerator.html#a95308c2300aeef9a881ba97786367977">  116</a></span>&#160;  <span class="keyword">static</span> uint64_t <a class="code" href="classqbpp_1_1misc_1_1RandomGenerator.html#a95308c2300aeef9a881ba97786367977">gen</a>() { <span class="keywordflow">return</span> <span class="keyword">static_cast&lt;</span>uint64_t<span class="keyword">&gt;</span>(<a class="code" href="classqbpp_1_1misc_1_1RandomGenerator.html#a6b19f6bde3a3b6ff087a908d11ece3c1">get_instance</a>().<a class="code" href="classqbpp_1_1misc_1_1RandomGenerator.html#a904d0331b4fff9392c7d3e3d01b398b8">mt</a>()); }</div>
<div class="line"><a name="l00117"></a><span class="lineno">  117</span>&#160; </div>
<div class="line"><a name="l00118"></a><span class="lineno">  118</span>&#160;  <span class="keyword">template</span> &lt;<span class="keyword">typename</span> T&gt;</div>
<div class="line"><a name="l00119"></a><span class="lineno"><a class="line" href="classqbpp_1_1misc_1_1RandomGenerator.html#a8a677c3af4a06d3dc325ac69dd6ade73">  119</a></span>&#160;  <span class="keyword">static</span> <span class="keyword">typename</span> std::enable_if&lt;std::is_integral&lt;T&gt;::value, T&gt;::type <a class="code" href="classqbpp_1_1misc_1_1RandomGenerator.html#a8a677c3af4a06d3dc325ac69dd6ade73">gen</a>(T n) {</div>
<div class="line"><a name="l00120"></a><span class="lineno">  120</span>&#160;    std::uniform_int_distribution&lt;qbpp::vindex_t&gt; dist(0, n - 1);</div>
<div class="line"><a name="l00121"></a><span class="lineno">  121</span>&#160;    <span class="keywordflow">return</span> dist(<a class="code" href="classqbpp_1_1misc_1_1RandomGenerator.html#a6b19f6bde3a3b6ff087a908d11ece3c1">get_instance</a>().<a class="code" href="classqbpp_1_1misc_1_1RandomGenerator.html#a904d0331b4fff9392c7d3e3d01b398b8">mt</a>);</div>
<div class="line"><a name="l00122"></a><span class="lineno">  122</span>&#160;  }</div>
<div class="line"><a name="l00123"></a><span class="lineno">  123</span>&#160; </div>
<div class="line"><a name="l00124"></a><span class="lineno"><a class="line" href="classqbpp_1_1misc_1_1RandomGenerator.html#aab365c6e914e457221f8fd9bcee8b63e">  124</a></span>&#160;  <span class="keyword">static</span> <a class="code" href="namespaceqbpp.html#aebbaf7087f1c6180b3b2d9821cddc695">qbpp::cpp_int</a> <a class="code" href="classqbpp_1_1misc_1_1RandomGenerator.html#aab365c6e914e457221f8fd9bcee8b63e">gen</a>(<span class="keyword">const</span> <a class="code" href="namespaceqbpp.html#aebbaf7087f1c6180b3b2d9821cddc695">qbpp::cpp_int</a> &amp;n) {</div>
<div class="line"><a name="l00125"></a><span class="lineno">  125</span>&#160;    <a class="code" href="namespaceqbpp.html#aebbaf7087f1c6180b3b2d9821cddc695">qbpp::cpp_int</a> val;</div>
<div class="line"><a name="l00126"></a><span class="lineno">  126</span>&#160;    <span class="keywordflow">do</span> {</div>
<div class="line"><a name="l00127"></a><span class="lineno">  127</span>&#160;      val = (val &lt;&lt; 64) + <a class="code" href="classqbpp_1_1misc_1_1RandomGenerator.html#a95308c2300aeef9a881ba97786367977">gen</a>();</div>
<div class="line"><a name="l00128"></a><span class="lineno">  128</span>&#160;    } <span class="keywordflow">while</span> (val &lt; (n &lt;&lt; 32));</div>
<div class="line"><a name="l00129"></a><span class="lineno">  129</span>&#160;    <span class="keywordflow">return</span> val % n;</div>
<div class="line"><a name="l00130"></a><span class="lineno">  130</span>&#160;  }</div>
<div class="line"><a name="l00131"></a><span class="lineno">  131</span>&#160; </div>
<div class="line"><a name="l00132"></a><span class="lineno"><a class="line" href="classqbpp_1_1misc_1_1RandomGenerator.html#a776cc0696fd80c04071edc5ebad550ca">  132</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">double</span> <a class="code" href="classqbpp_1_1misc_1_1RandomGenerator.html#a776cc0696fd80c04071edc5ebad550ca">gen_double</a>() {</div>
<div class="line"><a name="l00133"></a><span class="lineno">  133</span>&#160;    std::uniform_real_distribution&lt;double&gt; dist(0.0, 1.0);</div>
<div class="line"><a name="l00134"></a><span class="lineno">  134</span>&#160;    <span class="keywordflow">return</span> dist(<a class="code" href="classqbpp_1_1misc_1_1RandomGenerator.html#a6b19f6bde3a3b6ff087a908d11ece3c1">get_instance</a>().<a class="code" href="classqbpp_1_1misc_1_1RandomGenerator.html#a904d0331b4fff9392c7d3e3d01b398b8">mt</a>);</div>
<div class="line"><a name="l00135"></a><span class="lineno">  135</span>&#160;  }</div>
<div class="line"><a name="l00136"></a><span class="lineno">  136</span>&#160; </div>
<div class="line"><a name="l00137"></a><span class="lineno"><a class="line" href="classqbpp_1_1misc_1_1RandomGenerator.html#a1aae9a887ef81804e5405fd4ae6a7bda">  137</a></span>&#160;  <span class="keyword">static</span> std::mt19937_64 &amp;<a class="code" href="classqbpp_1_1misc_1_1RandomGenerator.html#a1aae9a887ef81804e5405fd4ae6a7bda">get_mt</a>() { <span class="keywordflow">return</span> <a class="code" href="classqbpp_1_1misc_1_1RandomGenerator.html#a6b19f6bde3a3b6ff087a908d11ece3c1">get_instance</a>().<a class="code" href="classqbpp_1_1misc_1_1RandomGenerator.html#a904d0331b4fff9392c7d3e3d01b398b8">mt</a>; }</div>
<div class="line"><a name="l00138"></a><span class="lineno">  138</span>&#160;};</div>
<div class="line"><a name="l00139"></a><span class="lineno">  139</span>&#160; </div>
<div class="line"><a name="l00140"></a><span class="lineno"><a class="line" href="classqbpp_1_1misc_1_1RandomPermutation.html">  140</a></span>&#160;<span class="keyword">class </span><a class="code" href="classqbpp_1_1misc_1_1RandomPermutation.html">RandomPermutation</a> {</div>
<div class="line"><a name="l00141"></a><span class="lineno"><a class="line" href="classqbpp_1_1misc_1_1RandomPermutation.html#a1d2ae800e63cf7b14aee887235bc53d8">  141</a></span>&#160;  <span class="keyword">const</span> uint32_t <a class="code" href="classqbpp_1_1misc_1_1RandomPermutation.html#a1d2ae800e63cf7b14aee887235bc53d8">size_</a>;</div>
<div class="line"><a name="l00142"></a><span class="lineno">  142</span>&#160; </div>
<div class="line"><a name="l00143"></a><span class="lineno"><a class="line" href="classqbpp_1_1misc_1_1RandomPermutation.html#abad62f7ceac7082c11662adcd60a4b7c">  143</a></span>&#160;  std::vector&lt;uint32_t&gt; <a class="code" href="classqbpp_1_1misc_1_1RandomPermutation.html#abad62f7ceac7082c11662adcd60a4b7c">perm</a>;</div>
<div class="line"><a name="l00144"></a><span class="lineno">  144</span>&#160; </div>
<div class="line"><a name="l00145"></a><span class="lineno"><a class="line" href="classqbpp_1_1misc_1_1RandomPermutation.html#a17e3493da87439bdb85b3f7989d43812">  145</a></span>&#160;  uint32_t <a class="code" href="classqbpp_1_1misc_1_1RandomPermutation.html#a17e3493da87439bdb85b3f7989d43812">index</a> = 0;</div>
<div class="line"><a name="l00146"></a><span class="lineno">  146</span>&#160; </div>
<div class="line"><a name="l00147"></a><span class="lineno">  147</span>&#160; <span class="keyword">public</span>:</div>
<div class="line"><a name="l00148"></a><span class="lineno"><a class="line" href="classqbpp_1_1misc_1_1RandomPermutation.html#accf8ebcfcad81a695023782a4226e43f">  148</a></span>&#160;  <span class="keyword">explicit</span> <a class="code" href="classqbpp_1_1misc_1_1RandomPermutation.html#accf8ebcfcad81a695023782a4226e43f">RandomPermutation</a>(uint32_t size) : <a class="code" href="classqbpp_1_1misc_1_1RandomPermutation.html#a1d2ae800e63cf7b14aee887235bc53d8">size_</a>(size), <a class="code" href="classqbpp_1_1misc_1_1RandomPermutation.html#abad62f7ceac7082c11662adcd60a4b7c">perm</a>(size) {</div>
<div class="line"><a name="l00149"></a><span class="lineno">  149</span>&#160;    std::iota(<a class="code" href="classqbpp_1_1misc_1_1RandomPermutation.html#abad62f7ceac7082c11662adcd60a4b7c">perm</a>.begin(), <a class="code" href="classqbpp_1_1misc_1_1RandomPermutation.html#abad62f7ceac7082c11662adcd60a4b7c">perm</a>.end(), 0);</div>
<div class="line"><a name="l00150"></a><span class="lineno">  150</span>&#160;  }</div>
<div class="line"><a name="l00151"></a><span class="lineno">  151</span>&#160; </div>
<div class="line"><a name="l00152"></a><span class="lineno"><a class="line" href="classqbpp_1_1misc_1_1RandomPermutation.html#a6524d54e76a9b7fdcd3f36e404cf909d">  152</a></span>&#160;  uint32_t <a class="code" href="classqbpp_1_1misc_1_1RandomPermutation.html#a6524d54e76a9b7fdcd3f36e404cf909d">get</a>() {</div>
<div class="line"><a name="l00153"></a><span class="lineno">  153</span>&#160;    <span class="keywordflow">if</span> (<a class="code" href="classqbpp_1_1misc_1_1RandomPermutation.html#a17e3493da87439bdb85b3f7989d43812">index</a> == 0) {</div>
<div class="line"><a name="l00154"></a><span class="lineno">  154</span>&#160;      std::shuffle(<a class="code" href="classqbpp_1_1misc_1_1RandomPermutation.html#abad62f7ceac7082c11662adcd60a4b7c">perm</a>.begin(), <a class="code" href="classqbpp_1_1misc_1_1RandomPermutation.html#abad62f7ceac7082c11662adcd60a4b7c">perm</a>.end(), <a class="code" href="classqbpp_1_1misc_1_1RandomGenerator.html#a1aae9a887ef81804e5405fd4ae6a7bda">RandomGenerator::get_mt</a>());</div>
<div class="line"><a name="l00155"></a><span class="lineno">  155</span>&#160;    }</div>
<div class="line"><a name="l00156"></a><span class="lineno">  156</span>&#160;    uint32_t current = <a class="code" href="classqbpp_1_1misc_1_1RandomPermutation.html#a17e3493da87439bdb85b3f7989d43812">index</a>;</div>
<div class="line"><a name="l00157"></a><span class="lineno">  157</span>&#160;    <a class="code" href="classqbpp_1_1misc_1_1RandomPermutation.html#a17e3493da87439bdb85b3f7989d43812">index</a> = (<a class="code" href="classqbpp_1_1misc_1_1RandomPermutation.html#a17e3493da87439bdb85b3f7989d43812">index</a> + 1) % <a class="code" href="classqbpp_1_1misc_1_1RandomPermutation.html#a1d2ae800e63cf7b14aee887235bc53d8">size_</a>;</div>
<div class="line"><a name="l00158"></a><span class="lineno">  158</span>&#160;    <span class="keywordflow">return</span> <a class="code" href="classqbpp_1_1misc_1_1RandomPermutation.html#abad62f7ceac7082c11662adcd60a4b7c">perm</a>[current];</div>
<div class="line"><a name="l00159"></a><span class="lineno">  159</span>&#160;  }</div>
<div class="line"><a name="l00160"></a><span class="lineno">  160</span>&#160;};</div>
<div class="line"><a name="l00161"></a><span class="lineno">  161</span>&#160; </div>
<div class="line"><a name="l00162"></a><span class="lineno"><a class="line" href="structqbpp_1_1misc_1_1PcloseDeleter.html">  162</a></span>&#160;<span class="keyword">struct </span><a class="code" href="structqbpp_1_1misc_1_1PcloseDeleter.html">PcloseDeleter</a> {</div>
<div class="line"><a name="l00163"></a><span class="lineno"><a class="line" href="structqbpp_1_1misc_1_1PcloseDeleter.html#aef9adb5c2fd60a14331d18ee52333d50">  163</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="structqbpp_1_1misc_1_1PcloseDeleter.html#aef9adb5c2fd60a14331d18ee52333d50">operator()</a>(FILE *file)<span class="keyword"> const </span>{</div>
<div class="line"><a name="l00164"></a><span class="lineno">  164</span>&#160;    <span class="keywordflow">if</span> (file) pclose(file);</div>
<div class="line"><a name="l00165"></a><span class="lineno">  165</span>&#160;  }</div>
<div class="line"><a name="l00166"></a><span class="lineno">  166</span>&#160;};</div>
<div class="line"><a name="l00167"></a><span class="lineno">  167</span>&#160; </div>
<div class="line"><a name="l00168"></a><span class="lineno">  168</span>&#160; </div>
<div class="line"><a name="l00169"></a><span class="lineno"><a class="line" href="classqbpp_1_1misc_1_1RandomSet.html">  169</a></span>&#160;<span class="keyword">class </span><a class="code" href="classqbpp_1_1misc_1_1RandomSet.html">RandomSet</a> {</div>
<div class="line"><a name="l00170"></a><span class="lineno"><a class="line" href="classqbpp_1_1misc_1_1RandomSet.html#a2562592074e847095092c21b9feb6fd4">  170</a></span>&#160;  std::vector&lt;vindex_t&gt; <a class="code" href="classqbpp_1_1misc_1_1RandomSet.html#a2562592074e847095092c21b9feb6fd4">position_</a>;</div>
<div class="line"><a name="l00171"></a><span class="lineno">  171</span>&#160; </div>
<div class="line"><a name="l00172"></a><span class="lineno"><a class="line" href="classqbpp_1_1misc_1_1RandomSet.html#afeeb15f5fc634c22e9326aecbff71d7c">  172</a></span>&#160;  std::vector&lt;vindex_t&gt; <a class="code" href="classqbpp_1_1misc_1_1RandomSet.html#afeeb15f5fc634c22e9326aecbff71d7c">variables_</a>;</div>
<div class="line"><a name="l00173"></a><span class="lineno">  173</span>&#160; </div>
<div class="line"><a name="l00174"></a><span class="lineno">  174</span>&#160; <span class="keyword">public</span>:</div>
<div class="line"><a name="l00175"></a><span class="lineno"><a class="line" href="classqbpp_1_1misc_1_1RandomSet.html#a78525e8ab3790dc530a04db4a3e2665e">  175</a></span>&#160;  <span class="keyword">explicit</span> <a class="code" href="classqbpp_1_1misc_1_1RandomSet.html#a78525e8ab3790dc530a04db4a3e2665e">RandomSet</a>(<a class="code" href="namespaceqbpp.html#a5cf436100ede362797d0c0501ea50a5a">vindex_t</a> size) : <a class="code" href="classqbpp_1_1misc_1_1RandomSet.html#a2562592074e847095092c21b9feb6fd4">position_</a>(size, <a class="code" href="namespaceqbpp.html#ac0b50505a1738411ca7c96487b296234">vindex_limit</a>) {</div>
<div class="line"><a name="l00176"></a><span class="lineno">  176</span>&#160;    <a class="code" href="classqbpp_1_1misc_1_1RandomSet.html#afeeb15f5fc634c22e9326aecbff71d7c">variables_</a>.reserve(size);</div>
<div class="line"><a name="l00177"></a><span class="lineno">  177</span>&#160;  }</div>
<div class="line"><a name="l00178"></a><span class="lineno">  178</span>&#160; </div>
<div class="line"><a name="l00179"></a><span class="lineno"><a class="line" href="classqbpp_1_1misc_1_1RandomSet.html#a950bd3acaf6f41fe4d1c926ab4f389cc">  179</a></span>&#160;  <a class="code" href="namespaceqbpp.html#a5cf436100ede362797d0c0501ea50a5a">vindex_t</a> <a class="code" href="classqbpp_1_1misc_1_1RandomSet.html#a950bd3acaf6f41fe4d1c926ab4f389cc">var_count</a>()<span class="keyword"> const </span>{</div>
<div class="line"><a name="l00180"></a><span class="lineno">  180</span>&#160;    <span class="keywordflow">return</span> <span class="keyword">static_cast&lt;</span><a class="code" href="namespaceqbpp.html#a5cf436100ede362797d0c0501ea50a5a">vindex_t</a><span class="keyword">&gt;</span>(<a class="code" href="classqbpp_1_1misc_1_1RandomSet.html#afeeb15f5fc634c22e9326aecbff71d7c">variables_</a>.size());</div>
<div class="line"><a name="l00181"></a><span class="lineno">  181</span>&#160;  }</div>
<div class="line"><a name="l00182"></a><span class="lineno">  182</span>&#160; </div>
<div class="line"><a name="l00183"></a><span class="lineno"><a class="line" href="classqbpp_1_1misc_1_1RandomSet.html#a543f429a80b910d5dd196764ed45488b">  183</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="classqbpp_1_1misc_1_1RandomSet.html#a543f429a80b910d5dd196764ed45488b">insert</a>(<a class="code" href="namespaceqbpp.html#a5cf436100ede362797d0c0501ea50a5a">vindex_t</a> index) {</div>
<div class="line"><a name="l00184"></a><span class="lineno">  184</span>&#160;    <span class="keywordflow">if</span> (<a class="code" href="classqbpp_1_1misc_1_1RandomSet.html#a2562592074e847095092c21b9feb6fd4">position_</a>[index] != <a class="code" href="namespaceqbpp.html#ac0b50505a1738411ca7c96487b296234">vindex_limit</a>) {</div>
<div class="line"><a name="l00185"></a><span class="lineno">  185</span>&#160;      <span class="keywordflow">throw</span> std::runtime_error(<a class="code" href="qbpp_8hpp.html#a265e0828fcea8e75fbc9c0be99f7156e">THROW_MESSAGE</a>(<span class="stringliteral">&quot;RandomSet: Insert variable (&quot;</span>,</div>
<div class="line"><a name="l00186"></a><span class="lineno">  186</span>&#160;                                             index, <span class="stringliteral">&quot;) already in set&quot;</span>));</div>
<div class="line"><a name="l00187"></a><span class="lineno">  187</span>&#160;    }</div>
<div class="line"><a name="l00188"></a><span class="lineno">  188</span>&#160;    <a class="code" href="classqbpp_1_1misc_1_1RandomSet.html#afeeb15f5fc634c22e9326aecbff71d7c">variables_</a>.push_back(index);</div>
<div class="line"><a name="l00189"></a><span class="lineno">  189</span>&#160;    <a class="code" href="classqbpp_1_1misc_1_1RandomSet.html#a2562592074e847095092c21b9feb6fd4">position_</a>[index] = <span class="keyword">static_cast&lt;</span><a class="code" href="namespaceqbpp.html#a5cf436100ede362797d0c0501ea50a5a">vindex_t</a><span class="keyword">&gt;</span>(<a class="code" href="classqbpp_1_1misc_1_1RandomSet.html#afeeb15f5fc634c22e9326aecbff71d7c">variables_</a>.size() - 1);</div>
<div class="line"><a name="l00190"></a><span class="lineno">  190</span>&#160;  }</div>
<div class="line"><a name="l00191"></a><span class="lineno">  191</span>&#160; </div>
<div class="line"><a name="l00192"></a><span class="lineno"><a class="line" href="classqbpp_1_1misc_1_1RandomSet.html#a64eed5f84e5a5b9bfdf2aed95dc10da0">  192</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="classqbpp_1_1misc_1_1RandomSet.html#a64eed5f84e5a5b9bfdf2aed95dc10da0">swap</a>(<a class="code" href="namespaceqbpp.html#a5cf436100ede362797d0c0501ea50a5a">vindex_t</a> pos1, <a class="code" href="namespaceqbpp.html#a5cf436100ede362797d0c0501ea50a5a">vindex_t</a> pos2) {</div>
<div class="line"><a name="l00193"></a><span class="lineno">  193</span>&#160;    <span class="keywordflow">if</span> (pos1 == pos2) <span class="keywordflow">return</span>;</div>
<div class="line"><a name="l00194"></a><span class="lineno">  194</span>&#160;    std::swap(<a class="code" href="classqbpp_1_1misc_1_1RandomSet.html#afeeb15f5fc634c22e9326aecbff71d7c">variables_</a>[pos1], <a class="code" href="classqbpp_1_1misc_1_1RandomSet.html#afeeb15f5fc634c22e9326aecbff71d7c">variables_</a>[pos2]);</div>
<div class="line"><a name="l00195"></a><span class="lineno">  195</span>&#160;    <a class="code" href="classqbpp_1_1misc_1_1RandomSet.html#a2562592074e847095092c21b9feb6fd4">position_</a>[<a class="code" href="classqbpp_1_1misc_1_1RandomSet.html#afeeb15f5fc634c22e9326aecbff71d7c">variables_</a>[pos1]] = pos1;</div>
<div class="line"><a name="l00196"></a><span class="lineno">  196</span>&#160;    <a class="code" href="classqbpp_1_1misc_1_1RandomSet.html#a2562592074e847095092c21b9feb6fd4">position_</a>[<a class="code" href="classqbpp_1_1misc_1_1RandomSet.html#afeeb15f5fc634c22e9326aecbff71d7c">variables_</a>[pos2]] = pos2;</div>
<div class="line"><a name="l00197"></a><span class="lineno">  197</span>&#160;  }</div>
<div class="line"><a name="l00198"></a><span class="lineno">  198</span>&#160; </div>
<div class="line"><a name="l00199"></a><span class="lineno"><a class="line" href="classqbpp_1_1misc_1_1RandomSet.html#a5af104acdf702d30127915161f5b0790">  199</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="classqbpp_1_1misc_1_1RandomSet.html#a5af104acdf702d30127915161f5b0790">erase</a>(<a class="code" href="namespaceqbpp.html#a5cf436100ede362797d0c0501ea50a5a">vindex_t</a> index) {</div>
<div class="line"><a name="l00200"></a><span class="lineno">  200</span>&#160;    <span class="keywordflow">if</span> (<a class="code" href="classqbpp_1_1misc_1_1RandomSet.html#a2562592074e847095092c21b9feb6fd4">position_</a>[index] == <a class="code" href="namespaceqbpp.html#ac0b50505a1738411ca7c96487b296234">vindex_limit</a>) {</div>
<div class="line"><a name="l00201"></a><span class="lineno">  201</span>&#160;      <span class="keywordflow">throw</span> std::runtime_error(</div>
<div class="line"><a name="l00202"></a><span class="lineno">  202</span>&#160;          <a class="code" href="qbpp_8hpp.html#a265e0828fcea8e75fbc9c0be99f7156e">THROW_MESSAGE</a>(<span class="stringliteral">&quot;RandomSet: Erase variable (&quot;</span>, index, <span class="stringliteral">&quot;) not in set&quot;</span>));</div>
<div class="line"><a name="l00203"></a><span class="lineno">  203</span>&#160;    }</div>
<div class="line"><a name="l00204"></a><span class="lineno">  204</span>&#160;    <a class="code" href="classqbpp_1_1misc_1_1RandomSet.html#a64eed5f84e5a5b9bfdf2aed95dc10da0">swap</a>(<a class="code" href="classqbpp_1_1misc_1_1RandomSet.html#a2562592074e847095092c21b9feb6fd4">position_</a>[index], <a class="code" href="classqbpp_1_1misc_1_1RandomSet.html#a950bd3acaf6f41fe4d1c926ab4f389cc">var_count</a>() - 1);</div>
<div class="line"><a name="l00205"></a><span class="lineno">  205</span>&#160;    <a class="code" href="classqbpp_1_1misc_1_1RandomSet.html#afeeb15f5fc634c22e9326aecbff71d7c">variables_</a>.pop_back();</div>
<div class="line"><a name="l00206"></a><span class="lineno">  206</span>&#160;    <a class="code" href="classqbpp_1_1misc_1_1RandomSet.html#a2562592074e847095092c21b9feb6fd4">position_</a>[index] = <a class="code" href="namespaceqbpp.html#ac0b50505a1738411ca7c96487b296234">vindex_limit</a>;</div>
<div class="line"><a name="l00207"></a><span class="lineno">  207</span>&#160;  }</div>
<div class="line"><a name="l00208"></a><span class="lineno">  208</span>&#160; </div>
<div class="line"><a name="l00209"></a><span class="lineno"><a class="line" href="classqbpp_1_1misc_1_1RandomSet.html#a9b527e66e301237adde36dfbfba4f866">  209</a></span>&#160;  <span class="keywordtype">bool</span> <a class="code" href="classqbpp_1_1misc_1_1RandomSet.html#a9b527e66e301237adde36dfbfba4f866">has</a>(<a class="code" href="namespaceqbpp.html#a5cf436100ede362797d0c0501ea50a5a">vindex_t</a> index)<span class="keyword"> const </span>{ <span class="keywordflow">return</span> <a class="code" href="classqbpp_1_1misc_1_1RandomSet.html#a2562592074e847095092c21b9feb6fd4">position_</a>[index] != <a class="code" href="namespaceqbpp.html#ac0b50505a1738411ca7c96487b296234">vindex_limit</a>; }</div>
<div class="line"><a name="l00210"></a><span class="lineno">  210</span>&#160; </div>
<div class="line"><a name="l00211"></a><span class="lineno"><a class="line" href="classqbpp_1_1misc_1_1RandomSet.html#a320528cb75db363c41628fa043cf7f66">  211</a></span>&#160;  <a class="code" href="namespaceqbpp.html#a5cf436100ede362797d0c0501ea50a5a">vindex_t</a> <a class="code" href="classqbpp_1_1misc_1_1RandomSet.html#a320528cb75db363c41628fa043cf7f66">select_at_random</a>()<span class="keyword"> const </span>{</div>
<div class="line"><a name="l00212"></a><span class="lineno">  212</span>&#160;    <span class="keywordflow">return</span> <a class="code" href="classqbpp_1_1misc_1_1RandomSet.html#afeeb15f5fc634c22e9326aecbff71d7c">variables_</a>[<a class="code" href="classqbpp_1_1misc_1_1RandomGenerator.html#a95308c2300aeef9a881ba97786367977">qbpp::misc::RandomGenerator::gen</a>(<a class="code" href="classqbpp_1_1misc_1_1RandomSet.html#a950bd3acaf6f41fe4d1c926ab4f389cc">var_count</a>())];</div>
<div class="line"><a name="l00213"></a><span class="lineno">  213</span>&#160;  }</div>
<div class="line"><a name="l00214"></a><span class="lineno">  214</span>&#160; </div>
<div class="line"><a name="l00215"></a><span class="lineno"><a class="line" href="classqbpp_1_1misc_1_1RandomSet.html#a04fe9fe1367448d458613fa8d1b016f4">  215</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="classqbpp_1_1misc_1_1RandomSet.html#a04fe9fe1367448d458613fa8d1b016f4">print</a>(<span class="keyword">const</span> std::string &amp;prefix = <span class="stringliteral">&quot;&quot;</span>)<span class="keyword"> const </span>{</div>
<div class="line"><a name="l00216"></a><span class="lineno">  216</span>&#160;    std::cout &lt;&lt; prefix &lt;&lt; <span class="stringliteral">&quot; Size = &quot;</span> &lt;&lt; <a class="code" href="classqbpp_1_1misc_1_1RandomSet.html#a950bd3acaf6f41fe4d1c926ab4f389cc">var_count</a>() &lt;&lt; <span class="stringliteral">&quot; : &quot;</span>;</div>
<div class="line"><a name="l00217"></a><span class="lineno">  217</span>&#160;    <span class="keywordflow">for</span> (<a class="code" href="namespaceqbpp.html#a5cf436100ede362797d0c0501ea50a5a">vindex_t</a> i = 0; i &lt; <a class="code" href="classqbpp_1_1misc_1_1RandomSet.html#a950bd3acaf6f41fe4d1c926ab4f389cc">var_count</a>(); ++i) {</div>
<div class="line"><a name="l00218"></a><span class="lineno">  218</span>&#160;      std::cout &lt;&lt; <span class="stringliteral">&quot; &quot;</span> &lt;&lt; <a class="code" href="classqbpp_1_1misc_1_1RandomSet.html#afeeb15f5fc634c22e9326aecbff71d7c">variables_</a>[i];</div>
<div class="line"><a name="l00219"></a><span class="lineno">  219</span>&#160;    }</div>
<div class="line"><a name="l00220"></a><span class="lineno">  220</span>&#160;    std::cout &lt;&lt; std::endl;</div>
<div class="line"><a name="l00221"></a><span class="lineno">  221</span>&#160;  }</div>
<div class="line"><a name="l00222"></a><span class="lineno">  222</span>&#160;};</div>
<div class="line"><a name="l00223"></a><span class="lineno">  223</span>&#160; </div>
<div class="line"><a name="l00224"></a><span class="lineno">  224</span>&#160;<span class="keyword">template</span> &lt;<span class="keyword">typename</span> T = energy_t&gt;</div>
<div class="line"><a name="l00225"></a><span class="lineno"><a class="line" href="classqbpp_1_1misc_1_1MinSet.html">  225</a></span>&#160;<span class="keyword">class </span><a class="code" href="classqbpp_1_1misc_1_1MinSet.html">MinSet</a> {</div>
<div class="line"><a name="l00226"></a><span class="lineno">  226</span>&#160; </div>
<div class="line"><a name="l00227"></a><span class="lineno"><a class="line" href="classqbpp_1_1misc_1_1MinSet.html#a065b55ecae0901ab86e9c8a8c7fc6af8">  227</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classqbpp_1_1misc_1_1MinSet.html#a065b55ecae0901ab86e9c8a8c7fc6af8">ValIndexMap</a> = std::set&lt;std::pair&lt;T, vindex_t&gt;&gt;;</div>
<div class="line"><a name="l00228"></a><span class="lineno">  228</span>&#160; </div>
<div class="line"><a name="l00229"></a><span class="lineno"><a class="line" href="classqbpp_1_1misc_1_1MinSet.html#a0a9fee085659a2b0421af613ec5f4433">  229</a></span>&#160;  <a class="code" href="classqbpp_1_1misc_1_1MinSet.html#a065b55ecae0901ab86e9c8a8c7fc6af8">ValIndexMap</a> <a class="code" href="classqbpp_1_1misc_1_1MinSet.html#a0a9fee085659a2b0421af613ec5f4433">set_</a>;</div>
<div class="line"><a name="l00230"></a><span class="lineno">  230</span>&#160; </div>
<div class="line"><a name="l00231"></a><span class="lineno">  231</span>&#160; <span class="keyword">public</span>:</div>
<div class="line"><a name="l00232"></a><span class="lineno">  232</span>&#160;  <a class="code" href="classqbpp_1_1misc_1_1MinSet.html#a5ba92c07c6b0da89324815c9a344bfe5">MinSet</a>() = <span class="keywordflow">default</span>;</div>
<div class="line"><a name="l00233"></a><span class="lineno">  233</span>&#160; </div>
<div class="line"><a name="l00234"></a><span class="lineno"><a class="line" href="classqbpp_1_1misc_1_1MinSet.html#ad9f980fba77f1f8d017354953a5cb434">  234</a></span>&#160;  <a class="code" href="namespaceqbpp.html#a5cf436100ede362797d0c0501ea50a5a">vindex_t</a> <a class="code" href="classqbpp_1_1misc_1_1MinSet.html#ad9f980fba77f1f8d017354953a5cb434">var_count</a>()<span class="keyword"> const </span>{ <span class="keywordflow">return</span> <span class="keyword">static_cast&lt;</span><a class="code" href="namespaceqbpp.html#a5cf436100ede362797d0c0501ea50a5a">vindex_t</a><span class="keyword">&gt;</span>(<a class="code" href="classqbpp_1_1misc_1_1MinSet.html#a0a9fee085659a2b0421af613ec5f4433">set_</a>.size()); }</div>
<div class="line"><a name="l00235"></a><span class="lineno">  235</span>&#160; </div>
<div class="line"><a name="l00236"></a><span class="lineno"><a class="line" href="classqbpp_1_1misc_1_1MinSet.html#a63576a251e5c467e6f4b1eae8a1b1f03">  236</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="classqbpp_1_1misc_1_1MinSet.html#a63576a251e5c467e6f4b1eae8a1b1f03">insert</a>(<a class="code" href="namespaceqbpp.html#a5cf436100ede362797d0c0501ea50a5a">vindex_t</a> index, T delta) {</div>
<div class="line"><a name="l00237"></a><span class="lineno">  237</span>&#160;    <span class="keyword">auto</span> tuple_val = std::make_pair(delta, index);</div>
<div class="line"><a name="l00238"></a><span class="lineno">  238</span>&#160;    <span class="keyword">auto</span> result = <a class="code" href="classqbpp_1_1misc_1_1MinSet.html#a0a9fee085659a2b0421af613ec5f4433">set_</a>.insert(tuple_val);</div>
<div class="line"><a name="l00239"></a><span class="lineno">  239</span>&#160;    <span class="keywordflow">if</span> (!result.second) {</div>
<div class="line"><a name="l00240"></a><span class="lineno">  240</span>&#160;      <span class="keywordflow">throw</span> std::runtime_error(<a class="code" href="qbpp_8hpp.html#a265e0828fcea8e75fbc9c0be99f7156e">THROW_MESSAGE</a>(<span class="stringliteral">&quot;MinSet: Insert variable (&quot;</span>, index,</div>
<div class="line"><a name="l00241"></a><span class="lineno">  241</span>&#160;                                             <span class="stringliteral">&quot;) already in set&quot;</span>));</div>
<div class="line"><a name="l00242"></a><span class="lineno">  242</span>&#160;    }</div>
<div class="line"><a name="l00243"></a><span class="lineno">  243</span>&#160;  }</div>
<div class="line"><a name="l00244"></a><span class="lineno">  244</span>&#160; </div>
<div class="line"><a name="l00245"></a><span class="lineno"><a class="line" href="classqbpp_1_1misc_1_1MinSet.html#abfb846a3ff4277f39e5cc4798933ca80">  245</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="classqbpp_1_1misc_1_1MinSet.html#abfb846a3ff4277f39e5cc4798933ca80">erase</a>(<a class="code" href="namespaceqbpp.html#a5cf436100ede362797d0c0501ea50a5a">vindex_t</a> index, T delta) {</div>
<div class="line"><a name="l00246"></a><span class="lineno">  246</span>&#160;    <span class="keyword">auto</span> pair_val = std::make_pair(delta, index);</div>
<div class="line"><a name="l00247"></a><span class="lineno">  247</span>&#160;    <span class="keyword">auto</span> result = <a class="code" href="classqbpp_1_1misc_1_1MinSet.html#a0a9fee085659a2b0421af613ec5f4433">set_</a>.erase(pair_val);</div>
<div class="line"><a name="l00248"></a><span class="lineno">  248</span>&#160;    <span class="keywordflow">if</span> (result == 0) {</div>
<div class="line"><a name="l00249"></a><span class="lineno">  249</span>&#160;      <span class="keywordflow">throw</span> std::runtime_error(</div>
<div class="line"><a name="l00250"></a><span class="lineno">  250</span>&#160;          <a class="code" href="qbpp_8hpp.html#a265e0828fcea8e75fbc9c0be99f7156e">THROW_MESSAGE</a>(<span class="stringliteral">&quot;MinSet: Erase variable (&quot;</span>, index, <span class="stringliteral">&quot;) not in set&quot;</span>));</div>
<div class="line"><a name="l00251"></a><span class="lineno">  251</span>&#160;    }</div>
<div class="line"><a name="l00252"></a><span class="lineno">  252</span>&#160;  }</div>
<div class="line"><a name="l00253"></a><span class="lineno">  253</span>&#160; </div>
<div class="line"><a name="l00254"></a><span class="lineno"><a class="line" href="classqbpp_1_1misc_1_1MinSet.html#aa534cb8cd4b9d3646c243ab8c8d448e1">  254</a></span>&#160;  <a class="code" href="namespaceqbpp.html#a5cf436100ede362797d0c0501ea50a5a">vindex_t</a> <a class="code" href="classqbpp_1_1misc_1_1MinSet.html#aa534cb8cd4b9d3646c243ab8c8d448e1">get_first</a>()<span class="keyword"> const </span>{ <span class="keywordflow">return</span> (*<a class="code" href="classqbpp_1_1misc_1_1MinSet.html#a0a9fee085659a2b0421af613ec5f4433">set_</a>.begin()).second; }</div>
<div class="line"><a name="l00255"></a><span class="lineno">  255</span>&#160; </div>
<div class="line"><a name="l00256"></a><span class="lineno"><a class="line" href="classqbpp_1_1misc_1_1MinSet.html#a86ef019499ebf9b72fe26fb7abcad094">  256</a></span>&#160;  std::pair&lt;T, vindex_t&gt; <a class="code" href="classqbpp_1_1misc_1_1MinSet.html#a86ef019499ebf9b72fe26fb7abcad094">get_min</a>()<span class="keyword"> const </span>{ <span class="keywordflow">return</span> *<a class="code" href="classqbpp_1_1misc_1_1MinSet.html#a0a9fee085659a2b0421af613ec5f4433">set_</a>.begin(); }</div>
<div class="line"><a name="l00257"></a><span class="lineno">  257</span>&#160; </div>
<div class="line"><a name="l00258"></a><span class="lineno"><a class="line" href="classqbpp_1_1misc_1_1MinSet.html#acfd21dd52cb7fffae88e04e839505680">  258</a></span>&#160;  <span class="keywordtype">bool</span> <a class="code" href="classqbpp_1_1misc_1_1MinSet.html#acfd21dd52cb7fffae88e04e839505680">empty</a>()<span class="keyword"> const </span>{ <span class="keywordflow">return</span> <a class="code" href="classqbpp_1_1misc_1_1MinSet.html#a0a9fee085659a2b0421af613ec5f4433">set_</a>.empty(); }</div>
<div class="line"><a name="l00259"></a><span class="lineno">  259</span>&#160; </div>
<div class="line"><a name="l00260"></a><span class="lineno"><a class="line" href="classqbpp_1_1misc_1_1MinSet.html#a8bfa3e020f7b6d2360b92f0982db6be4">  260</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="classqbpp_1_1misc_1_1MinSet.html#a8bfa3e020f7b6d2360b92f0982db6be4">print</a>(<span class="keyword">const</span> std::string &amp;prefix)<span class="keyword"> const </span>{</div>
<div class="line"><a name="l00261"></a><span class="lineno">  261</span>&#160;    std::cout &lt;&lt; prefix;</div>
<div class="line"><a name="l00262"></a><span class="lineno">  262</span>&#160;    <span class="keywordflow">for</span> (<span class="keyword">auto</span> &amp;[val, i] : <a class="code" href="classqbpp_1_1misc_1_1MinSet.html#a0a9fee085659a2b0421af613ec5f4433">set_</a>) {</div>
<div class="line"><a name="l00263"></a><span class="lineno">  263</span>&#160;      <span class="keywordflow">if</span> constexpr (std::is_same_v&lt;T, energy_t&gt;) {</div>
<div class="line"><a name="l00264"></a><span class="lineno">  264</span>&#160;        std::cout &lt;&lt; <span class="stringliteral">&quot;(&quot;</span> &lt;&lt; i &lt;&lt; <span class="stringliteral">&quot;,&quot;</span> &lt;&lt; val &lt;&lt; <span class="stringliteral">&quot;)&quot;</span>;</div>
<div class="line"><a name="l00265"></a><span class="lineno">  265</span>&#160;      } <span class="keywordflow">else</span> {</div>
<div class="line"><a name="l00266"></a><span class="lineno">  266</span>&#160;        std::cout &lt;&lt; <span class="stringliteral">&quot;(&quot;</span> &lt;&lt; i &lt;&lt; <span class="stringliteral">&quot;,&quot;</span> &lt;&lt; val.get_constraint_delta() &lt;&lt; <span class="stringliteral">&quot;,&quot;</span></div>
<div class="line"><a name="l00267"></a><span class="lineno">  267</span>&#160;                  &lt;&lt; val.get_objective_delta() &lt;&lt; <span class="stringliteral">&quot;)&quot;</span>;</div>
<div class="line"><a name="l00268"></a><span class="lineno">  268</span>&#160;      }</div>
<div class="line"><a name="l00269"></a><span class="lineno">  269</span>&#160;    }</div>
<div class="line"><a name="l00270"></a><span class="lineno">  270</span>&#160;    std::cout &lt;&lt; std::endl;</div>
<div class="line"><a name="l00271"></a><span class="lineno">  271</span>&#160;  }</div>
<div class="line"><a name="l00272"></a><span class="lineno">  272</span>&#160;};</div>
<div class="line"><a name="l00273"></a><span class="lineno">  273</span>&#160; </div>
<div class="line"><a name="l00274"></a><span class="lineno">  274</span>&#160;<span class="keyword">template</span> &lt;<span class="keyword">typename</span> T = energy_t&gt;</div>
<div class="line"><a name="l00275"></a><span class="lineno"><a class="line" href="classqbpp_1_1misc_1_1MinHeap.html">  275</a></span>&#160;<span class="keyword">class </span><a class="code" href="classqbpp_1_1misc_1_1MinHeap.html">MinHeap</a> {</div>
<div class="line"><a name="l00276"></a><span class="lineno"><a class="line" href="classqbpp_1_1misc_1_1MinHeap.html#a8368e49fcd52062a31aa53fd6d10013e">  276</a></span>&#160;  std::vector&lt;std::pair&lt;T, vindex_t&gt;&gt; <a class="code" href="classqbpp_1_1misc_1_1MinHeap.html#a8368e49fcd52062a31aa53fd6d10013e">heap_</a>;</div>
<div class="line"><a name="l00277"></a><span class="lineno"><a class="line" href="classqbpp_1_1misc_1_1MinHeap.html#a8b67c7050c9ff2553a95b7ce1f9f8a83">  277</a></span>&#160;  std::vector&lt;vindex_t&gt; <a class="code" href="classqbpp_1_1misc_1_1MinHeap.html#a8b67c7050c9ff2553a95b7ce1f9f8a83">index_</a>;</div>
<div class="line"><a name="l00278"></a><span class="lineno">  278</span>&#160; </div>
<div class="line"><a name="l00279"></a><span class="lineno"><a class="line" href="classqbpp_1_1misc_1_1MinHeap.html#a79b0070d23b88929420292554d186236">  279</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="classqbpp_1_1misc_1_1MinHeap.html#a79b0070d23b88929420292554d186236">emplace_back</a>(<a class="code" href="namespaceqbpp.html#a5cf436100ede362797d0c0501ea50a5a">vindex_t</a> index, <a class="code" href="namespaceqbpp.html#a9c324f641779d1a7a3c7723f28d8cff4">energy_t</a> delta) {</div>
<div class="line"><a name="l00280"></a><span class="lineno">  280</span>&#160;    <a class="code" href="classqbpp_1_1misc_1_1MinHeap.html#a8368e49fcd52062a31aa53fd6d10013e">heap_</a>.emplace_back(std::make_pair(delta, index));</div>
<div class="line"><a name="l00281"></a><span class="lineno">  281</span>&#160;    <a class="code" href="classqbpp_1_1misc_1_1MinHeap.html#a8b67c7050c9ff2553a95b7ce1f9f8a83">index_</a>[index] = <a class="code" href="classqbpp_1_1misc_1_1MinHeap.html#ae61d02fae1382293fb465d2c52477dd0">heap_size</a>() - 1;</div>
<div class="line"><a name="l00282"></a><span class="lineno">  282</span>&#160;  }</div>
<div class="line"><a name="l00283"></a><span class="lineno">  283</span>&#160; </div>
<div class="line"><a name="l00284"></a><span class="lineno"><a class="line" href="classqbpp_1_1misc_1_1MinHeap.html#a2378512cf4e99296fe714ef41f3df930">  284</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="classqbpp_1_1misc_1_1MinHeap.html#a2378512cf4e99296fe714ef41f3df930">swap_heap</a>(<a class="code" href="namespaceqbpp.html#a5cf436100ede362797d0c0501ea50a5a">vindex_t</a> i, <a class="code" href="namespaceqbpp.html#a5cf436100ede362797d0c0501ea50a5a">vindex_t</a> j) {</div>
<div class="line"><a name="l00285"></a><span class="lineno">  285</span>&#160;    std::swap(<a class="code" href="classqbpp_1_1misc_1_1MinHeap.html#a8368e49fcd52062a31aa53fd6d10013e">heap_</a>[i], <a class="code" href="classqbpp_1_1misc_1_1MinHeap.html#a8368e49fcd52062a31aa53fd6d10013e">heap_</a>[j]);</div>
<div class="line"><a name="l00286"></a><span class="lineno">  286</span>&#160;    <a class="code" href="classqbpp_1_1misc_1_1MinHeap.html#a8b67c7050c9ff2553a95b7ce1f9f8a83">index_</a>[<a class="code" href="classqbpp_1_1misc_1_1MinHeap.html#a8368e49fcd52062a31aa53fd6d10013e">heap_</a>[i].second] = i;</div>
<div class="line"><a name="l00287"></a><span class="lineno">  287</span>&#160;    <a class="code" href="classqbpp_1_1misc_1_1MinHeap.html#a8b67c7050c9ff2553a95b7ce1f9f8a83">index_</a>[<a class="code" href="classqbpp_1_1misc_1_1MinHeap.html#a8368e49fcd52062a31aa53fd6d10013e">heap_</a>[j].second] = j;</div>
<div class="line"><a name="l00288"></a><span class="lineno">  288</span>&#160;  }</div>
<div class="line"><a name="l00289"></a><span class="lineno">  289</span>&#160; </div>
<div class="line"><a name="l00290"></a><span class="lineno"><a class="line" href="classqbpp_1_1misc_1_1MinHeap.html#a166fa97ef203cf94a106eb0e28eb46f0">  290</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="classqbpp_1_1misc_1_1MinHeap.html#a166fa97ef203cf94a106eb0e28eb46f0">bubble_up</a>(<a class="code" href="namespaceqbpp.html#a5cf436100ede362797d0c0501ea50a5a">vindex_t</a> i) {</div>
<div class="line"><a name="l00291"></a><span class="lineno">  291</span>&#160;    <span class="keywordflow">while</span> (i &gt; 0) {</div>
<div class="line"><a name="l00292"></a><span class="lineno">  292</span>&#160;      <a class="code" href="namespaceqbpp.html#a5cf436100ede362797d0c0501ea50a5a">vindex_t</a> parent = (i - 1) / 2;</div>
<div class="line"><a name="l00293"></a><span class="lineno">  293</span>&#160;      <span class="keywordflow">if</span> (<a class="code" href="classqbpp_1_1misc_1_1MinHeap.html#a8368e49fcd52062a31aa53fd6d10013e">heap_</a>[i] &lt; <a class="code" href="classqbpp_1_1misc_1_1MinHeap.html#a8368e49fcd52062a31aa53fd6d10013e">heap_</a>[parent]) {</div>
<div class="line"><a name="l00294"></a><span class="lineno">  294</span>&#160;        <a class="code" href="classqbpp_1_1misc_1_1MinHeap.html#a2378512cf4e99296fe714ef41f3df930">swap_heap</a>(i, parent);</div>
<div class="line"><a name="l00295"></a><span class="lineno">  295</span>&#160;        i = parent;</div>
<div class="line"><a name="l00296"></a><span class="lineno">  296</span>&#160;      } <span class="keywordflow">else</span> {</div>
<div class="line"><a name="l00297"></a><span class="lineno">  297</span>&#160;        <span class="keywordflow">break</span>;</div>
<div class="line"><a name="l00298"></a><span class="lineno">  298</span>&#160;      }</div>
<div class="line"><a name="l00299"></a><span class="lineno">  299</span>&#160;    }</div>
<div class="line"><a name="l00300"></a><span class="lineno">  300</span>&#160;  }</div>
<div class="line"><a name="l00301"></a><span class="lineno">  301</span>&#160; </div>
<div class="line"><a name="l00302"></a><span class="lineno"><a class="line" href="classqbpp_1_1misc_1_1MinHeap.html#a7df936d574ef6d854b01febdf52a4492">  302</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="classqbpp_1_1misc_1_1MinHeap.html#a7df936d574ef6d854b01febdf52a4492">bubble_down</a>(<a class="code" href="namespaceqbpp.html#a5cf436100ede362797d0c0501ea50a5a">vindex_t</a> i) {</div>
<div class="line"><a name="l00303"></a><span class="lineno">  303</span>&#160;    <span class="keywordflow">while</span> (<span class="keyword">true</span>) {</div>
<div class="line"><a name="l00304"></a><span class="lineno">  304</span>&#160;      <a class="code" href="namespaceqbpp.html#a5cf436100ede362797d0c0501ea50a5a">vindex_t</a> left = 2 * i + 1;</div>
<div class="line"><a name="l00305"></a><span class="lineno">  305</span>&#160;      <a class="code" href="namespaceqbpp.html#a5cf436100ede362797d0c0501ea50a5a">vindex_t</a> right = 2 * i + 2;</div>
<div class="line"><a name="l00306"></a><span class="lineno">  306</span>&#160;      <a class="code" href="namespaceqbpp.html#a5cf436100ede362797d0c0501ea50a5a">vindex_t</a> smallest = i;</div>
<div class="line"><a name="l00307"></a><span class="lineno">  307</span>&#160; </div>
<div class="line"><a name="l00308"></a><span class="lineno">  308</span>&#160;      <span class="keywordflow">if</span> (left &lt; <a class="code" href="classqbpp_1_1misc_1_1MinHeap.html#ae61d02fae1382293fb465d2c52477dd0">heap_size</a>() &amp;&amp; <a class="code" href="classqbpp_1_1misc_1_1MinHeap.html#a8368e49fcd52062a31aa53fd6d10013e">heap_</a>[left] &lt; <a class="code" href="classqbpp_1_1misc_1_1MinHeap.html#a8368e49fcd52062a31aa53fd6d10013e">heap_</a>[smallest]) {</div>
<div class="line"><a name="l00309"></a><span class="lineno">  309</span>&#160;        smallest = left;</div>
<div class="line"><a name="l00310"></a><span class="lineno">  310</span>&#160;      }</div>
<div class="line"><a name="l00311"></a><span class="lineno">  311</span>&#160;      <span class="keywordflow">if</span> (right &lt; <a class="code" href="classqbpp_1_1misc_1_1MinHeap.html#ae61d02fae1382293fb465d2c52477dd0">heap_size</a>() &amp;&amp; <a class="code" href="classqbpp_1_1misc_1_1MinHeap.html#a8368e49fcd52062a31aa53fd6d10013e">heap_</a>[right] &lt; <a class="code" href="classqbpp_1_1misc_1_1MinHeap.html#a8368e49fcd52062a31aa53fd6d10013e">heap_</a>[smallest]) {</div>
<div class="line"><a name="l00312"></a><span class="lineno">  312</span>&#160;        smallest = right;</div>
<div class="line"><a name="l00313"></a><span class="lineno">  313</span>&#160;      }</div>
<div class="line"><a name="l00314"></a><span class="lineno">  314</span>&#160;      <span class="keywordflow">if</span> (smallest != i) {</div>
<div class="line"><a name="l00315"></a><span class="lineno">  315</span>&#160;        <a class="code" href="classqbpp_1_1misc_1_1MinHeap.html#a2378512cf4e99296fe714ef41f3df930">swap_heap</a>(i, smallest);</div>
<div class="line"><a name="l00316"></a><span class="lineno">  316</span>&#160;        i = smallest;</div>
<div class="line"><a name="l00317"></a><span class="lineno">  317</span>&#160;      } <span class="keywordflow">else</span> {</div>
<div class="line"><a name="l00318"></a><span class="lineno">  318</span>&#160;        <span class="keywordflow">break</span>;</div>
<div class="line"><a name="l00319"></a><span class="lineno">  319</span>&#160;      }</div>
<div class="line"><a name="l00320"></a><span class="lineno">  320</span>&#160;    }</div>
<div class="line"><a name="l00321"></a><span class="lineno">  321</span>&#160;  }</div>
<div class="line"><a name="l00322"></a><span class="lineno">  322</span>&#160; </div>
<div class="line"><a name="l00323"></a><span class="lineno">  323</span>&#160; <span class="keyword">public</span>:</div>
<div class="line"><a name="l00324"></a><span class="lineno"><a class="line" href="classqbpp_1_1misc_1_1MinHeap.html#a0347d3112d719360c4348c7f688a369b">  324</a></span>&#160;  <a class="code" href="classqbpp_1_1misc_1_1MinHeap.html#a0347d3112d719360c4348c7f688a369b">MinHeap</a>(<span class="keywordtype">size_t</span> size) : <a class="code" href="classqbpp_1_1misc_1_1MinHeap.html#a8b67c7050c9ff2553a95b7ce1f9f8a83">index_</a>(size, <a class="code" href="namespaceqbpp.html#ac0b50505a1738411ca7c96487b296234">vindex_limit</a>) {}</div>
<div class="line"><a name="l00325"></a><span class="lineno">  325</span>&#160; </div>
<div class="line"><a name="l00326"></a><span class="lineno"><a class="line" href="classqbpp_1_1misc_1_1MinHeap.html#ae61d02fae1382293fb465d2c52477dd0">  326</a></span>&#160;  <a class="code" href="namespaceqbpp.html#a5cf436100ede362797d0c0501ea50a5a">vindex_t</a> <a class="code" href="classqbpp_1_1misc_1_1MinHeap.html#ae61d02fae1382293fb465d2c52477dd0">heap_size</a>()<span class="keyword"> const </span>{ <span class="keywordflow">return</span> <span class="keyword">static_cast&lt;</span><a class="code" href="namespaceqbpp.html#a5cf436100ede362797d0c0501ea50a5a">vindex_t</a><span class="keyword">&gt;</span>(<a class="code" href="classqbpp_1_1misc_1_1MinHeap.html#a8368e49fcd52062a31aa53fd6d10013e">heap_</a>.size()); }</div>
<div class="line"><a name="l00327"></a><span class="lineno"><a class="line" href="classqbpp_1_1misc_1_1MinHeap.html#ad5e42b4bd3f622c159a7863a1b7c011f">  327</a></span>&#160;  <a class="code" href="namespaceqbpp.html#a5cf436100ede362797d0c0501ea50a5a">vindex_t</a> <a class="code" href="classqbpp_1_1misc_1_1MinHeap.html#ad5e42b4bd3f622c159a7863a1b7c011f">get_first</a>()<span class="keyword"> const </span>{ <span class="keywordflow">return</span> <a class="code" href="classqbpp_1_1misc_1_1MinHeap.html#a8368e49fcd52062a31aa53fd6d10013e">heap_</a>[0].second; }</div>
<div class="line"><a name="l00328"></a><span class="lineno">  328</span>&#160; </div>
<div class="line"><a name="l00329"></a><span class="lineno"><a class="line" href="classqbpp_1_1misc_1_1MinHeap.html#a16b2fe5b4cf386da0e28fa16e26fbef7">  329</a></span>&#160;  <span class="keywordtype">bool</span> <a class="code" href="classqbpp_1_1misc_1_1MinHeap.html#a16b2fe5b4cf386da0e28fa16e26fbef7">has</a>(<a class="code" href="namespaceqbpp.html#a5cf436100ede362797d0c0501ea50a5a">vindex_t</a> index)<span class="keyword"> const </span>{ <span class="keywordflow">return</span> <a class="code" href="classqbpp_1_1misc_1_1MinHeap.html#a8b67c7050c9ff2553a95b7ce1f9f8a83">index_</a>[index] != <a class="code" href="namespaceqbpp.html#ac0b50505a1738411ca7c96487b296234">vindex_limit</a>; }</div>
<div class="line"><a name="l00330"></a><span class="lineno">  330</span>&#160; </div>
<div class="line"><a name="l00331"></a><span class="lineno"><a class="line" href="classqbpp_1_1misc_1_1MinHeap.html#ac6ccb0d64b36d2819e1b98e06ce03486">  331</a></span>&#160;  <span class="keywordtype">bool</span> <a class="code" href="classqbpp_1_1misc_1_1MinHeap.html#ac6ccb0d64b36d2819e1b98e06ce03486">empty</a>()<span class="keyword"> const </span>{ <span class="keywordflow">return</span> <a class="code" href="classqbpp_1_1misc_1_1MinHeap.html#a8368e49fcd52062a31aa53fd6d10013e">heap_</a>.empty(); }</div>
<div class="line"><a name="l00332"></a><span class="lineno">  332</span>&#160; </div>
<div class="line"><a name="l00333"></a><span class="lineno"><a class="line" href="classqbpp_1_1misc_1_1MinHeap.html#acba907f46558aefc996cc8aafa381249">  333</a></span>&#160;  <a class="code" href="namespaceqbpp.html#a5cf436100ede362797d0c0501ea50a5a">vindex_t</a> <a class="code" href="classqbpp_1_1misc_1_1MinHeap.html#acba907f46558aefc996cc8aafa381249">select_at_random</a>()<span class="keyword"> const </span>{</div>
<div class="line"><a name="l00334"></a><span class="lineno">  334</span>&#160;    <span class="keywordflow">return</span> <a class="code" href="classqbpp_1_1misc_1_1MinHeap.html#a8368e49fcd52062a31aa53fd6d10013e">heap_</a>[<a class="code" href="classqbpp_1_1misc_1_1RandomGenerator.html#a95308c2300aeef9a881ba97786367977">qbpp::misc::RandomGenerator::gen</a>(<a class="code" href="classqbpp_1_1misc_1_1MinHeap.html#ae61d02fae1382293fb465d2c52477dd0">heap_size</a>())].second;</div>
<div class="line"><a name="l00335"></a><span class="lineno">  335</span>&#160;  }</div>
<div class="line"><a name="l00336"></a><span class="lineno">  336</span>&#160; </div>
<div class="line"><a name="l00337"></a><span class="lineno"><a class="line" href="classqbpp_1_1misc_1_1MinHeap.html#a4ffbfe7ef3c611efd3de67cd523c94bd">  337</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="classqbpp_1_1misc_1_1MinHeap.html#a4ffbfe7ef3c611efd3de67cd523c94bd">insert</a>(<a class="code" href="namespaceqbpp.html#a5cf436100ede362797d0c0501ea50a5a">vindex_t</a> index, <a class="code" href="namespaceqbpp.html#a9c324f641779d1a7a3c7723f28d8cff4">energy_t</a> delta) {</div>
<div class="line"><a name="l00338"></a><span class="lineno">  338</span>&#160;    <span class="keywordflow">if</span> (<a class="code" href="classqbpp_1_1misc_1_1MinHeap.html#a8b67c7050c9ff2553a95b7ce1f9f8a83">index_</a>[index] != <a class="code" href="namespaceqbpp.html#ac0b50505a1738411ca7c96487b296234">vindex_limit</a>) {</div>
<div class="line"><a name="l00339"></a><span class="lineno">  339</span>&#160;      <span class="keywordflow">throw</span> std::runtime_error(<a class="code" href="qbpp_8hpp.html#a265e0828fcea8e75fbc9c0be99f7156e">THROW_MESSAGE</a>(<span class="stringliteral">&quot;DualMinHeap: Insert variable (&quot;</span>,</div>
<div class="line"><a name="l00340"></a><span class="lineno">  340</span>&#160;                                             index,</div>
<div class="line"><a name="l00341"></a><span class="lineno">  341</span>&#160;                                             <span class="stringliteral">&quot;) already in &quot;</span></div>
<div class="line"><a name="l00342"></a><span class="lineno">  342</span>&#160;                                             <span class="stringliteral">&quot;heap&quot;</span>));</div>
<div class="line"><a name="l00343"></a><span class="lineno">  343</span>&#160;    }</div>
<div class="line"><a name="l00344"></a><span class="lineno">  344</span>&#160;    <a class="code" href="classqbpp_1_1misc_1_1MinHeap.html#a79b0070d23b88929420292554d186236">emplace_back</a>(index, delta);</div>
<div class="line"><a name="l00345"></a><span class="lineno">  345</span>&#160;    <a class="code" href="classqbpp_1_1misc_1_1MinHeap.html#a166fa97ef203cf94a106eb0e28eb46f0">bubble_up</a>(<a class="code" href="classqbpp_1_1misc_1_1MinHeap.html#ae61d02fae1382293fb465d2c52477dd0">heap_size</a>() - 1);</div>
<div class="line"><a name="l00346"></a><span class="lineno">  346</span>&#160;  }</div>
<div class="line"><a name="l00347"></a><span class="lineno">  347</span>&#160; </div>
<div class="line"><a name="l00348"></a><span class="lineno"><a class="line" href="classqbpp_1_1misc_1_1MinHeap.html#aa209280cf2cfd0dd5ee04131bc96a582">  348</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="classqbpp_1_1misc_1_1MinHeap.html#aa209280cf2cfd0dd5ee04131bc96a582">erase</a>(<a class="code" href="namespaceqbpp.html#a5cf436100ede362797d0c0501ea50a5a">vindex_t</a> index) {</div>
<div class="line"><a name="l00349"></a><span class="lineno">  349</span>&#160;    <a class="code" href="namespaceqbpp.html#a5cf436100ede362797d0c0501ea50a5a">vindex_t</a> i = <a class="code" href="classqbpp_1_1misc_1_1MinHeap.html#a8b67c7050c9ff2553a95b7ce1f9f8a83">index_</a>[index];</div>
<div class="line"><a name="l00350"></a><span class="lineno">  350</span>&#160;    <span class="keywordflow">if</span> (i == <a class="code" href="namespaceqbpp.html#ac0b50505a1738411ca7c96487b296234">vindex_limit</a>) {</div>
<div class="line"><a name="l00351"></a><span class="lineno">  351</span>&#160;      <span class="keywordflow">throw</span> std::runtime_error(<a class="code" href="qbpp_8hpp.html#a265e0828fcea8e75fbc9c0be99f7156e">THROW_MESSAGE</a>(<span class="stringliteral">&quot;DualMinHeap: Erase variable (&quot;</span>,</div>
<div class="line"><a name="l00352"></a><span class="lineno">  352</span>&#160;                                             index,</div>
<div class="line"><a name="l00353"></a><span class="lineno">  353</span>&#160;                                             <span class="stringliteral">&quot;) not in &quot;</span></div>
<div class="line"><a name="l00354"></a><span class="lineno">  354</span>&#160;                                             <span class="stringliteral">&quot;heap&quot;</span>));</div>
<div class="line"><a name="l00355"></a><span class="lineno">  355</span>&#160;    }</div>
<div class="line"><a name="l00356"></a><span class="lineno">  356</span>&#160;    <a class="code" href="classqbpp_1_1misc_1_1MinHeap.html#a2378512cf4e99296fe714ef41f3df930">swap_heap</a>(i, <a class="code" href="classqbpp_1_1misc_1_1MinHeap.html#ae61d02fae1382293fb465d2c52477dd0">heap_size</a>() - 1);</div>
<div class="line"><a name="l00357"></a><span class="lineno">  357</span>&#160;    <a class="code" href="classqbpp_1_1misc_1_1MinHeap.html#a8368e49fcd52062a31aa53fd6d10013e">heap_</a>.pop_back();</div>
<div class="line"><a name="l00358"></a><span class="lineno">  358</span>&#160;    <a class="code" href="classqbpp_1_1misc_1_1MinHeap.html#a8b67c7050c9ff2553a95b7ce1f9f8a83">index_</a>[index] = <a class="code" href="namespaceqbpp.html#ac0b50505a1738411ca7c96487b296234">vindex_limit</a>;</div>
<div class="line"><a name="l00359"></a><span class="lineno">  359</span>&#160;    <span class="keywordflow">if</span> (i &lt; <a class="code" href="classqbpp_1_1misc_1_1MinHeap.html#ae61d02fae1382293fb465d2c52477dd0">heap_size</a>()) {</div>
<div class="line"><a name="l00360"></a><span class="lineno">  360</span>&#160;      <a class="code" href="classqbpp_1_1misc_1_1MinHeap.html#a166fa97ef203cf94a106eb0e28eb46f0">bubble_up</a>(i);</div>
<div class="line"><a name="l00361"></a><span class="lineno">  361</span>&#160;      <a class="code" href="classqbpp_1_1misc_1_1MinHeap.html#a7df936d574ef6d854b01febdf52a4492">bubble_down</a>(i);</div>
<div class="line"><a name="l00362"></a><span class="lineno">  362</span>&#160;    }</div>
<div class="line"><a name="l00363"></a><span class="lineno">  363</span>&#160;  }</div>
<div class="line"><a name="l00364"></a><span class="lineno">  364</span>&#160; </div>
<div class="line"><a name="l00365"></a><span class="lineno"><a class="line" href="classqbpp_1_1misc_1_1MinHeap.html#ae0d409ca2b14bae78db373f1eecbd99c">  365</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="classqbpp_1_1misc_1_1MinHeap.html#ae0d409ca2b14bae78db373f1eecbd99c">print</a>(<span class="keyword">const</span> std::string &amp;prefix)<span class="keyword"> const </span>{</div>
<div class="line"><a name="l00366"></a><span class="lineno">  366</span>&#160;    std::cout &lt;&lt; prefix;</div>
<div class="line"><a name="l00367"></a><span class="lineno">  367</span>&#160;    <span class="keywordflow">for</span> (<span class="keyword">auto</span> &amp;[delta, i] : <a class="code" href="classqbpp_1_1misc_1_1MinHeap.html#a8368e49fcd52062a31aa53fd6d10013e">heap_</a>) {</div>
<div class="line"><a name="l00368"></a><span class="lineno">  368</span>&#160;      std::cout &lt;&lt; <span class="stringliteral">&quot;(&quot;</span> &lt;&lt; i &lt;&lt; <span class="stringliteral">&quot;,&quot;</span> &lt;&lt; delta &lt;&lt; <span class="stringliteral">&quot;)&quot;</span>;</div>
<div class="line"><a name="l00369"></a><span class="lineno">  369</span>&#160;    }</div>
<div class="line"><a name="l00370"></a><span class="lineno">  370</span>&#160;    std::cout &lt;&lt; std::endl;</div>
<div class="line"><a name="l00371"></a><span class="lineno">  371</span>&#160;  }</div>
<div class="line"><a name="l00372"></a><span class="lineno">  372</span>&#160;};</div>
<div class="line"><a name="l00373"></a><span class="lineno">  373</span>&#160; </div>
<div class="line"><a name="l00374"></a><span class="lineno">  374</span>&#160;<span class="keyword">template</span> &lt;<span class="keyword">typename</span> T = energy_t&gt;</div>
<div class="line"><a name="l00375"></a><span class="lineno"><a class="line" href="classqbpp_1_1misc_1_1RandomMinSet.html">  375</a></span>&#160;<span class="keyword">class </span><a class="code" href="classqbpp_1_1misc_1_1RandomMinSet.html">RandomMinSet</a> {</div>
<div class="line"><a name="l00376"></a><span class="lineno"><a class="line" href="classqbpp_1_1misc_1_1RandomMinSet.html#a75fca9f5b81af8738a43f16ce849c292">  376</a></span>&#160;  <a class="code" href="classqbpp_1_1misc_1_1MinSet.html">MinSet&lt;T&gt;</a> <a class="code" href="classqbpp_1_1misc_1_1RandomMinSet.html#a75fca9f5b81af8738a43f16ce849c292">min_set_</a>;</div>
<div class="line"><a name="l00377"></a><span class="lineno"><a class="line" href="classqbpp_1_1misc_1_1RandomMinSet.html#ae6e28112e240de351a7efc77d58dc7a6">  377</a></span>&#160;  <a class="code" href="classqbpp_1_1misc_1_1RandomSet.html">RandomSet</a> <a class="code" href="classqbpp_1_1misc_1_1RandomMinSet.html#ae6e28112e240de351a7efc77d58dc7a6">random_set_</a>;</div>
<div class="line"><a name="l00378"></a><span class="lineno">  378</span>&#160; </div>
<div class="line"><a name="l00379"></a><span class="lineno">  379</span>&#160; <span class="keyword">public</span>:</div>
<div class="line"><a name="l00380"></a><span class="lineno"><a class="line" href="classqbpp_1_1misc_1_1RandomMinSet.html#a793253f32a42615244d2e353d5ff98eb">  380</a></span>&#160;  <a class="code" href="classqbpp_1_1misc_1_1RandomMinSet.html#a793253f32a42615244d2e353d5ff98eb">RandomMinSet</a>(<a class="code" href="namespaceqbpp.html#a5cf436100ede362797d0c0501ea50a5a">vindex_t</a> size) : <a class="code" href="classqbpp_1_1misc_1_1RandomMinSet.html#ae6e28112e240de351a7efc77d58dc7a6">random_set_</a>(size) {};</div>
<div class="line"><a name="l00381"></a><span class="lineno">  381</span>&#160; </div>
<div class="line"><a name="l00382"></a><span class="lineno"><a class="line" href="classqbpp_1_1misc_1_1RandomMinSet.html#a6410c2df27825c663516b0dce5c19fc6">  382</a></span>&#160;  <a class="code" href="namespaceqbpp.html#a5cf436100ede362797d0c0501ea50a5a">vindex_t</a> <a class="code" href="classqbpp_1_1misc_1_1RandomMinSet.html#a6410c2df27825c663516b0dce5c19fc6">var_count</a>()<span class="keyword"> const </span>{</div>
<div class="line"><a name="l00383"></a><span class="lineno">  383</span>&#160;    <span class="keywordflow">return</span> <span class="keyword">static_cast&lt;</span><a class="code" href="namespaceqbpp.html#a5cf436100ede362797d0c0501ea50a5a">vindex_t</a><span class="keyword">&gt;</span>(<a class="code" href="classqbpp_1_1misc_1_1RandomMinSet.html#a75fca9f5b81af8738a43f16ce849c292">min_set_</a>.var_count());</div>
<div class="line"><a name="l00384"></a><span class="lineno">  384</span>&#160;  }</div>
<div class="line"><a name="l00385"></a><span class="lineno">  385</span>&#160; </div>
<div class="line"><a name="l00386"></a><span class="lineno"><a class="line" href="classqbpp_1_1misc_1_1RandomMinSet.html#ad825296a0c90b57a2594b85d9c50c827">  386</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="classqbpp_1_1misc_1_1RandomMinSet.html#ad825296a0c90b57a2594b85d9c50c827">insert</a>(<a class="code" href="namespaceqbpp.html#a5cf436100ede362797d0c0501ea50a5a">vindex_t</a> index, T delta) {</div>
<div class="line"><a name="l00387"></a><span class="lineno">  387</span>&#160;    <a class="code" href="classqbpp_1_1misc_1_1RandomMinSet.html#a75fca9f5b81af8738a43f16ce849c292">min_set_</a>.insert(index, delta);</div>
<div class="line"><a name="l00388"></a><span class="lineno">  388</span>&#160;    <a class="code" href="classqbpp_1_1misc_1_1RandomMinSet.html#ae6e28112e240de351a7efc77d58dc7a6">random_set_</a>.<a class="code" href="classqbpp_1_1misc_1_1RandomSet.html#a543f429a80b910d5dd196764ed45488b">insert</a>(index);</div>
<div class="line"><a name="l00389"></a><span class="lineno">  389</span>&#160;  }</div>
<div class="line"><a name="l00390"></a><span class="lineno">  390</span>&#160; </div>
<div class="line"><a name="l00391"></a><span class="lineno"><a class="line" href="classqbpp_1_1misc_1_1RandomMinSet.html#acd4a41b2d92a9a5376f39ef5a56a240b">  391</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="classqbpp_1_1misc_1_1RandomMinSet.html#acd4a41b2d92a9a5376f39ef5a56a240b">erase</a>(<a class="code" href="namespaceqbpp.html#a5cf436100ede362797d0c0501ea50a5a">vindex_t</a> index, T delta) {</div>
<div class="line"><a name="l00392"></a><span class="lineno">  392</span>&#160;    <a class="code" href="classqbpp_1_1misc_1_1RandomMinSet.html#a75fca9f5b81af8738a43f16ce849c292">min_set_</a>.erase(index, delta);</div>
<div class="line"><a name="l00393"></a><span class="lineno">  393</span>&#160;    <a class="code" href="classqbpp_1_1misc_1_1RandomMinSet.html#ae6e28112e240de351a7efc77d58dc7a6">random_set_</a>.<a class="code" href="classqbpp_1_1misc_1_1RandomSet.html#a5af104acdf702d30127915161f5b0790">erase</a>(index);</div>
<div class="line"><a name="l00394"></a><span class="lineno">  394</span>&#160;  }</div>
<div class="line"><a name="l00395"></a><span class="lineno">  395</span>&#160; </div>
<div class="line"><a name="l00396"></a><span class="lineno"><a class="line" href="classqbpp_1_1misc_1_1RandomMinSet.html#a93413c1f3fdf124693c228feb6f564eb">  396</a></span>&#160;  <span class="keywordtype">bool</span> <a class="code" href="classqbpp_1_1misc_1_1RandomMinSet.html#a93413c1f3fdf124693c228feb6f564eb">has</a>(<a class="code" href="namespaceqbpp.html#a5cf436100ede362797d0c0501ea50a5a">vindex_t</a> index)<span class="keyword"> const </span>{ <span class="keywordflow">return</span> <a class="code" href="classqbpp_1_1misc_1_1RandomMinSet.html#ae6e28112e240de351a7efc77d58dc7a6">random_set_</a>.<a class="code" href="classqbpp_1_1misc_1_1RandomSet.html#a9b527e66e301237adde36dfbfba4f866">has</a>(index); }</div>
<div class="line"><a name="l00397"></a><span class="lineno">  397</span>&#160; </div>
<div class="line"><a name="l00398"></a><span class="lineno"><a class="line" href="classqbpp_1_1misc_1_1RandomMinSet.html#a2fa219f7819ed65ad0c01fea3ea97c79">  398</a></span>&#160;  <a class="code" href="namespaceqbpp.html#a5cf436100ede362797d0c0501ea50a5a">vindex_t</a> <a class="code" href="classqbpp_1_1misc_1_1RandomMinSet.html#a2fa219f7819ed65ad0c01fea3ea97c79">select_at_random</a>()<span class="keyword"> const </span>{ <span class="keywordflow">return</span> <a class="code" href="classqbpp_1_1misc_1_1RandomMinSet.html#ae6e28112e240de351a7efc77d58dc7a6">random_set_</a>.<a class="code" href="classqbpp_1_1misc_1_1RandomSet.html#a320528cb75db363c41628fa043cf7f66">select_at_random</a>(); }</div>
<div class="line"><a name="l00399"></a><span class="lineno">  399</span>&#160; </div>
<div class="line"><a name="l00400"></a><span class="lineno"><a class="line" href="classqbpp_1_1misc_1_1RandomMinSet.html#a91425429be94199856c786b678a78a80">  400</a></span>&#160;  <a class="code" href="namespaceqbpp.html#a5cf436100ede362797d0c0501ea50a5a">vindex_t</a> <a class="code" href="classqbpp_1_1misc_1_1RandomMinSet.html#a91425429be94199856c786b678a78a80">get_first</a>()<span class="keyword"> const </span>{ <span class="keywordflow">return</span> <a class="code" href="classqbpp_1_1misc_1_1RandomMinSet.html#a75fca9f5b81af8738a43f16ce849c292">min_set_</a>.get_first(); }</div>
<div class="line"><a name="l00401"></a><span class="lineno">  401</span>&#160; </div>
<div class="line"><a name="l00402"></a><span class="lineno"><a class="line" href="classqbpp_1_1misc_1_1RandomMinSet.html#ae1b1ec0409990038683451d692ae55b1">  402</a></span>&#160;  std::pair&lt;T, vindex_t&gt; <a class="code" href="classqbpp_1_1misc_1_1RandomMinSet.html#ae1b1ec0409990038683451d692ae55b1">get_min</a>()<span class="keyword"> const </span>{ <span class="keywordflow">return</span> <a class="code" href="classqbpp_1_1misc_1_1RandomMinSet.html#a75fca9f5b81af8738a43f16ce849c292">min_set_</a>.get_min(); }</div>
<div class="line"><a name="l00403"></a><span class="lineno">  403</span>&#160; </div>
<div class="line"><a name="l00404"></a><span class="lineno"><a class="line" href="classqbpp_1_1misc_1_1RandomMinSet.html#ac61249a6a95ca3647cfc88d74f778a82">  404</a></span>&#160;  <span class="keywordtype">bool</span> <a class="code" href="classqbpp_1_1misc_1_1RandomMinSet.html#ac61249a6a95ca3647cfc88d74f778a82">empty</a>()<span class="keyword"> const </span>{ <span class="keywordflow">return</span> <a class="code" href="classqbpp_1_1misc_1_1RandomMinSet.html#a75fca9f5b81af8738a43f16ce849c292">min_set_</a>.empty(); }</div>
<div class="line"><a name="l00405"></a><span class="lineno">  405</span>&#160; </div>
<div class="line"><a name="l00406"></a><span class="lineno"><a class="line" href="classqbpp_1_1misc_1_1RandomMinSet.html#a53f8fcc9bb8fe523fd0dda83f3f86b7f">  406</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="classqbpp_1_1misc_1_1RandomMinSet.html#a53f8fcc9bb8fe523fd0dda83f3f86b7f">print</a>(<span class="keyword">const</span> std::string &amp;prefix)<span class="keyword"> const </span>{</div>
<div class="line"><a name="l00407"></a><span class="lineno">  407</span>&#160;    <a class="code" href="classqbpp_1_1misc_1_1RandomMinSet.html#a75fca9f5b81af8738a43f16ce849c292">min_set_</a>.print(prefix + <span class="stringliteral">&quot; MIN:&quot;</span>);</div>
<div class="line"><a name="l00408"></a><span class="lineno">  408</span>&#160;    <a class="code" href="classqbpp_1_1misc_1_1RandomMinSet.html#ae6e28112e240de351a7efc77d58dc7a6">random_set_</a>.<a class="code" href="classqbpp_1_1misc_1_1RandomSet.html#a04fe9fe1367448d458613fa8d1b016f4">print</a>(prefix + <span class="stringliteral">&quot;RANDOM:&quot;</span>);</div>
<div class="line"><a name="l00409"></a><span class="lineno">  409</span>&#160;  }</div>
<div class="line"><a name="l00410"></a><span class="lineno">  410</span>&#160;};</div>
<div class="line"><a name="l00411"></a><span class="lineno">  411</span>&#160; </div>
<div class="line"><a name="l00412"></a><span class="lineno"><a class="line" href="classqbpp_1_1misc_1_1Tabu.html">  412</a></span>&#160;<span class="keyword">class </span><a class="code" href="classqbpp_1_1misc_1_1Tabu.html">Tabu</a> {</div>
<div class="line"><a name="l00413"></a><span class="lineno">  413</span>&#160; </div>
<div class="line"><a name="l00414"></a><span class="lineno"><a class="line" href="classqbpp_1_1misc_1_1Tabu.html#a1ddcd6e27e75e7fc1097b5db0a5865c2">  414</a></span>&#160;  <span class="keyword">const</span> <a class="code" href="namespaceqbpp.html#a5cf436100ede362797d0c0501ea50a5a">vindex_t</a> <a class="code" href="classqbpp_1_1misc_1_1Tabu.html#a1ddcd6e27e75e7fc1097b5db0a5865c2">var_count_</a>;</div>
<div class="line"><a name="l00415"></a><span class="lineno">  415</span>&#160; </div>
<div class="line"><a name="l00416"></a><span class="lineno"><a class="line" href="classqbpp_1_1misc_1_1Tabu.html#a0219883d6003e19f7cf6167f43b639d8">  416</a></span>&#160;  std::unordered_set&lt;vindex_t&gt; <a class="code" href="classqbpp_1_1misc_1_1Tabu.html#a0219883d6003e19f7cf6167f43b639d8">tabu_set_</a>;</div>
<div class="line"><a name="l00417"></a><span class="lineno">  417</span>&#160; </div>
<div class="line"><a name="l00418"></a><span class="lineno"><a class="line" href="classqbpp_1_1misc_1_1Tabu.html#a144ee6fa4a8a40960b22e7aa732c7882">  418</a></span>&#160;  boost::circular_buffer&lt;vindex_t&gt; <a class="code" href="classqbpp_1_1misc_1_1Tabu.html#a144ee6fa4a8a40960b22e7aa732c7882">tabu_list</a>;</div>
<div class="line"><a name="l00419"></a><span class="lineno">  419</span>&#160; </div>
<div class="line"><a name="l00420"></a><span class="lineno">  420</span>&#160; <span class="keyword">public</span>:</div>
<div class="line"><a name="l00421"></a><span class="lineno"><a class="line" href="classqbpp_1_1misc_1_1Tabu.html#afb324d3b3057cdd6872cd29fd8bb92f8">  421</a></span>&#160;  <a class="code" href="classqbpp_1_1misc_1_1Tabu.html#afb324d3b3057cdd6872cd29fd8bb92f8">Tabu</a>(<a class="code" href="namespaceqbpp.html#a5cf436100ede362797d0c0501ea50a5a">vindex_t</a> var_count, <a class="code" href="namespaceqbpp.html#a5cf436100ede362797d0c0501ea50a5a">vindex_t</a> tabu_capacity)</div>
<div class="line"><a name="l00422"></a><span class="lineno">  422</span>&#160;      : <a class="code" href="classqbpp_1_1misc_1_1Tabu.html#a1ddcd6e27e75e7fc1097b5db0a5865c2">var_count_</a>(var_count), <a class="code" href="classqbpp_1_1misc_1_1Tabu.html#a144ee6fa4a8a40960b22e7aa732c7882">tabu_list</a>(tabu_capacity) {}</div>
<div class="line"><a name="l00423"></a><span class="lineno">  423</span>&#160; </div>
<div class="line"><a name="l00424"></a><span class="lineno"><a class="line" href="classqbpp_1_1misc_1_1Tabu.html#abb4a9c6ae969ee3dcafbbe2260fdc921">  424</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="classqbpp_1_1misc_1_1Tabu.html#abb4a9c6ae969ee3dcafbbe2260fdc921">set_capacity</a>(<a class="code" href="namespaceqbpp.html#a5cf436100ede362797d0c0501ea50a5a">vindex_t</a> capacity) {</div>
<div class="line"><a name="l00425"></a><span class="lineno">  425</span>&#160;    <a class="code" href="classqbpp_1_1misc_1_1Tabu.html#a0219883d6003e19f7cf6167f43b639d8">tabu_set_</a>.clear();</div>
<div class="line"><a name="l00426"></a><span class="lineno">  426</span>&#160;    <a class="code" href="classqbpp_1_1misc_1_1Tabu.html#a144ee6fa4a8a40960b22e7aa732c7882">tabu_list</a> = boost::circular_buffer&lt;vindex_t&gt;(capacity);</div>
<div class="line"><a name="l00427"></a><span class="lineno">  427</span>&#160;  }</div>
<div class="line"><a name="l00428"></a><span class="lineno">  428</span>&#160; </div>
<div class="line"><a name="l00429"></a><span class="lineno"><a class="line" href="classqbpp_1_1misc_1_1Tabu.html#a21e6168be034685d15b35252352e6b6a">  429</a></span>&#160;  <a class="code" href="namespaceqbpp.html#a5cf436100ede362797d0c0501ea50a5a">vindex_t</a> <a class="code" href="classqbpp_1_1misc_1_1Tabu.html#a21e6168be034685d15b35252352e6b6a">size</a>()<span class="keyword"> const </span>{ <span class="keywordflow">return</span> <span class="keyword">static_cast&lt;</span><a class="code" href="namespaceqbpp.html#a5cf436100ede362797d0c0501ea50a5a">vindex_t</a><span class="keyword">&gt;</span>(<a class="code" href="classqbpp_1_1misc_1_1Tabu.html#a144ee6fa4a8a40960b22e7aa732c7882">tabu_list</a>.size()); }</div>
<div class="line"><a name="l00430"></a><span class="lineno">  430</span>&#160; </div>
<div class="line"><a name="l00431"></a><span class="lineno"><a class="line" href="classqbpp_1_1misc_1_1Tabu.html#a403f5cc095a3a7398c593168792be96d">  431</a></span>&#160;  <a class="code" href="namespaceqbpp.html#a5cf436100ede362797d0c0501ea50a5a">vindex_t</a> <a class="code" href="classqbpp_1_1misc_1_1Tabu.html#a403f5cc095a3a7398c593168792be96d">get_capacity</a>()<span class="keyword"> const </span>{</div>
<div class="line"><a name="l00432"></a><span class="lineno">  432</span>&#160;    <span class="keywordflow">return</span> <span class="keyword">static_cast&lt;</span><a class="code" href="namespaceqbpp.html#a5cf436100ede362797d0c0501ea50a5a">vindex_t</a><span class="keyword">&gt;</span>(<a class="code" href="classqbpp_1_1misc_1_1Tabu.html#a144ee6fa4a8a40960b22e7aa732c7882">tabu_list</a>.capacity());</div>
<div class="line"><a name="l00433"></a><span class="lineno">  433</span>&#160;  }</div>
<div class="line"><a name="l00434"></a><span class="lineno">  434</span>&#160; </div>
<div class="line"><a name="l00435"></a><span class="lineno"><a class="line" href="classqbpp_1_1misc_1_1Tabu.html#a44a9ecb55678821cc1021e33b4faba72">  435</a></span>&#160;  <span class="keywordtype">bool</span> <a class="code" href="classqbpp_1_1misc_1_1Tabu.html#a44a9ecb55678821cc1021e33b4faba72">has</a>(<a class="code" href="namespaceqbpp.html#a5cf436100ede362797d0c0501ea50a5a">vindex_t</a> index)<span class="keyword"> const </span>{</div>
<div class="line"><a name="l00436"></a><span class="lineno">  436</span>&#160;    <span class="keywordflow">return</span> <a class="code" href="classqbpp_1_1misc_1_1Tabu.html#a0219883d6003e19f7cf6167f43b639d8">tabu_set_</a>.find(index) != <a class="code" href="classqbpp_1_1misc_1_1Tabu.html#a0219883d6003e19f7cf6167f43b639d8">tabu_set_</a>.end();</div>
<div class="line"><a name="l00437"></a><span class="lineno">  437</span>&#160;  }</div>
<div class="line"><a name="l00438"></a><span class="lineno">  438</span>&#160; </div>
<div class="line"><a name="l00439"></a><span class="lineno"><a class="line" href="classqbpp_1_1misc_1_1Tabu.html#a549866fee09f51bb17a90e3a1831bf77">  439</a></span>&#160;  std::optional&lt;vindex_t&gt; <a class="code" href="classqbpp_1_1misc_1_1Tabu.html#a549866fee09f51bb17a90e3a1831bf77">insert</a>(<a class="code" href="namespaceqbpp.html#a5cf436100ede362797d0c0501ea50a5a">vindex_t</a> index, <span class="keywordtype">bool</span> must_be_new) {</div>
<div class="line"><a name="l00440"></a><span class="lineno">  440</span>&#160;    std::optional&lt;vindex_t&gt; removed = std::nullopt;</div>
<div class="line"><a name="l00441"></a><span class="lineno">  441</span>&#160; </div>
<div class="line"><a name="l00442"></a><span class="lineno">  442</span>&#160;    <span class="keywordflow">if</span> (<a class="code" href="classqbpp_1_1misc_1_1Tabu.html#a144ee6fa4a8a40960b22e7aa732c7882">tabu_list</a>.capacity() == 0) {</div>
<div class="line"><a name="l00443"></a><span class="lineno">  443</span>&#160;      <span class="keywordflow">return</span> index;</div>
<div class="line"><a name="l00444"></a><span class="lineno">  444</span>&#160;    }</div>
<div class="line"><a name="l00445"></a><span class="lineno">  445</span>&#160;    <span class="keywordflow">if</span> (<a class="code" href="classqbpp_1_1misc_1_1Tabu.html#a44a9ecb55678821cc1021e33b4faba72">has</a>(index)) {</div>
<div class="line"><a name="l00446"></a><span class="lineno">  446</span>&#160;      <span class="keywordflow">if</span> (must_be_new) {</div>
<div class="line"><a name="l00447"></a><span class="lineno">  447</span>&#160;        <span class="keywordflow">throw</span> std::runtime_error(<a class="code" href="qbpp_8hpp.html#a265e0828fcea8e75fbc9c0be99f7156e">THROW_MESSAGE</a>(<span class="stringliteral">&quot;Tabu: Insert variable (&quot;</span>, index,</div>
<div class="line"><a name="l00448"></a><span class="lineno">  448</span>&#160;                                               <span class="stringliteral">&quot;) already in tabu list&quot;</span>));</div>
<div class="line"><a name="l00449"></a><span class="lineno">  449</span>&#160; </div>
<div class="line"><a name="l00450"></a><span class="lineno">  450</span>&#160;      } <span class="keywordflow">else</span> {</div>
<div class="line"><a name="l00451"></a><span class="lineno">  451</span>&#160;        <span class="keyword">auto</span> it = std::find(<a class="code" href="classqbpp_1_1misc_1_1Tabu.html#a144ee6fa4a8a40960b22e7aa732c7882">tabu_list</a>.begin(), <a class="code" href="classqbpp_1_1misc_1_1Tabu.html#a144ee6fa4a8a40960b22e7aa732c7882">tabu_list</a>.end(), index);</div>
<div class="line"><a name="l00452"></a><span class="lineno">  452</span>&#160;        <a class="code" href="classqbpp_1_1misc_1_1Tabu.html#a144ee6fa4a8a40960b22e7aa732c7882">tabu_list</a>.erase(it);</div>
<div class="line"><a name="l00453"></a><span class="lineno">  453</span>&#160;      }</div>
<div class="line"><a name="l00454"></a><span class="lineno">  454</span>&#160;    }</div>
<div class="line"><a name="l00455"></a><span class="lineno">  455</span>&#160;    <span class="keywordflow">if</span> (<a class="code" href="classqbpp_1_1misc_1_1Tabu.html#a144ee6fa4a8a40960b22e7aa732c7882">tabu_list</a>.full()) {</div>
<div class="line"><a name="l00456"></a><span class="lineno">  456</span>&#160;      removed = <a class="code" href="classqbpp_1_1misc_1_1Tabu.html#a144ee6fa4a8a40960b22e7aa732c7882">tabu_list</a>.front();</div>
<div class="line"><a name="l00457"></a><span class="lineno">  457</span>&#160;      <a class="code" href="classqbpp_1_1misc_1_1Tabu.html#a0219883d6003e19f7cf6167f43b639d8">tabu_set_</a>.erase(*removed);</div>
<div class="line"><a name="l00458"></a><span class="lineno">  458</span>&#160;      <a class="code" href="classqbpp_1_1misc_1_1Tabu.html#a144ee6fa4a8a40960b22e7aa732c7882">tabu_list</a>.pop_front();</div>
<div class="line"><a name="l00459"></a><span class="lineno">  459</span>&#160;    }</div>
<div class="line"><a name="l00460"></a><span class="lineno">  460</span>&#160;    <a class="code" href="classqbpp_1_1misc_1_1Tabu.html#a0219883d6003e19f7cf6167f43b639d8">tabu_set_</a>.insert(index);</div>
<div class="line"><a name="l00461"></a><span class="lineno">  461</span>&#160;    <a class="code" href="classqbpp_1_1misc_1_1Tabu.html#a144ee6fa4a8a40960b22e7aa732c7882">tabu_list</a>.push_back(index);</div>
<div class="line"><a name="l00462"></a><span class="lineno">  462</span>&#160;    <span class="keywordflow">return</span> removed;</div>
<div class="line"><a name="l00463"></a><span class="lineno">  463</span>&#160;  }</div>
<div class="line"><a name="l00464"></a><span class="lineno">  464</span>&#160; </div>
<div class="line"><a name="l00465"></a><span class="lineno"><a class="line" href="classqbpp_1_1misc_1_1Tabu.html#ab4d67548caee2f5d5207fce45e40713a">  465</a></span>&#160;  std::optional&lt;vindex_t&gt; <a class="code" href="classqbpp_1_1misc_1_1Tabu.html#ab4d67548caee2f5d5207fce45e40713a">insert</a>(<a class="code" href="namespaceqbpp.html#a5cf436100ede362797d0c0501ea50a5a">vindex_t</a> index) {</div>
<div class="line"><a name="l00466"></a><span class="lineno">  466</span>&#160;    <span class="keywordflow">return</span> <a class="code" href="classqbpp_1_1misc_1_1Tabu.html#a549866fee09f51bb17a90e3a1831bf77">insert</a>(index, <span class="keyword">false</span>);</div>
<div class="line"><a name="l00467"></a><span class="lineno">  467</span>&#160;  }</div>
<div class="line"><a name="l00468"></a><span class="lineno">  468</span>&#160; </div>
<div class="line"><a name="l00469"></a><span class="lineno"><a class="line" href="classqbpp_1_1misc_1_1Tabu.html#a4dd6094561db3b92395a5bf7df1b5b18">  469</a></span>&#160;  std::optional&lt;vindex_t&gt; <a class="code" href="classqbpp_1_1misc_1_1Tabu.html#a4dd6094561db3b92395a5bf7df1b5b18">insert_new</a>(<a class="code" href="namespaceqbpp.html#a5cf436100ede362797d0c0501ea50a5a">vindex_t</a> index) {</div>
<div class="line"><a name="l00470"></a><span class="lineno">  470</span>&#160;    <span class="keywordflow">return</span> <a class="code" href="classqbpp_1_1misc_1_1Tabu.html#a549866fee09f51bb17a90e3a1831bf77">insert</a>(index, <span class="keyword">true</span>);</div>
<div class="line"><a name="l00471"></a><span class="lineno">  471</span>&#160;  }</div>
<div class="line"><a name="l00472"></a><span class="lineno">  472</span>&#160; </div>
<div class="line"><a name="l00473"></a><span class="lineno"><a class="line" href="classqbpp_1_1misc_1_1Tabu.html#a20a0eb6dd580b49c912404818e6314d8">  473</a></span>&#160;  std::optional&lt;vindex_t&gt; <a class="code" href="classqbpp_1_1misc_1_1Tabu.html#a20a0eb6dd580b49c912404818e6314d8">erase_front</a>() {</div>
<div class="line"><a name="l00474"></a><span class="lineno">  474</span>&#160;    std::optional&lt;vindex_t&gt; removed = std::nullopt;</div>
<div class="line"><a name="l00475"></a><span class="lineno">  475</span>&#160;    <span class="keywordflow">if</span> (<a class="code" href="classqbpp_1_1misc_1_1Tabu.html#a21e6168be034685d15b35252352e6b6a">size</a>() == 0) {</div>
<div class="line"><a name="l00476"></a><span class="lineno">  476</span>&#160;      <span class="keywordflow">return</span> removed;</div>
<div class="line"><a name="l00477"></a><span class="lineno">  477</span>&#160;    }</div>
<div class="line"><a name="l00478"></a><span class="lineno">  478</span>&#160;    removed = <a class="code" href="classqbpp_1_1misc_1_1Tabu.html#a144ee6fa4a8a40960b22e7aa732c7882">tabu_list</a>.front();</div>
<div class="line"><a name="l00479"></a><span class="lineno">  479</span>&#160;    <a class="code" href="classqbpp_1_1misc_1_1Tabu.html#a0219883d6003e19f7cf6167f43b639d8">tabu_set_</a>.erase(*removed);</div>
<div class="line"><a name="l00480"></a><span class="lineno">  480</span>&#160;    <a class="code" href="classqbpp_1_1misc_1_1Tabu.html#a144ee6fa4a8a40960b22e7aa732c7882">tabu_list</a>.pop_front();</div>
<div class="line"><a name="l00481"></a><span class="lineno">  481</span>&#160;    <span class="keywordflow">return</span> removed;</div>
<div class="line"><a name="l00482"></a><span class="lineno">  482</span>&#160;  }</div>
<div class="line"><a name="l00483"></a><span class="lineno">  483</span>&#160; </div>
<div class="line"><a name="l00484"></a><span class="lineno"><a class="line" href="classqbpp_1_1misc_1_1Tabu.html#ac79f02e24591065f8224dc1aa5355e20">  484</a></span>&#160;  <a class="code" href="namespaceqbpp.html#a5cf436100ede362797d0c0501ea50a5a">vindex_t</a> <a class="code" href="classqbpp_1_1misc_1_1Tabu.html#ac79f02e24591065f8224dc1aa5355e20">non_tabu_random</a>() {</div>
<div class="line"><a name="l00485"></a><span class="lineno">  485</span>&#160;    <a class="code" href="namespaceqbpp.html#a5cf436100ede362797d0c0501ea50a5a">vindex_t</a> index;</div>
<div class="line"><a name="l00486"></a><span class="lineno">  486</span>&#160;    <span class="keywordflow">do</span> {</div>
<div class="line"><a name="l00487"></a><span class="lineno">  487</span>&#160;      index = <a class="code" href="classqbpp_1_1misc_1_1RandomEngine.html#ad836550b67c1c3784cf95a23d43bcd96">qbpp::misc::RandomEngine::gen</a>(<a class="code" href="classqbpp_1_1misc_1_1Tabu.html#a1ddcd6e27e75e7fc1097b5db0a5865c2">var_count_</a>);</div>
<div class="line"><a name="l00488"></a><span class="lineno">  488</span>&#160;    } <span class="keywordflow">while</span> (<a class="code" href="classqbpp_1_1misc_1_1Tabu.html#a44a9ecb55678821cc1021e33b4faba72">has</a>(index));</div>
<div class="line"><a name="l00489"></a><span class="lineno">  489</span>&#160;    <span class="keywordflow">return</span> index;</div>
<div class="line"><a name="l00490"></a><span class="lineno">  490</span>&#160;  }</div>
<div class="line"><a name="l00491"></a><span class="lineno">  491</span>&#160; </div>
<div class="line"><a name="l00492"></a><span class="lineno"><a class="line" href="classqbpp_1_1misc_1_1Tabu.html#aad1ae55dedb6843646e64cf2bee75971">  492</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="classqbpp_1_1misc_1_1Tabu.html#aad1ae55dedb6843646e64cf2bee75971">print</a>() {</div>
<div class="line"><a name="l00493"></a><span class="lineno">  493</span>&#160;    std::cout &lt;&lt; <span class="stringliteral">&quot;Tabu list (capacity = &quot;</span> &lt;&lt; <a class="code" href="classqbpp_1_1misc_1_1Tabu.html#a144ee6fa4a8a40960b22e7aa732c7882">tabu_list</a>.capacity() &lt;&lt; <span class="stringliteral">&quot;):&quot;</span>;</div>
<div class="line"><a name="l00494"></a><span class="lineno">  494</span>&#160;    <span class="keywordflow">for</span> (<span class="keyword">const</span> <span class="keyword">auto</span> &amp;i : <a class="code" href="classqbpp_1_1misc_1_1Tabu.html#a144ee6fa4a8a40960b22e7aa732c7882">tabu_list</a>) {</div>
<div class="line"><a name="l00495"></a><span class="lineno">  495</span>&#160;      std::cout &lt;&lt; <span class="stringliteral">&quot; &quot;</span> &lt;&lt; i;</div>
<div class="line"><a name="l00496"></a><span class="lineno">  496</span>&#160;    }</div>
<div class="line"><a name="l00497"></a><span class="lineno">  497</span>&#160;    std::cout &lt;&lt; std::endl;</div>
<div class="line"><a name="l00498"></a><span class="lineno">  498</span>&#160;  }</div>
<div class="line"><a name="l00499"></a><span class="lineno">  499</span>&#160;};</div>
<div class="line"><a name="l00500"></a><span class="lineno">  500</span>&#160; </div>
<div class="line"><a name="l00501"></a><span class="lineno">  501</span>&#160;}  </div>
<div class="line"><a name="l00502"></a><span class="lineno">  502</span>&#160;}  </div>
<div class="line"><a name="l00503"></a><span class="lineno">  503</span>&#160; </div>
<div class="line"><a name="l00504"></a><span class="lineno">  504</span>&#160;<span class="preprocessor">#endif  </span></div>
</div><!-- fragment --></div><!-- contents -->
<div class="ttc" id="aclassqbpp_1_1misc_1_1Tabu_html_a144ee6fa4a8a40960b22e7aa732c7882"><div class="ttname"><a href="classqbpp_1_1misc_1_1Tabu.html#a144ee6fa4a8a40960b22e7aa732c7882">qbpp::misc::Tabu::tabu_list</a></div><div class="ttdeci">boost::circular_buffer&lt; vindex_t &gt; tabu_list</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__misc_8hpp_source.html#l00418">qbpp_misc.hpp:418</a></div></div>
<div class="ttc" id="aclassqbpp_1_1misc_1_1MinSet_html_a5ba92c07c6b0da89324815c9a344bfe5"><div class="ttname"><a href="classqbpp_1_1misc_1_1MinSet.html#a5ba92c07c6b0da89324815c9a344bfe5">qbpp::misc::MinSet::MinSet</a></div><div class="ttdeci">MinSet()=default</div></div>
<div class="ttc" id="aclassqbpp_1_1misc_1_1Tabu_html_aad1ae55dedb6843646e64cf2bee75971"><div class="ttname"><a href="classqbpp_1_1misc_1_1Tabu.html#aad1ae55dedb6843646e64cf2bee75971">qbpp::misc::Tabu::print</a></div><div class="ttdeci">void print()</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__misc_8hpp_source.html#l00492">qbpp_misc.hpp:492</a></div></div>
<div class="ttc" id="aclassqbpp_1_1misc_1_1RandomGenerator_html_a776cc0696fd80c04071edc5ebad550ca"><div class="ttname"><a href="classqbpp_1_1misc_1_1RandomGenerator.html#a776cc0696fd80c04071edc5ebad550ca">qbpp::misc::RandomGenerator::gen_double</a></div><div class="ttdeci">static double gen_double()</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__misc_8hpp_source.html#l00132">qbpp_misc.hpp:132</a></div></div>
<div class="ttc" id="aclassqbpp_1_1misc_1_1MinHeap_html_a16b2fe5b4cf386da0e28fa16e26fbef7"><div class="ttname"><a href="classqbpp_1_1misc_1_1MinHeap.html#a16b2fe5b4cf386da0e28fa16e26fbef7">qbpp::misc::MinHeap::has</a></div><div class="ttdeci">bool has(vindex_t index) const</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__misc_8hpp_source.html#l00329">qbpp_misc.hpp:329</a></div></div>
<div class="ttc" id="aclassqbpp_1_1misc_1_1RandomEngine_html_ae181ae930d1f490d6a57dc7ee1058a30"><div class="ttname"><a href="classqbpp_1_1misc_1_1RandomEngine.html#ae181ae930d1f490d6a57dc7ee1058a30">qbpp::misc::RandomEngine::gen64</a></div><div class="ttdeci">static uint64_t gen64()</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__misc_8hpp_source.html#l00087">qbpp_misc.hpp:87</a></div></div>
<div class="ttc" id="aclassqbpp_1_1misc_1_1Tabu_html_a403f5cc095a3a7398c593168792be96d"><div class="ttname"><a href="classqbpp_1_1misc_1_1Tabu.html#a403f5cc095a3a7398c593168792be96d">qbpp::misc::Tabu::get_capacity</a></div><div class="ttdeci">vindex_t get_capacity() const</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__misc_8hpp_source.html#l00431">qbpp_misc.hpp:431</a></div></div>
<div class="ttc" id="anamespaceqbpp_html_aebbaf7087f1c6180b3b2d9821cddc695"><div class="ttname"><a href="namespaceqbpp.html#aebbaf7087f1c6180b3b2d9821cddc695">qbpp::cpp_int</a></div><div class="ttdeci">boost::multiprecision::cpp_int cpp_int</div><div class="ttdef"><b>Definition:</b> <a href="qbpp_8hpp_source.html#l00119">qbpp.hpp:119</a></div></div>
<div class="ttc" id="aclassqbpp_1_1misc_1_1RandomMinSet_html"><div class="ttname"><a href="classqbpp_1_1misc_1_1RandomMinSet.html">qbpp::misc::RandomMinSet</a></div><div class="ttdef"><b>Definition:</b> <a href="qbpp__misc_8hpp_source.html#l00375">qbpp_misc.hpp:375</a></div></div>
<div class="ttc" id="aclassqbpp_1_1misc_1_1RandomPermutation_html_a6524d54e76a9b7fdcd3f36e404cf909d"><div class="ttname"><a href="classqbpp_1_1misc_1_1RandomPermutation.html#a6524d54e76a9b7fdcd3f36e404cf909d">qbpp::misc::RandomPermutation::get</a></div><div class="ttdeci">uint32_t get()</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__misc_8hpp_source.html#l00152">qbpp_misc.hpp:152</a></div></div>
<div class="ttc" id="aclassqbpp_1_1misc_1_1MinHeap_html_a8b67c7050c9ff2553a95b7ce1f9f8a83"><div class="ttname"><a href="classqbpp_1_1misc_1_1MinHeap.html#a8b67c7050c9ff2553a95b7ce1f9f8a83">qbpp::misc::MinHeap::index_</a></div><div class="ttdeci">std::vector&lt; vindex_t &gt; index_</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__misc_8hpp_source.html#l00277">qbpp_misc.hpp:277</a></div></div>
<div class="ttc" id="aclassqbpp_1_1misc_1_1MinHeap_html_a0347d3112d719360c4348c7f688a369b"><div class="ttname"><a href="classqbpp_1_1misc_1_1MinHeap.html#a0347d3112d719360c4348c7f688a369b">qbpp::misc::MinHeap::MinHeap</a></div><div class="ttdeci">MinHeap(size_t size)</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__misc_8hpp_source.html#l00324">qbpp_misc.hpp:324</a></div></div>
<div class="ttc" id="aclassqbpp_1_1misc_1_1RandomEngine_html_a562e1b7897692b5591f9c61e89dcc404"><div class="ttname"><a href="classqbpp_1_1misc_1_1RandomEngine.html#a562e1b7897692b5591f9c61e89dcc404">qbpp::misc::RandomEngine::gen_double</a></div><div class="ttdeci">static double gen_double()</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__misc_8hpp_source.html#l00094">qbpp_misc.hpp:94</a></div></div>
<div class="ttc" id="aclassqbpp_1_1misc_1_1RandomGenerator_html_a6ab877662e0f56dad77f70ded6528ded"><div class="ttname"><a href="classqbpp_1_1misc_1_1RandomGenerator.html#a6ab877662e0f56dad77f70ded6528ded">qbpp::misc::RandomGenerator::RandomGenerator</a></div><div class="ttdeci">RandomGenerator()</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__misc_8hpp_source.html#l00100">qbpp_misc.hpp:100</a></div></div>
<div class="ttc" id="aclassqbpp_1_1misc_1_1MinHeap_html_ae61d02fae1382293fb465d2c52477dd0"><div class="ttname"><a href="classqbpp_1_1misc_1_1MinHeap.html#ae61d02fae1382293fb465d2c52477dd0">qbpp::misc::MinHeap::heap_size</a></div><div class="ttdeci">vindex_t heap_size() const</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__misc_8hpp_source.html#l00326">qbpp_misc.hpp:326</a></div></div>
<div class="ttc" id="anamespaceqbpp_html_a9c324f641779d1a7a3c7723f28d8cff4"><div class="ttname"><a href="namespaceqbpp.html#a9c324f641779d1a7a3c7723f28d8cff4">qbpp::energy_t</a></div><div class="ttdeci">ENERGY_TYPE energy_t</div><div class="ttdef"><b>Definition:</b> <a href="qbpp_8hpp_source.html#l00137">qbpp.hpp:137</a></div></div>
<div class="ttc" id="aclassqbpp_1_1misc_1_1MinSet_html_a8bfa3e020f7b6d2360b92f0982db6be4"><div class="ttname"><a href="classqbpp_1_1misc_1_1MinSet.html#a8bfa3e020f7b6d2360b92f0982db6be4">qbpp::misc::MinSet::print</a></div><div class="ttdeci">void print(const std::string &amp;prefix) const</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__misc_8hpp_source.html#l00260">qbpp_misc.hpp:260</a></div></div>
<div class="ttc" id="aclassqbpp_1_1misc_1_1Tabu_html_ab4d67548caee2f5d5207fce45e40713a"><div class="ttname"><a href="classqbpp_1_1misc_1_1Tabu.html#ab4d67548caee2f5d5207fce45e40713a">qbpp::misc::Tabu::insert</a></div><div class="ttdeci">std::optional&lt; vindex_t &gt; insert(vindex_t index)</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__misc_8hpp_source.html#l00465">qbpp_misc.hpp:465</a></div></div>
<div class="ttc" id="aclassqbpp_1_1misc_1_1RandomMinSet_html_acd4a41b2d92a9a5376f39ef5a56a240b"><div class="ttname"><a href="classqbpp_1_1misc_1_1RandomMinSet.html#acd4a41b2d92a9a5376f39ef5a56a240b">qbpp::misc::RandomMinSet::erase</a></div><div class="ttdeci">void erase(vindex_t index, T delta)</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__misc_8hpp_source.html#l00391">qbpp_misc.hpp:391</a></div></div>
<div class="ttc" id="aclassqbpp_1_1misc_1_1RandomEngine_html_ad836550b67c1c3784cf95a23d43bcd96"><div class="ttname"><a href="classqbpp_1_1misc_1_1RandomEngine.html#ad836550b67c1c3784cf95a23d43bcd96">qbpp::misc::RandomEngine::gen</a></div><div class="ttdeci">static T gen(T n)</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__misc_8hpp_source.html#l00090">qbpp_misc.hpp:90</a></div></div>
<div class="ttc" id="aclassqbpp_1_1misc_1_1RandomPermutation_html"><div class="ttname"><a href="classqbpp_1_1misc_1_1RandomPermutation.html">qbpp::misc::RandomPermutation</a></div><div class="ttdef"><b>Definition:</b> <a href="qbpp__misc_8hpp_source.html#l00140">qbpp_misc.hpp:140</a></div></div>
<div class="ttc" id="aclassqbpp_1_1misc_1_1MinHeap_html_ad5e42b4bd3f622c159a7863a1b7c011f"><div class="ttname"><a href="classqbpp_1_1misc_1_1MinHeap.html#ad5e42b4bd3f622c159a7863a1b7c011f">qbpp::misc::MinHeap::get_first</a></div><div class="ttdeci">vindex_t get_first() const</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__misc_8hpp_source.html#l00327">qbpp_misc.hpp:327</a></div></div>
<div class="ttc" id="aclassqbpp_1_1misc_1_1RandomGenerator_html_a513a93d293d9fa08a7fee2bba9cb73ff"><div class="ttname"><a href="classqbpp_1_1misc_1_1RandomGenerator.html#a513a93d293d9fa08a7fee2bba9cb73ff">qbpp::misc::RandomGenerator::rd_seed</a></div><div class="ttdeci">static void rd_seed()</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__misc_8hpp_source.html#l00114">qbpp_misc.hpp:114</a></div></div>
<div class="ttc" id="aclassqbpp_1_1misc_1_1MinHeap_html_a79b0070d23b88929420292554d186236"><div class="ttname"><a href="classqbpp_1_1misc_1_1MinHeap.html#a79b0070d23b88929420292554d186236">qbpp::misc::MinHeap::emplace_back</a></div><div class="ttdeci">void emplace_back(vindex_t index, energy_t delta)</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__misc_8hpp_source.html#l00279">qbpp_misc.hpp:279</a></div></div>
<div class="ttc" id="aclassqbpp_1_1misc_1_1RandomMinSet_html_a75fca9f5b81af8738a43f16ce849c292"><div class="ttname"><a href="classqbpp_1_1misc_1_1RandomMinSet.html#a75fca9f5b81af8738a43f16ce849c292">qbpp::misc::RandomMinSet::min_set_</a></div><div class="ttdeci">MinSet&lt; T &gt; min_set_</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__misc_8hpp_source.html#l00376">qbpp_misc.hpp:376</a></div></div>
<div class="ttc" id="aclassqbpp_1_1misc_1_1RandomMinSet_html_ad825296a0c90b57a2594b85d9c50c827"><div class="ttname"><a href="classqbpp_1_1misc_1_1RandomMinSet.html#ad825296a0c90b57a2594b85d9c50c827">qbpp::misc::RandomMinSet::insert</a></div><div class="ttdeci">void insert(vindex_t index, T delta)</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__misc_8hpp_source.html#l00386">qbpp_misc.hpp:386</a></div></div>
<div class="ttc" id="aclassqbpp_1_1misc_1_1RandomGenerator_html_aab365c6e914e457221f8fd9bcee8b63e"><div class="ttname"><a href="classqbpp_1_1misc_1_1RandomGenerator.html#aab365c6e914e457221f8fd9bcee8b63e">qbpp::misc::RandomGenerator::gen</a></div><div class="ttdeci">static qbpp::cpp_int gen(const qbpp::cpp_int &amp;n)</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__misc_8hpp_source.html#l00124">qbpp_misc.hpp:124</a></div></div>
<div class="ttc" id="aclassqbpp_1_1misc_1_1MinHeap_html_aa209280cf2cfd0dd5ee04131bc96a582"><div class="ttname"><a href="classqbpp_1_1misc_1_1MinHeap.html#aa209280cf2cfd0dd5ee04131bc96a582">qbpp::misc::MinHeap::erase</a></div><div class="ttdeci">void erase(vindex_t index)</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__misc_8hpp_source.html#l00348">qbpp_misc.hpp:348</a></div></div>
<div class="ttc" id="aclassqbpp_1_1misc_1_1MinHeap_html_ae0d409ca2b14bae78db373f1eecbd99c"><div class="ttname"><a href="classqbpp_1_1misc_1_1MinHeap.html#ae0d409ca2b14bae78db373f1eecbd99c">qbpp::misc::MinHeap::print</a></div><div class="ttdeci">void print(const std::string &amp;prefix) const</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__misc_8hpp_source.html#l00365">qbpp_misc.hpp:365</a></div></div>
<div class="ttc" id="aclassqbpp_1_1misc_1_1MinSet_html_a86ef019499ebf9b72fe26fb7abcad094"><div class="ttname"><a href="classqbpp_1_1misc_1_1MinSet.html#a86ef019499ebf9b72fe26fb7abcad094">qbpp::misc::MinSet::get_min</a></div><div class="ttdeci">std::pair&lt; T, vindex_t &gt; get_min() const</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__misc_8hpp_source.html#l00256">qbpp_misc.hpp:256</a></div></div>
<div class="ttc" id="aclassqbpp_1_1misc_1_1Tabu_html_ac79f02e24591065f8224dc1aa5355e20"><div class="ttname"><a href="classqbpp_1_1misc_1_1Tabu.html#ac79f02e24591065f8224dc1aa5355e20">qbpp::misc::Tabu::non_tabu_random</a></div><div class="ttdeci">vindex_t non_tabu_random()</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__misc_8hpp_source.html#l00484">qbpp_misc.hpp:484</a></div></div>
<div class="ttc" id="aclassqbpp_1_1misc_1_1RandomGenerator_html_a1aae9a887ef81804e5405fd4ae6a7bda"><div class="ttname"><a href="classqbpp_1_1misc_1_1RandomGenerator.html#a1aae9a887ef81804e5405fd4ae6a7bda">qbpp::misc::RandomGenerator::get_mt</a></div><div class="ttdeci">static std::mt19937_64 &amp; get_mt()</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__misc_8hpp_source.html#l00137">qbpp_misc.hpp:137</a></div></div>
<div class="ttc" id="aclassqbpp_1_1misc_1_1RandomSet_html_a78525e8ab3790dc530a04db4a3e2665e"><div class="ttname"><a href="classqbpp_1_1misc_1_1RandomSet.html#a78525e8ab3790dc530a04db4a3e2665e">qbpp::misc::RandomSet::RandomSet</a></div><div class="ttdeci">RandomSet(vindex_t size)</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__misc_8hpp_source.html#l00175">qbpp_misc.hpp:175</a></div></div>
<div class="ttc" id="aclassqbpp_1_1misc_1_1RandomSet_html_a320528cb75db363c41628fa043cf7f66"><div class="ttname"><a href="classqbpp_1_1misc_1_1RandomSet.html#a320528cb75db363c41628fa043cf7f66">qbpp::misc::RandomSet::select_at_random</a></div><div class="ttdeci">vindex_t select_at_random() const</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__misc_8hpp_source.html#l00211">qbpp_misc.hpp:211</a></div></div>
<div class="ttc" id="aclassqbpp_1_1misc_1_1RandomGenerator_html_a95308c2300aeef9a881ba97786367977"><div class="ttname"><a href="classqbpp_1_1misc_1_1RandomGenerator.html#a95308c2300aeef9a881ba97786367977">qbpp::misc::RandomGenerator::gen</a></div><div class="ttdeci">static uint64_t gen()</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__misc_8hpp_source.html#l00116">qbpp_misc.hpp:116</a></div></div>
<div class="ttc" id="aclassqbpp_1_1misc_1_1RandomEngine_html"><div class="ttname"><a href="classqbpp_1_1misc_1_1RandomEngine.html">qbpp::misc::RandomEngine</a></div><div class="ttdef"><b>Definition:</b> <a href="qbpp__misc_8hpp_source.html#l00037">qbpp_misc.hpp:37</a></div></div>
<div class="ttc" id="aclassqbpp_1_1misc_1_1RandomPermutation_html_accf8ebcfcad81a695023782a4226e43f"><div class="ttname"><a href="classqbpp_1_1misc_1_1RandomPermutation.html#accf8ebcfcad81a695023782a4226e43f">qbpp::misc::RandomPermutation::RandomPermutation</a></div><div class="ttdeci">RandomPermutation(uint32_t size)</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__misc_8hpp_source.html#l00148">qbpp_misc.hpp:148</a></div></div>
<div class="ttc" id="astructqbpp_1_1misc_1_1PcloseDeleter_html_aef9adb5c2fd60a14331d18ee52333d50"><div class="ttname"><a href="structqbpp_1_1misc_1_1PcloseDeleter.html#aef9adb5c2fd60a14331d18ee52333d50">qbpp::misc::PcloseDeleter::operator()</a></div><div class="ttdeci">void operator()(FILE *file) const</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__misc_8hpp_source.html#l00163">qbpp_misc.hpp:163</a></div></div>
<div class="ttc" id="aclassqbpp_1_1misc_1_1RandomSet_html_a04fe9fe1367448d458613fa8d1b016f4"><div class="ttname"><a href="classqbpp_1_1misc_1_1RandomSet.html#a04fe9fe1367448d458613fa8d1b016f4">qbpp::misc::RandomSet::print</a></div><div class="ttdeci">void print(const std::string &amp;prefix=&quot;&quot;) const</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__misc_8hpp_source.html#l00215">qbpp_misc.hpp:215</a></div></div>
<div class="ttc" id="aclassqbpp_1_1misc_1_1MinSet_html_a63576a251e5c467e6f4b1eae8a1b1f03"><div class="ttname"><a href="classqbpp_1_1misc_1_1MinSet.html#a63576a251e5c467e6f4b1eae8a1b1f03">qbpp::misc::MinSet::insert</a></div><div class="ttdeci">void insert(vindex_t index, T delta)</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__misc_8hpp_source.html#l00236">qbpp_misc.hpp:236</a></div></div>
<div class="ttc" id="aclassqbpp_1_1misc_1_1RandomEngine_html_af39f35312c1e432c923035679333f455"><div class="ttname"><a href="classqbpp_1_1misc_1_1RandomEngine.html#af39f35312c1e432c923035679333f455">qbpp::misc::RandomEngine::gen_impl</a></div><div class="ttdeci">T gen_impl(T n)</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__misc_8hpp_source.html#l00050">qbpp_misc.hpp:50</a></div></div>
<div class="ttc" id="aclassqbpp_1_1misc_1_1RandomGenerator_html_a8a677c3af4a06d3dc325ac69dd6ade73"><div class="ttname"><a href="classqbpp_1_1misc_1_1RandomGenerator.html#a8a677c3af4a06d3dc325ac69dd6ade73">qbpp::misc::RandomGenerator::gen</a></div><div class="ttdeci">static std::enable_if&lt; std::is_integral&lt; T &gt;::value, T &gt;::type gen(T n)</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__misc_8hpp_source.html#l00119">qbpp_misc.hpp:119</a></div></div>
<div class="ttc" id="aclassqbpp_1_1misc_1_1MinHeap_html_acba907f46558aefc996cc8aafa381249"><div class="ttname"><a href="classqbpp_1_1misc_1_1MinHeap.html#acba907f46558aefc996cc8aafa381249">qbpp::misc::MinHeap::select_at_random</a></div><div class="ttdeci">vindex_t select_at_random() const</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__misc_8hpp_source.html#l00333">qbpp_misc.hpp:333</a></div></div>
<div class="ttc" id="aclassqbpp_1_1misc_1_1Tabu_html_a21e6168be034685d15b35252352e6b6a"><div class="ttname"><a href="classqbpp_1_1misc_1_1Tabu.html#a21e6168be034685d15b35252352e6b6a">qbpp::misc::Tabu::size</a></div><div class="ttdeci">vindex_t size() const</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__misc_8hpp_source.html#l00429">qbpp_misc.hpp:429</a></div></div>
<div class="ttc" id="aclassqbpp_1_1misc_1_1Tabu_html_a1ddcd6e27e75e7fc1097b5db0a5865c2"><div class="ttname"><a href="classqbpp_1_1misc_1_1Tabu.html#a1ddcd6e27e75e7fc1097b5db0a5865c2">qbpp::misc::Tabu::var_count_</a></div><div class="ttdeci">const vindex_t var_count_</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__misc_8hpp_source.html#l00414">qbpp_misc.hpp:414</a></div></div>
<div class="ttc" id="aclassqbpp_1_1misc_1_1RandomSet_html_afeeb15f5fc634c22e9326aecbff71d7c"><div class="ttname"><a href="classqbpp_1_1misc_1_1RandomSet.html#afeeb15f5fc634c22e9326aecbff71d7c">qbpp::misc::RandomSet::variables_</a></div><div class="ttdeci">std::vector&lt; vindex_t &gt; variables_</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__misc_8hpp_source.html#l00172">qbpp_misc.hpp:172</a></div></div>
<div class="ttc" id="aclassqbpp_1_1misc_1_1Tabu_html_a4dd6094561db3b92395a5bf7df1b5b18"><div class="ttname"><a href="classqbpp_1_1misc_1_1Tabu.html#a4dd6094561db3b92395a5bf7df1b5b18">qbpp::misc::Tabu::insert_new</a></div><div class="ttdeci">std::optional&lt; vindex_t &gt; insert_new(vindex_t index)</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__misc_8hpp_source.html#l00469">qbpp_misc.hpp:469</a></div></div>
<div class="ttc" id="aclassqbpp_1_1misc_1_1RandomSet_html_a5af104acdf702d30127915161f5b0790"><div class="ttname"><a href="classqbpp_1_1misc_1_1RandomSet.html#a5af104acdf702d30127915161f5b0790">qbpp::misc::RandomSet::erase</a></div><div class="ttdeci">void erase(vindex_t index)</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__misc_8hpp_source.html#l00199">qbpp_misc.hpp:199</a></div></div>
<div class="ttc" id="aclassqbpp_1_1misc_1_1MinHeap_html_a7df936d574ef6d854b01febdf52a4492"><div class="ttname"><a href="classqbpp_1_1misc_1_1MinHeap.html#a7df936d574ef6d854b01febdf52a4492">qbpp::misc::MinHeap::bubble_down</a></div><div class="ttdeci">void bubble_down(vindex_t i)</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__misc_8hpp_source.html#l00302">qbpp_misc.hpp:302</a></div></div>
<div class="ttc" id="aclassqbpp_1_1misc_1_1RandomGenerator_html_a904d0331b4fff9392c7d3e3d01b398b8"><div class="ttname"><a href="classqbpp_1_1misc_1_1RandomGenerator.html#a904d0331b4fff9392c7d3e3d01b398b8">qbpp::misc::RandomGenerator::mt</a></div><div class="ttdeci">std::mt19937_64 mt</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__misc_8hpp_source.html#l00098">qbpp_misc.hpp:98</a></div></div>
<div class="ttc" id="aqbpp_8hpp_html"><div class="ttname"><a href="qbpp_8hpp.html">qbpp.hpp</a></div><div class="ttdoc">QUBO++, a C++ library for generating expressions for binary and spin variables.</div></div>
<div class="ttc" id="aclassqbpp_1_1misc_1_1RandomEngine_html_ae8c8cbd9cba067b7d5a50a82081b6afc"><div class="ttname"><a href="classqbpp_1_1misc_1_1RandomEngine.html#ae8c8cbd9cba067b7d5a50a82081b6afc">qbpp::misc::RandomEngine::gen_double_impl</a></div><div class="ttdeci">double gen_double_impl()</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__misc_8hpp_source.html#l00069">qbpp_misc.hpp:69</a></div></div>
<div class="ttc" id="aclassqbpp_1_1misc_1_1RandomGenerator_html_a6b19f6bde3a3b6ff087a908d11ece3c1"><div class="ttname"><a href="classqbpp_1_1misc_1_1RandomGenerator.html#a6b19f6bde3a3b6ff087a908d11ece3c1">qbpp::misc::RandomGenerator::get_instance</a></div><div class="ttdeci">static RandomGenerator &amp; get_instance()</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__misc_8hpp_source.html#l00106">qbpp_misc.hpp:106</a></div></div>
<div class="ttc" id="aclassqbpp_1_1misc_1_1RandomEngine_html_a9c5c1b9de3f73bd00a4444965021e39c"><div class="ttname"><a href="classqbpp_1_1misc_1_1RandomEngine.html#a9c5c1b9de3f73bd00a4444965021e39c">qbpp::misc::RandomEngine::gen64_impl</a></div><div class="ttdeci">uint64_t gen64_impl()</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__misc_8hpp_source.html#l00047">qbpp_misc.hpp:47</a></div></div>
<div class="ttc" id="aclassqbpp_1_1misc_1_1RandomMinSet_html_ae1b1ec0409990038683451d692ae55b1"><div class="ttname"><a href="classqbpp_1_1misc_1_1RandomMinSet.html#ae1b1ec0409990038683451d692ae55b1">qbpp::misc::RandomMinSet::get_min</a></div><div class="ttdeci">std::pair&lt; T, vindex_t &gt; get_min() const</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__misc_8hpp_source.html#l00402">qbpp_misc.hpp:402</a></div></div>
<div class="ttc" id="aclassqbpp_1_1misc_1_1RandomMinSet_html_a2fa219f7819ed65ad0c01fea3ea97c79"><div class="ttname"><a href="classqbpp_1_1misc_1_1RandomMinSet.html#a2fa219f7819ed65ad0c01fea3ea97c79">qbpp::misc::RandomMinSet::select_at_random</a></div><div class="ttdeci">vindex_t select_at_random() const</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__misc_8hpp_source.html#l00398">qbpp_misc.hpp:398</a></div></div>
<div class="ttc" id="aclassqbpp_1_1misc_1_1RandomGenerator_html"><div class="ttname"><a href="classqbpp_1_1misc_1_1RandomGenerator.html">qbpp::misc::RandomGenerator</a></div><div class="ttdef"><b>Definition:</b> <a href="qbpp__misc_8hpp_source.html#l00097">qbpp_misc.hpp:97</a></div></div>
<div class="ttc" id="aclassqbpp_1_1misc_1_1RandomSet_html_a2562592074e847095092c21b9feb6fd4"><div class="ttname"><a href="classqbpp_1_1misc_1_1RandomSet.html#a2562592074e847095092c21b9feb6fd4">qbpp::misc::RandomSet::position_</a></div><div class="ttdeci">std::vector&lt; vindex_t &gt; position_</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__misc_8hpp_source.html#l00170">qbpp_misc.hpp:170</a></div></div>
<div class="ttc" id="aclassqbpp_1_1misc_1_1RandomSet_html_a64eed5f84e5a5b9bfdf2aed95dc10da0"><div class="ttname"><a href="classqbpp_1_1misc_1_1RandomSet.html#a64eed5f84e5a5b9bfdf2aed95dc10da0">qbpp::misc::RandomSet::swap</a></div><div class="ttdeci">void swap(vindex_t pos1, vindex_t pos2)</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__misc_8hpp_source.html#l00192">qbpp_misc.hpp:192</a></div></div>
<div class="ttc" id="anamespaceqbpp_html_ac0b50505a1738411ca7c96487b296234"><div class="ttname"><a href="namespaceqbpp.html#ac0b50505a1738411ca7c96487b296234">qbpp::vindex_limit</a></div><div class="ttdeci">const vindex_t vindex_limit</div><div class="ttdef"><b>Definition:</b> <a href="qbpp_8hpp_source.html#l00131">qbpp.hpp:131</a></div></div>
<div class="ttc" id="aclassqbpp_1_1misc_1_1MinSet_html_ad9f980fba77f1f8d017354953a5cb434"><div class="ttname"><a href="classqbpp_1_1misc_1_1MinSet.html#ad9f980fba77f1f8d017354953a5cb434">qbpp::misc::MinSet::var_count</a></div><div class="ttdeci">vindex_t var_count() const</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__misc_8hpp_source.html#l00234">qbpp_misc.hpp:234</a></div></div>
<div class="ttc" id="aclassqbpp_1_1misc_1_1MinSet_html_a0a9fee085659a2b0421af613ec5f4433"><div class="ttname"><a href="classqbpp_1_1misc_1_1MinSet.html#a0a9fee085659a2b0421af613ec5f4433">qbpp::misc::MinSet::set_</a></div><div class="ttdeci">ValIndexMap set_</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__misc_8hpp_source.html#l00229">qbpp_misc.hpp:229</a></div></div>
<div class="ttc" id="aclassqbpp_1_1misc_1_1Tabu_html"><div class="ttname"><a href="classqbpp_1_1misc_1_1Tabu.html">qbpp::misc::Tabu</a></div><div class="ttdef"><b>Definition:</b> <a href="qbpp__misc_8hpp_source.html#l00412">qbpp_misc.hpp:412</a></div></div>
<div class="ttc" id="aclassqbpp_1_1misc_1_1RandomSet_html"><div class="ttname"><a href="classqbpp_1_1misc_1_1RandomSet.html">qbpp::misc::RandomSet</a></div><div class="ttdef"><b>Definition:</b> <a href="qbpp__misc_8hpp_source.html#l00169">qbpp_misc.hpp:169</a></div></div>
<div class="ttc" id="aclassqbpp_1_1misc_1_1Tabu_html_a549866fee09f51bb17a90e3a1831bf77"><div class="ttname"><a href="classqbpp_1_1misc_1_1Tabu.html#a549866fee09f51bb17a90e3a1831bf77">qbpp::misc::Tabu::insert</a></div><div class="ttdeci">std::optional&lt; vindex_t &gt; insert(vindex_t index, bool must_be_new)</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__misc_8hpp_source.html#l00439">qbpp_misc.hpp:439</a></div></div>
<div class="ttc" id="aclassqbpp_1_1misc_1_1RandomPermutation_html_a1d2ae800e63cf7b14aee887235bc53d8"><div class="ttname"><a href="classqbpp_1_1misc_1_1RandomPermutation.html#a1d2ae800e63cf7b14aee887235bc53d8">qbpp::misc::RandomPermutation::size_</a></div><div class="ttdeci">const uint32_t size_</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__misc_8hpp_source.html#l00141">qbpp_misc.hpp:141</a></div></div>
<div class="ttc" id="aclassqbpp_1_1misc_1_1MinSet_html_abfb846a3ff4277f39e5cc4798933ca80"><div class="ttname"><a href="classqbpp_1_1misc_1_1MinSet.html#abfb846a3ff4277f39e5cc4798933ca80">qbpp::misc::MinSet::erase</a></div><div class="ttdeci">void erase(vindex_t index, T delta)</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__misc_8hpp_source.html#l00245">qbpp_misc.hpp:245</a></div></div>
<div class="ttc" id="aclassqbpp_1_1misc_1_1RandomPermutation_html_abad62f7ceac7082c11662adcd60a4b7c"><div class="ttname"><a href="classqbpp_1_1misc_1_1RandomPermutation.html#abad62f7ceac7082c11662adcd60a4b7c">qbpp::misc::RandomPermutation::perm</a></div><div class="ttdeci">std::vector&lt; uint32_t &gt; perm</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__misc_8hpp_source.html#l00143">qbpp_misc.hpp:143</a></div></div>
<div class="ttc" id="aclassqbpp_1_1misc_1_1RandomMinSet_html_a793253f32a42615244d2e353d5ff98eb"><div class="ttname"><a href="classqbpp_1_1misc_1_1RandomMinSet.html#a793253f32a42615244d2e353d5ff98eb">qbpp::misc::RandomMinSet::RandomMinSet</a></div><div class="ttdeci">RandomMinSet(vindex_t size)</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__misc_8hpp_source.html#l00380">qbpp_misc.hpp:380</a></div></div>
<div class="ttc" id="aclassqbpp_1_1misc_1_1RandomMinSet_html_a6410c2df27825c663516b0dce5c19fc6"><div class="ttname"><a href="classqbpp_1_1misc_1_1RandomMinSet.html#a6410c2df27825c663516b0dce5c19fc6">qbpp::misc::RandomMinSet::var_count</a></div><div class="ttdeci">vindex_t var_count() const</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__misc_8hpp_source.html#l00382">qbpp_misc.hpp:382</a></div></div>
<div class="ttc" id="aclassqbpp_1_1misc_1_1RandomGenerator_html_a0f1121b27e368bf92130ce84842758ff"><div class="ttname"><a href="classqbpp_1_1misc_1_1RandomGenerator.html#a0f1121b27e368bf92130ce84842758ff">qbpp::misc::RandomGenerator::operator=</a></div><div class="ttdeci">RandomGenerator &amp; operator=(const RandomGenerator &amp;)=delete</div></div>
<div class="ttc" id="aclassqbpp_1_1misc_1_1MinSet_html_aa534cb8cd4b9d3646c243ab8c8d448e1"><div class="ttname"><a href="classqbpp_1_1misc_1_1MinSet.html#aa534cb8cd4b9d3646c243ab8c8d448e1">qbpp::misc::MinSet::get_first</a></div><div class="ttdeci">vindex_t get_first() const</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__misc_8hpp_source.html#l00254">qbpp_misc.hpp:254</a></div></div>
<div class="ttc" id="aclassqbpp_1_1misc_1_1Tabu_html_afb324d3b3057cdd6872cd29fd8bb92f8"><div class="ttname"><a href="classqbpp_1_1misc_1_1Tabu.html#afb324d3b3057cdd6872cd29fd8bb92f8">qbpp::misc::Tabu::Tabu</a></div><div class="ttdeci">Tabu(vindex_t var_count, vindex_t tabu_capacity)</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__misc_8hpp_source.html#l00421">qbpp_misc.hpp:421</a></div></div>
<div class="ttc" id="aclassqbpp_1_1misc_1_1Tabu_html_abb4a9c6ae969ee3dcafbbe2260fdc921"><div class="ttname"><a href="classqbpp_1_1misc_1_1Tabu.html#abb4a9c6ae969ee3dcafbbe2260fdc921">qbpp::misc::Tabu::set_capacity</a></div><div class="ttdeci">void set_capacity(vindex_t capacity)</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__misc_8hpp_source.html#l00424">qbpp_misc.hpp:424</a></div></div>
<div class="ttc" id="anamespaceqbpp_html"><div class="ttname"><a href="namespaceqbpp.html">qbpp</a></div><div class="ttdoc">Generates a QUBO Expression for the Graph Coloring Problem using QUBO++ library.</div><div class="ttdef"><b>Definition:</b> <a href="qbpp_8hpp_source.html#l00052">qbpp.hpp:52</a></div></div>
<div class="ttc" id="aclassqbpp_1_1misc_1_1RandomGenerator_html_a1f4f3d675aee33dce174c8222c3bc22a"><div class="ttname"><a href="classqbpp_1_1misc_1_1RandomGenerator.html#a1f4f3d675aee33dce174c8222c3bc22a">qbpp::misc::RandomGenerator::set_seed</a></div><div class="ttdeci">static void set_seed(uint32_t seed=1)</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__misc_8hpp_source.html#l00112">qbpp_misc.hpp:112</a></div></div>
<div class="ttc" id="aclassqbpp_1_1misc_1_1MinSet_html_a065b55ecae0901ab86e9c8a8c7fc6af8"><div class="ttname"><a href="classqbpp_1_1misc_1_1MinSet.html#a065b55ecae0901ab86e9c8a8c7fc6af8">qbpp::misc::MinSet&lt; energy_t &gt;::ValIndexMap</a></div><div class="ttdeci">std::set&lt; std::pair&lt; energy_t, vindex_t &gt; &gt; ValIndexMap</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__misc_8hpp_source.html#l00227">qbpp_misc.hpp:227</a></div></div>
<div class="ttc" id="aclassqbpp_1_1misc_1_1MinHeap_html_ac6ccb0d64b36d2819e1b98e06ce03486"><div class="ttname"><a href="classqbpp_1_1misc_1_1MinHeap.html#ac6ccb0d64b36d2819e1b98e06ce03486">qbpp::misc::MinHeap::empty</a></div><div class="ttdeci">bool empty() const</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__misc_8hpp_source.html#l00331">qbpp_misc.hpp:331</a></div></div>
<div class="ttc" id="astructqbpp_1_1misc_1_1PcloseDeleter_html"><div class="ttname"><a href="structqbpp_1_1misc_1_1PcloseDeleter.html">qbpp::misc::PcloseDeleter</a></div><div class="ttdef"><b>Definition:</b> <a href="qbpp__misc_8hpp_source.html#l00162">qbpp_misc.hpp:162</a></div></div>
<div class="ttc" id="aclassqbpp_1_1misc_1_1RandomEngine_html_a5b8a96fee1e7b4e1900d0d1eb9332a4b"><div class="ttname"><a href="classqbpp_1_1misc_1_1RandomEngine.html#a5b8a96fee1e7b4e1900d0d1eb9332a4b">qbpp::misc::RandomEngine::gen32_impl</a></div><div class="ttdeci">uint32_t gen32_impl()</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__misc_8hpp_source.html#l00045">qbpp_misc.hpp:45</a></div></div>
<div class="ttc" id="anamespaceqbpp_html_a5cf436100ede362797d0c0501ea50a5a"><div class="ttname"><a href="namespaceqbpp.html#a5cf436100ede362797d0c0501ea50a5a">qbpp::vindex_t</a></div><div class="ttdeci">uint32_t vindex_t</div><div class="ttdef"><b>Definition:</b> <a href="qbpp_8hpp_source.html#l00129">qbpp.hpp:129</a></div></div>
<div class="ttc" id="aclassqbpp_1_1misc_1_1MinHeap_html"><div class="ttname"><a href="classqbpp_1_1misc_1_1MinHeap.html">qbpp::misc::MinHeap</a></div><div class="ttdef"><b>Definition:</b> <a href="qbpp__misc_8hpp_source.html#l00275">qbpp_misc.hpp:275</a></div></div>
<div class="ttc" id="aclassqbpp_1_1misc_1_1RandomMinSet_html_a93413c1f3fdf124693c228feb6f564eb"><div class="ttname"><a href="classqbpp_1_1misc_1_1RandomMinSet.html#a93413c1f3fdf124693c228feb6f564eb">qbpp::misc::RandomMinSet::has</a></div><div class="ttdeci">bool has(vindex_t index) const</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__misc_8hpp_source.html#l00396">qbpp_misc.hpp:396</a></div></div>
<div class="ttc" id="aclassqbpp_1_1misc_1_1RandomSet_html_a950bd3acaf6f41fe4d1c926ab4f389cc"><div class="ttname"><a href="classqbpp_1_1misc_1_1RandomSet.html#a950bd3acaf6f41fe4d1c926ab4f389cc">qbpp::misc::RandomSet::var_count</a></div><div class="ttdeci">vindex_t var_count() const</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__misc_8hpp_source.html#l00179">qbpp_misc.hpp:179</a></div></div>
<div class="ttc" id="aclassqbpp_1_1misc_1_1Tabu_html_a20a0eb6dd580b49c912404818e6314d8"><div class="ttname"><a href="classqbpp_1_1misc_1_1Tabu.html#a20a0eb6dd580b49c912404818e6314d8">qbpp::misc::Tabu::erase_front</a></div><div class="ttdeci">std::optional&lt; vindex_t &gt; erase_front()</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__misc_8hpp_source.html#l00473">qbpp_misc.hpp:473</a></div></div>
<div class="ttc" id="aclassqbpp_1_1misc_1_1RandomEngine_html_aaa825378e652a8d4b77b7ef5d8ee9f8b"><div class="ttname"><a href="classqbpp_1_1misc_1_1RandomEngine.html#aaa825378e652a8d4b77b7ef5d8ee9f8b">qbpp::misc::RandomEngine::assign_thread_id</a></div><div class="ttdeci">static uint32_t assign_thread_id()</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__misc_8hpp_source.html#l00079">qbpp_misc.hpp:79</a></div></div>
<div class="ttc" id="aclassqbpp_1_1misc_1_1RandomEngine_html_a5a5c90642189352ccd25ce46334be9d4"><div class="ttname"><a href="classqbpp_1_1misc_1_1RandomEngine.html#a5a5c90642189352ccd25ce46334be9d4">qbpp::misc::RandomEngine::rng</a></div><div class="ttdeci">boost::random::taus88 rng</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__misc_8hpp_source.html#l00038">qbpp_misc.hpp:38</a></div></div>
<div class="ttc" id="aclassqbpp_1_1misc_1_1Tabu_html_a44a9ecb55678821cc1021e33b4faba72"><div class="ttname"><a href="classqbpp_1_1misc_1_1Tabu.html#a44a9ecb55678821cc1021e33b4faba72">qbpp::misc::Tabu::has</a></div><div class="ttdeci">bool has(vindex_t index) const</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__misc_8hpp_source.html#l00435">qbpp_misc.hpp:435</a></div></div>
<div class="ttc" id="aclassqbpp_1_1misc_1_1RandomEngine_html_a47e455a933a579a66e3ef26239cf8737"><div class="ttname"><a href="classqbpp_1_1misc_1_1RandomEngine.html#a47e455a933a579a66e3ef26239cf8737">qbpp::misc::RandomEngine::gen32</a></div><div class="ttdeci">static uint32_t gen32()</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__misc_8hpp_source.html#l00085">qbpp_misc.hpp:85</a></div></div>
<div class="ttc" id="aclassqbpp_1_1misc_1_1RandomMinSet_html_a91425429be94199856c786b678a78a80"><div class="ttname"><a href="classqbpp_1_1misc_1_1RandomMinSet.html#a91425429be94199856c786b678a78a80">qbpp::misc::RandomMinSet::get_first</a></div><div class="ttdeci">vindex_t get_first() const</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__misc_8hpp_source.html#l00400">qbpp_misc.hpp:400</a></div></div>
<div class="ttc" id="aclassqbpp_1_1misc_1_1MinHeap_html_a4ffbfe7ef3c611efd3de67cd523c94bd"><div class="ttname"><a href="classqbpp_1_1misc_1_1MinHeap.html#a4ffbfe7ef3c611efd3de67cd523c94bd">qbpp::misc::MinHeap::insert</a></div><div class="ttdeci">void insert(vindex_t index, energy_t delta)</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__misc_8hpp_source.html#l00337">qbpp_misc.hpp:337</a></div></div>
<div class="ttc" id="aclassqbpp_1_1misc_1_1RandomMinSet_html_a53f8fcc9bb8fe523fd0dda83f3f86b7f"><div class="ttname"><a href="classqbpp_1_1misc_1_1RandomMinSet.html#a53f8fcc9bb8fe523fd0dda83f3f86b7f">qbpp::misc::RandomMinSet::print</a></div><div class="ttdeci">void print(const std::string &amp;prefix) const</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__misc_8hpp_source.html#l00406">qbpp_misc.hpp:406</a></div></div>
<div class="ttc" id="aclassqbpp_1_1misc_1_1RandomPermutation_html_a17e3493da87439bdb85b3f7989d43812"><div class="ttname"><a href="classqbpp_1_1misc_1_1RandomPermutation.html#a17e3493da87439bdb85b3f7989d43812">qbpp::misc::RandomPermutation::index</a></div><div class="ttdeci">uint32_t index</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__misc_8hpp_source.html#l00145">qbpp_misc.hpp:145</a></div></div>
<div class="ttc" id="aclassqbpp_1_1misc_1_1RandomSet_html_a9b527e66e301237adde36dfbfba4f866"><div class="ttname"><a href="classqbpp_1_1misc_1_1RandomSet.html#a9b527e66e301237adde36dfbfba4f866">qbpp::misc::RandomSet::has</a></div><div class="ttdeci">bool has(vindex_t index) const</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__misc_8hpp_source.html#l00209">qbpp_misc.hpp:209</a></div></div>
<div class="ttc" id="aclassqbpp_1_1misc_1_1RandomMinSet_html_ac61249a6a95ca3647cfc88d74f778a82"><div class="ttname"><a href="classqbpp_1_1misc_1_1RandomMinSet.html#ac61249a6a95ca3647cfc88d74f778a82">qbpp::misc::RandomMinSet::empty</a></div><div class="ttdeci">bool empty() const</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__misc_8hpp_source.html#l00404">qbpp_misc.hpp:404</a></div></div>
<div class="ttc" id="aclassqbpp_1_1misc_1_1MinSet_html_acfd21dd52cb7fffae88e04e839505680"><div class="ttname"><a href="classqbpp_1_1misc_1_1MinSet.html#acfd21dd52cb7fffae88e04e839505680">qbpp::misc::MinSet::empty</a></div><div class="ttdeci">bool empty() const</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__misc_8hpp_source.html#l00258">qbpp_misc.hpp:258</a></div></div>
<div class="ttc" id="aclassqbpp_1_1misc_1_1MinHeap_html_a8368e49fcd52062a31aa53fd6d10013e"><div class="ttname"><a href="classqbpp_1_1misc_1_1MinHeap.html#a8368e49fcd52062a31aa53fd6d10013e">qbpp::misc::MinHeap::heap_</a></div><div class="ttdeci">std::vector&lt; std::pair&lt; T, vindex_t &gt; &gt; heap_</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__misc_8hpp_source.html#l00276">qbpp_misc.hpp:276</a></div></div>
<div class="ttc" id="aclassqbpp_1_1misc_1_1RandomEngine_html_a425f33f093cefe100a65fa7c8f6a221f"><div class="ttname"><a href="classqbpp_1_1misc_1_1RandomEngine.html#a425f33f093cefe100a65fa7c8f6a221f">qbpp::misc::RandomEngine::RandomEngine</a></div><div class="ttdeci">RandomEngine(uint32_t base_seed=0)</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__misc_8hpp_source.html#l00040">qbpp_misc.hpp:40</a></div></div>
<div class="ttc" id="aclassqbpp_1_1misc_1_1Tabu_html_a0219883d6003e19f7cf6167f43b639d8"><div class="ttname"><a href="classqbpp_1_1misc_1_1Tabu.html#a0219883d6003e19f7cf6167f43b639d8">qbpp::misc::Tabu::tabu_set_</a></div><div class="ttdeci">std::unordered_set&lt; vindex_t &gt; tabu_set_</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__misc_8hpp_source.html#l00416">qbpp_misc.hpp:416</a></div></div>
<div class="ttc" id="aqbpp_8hpp_html_a265e0828fcea8e75fbc9c0be99f7156e"><div class="ttname"><a href="qbpp_8hpp.html#a265e0828fcea8e75fbc9c0be99f7156e">THROW_MESSAGE</a></div><div class="ttdeci">#define THROW_MESSAGE(...)</div><div class="ttdef"><b>Definition:</b> <a href="qbpp_8hpp_source.html#l00091">qbpp.hpp:91</a></div></div>
<div class="ttc" id="aclassqbpp_1_1misc_1_1RandomMinSet_html_ae6e28112e240de351a7efc77d58dc7a6"><div class="ttname"><a href="classqbpp_1_1misc_1_1RandomMinSet.html#ae6e28112e240de351a7efc77d58dc7a6">qbpp::misc::RandomMinSet::random_set_</a></div><div class="ttdeci">RandomSet random_set_</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__misc_8hpp_source.html#l00377">qbpp_misc.hpp:377</a></div></div>
<div class="ttc" id="aclassqbpp_1_1misc_1_1MinHeap_html_a2378512cf4e99296fe714ef41f3df930"><div class="ttname"><a href="classqbpp_1_1misc_1_1MinHeap.html#a2378512cf4e99296fe714ef41f3df930">qbpp::misc::MinHeap::swap_heap</a></div><div class="ttdeci">void swap_heap(vindex_t i, vindex_t j)</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__misc_8hpp_source.html#l00284">qbpp_misc.hpp:284</a></div></div>
<div class="ttc" id="aclassqbpp_1_1misc_1_1RandomEngine_html_a6f2a7cd42073b86d7128dafa9cd529e1"><div class="ttname"><a href="classqbpp_1_1misc_1_1RandomEngine.html#a6f2a7cd42073b86d7128dafa9cd529e1">qbpp::misc::RandomEngine::instance</a></div><div class="ttdeci">static RandomEngine &amp; instance()</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__misc_8hpp_source.html#l00074">qbpp_misc.hpp:74</a></div></div>
<div class="ttc" id="aclassqbpp_1_1misc_1_1RandomSet_html_a543f429a80b910d5dd196764ed45488b"><div class="ttname"><a href="classqbpp_1_1misc_1_1RandomSet.html#a543f429a80b910d5dd196764ed45488b">qbpp::misc::RandomSet::insert</a></div><div class="ttdeci">void insert(vindex_t index)</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__misc_8hpp_source.html#l00183">qbpp_misc.hpp:183</a></div></div>
<div class="ttc" id="aclassqbpp_1_1misc_1_1MinHeap_html_a166fa97ef203cf94a106eb0e28eb46f0"><div class="ttname"><a href="classqbpp_1_1misc_1_1MinHeap.html#a166fa97ef203cf94a106eb0e28eb46f0">qbpp::misc::MinHeap::bubble_up</a></div><div class="ttdeci">void bubble_up(vindex_t i)</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__misc_8hpp_source.html#l00290">qbpp_misc.hpp:290</a></div></div>
<div class="ttc" id="aclassqbpp_1_1misc_1_1MinSet_html"><div class="ttname"><a href="classqbpp_1_1misc_1_1MinSet.html">qbpp::misc::MinSet</a></div><div class="ttdef"><b>Definition:</b> <a href="qbpp__misc_8hpp_source.html#l00225">qbpp_misc.hpp:225</a></div></div>
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.17
</small></address>
</body>
</html>
