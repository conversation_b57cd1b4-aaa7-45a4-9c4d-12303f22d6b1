<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.17"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>QUBO++ Library with QUBO Solver APIs: sample/qbpp_tsp.hpp Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/x-mathjax-config">
  MathJax.Hub.Config({
    extensions: ["tex2jax.js", "TeX/AMSmath.js", "TeX/AMSsymbols.js"],
    jax: ["input/TeX","output/HTML-CSS"],
});
</script>
<script type="text/javascript" async="async" src="https://cdn.jsdelivr.net/npm/mathjax@2/MathJax.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">QUBO++ Library with QUBO Solver APIs
   </div>
   <div id="projectbrief">Author: Koji Nakano, License: Non-commercial research and evaluation purposes without any guarantees.</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.17 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_c29eeb5af533606caea8a09e66794740.html">sample</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">qbpp_tsp.hpp</div>  </div>
</div><!--header-->
<div class="contents">
<a href="qbpp__tsp_8hpp.html">Go to the documentation of this file.</a><div class="fragment"><div class="line"><a name="l00001"></a><span class="lineno">    1</span>&#160; </div>
<div class="line"><a name="l00007"></a><span class="lineno">    7</span>&#160;<span class="preprocessor">#include &lt;iostream&gt;</span></div>
<div class="line"><a name="l00008"></a><span class="lineno">    8</span>&#160;<span class="preprocessor">#include &lt;limits&gt;</span></div>
<div class="line"><a name="l00009"></a><span class="lineno">    9</span>&#160;<span class="preprocessor">#include &lt;memory&gt;</span></div>
<div class="line"><a name="l00010"></a><span class="lineno">   10</span>&#160;<span class="preprocessor">#include &lt;random&gt;</span></div>
<div class="line"><a name="l00011"></a><span class="lineno">   11</span>&#160;<span class="preprocessor">#include &lt;sstream&gt;</span></div>
<div class="line"><a name="l00012"></a><span class="lineno">   12</span>&#160;<span class="preprocessor">#include &lt;string&gt;</span></div>
<div class="line"><a name="l00013"></a><span class="lineno">   13</span>&#160;<span class="preprocessor">#include &lt;utility&gt;</span></div>
<div class="line"><a name="l00014"></a><span class="lineno">   14</span>&#160;<span class="preprocessor">#include &lt;vector&gt;</span></div>
<div class="line"><a name="l00015"></a><span class="lineno">   15</span>&#160; </div>
<div class="line"><a name="l00016"></a><span class="lineno">   16</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="qbpp_8hpp.html">qbpp.hpp</a>&quot;</span></div>
<div class="line"><a name="l00017"></a><span class="lineno">   17</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="qbpp__misc_8hpp.html">qbpp_misc.hpp</a>&quot;</span></div>
<div class="line"><a name="l00018"></a><span class="lineno">   18</span>&#160; </div>
<div class="line"><a name="l00019"></a><span class="lineno">   19</span>&#160;<span class="keyword">namespace </span><a class="code" href="namespaceqbpp.html">qbpp</a> {</div>
<div class="line"><a name="l00020"></a><span class="lineno">   20</span>&#160; </div>
<div class="line"><a name="l00022"></a><span class="lineno"><a class="line" href="namespaceqbpp_1_1tsp.html">   22</a></span>&#160;<span class="keyword">namespace </span>tsp {</div>
<div class="line"><a name="l00023"></a><span class="lineno">   23</span>&#160; </div>
<div class="line"><a name="l00024"></a><span class="lineno"><a class="line" href="namespaceqbpp_1_1tsp.html#a42bd2cc57ea78b95b2fe53599c143990">   24</a></span>&#160;constexpr uint32_t <a class="code" href="namespaceqbpp_1_1tsp.html#a42bd2cc57ea78b95b2fe53599c143990">uint32_limit</a> = std::numeric_limits&lt;uint32_t&gt;::max();</div>
<div class="line"><a name="l00025"></a><span class="lineno">   25</span>&#160; </div>
<div class="line"><a name="l00030"></a><span class="lineno"><a class="line" href="classqbpp_1_1tsp_1_1TSPMap.html">   30</a></span>&#160;<span class="keyword">class </span><a class="code" href="classqbpp_1_1tsp_1_1TSPMap.html">TSPMap</a> {</div>
<div class="line"><a name="l00033"></a><span class="lineno"><a class="line" href="classqbpp_1_1tsp_1_1TSPMap.html#adf14df1ea49eaf026cf00f23cc62383f">   33</a></span>&#160;  <span class="keyword">const</span> uint32_t <a class="code" href="classqbpp_1_1tsp_1_1TSPMap.html#adf14df1ea49eaf026cf00f23cc62383f">grid_size</a>;</div>
<div class="line"><a name="l00035"></a><span class="lineno"><a class="line" href="classqbpp_1_1tsp_1_1TSPMap.html#acfc9451587483492e4f4382933f1508c">   35</a></span>&#160;  std::vector&lt;std::pair&lt;int32_t, int32_t&gt;&gt; <a class="code" href="classqbpp_1_1tsp_1_1TSPMap.html#acfc9451587483492e4f4382933f1508c">nodes</a>;</div>
<div class="line"><a name="l00036"></a><span class="lineno">   36</span>&#160; </div>
<div class="line"><a name="l00037"></a><span class="lineno">   37</span>&#160;<span class="keyword">public</span>:</div>
<div class="line"><a name="l00041"></a><span class="lineno"><a class="line" href="classqbpp_1_1tsp_1_1TSPMap.html#a1a60e3abe1643aae812789c53f678111">   41</a></span>&#160;  <a class="code" href="classqbpp_1_1tsp_1_1TSPMap.html#a1a60e3abe1643aae812789c53f678111">TSPMap</a>(uint32_t <a class="code" href="classqbpp_1_1tsp_1_1TSPMap.html#adf14df1ea49eaf026cf00f23cc62383f">grid_size</a> = 100) : <a class="code" href="classqbpp_1_1tsp_1_1TSPMap.html#adf14df1ea49eaf026cf00f23cc62383f">grid_size</a>(<a class="code" href="classqbpp_1_1tsp_1_1TSPMap.html#adf14df1ea49eaf026cf00f23cc62383f">grid_size</a>) {};</div>
<div class="line"><a name="l00042"></a><span class="lineno">   42</span>&#160; </div>
<div class="line"><a name="l00045"></a><span class="lineno">   45</span>&#160;  <span class="keywordtype">void</span> <a class="code" href="classqbpp_1_1tsp_1_1TSPMap.html#ace1b4039b9447fae50b62c325acefc6e">gen_random_map</a>(uint32_t n);</div>
<div class="line"><a name="l00046"></a><span class="lineno">   46</span>&#160; </div>
<div class="line"><a name="l00050"></a><span class="lineno"><a class="line" href="classqbpp_1_1tsp_1_1TSPMap.html#abb5ed5d5beb5b8c639384b42adae35d5">   50</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="classqbpp_1_1tsp_1_1TSPMap.html#abb5ed5d5beb5b8c639384b42adae35d5">add_node</a>(uint32_t x, uint32_t y) { <a class="code" href="classqbpp_1_1tsp_1_1TSPMap.html#acfc9451587483492e4f4382933f1508c">nodes</a>.push_back({x, y}); }</div>
<div class="line"><a name="l00051"></a><span class="lineno">   51</span>&#160; </div>
<div class="line"><a name="l00057"></a><span class="lineno"><a class="line" href="classqbpp_1_1tsp_1_1TSPMap.html#a6ca32411c34b040e6f09790f8376b0d7">   57</a></span>&#160;  uint32_t <a class="code" href="classqbpp_1_1tsp_1_1TSPMap.html#a6ca32411c34b040e6f09790f8376b0d7">dist</a>(<span class="keyword">const</span> std::pair&lt;int32_t, int32_t&gt; &amp;p1,</div>
<div class="line"><a name="l00058"></a><span class="lineno">   58</span>&#160;                <span class="keyword">const</span> std::pair&lt;int32_t, int32_t&gt; &amp;p2)<span class="keyword"> const </span>{</div>
<div class="line"><a name="l00059"></a><span class="lineno">   59</span>&#160;    <span class="keywordflow">return</span> <span class="keyword">static_cast&lt;</span>uint32_t<span class="keyword">&gt;</span>(std::round(</div>
<div class="line"><a name="l00060"></a><span class="lineno">   60</span>&#160;        std::sqrt((p1.first - p2.first) * (p1.first - p2.first) +</div>
<div class="line"><a name="l00061"></a><span class="lineno">   61</span>&#160;                  (p1.second - p2.second) * (p1.second - p2.second))));</div>
<div class="line"><a name="l00062"></a><span class="lineno">   62</span>&#160;  }</div>
<div class="line"><a name="l00063"></a><span class="lineno">   63</span>&#160; </div>
<div class="line"><a name="l00068"></a><span class="lineno"><a class="line" href="classqbpp_1_1tsp_1_1TSPMap.html#a9de8188d7bf2ccb04463168265557ae7">   68</a></span>&#160;  uint32_t <a class="code" href="classqbpp_1_1tsp_1_1TSPMap.html#a9de8188d7bf2ccb04463168265557ae7">dist</a>(uint32_t i, uint32_t j)<span class="keyword"> const </span>{</div>
<div class="line"><a name="l00069"></a><span class="lineno">   69</span>&#160;    <span class="keywordflow">return</span> <a class="code" href="classqbpp_1_1tsp_1_1TSPMap.html#a6ca32411c34b040e6f09790f8376b0d7">dist</a>(<a class="code" href="classqbpp_1_1tsp_1_1TSPMap.html#acfc9451587483492e4f4382933f1508c">nodes</a>[i], <a class="code" href="classqbpp_1_1tsp_1_1TSPMap.html#acfc9451587483492e4f4382933f1508c">nodes</a>[j]);</div>
<div class="line"><a name="l00070"></a><span class="lineno">   70</span>&#160;  }</div>
<div class="line"><a name="l00071"></a><span class="lineno">   71</span>&#160; </div>
<div class="line"><a name="l00077"></a><span class="lineno"><a class="line" href="classqbpp_1_1tsp_1_1TSPMap.html#a611e61c620a84590c1ff675cc3a6e332">   77</a></span>&#160;  uint32_t <a class="code" href="classqbpp_1_1tsp_1_1TSPMap.html#a611e61c620a84590c1ff675cc3a6e332">min_dist</a>(uint32_t x, uint32_t y)<span class="keyword"> const </span>{</div>
<div class="line"><a name="l00078"></a><span class="lineno">   78</span>&#160;    uint32_t <a class="code" href="classqbpp_1_1tsp_1_1TSPMap.html#a611e61c620a84590c1ff675cc3a6e332">min_dist</a> = <a class="code" href="classqbpp_1_1tsp_1_1TSPMap.html#adf14df1ea49eaf026cf00f23cc62383f">grid_size</a> * 2;</div>
<div class="line"><a name="l00079"></a><span class="lineno">   79</span>&#160;    <span class="keywordflow">for</span> (<span class="keyword">const</span> <span class="keyword">auto</span> &amp;[px, py] : <a class="code" href="classqbpp_1_1tsp_1_1TSPMap.html#acfc9451587483492e4f4382933f1508c">nodes</a>) {</div>
<div class="line"><a name="l00080"></a><span class="lineno">   80</span>&#160;      <span class="keywordflow">if</span> (<a class="code" href="classqbpp_1_1tsp_1_1TSPMap.html#a6ca32411c34b040e6f09790f8376b0d7">dist</a>({x, y}, {px, py}) &lt; <a class="code" href="classqbpp_1_1tsp_1_1TSPMap.html#a611e61c620a84590c1ff675cc3a6e332">min_dist</a>)</div>
<div class="line"><a name="l00081"></a><span class="lineno">   81</span>&#160;        <a class="code" href="classqbpp_1_1tsp_1_1TSPMap.html#a611e61c620a84590c1ff675cc3a6e332">min_dist</a> = <a class="code" href="classqbpp_1_1tsp_1_1TSPMap.html#a6ca32411c34b040e6f09790f8376b0d7">dist</a>({x, y}, {px, py});</div>
<div class="line"><a name="l00082"></a><span class="lineno">   82</span>&#160;    }</div>
<div class="line"><a name="l00083"></a><span class="lineno">   83</span>&#160;    <span class="keywordflow">return</span> <a class="code" href="classqbpp_1_1tsp_1_1TSPMap.html#a611e61c620a84590c1ff675cc3a6e332">min_dist</a>;</div>
<div class="line"><a name="l00084"></a><span class="lineno">   84</span>&#160;  }</div>
<div class="line"><a name="l00085"></a><span class="lineno">   85</span>&#160; </div>
<div class="line"><a name="l00088"></a><span class="lineno"><a class="line" href="classqbpp_1_1tsp_1_1TSPMap.html#ac6e6a348dc5646adb11b4a1a6ee6eab0">   88</a></span>&#160;  uint32_t <a class="code" href="classqbpp_1_1tsp_1_1TSPMap.html#ac6e6a348dc5646adb11b4a1a6ee6eab0">node_count</a>()<span class="keyword"> const </span>{ <span class="keywordflow">return</span> <span class="keyword">static_cast&lt;</span>uint32_t<span class="keyword">&gt;</span>(<a class="code" href="classqbpp_1_1tsp_1_1TSPMap.html#acfc9451587483492e4f4382933f1508c">nodes</a>.size()); }</div>
<div class="line"><a name="l00089"></a><span class="lineno">   89</span>&#160; </div>
<div class="line"><a name="l00092"></a><span class="lineno"><a class="line" href="classqbpp_1_1tsp_1_1TSPMap.html#ae04b2ce04ee9f652f04b83ff18ce1704">   92</a></span>&#160;  uint32_t <a class="code" href="classqbpp_1_1tsp_1_1TSPMap.html#ae04b2ce04ee9f652f04b83ff18ce1704">get_grid_size</a>()<span class="keyword"> const </span>{ <span class="keywordflow">return</span> <a class="code" href="classqbpp_1_1tsp_1_1TSPMap.html#adf14df1ea49eaf026cf00f23cc62383f">grid_size</a>; }</div>
<div class="line"><a name="l00093"></a><span class="lineno">   93</span>&#160; </div>
<div class="line"><a name="l00097"></a><span class="lineno"><a class="line" href="classqbpp_1_1tsp_1_1TSPMap.html#ad5f7eccec59b5b6ae69ce654fb07d8d8">   97</a></span>&#160;  std::pair&lt;int32_t, int32_t&gt; &amp;<a class="code" href="classqbpp_1_1tsp_1_1TSPMap.html#ad5f7eccec59b5b6ae69ce654fb07d8d8">operator[]</a>(uint32_t index) {</div>
<div class="line"><a name="l00098"></a><span class="lineno">   98</span>&#160;    <span class="keywordflow">return</span> <a class="code" href="classqbpp_1_1tsp_1_1TSPMap.html#acfc9451587483492e4f4382933f1508c">nodes</a>[index];</div>
<div class="line"><a name="l00099"></a><span class="lineno">   99</span>&#160;  }</div>
<div class="line"><a name="l00100"></a><span class="lineno">  100</span>&#160;};</div>
<div class="line"><a name="l00101"></a><span class="lineno">  101</span>&#160; </div>
<div class="line"><a name="l00104"></a><span class="lineno"><a class="line" href="classqbpp_1_1tsp_1_1TSPQuadModel.html">  104</a></span>&#160;<span class="keyword">class </span><a class="code" href="classqbpp_1_1tsp_1_1TSPQuadModel.html">TSPQuadModel</a> : <span class="keyword">public</span> <a class="code" href="classqbpp_1_1QuadModel.html">qbpp::QuadModel</a> {</div>
<div class="line"><a name="l00106"></a><span class="lineno"><a class="line" href="classqbpp_1_1tsp_1_1TSPQuadModel.html#af77e9c5a87a8b14aaf11ecac2da2d285">  106</a></span>&#160;  <span class="keyword">const</span> <span class="keywordtype">bool</span> <a class="code" href="classqbpp_1_1tsp_1_1TSPQuadModel.html#af77e9c5a87a8b14aaf11ecac2da2d285">fix_first</a>;</div>
<div class="line"><a name="l00108"></a><span class="lineno"><a class="line" href="classqbpp_1_1tsp_1_1TSPQuadModel.html#a9612c7de2af2dbafb0e232124441aed5">  108</a></span>&#160;  <span class="keyword">const</span> <a class="code" href="classqbpp_1_1Vector.html">qbpp::Vector&lt;qbpp::Vector&lt;qbpp::Var&gt;</a>&gt; <a class="code" href="classqbpp_1_1tsp_1_1TSPQuadModel.html#a9612c7de2af2dbafb0e232124441aed5">x</a>;</div>
<div class="line"><a name="l00109"></a><span class="lineno">  109</span>&#160; </div>
<div class="line"><a name="l00114"></a><span class="lineno">  114</span>&#160;  std::tuple&lt;qbpp::Model, bool, qbpp::Vector&lt;qbpp::Vector&lt;qbpp::Var&gt;&gt;&gt;</div>
<div class="line"><a name="l00115"></a><span class="lineno">  115</span>&#160;  <a class="code" href="classqbpp_1_1tsp_1_1TSPQuadModel.html#afa2124847942c070b4cb95ab9ae59284">helper_func</a>(<span class="keyword">const</span> <a class="code" href="classqbpp_1_1tsp_1_1TSPMap.html">TSPMap</a> &amp;map, <span class="keywordtype">bool</span> <a class="code" href="classqbpp_1_1tsp_1_1TSPQuadModel.html#af77e9c5a87a8b14aaf11ecac2da2d285">fix_first</a>);</div>
<div class="line"><a name="l00116"></a><span class="lineno">  116</span>&#160; </div>
<div class="line"><a name="l00117"></a><span class="lineno"><a class="line" href="classqbpp_1_1tsp_1_1TSPQuadModel.html#af1e28b60e4d5f8454483f9aff3f86a51">  117</a></span>&#160;  <a class="code" href="classqbpp_1_1tsp_1_1TSPQuadModel.html#af1e28b60e4d5f8454483f9aff3f86a51">TSPQuadModel</a>(</div>
<div class="line"><a name="l00118"></a><span class="lineno">  118</span>&#160;      std::tuple&lt;<a class="code" href="classqbpp_1_1QuadModel.html">qbpp::QuadModel</a>, <span class="keywordtype">bool</span>, <a class="code" href="classqbpp_1_1Vector.html">qbpp::Vector</a>&lt;<a class="code" href="classqbpp_1_1Vector.html">qbpp::Vector&lt;qbpp::Var&gt;</a>&gt;&gt;</div>
<div class="line"><a name="l00119"></a><span class="lineno">  119</span>&#160;          tuple)</div>
<div class="line"><a name="l00120"></a><span class="lineno">  120</span>&#160;      : <a class="code" href="namespaceqbpp.html">qbpp</a>::<a class="code" href="classqbpp_1_1QuadModel.html">QuadModel</a>(std::get&lt;0&gt;(tuple)), <a class="code" href="classqbpp_1_1tsp_1_1TSPQuadModel.html#af77e9c5a87a8b14aaf11ecac2da2d285">fix_first</a>(std::get&lt;1&gt;(tuple)),</div>
<div class="line"><a name="l00121"></a><span class="lineno">  121</span>&#160;        <a class="code" href="classqbpp_1_1tsp_1_1TSPQuadModel.html#a9612c7de2af2dbafb0e232124441aed5">x</a>(std::get&lt;2&gt;(tuple)) {}</div>
<div class="line"><a name="l00122"></a><span class="lineno">  122</span>&#160; </div>
<div class="line"><a name="l00123"></a><span class="lineno">  123</span>&#160;<span class="keyword">public</span>:</div>
<div class="line"><a name="l00128"></a><span class="lineno"><a class="line" href="classqbpp_1_1tsp_1_1TSPQuadModel.html#a0d47d1ade744a192570817a242475c1c">  128</a></span>&#160;  <a class="code" href="classqbpp_1_1tsp_1_1TSPQuadModel.html#a0d47d1ade744a192570817a242475c1c">TSPQuadModel</a>(<span class="keyword">const</span> <a class="code" href="classqbpp_1_1tsp_1_1TSPMap.html">TSPMap</a> &amp;map, <span class="keywordtype">bool</span> <a class="code" href="classqbpp_1_1tsp_1_1TSPQuadModel.html#af77e9c5a87a8b14aaf11ecac2da2d285">fix_first</a> = <span class="keyword">false</span>)</div>
<div class="line"><a name="l00129"></a><span class="lineno">  129</span>&#160;      : <a class="code" href="classqbpp_1_1tsp_1_1TSPQuadModel.html">TSPQuadModel</a>(<a class="code" href="classqbpp_1_1tsp_1_1TSPQuadModel.html#afa2124847942c070b4cb95ab9ae59284">helper_func</a>(map, <a class="code" href="classqbpp_1_1tsp_1_1TSPQuadModel.html#af77e9c5a87a8b14aaf11ecac2da2d285">fix_first</a>)) {}</div>
<div class="line"><a name="l00130"></a><span class="lineno">  130</span>&#160; </div>
<div class="line"><a name="l00133"></a><span class="lineno"><a class="line" href="classqbpp_1_1tsp_1_1TSPQuadModel.html#a3c1e511fe6fbd822ec7f871484cfd67a">  133</a></span>&#160;  uint32_t <a class="code" href="classqbpp_1_1tsp_1_1TSPQuadModel.html#a3c1e511fe6fbd822ec7f871484cfd67a">node_count</a>()<span class="keyword"> const </span>{ <span class="keywordflow">return</span> <span class="keyword">static_cast&lt;</span>uint32_t<span class="keyword">&gt;</span>(<a class="code" href="classqbpp_1_1tsp_1_1TSPQuadModel.html#a9612c7de2af2dbafb0e232124441aed5">x</a>.<a class="code" href="classqbpp_1_1Vector.html#af719287ecdb5f44bbd0aa5d9ba3aaea1">size</a>()); }</div>
<div class="line"><a name="l00134"></a><span class="lineno">  134</span>&#160; </div>
<div class="line"><a name="l00136"></a><span class="lineno"><a class="line" href="classqbpp_1_1tsp_1_1TSPQuadModel.html#a9b20af5a44b761114e4f7eefe8bde336">  136</a></span>&#160;  <a class="code" href="classqbpp_1_1Var.html">qbpp::Var</a> <a class="code" href="classqbpp_1_1tsp_1_1TSPQuadModel.html#a9b20af5a44b761114e4f7eefe8bde336">get_var</a>(uint32_t i, uint32_t j)<span class="keyword"> const </span>{ <span class="keywordflow">return</span> <a class="code" href="classqbpp_1_1tsp_1_1TSPQuadModel.html#a9612c7de2af2dbafb0e232124441aed5">x</a>[i][j]; }</div>
<div class="line"><a name="l00137"></a><span class="lineno">  137</span>&#160; </div>
<div class="line"><a name="l00139"></a><span class="lineno"><a class="line" href="classqbpp_1_1tsp_1_1TSPQuadModel.html#a3beca816eb5a9d8591a88da727d7bbf9">  139</a></span>&#160;  <span class="keywordtype">bool</span> <a class="code" href="classqbpp_1_1tsp_1_1TSPQuadModel.html#a3beca816eb5a9d8591a88da727d7bbf9">get_fix_first</a>()<span class="keyword"> const </span>{ <span class="keywordflow">return</span> <a class="code" href="classqbpp_1_1tsp_1_1TSPQuadModel.html#af77e9c5a87a8b14aaf11ecac2da2d285">fix_first</a>; }</div>
<div class="line"><a name="l00140"></a><span class="lineno">  140</span>&#160;};</div>
<div class="line"><a name="l00141"></a><span class="lineno">  141</span>&#160; </div>
<div class="line"><a name="l00143"></a><span class="lineno"><a class="line" href="classqbpp_1_1tsp_1_1TSPSol.html">  143</a></span>&#160;<span class="keyword">class </span><a class="code" href="classqbpp_1_1tsp_1_1TSPSol.html">TSPSol</a> {</div>
<div class="line"><a name="l00145"></a><span class="lineno"><a class="line" href="classqbpp_1_1tsp_1_1TSPSol.html#a5c80e1d34f42b58098d9738725a6d3d8">  145</a></span>&#160;  <span class="keyword">const</span> <a class="code" href="classqbpp_1_1tsp_1_1TSPQuadModel.html">TSPQuadModel</a> <a class="code" href="classqbpp_1_1tsp_1_1TSPSol.html#a5c80e1d34f42b58098d9738725a6d3d8">tsp_quad_model</a>;</div>
<div class="line"><a name="l00147"></a><span class="lineno"><a class="line" href="classqbpp_1_1tsp_1_1TSPSol.html#a260cb897debad81d09142ee2ed559f16">  147</a></span>&#160;  <span class="keyword">const</span> <a class="code" href="classqbpp_1_1Sol.html">Sol</a> <a class="code" href="classqbpp_1_1tsp_1_1TSPSol.html#a260cb897debad81d09142ee2ed559f16">sol</a>;</div>
<div class="line"><a name="l00149"></a><span class="lineno"><a class="line" href="classqbpp_1_1tsp_1_1TSPSol.html#af5a209140f85294b3728cf54d3603781">  149</a></span>&#160;  <span class="keyword">const</span> std::vector&lt;uint32_t&gt; <a class="code" href="classqbpp_1_1tsp_1_1TSPSol.html#af5a209140f85294b3728cf54d3603781">tour</a>;</div>
<div class="line"><a name="l00150"></a><span class="lineno">  150</span>&#160; </div>
<div class="line"><a name="l00155"></a><span class="lineno">  155</span>&#160;  <span class="keyword">static</span> std::vector&lt;uint32_t&gt; <a class="code" href="classqbpp_1_1tsp_1_1TSPSol.html#a850469299d3a30962180e13657e16452">gen_tour</a>(<span class="keyword">const</span> <a class="code" href="classqbpp_1_1tsp_1_1TSPQuadModel.html">TSPQuadModel</a> &amp;<a class="code" href="classqbpp_1_1tsp_1_1TSPSol.html#a5c80e1d34f42b58098d9738725a6d3d8">tsp_quad_model</a>,</div>
<div class="line"><a name="l00156"></a><span class="lineno">  156</span>&#160;                                        <span class="keyword">const</span> <a class="code" href="classqbpp_1_1Sol.html">Sol</a> &amp;<a class="code" href="classqbpp_1_1tsp_1_1TSPSol.html#a260cb897debad81d09142ee2ed559f16">sol</a>);</div>
<div class="line"><a name="l00157"></a><span class="lineno">  157</span>&#160; </div>
<div class="line"><a name="l00158"></a><span class="lineno">  158</span>&#160;<span class="keyword">public</span>:</div>
<div class="line"><a name="l00163"></a><span class="lineno"><a class="line" href="classqbpp_1_1tsp_1_1TSPSol.html#a06ad43713331494cb541dde344c04780">  163</a></span>&#160;  <a class="code" href="classqbpp_1_1tsp_1_1TSPSol.html#a06ad43713331494cb541dde344c04780">TSPSol</a>(<span class="keyword">const</span> <a class="code" href="classqbpp_1_1tsp_1_1TSPQuadModel.html">TSPQuadModel</a> &amp;<a class="code" href="classqbpp_1_1tsp_1_1TSPSol.html#a5c80e1d34f42b58098d9738725a6d3d8">tsp_quad_model</a>, <span class="keyword">const</span> <a class="code" href="classqbpp_1_1Sol.html">qbpp::Sol</a> &amp;<a class="code" href="classqbpp_1_1tsp_1_1TSPSol.html#a260cb897debad81d09142ee2ed559f16">sol</a>)</div>
<div class="line"><a name="l00164"></a><span class="lineno">  164</span>&#160;      : <a class="code" href="classqbpp_1_1tsp_1_1TSPSol.html#a5c80e1d34f42b58098d9738725a6d3d8">tsp_quad_model</a>(<a class="code" href="classqbpp_1_1tsp_1_1TSPSol.html#a5c80e1d34f42b58098d9738725a6d3d8">tsp_quad_model</a>), <a class="code" href="classqbpp_1_1tsp_1_1TSPSol.html#a260cb897debad81d09142ee2ed559f16">sol</a>(<a class="code" href="classqbpp_1_1tsp_1_1TSPSol.html#a260cb897debad81d09142ee2ed559f16">sol</a>),</div>
<div class="line"><a name="l00165"></a><span class="lineno">  165</span>&#160;        <a class="code" href="classqbpp_1_1tsp_1_1TSPSol.html#af5a209140f85294b3728cf54d3603781">tour</a>(<a class="code" href="classqbpp_1_1tsp_1_1TSPSol.html#a850469299d3a30962180e13657e16452">gen_tour</a>(<a class="code" href="classqbpp_1_1tsp_1_1TSPSol.html#a5c80e1d34f42b58098d9738725a6d3d8">tsp_quad_model</a>, <a class="code" href="classqbpp_1_1tsp_1_1TSPSol.html#a260cb897debad81d09142ee2ed559f16">sol</a>)) {}</div>
<div class="line"><a name="l00166"></a><span class="lineno">  166</span>&#160; </div>
<div class="line"><a name="l00170"></a><span class="lineno"><a class="line" href="classqbpp_1_1tsp_1_1TSPSol.html#a8d9b29b1cc361cc18950421e2dc2221d">  170</a></span>&#160;  uint32_t <a class="code" href="classqbpp_1_1tsp_1_1TSPSol.html#a8d9b29b1cc361cc18950421e2dc2221d">operator[]</a>(uint32_t index)<span class="keyword"> const </span>{ <span class="keywordflow">return</span> <a class="code" href="classqbpp_1_1tsp_1_1TSPSol.html#af5a209140f85294b3728cf54d3603781">tour</a>[index]; }</div>
<div class="line"><a name="l00171"></a><span class="lineno">  171</span>&#160; </div>
<div class="line"><a name="l00173"></a><span class="lineno"><a class="line" href="classqbpp_1_1tsp_1_1TSPSol.html#a16eb43543fe8d4af32107abc3a2c9595">  173</a></span>&#160;  uint32_t <a class="code" href="classqbpp_1_1tsp_1_1TSPSol.html#a16eb43543fe8d4af32107abc3a2c9595">node_count</a>()<span class="keyword"> const </span>{ <span class="keywordflow">return</span> <a class="code" href="classqbpp_1_1tsp_1_1TSPSol.html#a5c80e1d34f42b58098d9738725a6d3d8">tsp_quad_model</a>.<a class="code" href="classqbpp_1_1tsp_1_1TSPQuadModel.html#a3c1e511fe6fbd822ec7f871484cfd67a">node_count</a>(); }</div>
<div class="line"><a name="l00174"></a><span class="lineno">  174</span>&#160; </div>
<div class="line"><a name="l00176"></a><span class="lineno"><a class="line" href="classqbpp_1_1tsp_1_1TSPSol.html#ab2abe5283ba0f9fefad71b1beee340e3">  176</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="classqbpp_1_1tsp_1_1TSPSol.html#ab2abe5283ba0f9fefad71b1beee340e3">print</a>()<span class="keyword"> const </span>{</div>
<div class="line"><a name="l00177"></a><span class="lineno">  177</span>&#160;    std::cout &lt;&lt; <a class="code" href="classqbpp_1_1tsp_1_1TSPSol.html#a260cb897debad81d09142ee2ed559f16">sol</a>.<a class="code" href="classqbpp_1_1Sol.html#a3ad455870df155ffed4701ce070cf335">energy</a>() &lt;&lt; <span class="stringliteral">&quot; :&quot;</span>;</div>
<div class="line"><a name="l00178"></a><span class="lineno">  178</span>&#160;    <span class="keywordflow">for</span> (<span class="keyword">const</span> <span class="keyword">auto</span> &amp;i : <a class="code" href="classqbpp_1_1tsp_1_1TSPSol.html#af5a209140f85294b3728cf54d3603781">tour</a>)</div>
<div class="line"><a name="l00179"></a><span class="lineno">  179</span>&#160;      <span class="keywordflow">if</span> (i != <a class="code" href="namespaceqbpp_1_1tsp.html#a42bd2cc57ea78b95b2fe53599c143990">uint32_limit</a>)</div>
<div class="line"><a name="l00180"></a><span class="lineno">  180</span>&#160;        std::cout &lt;&lt; <span class="stringliteral">&quot; &quot;</span> &lt;&lt; i;</div>
<div class="line"><a name="l00181"></a><span class="lineno">  181</span>&#160;      <span class="keywordflow">else</span></div>
<div class="line"><a name="l00182"></a><span class="lineno">  182</span>&#160;        std::cout &lt;&lt; <span class="stringliteral">&quot; -&quot;</span>;</div>
<div class="line"><a name="l00183"></a><span class="lineno">  183</span>&#160;    std::cout &lt;&lt; std::endl;</div>
<div class="line"><a name="l00184"></a><span class="lineno">  184</span>&#160;  }</div>
<div class="line"><a name="l00185"></a><span class="lineno">  185</span>&#160; </div>
<div class="line"><a name="l00186"></a><span class="lineno"><a class="line" href="classqbpp_1_1tsp_1_1TSPSol.html#aeaddec0d0368bef74ebcbda49fe863fe">  186</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="classqbpp_1_1tsp_1_1TSPSol.html#aeaddec0d0368bef74ebcbda49fe863fe">print_matrix</a>()<span class="keyword"> const </span>{</div>
<div class="line"><a name="l00187"></a><span class="lineno">  187</span>&#160;    <span class="keywordflow">for</span> (uint32_t i = 0; i &lt; <a class="code" href="classqbpp_1_1tsp_1_1TSPSol.html#a5c80e1d34f42b58098d9738725a6d3d8">tsp_quad_model</a>.<a class="code" href="classqbpp_1_1tsp_1_1TSPQuadModel.html#a3c1e511fe6fbd822ec7f871484cfd67a">node_count</a>(); i++) {</div>
<div class="line"><a name="l00188"></a><span class="lineno">  188</span>&#160;      <span class="keywordflow">for</span> (uint32_t j = 0; j &lt; <a class="code" href="classqbpp_1_1tsp_1_1TSPSol.html#a5c80e1d34f42b58098d9738725a6d3d8">tsp_quad_model</a>.<a class="code" href="classqbpp_1_1tsp_1_1TSPQuadModel.html#a3c1e511fe6fbd822ec7f871484cfd67a">node_count</a>(); j++) {</div>
<div class="line"><a name="l00189"></a><span class="lineno">  189</span>&#160;        <span class="keywordflow">if</span> (i == 0 &amp;&amp; j == 0)</div>
<div class="line"><a name="l00190"></a><span class="lineno">  190</span>&#160;          std::cout &lt;&lt; <span class="stringliteral">&quot;1&quot;</span>;</div>
<div class="line"><a name="l00191"></a><span class="lineno">  191</span>&#160;        <span class="keywordflow">else</span> <span class="keywordflow">if</span> (i == 0 || j == 0)</div>
<div class="line"><a name="l00192"></a><span class="lineno">  192</span>&#160;          std::cout &lt;&lt; <span class="stringliteral">&quot;0&quot;</span>;</div>
<div class="line"><a name="l00193"></a><span class="lineno">  193</span>&#160;        <span class="keywordflow">else</span> {</div>
<div class="line"><a name="l00194"></a><span class="lineno">  194</span>&#160;          std::cout &lt;&lt; <a class="code" href="classqbpp_1_1tsp_1_1TSPSol.html#a260cb897debad81d09142ee2ed559f16">sol</a>.<a class="code" href="classqbpp_1_1Sol.html#abdb4f6b779678720715f7845f264440f">get</a>(<a class="code" href="classqbpp_1_1tsp_1_1TSPSol.html#a5c80e1d34f42b58098d9738725a6d3d8">tsp_quad_model</a>.<a class="code" href="classqbpp_1_1tsp_1_1TSPQuadModel.html#a9b20af5a44b761114e4f7eefe8bde336">get_var</a>(i, j));</div>
<div class="line"><a name="l00195"></a><span class="lineno">  195</span>&#160;        }</div>
<div class="line"><a name="l00196"></a><span class="lineno">  196</span>&#160;      }</div>
<div class="line"><a name="l00197"></a><span class="lineno">  197</span>&#160;      std::cout &lt;&lt; std::endl;</div>
<div class="line"><a name="l00198"></a><span class="lineno">  198</span>&#160;    }</div>
<div class="line"><a name="l00199"></a><span class="lineno">  199</span>&#160;  }</div>
<div class="line"><a name="l00200"></a><span class="lineno">  200</span>&#160;};</div>
<div class="line"><a name="l00201"></a><span class="lineno">  201</span>&#160; </div>
<div class="line"><a name="l00204"></a><span class="lineno"><a class="line" href="classqbpp_1_1tsp_1_1TSPModel.html">  204</a></span>&#160;<span class="keyword">class </span><a class="code" href="classqbpp_1_1tsp_1_1TSPModel.html">TSPModel</a> : <span class="keyword">public</span> <a class="code" href="classqbpp_1_1QuadModel.html">qbpp::QuadModel</a> {</div>
<div class="line"><a name="l00206"></a><span class="lineno"><a class="line" href="classqbpp_1_1tsp_1_1TSPModel.html#a46141e00a4cc6554171bbd57406132a3">  206</a></span>&#160;  <span class="keyword">const</span> <span class="keywordtype">bool</span> <a class="code" href="classqbpp_1_1tsp_1_1TSPModel.html#a46141e00a4cc6554171bbd57406132a3">fix_first</a>;</div>
<div class="line"><a name="l00208"></a><span class="lineno"><a class="line" href="classqbpp_1_1tsp_1_1TSPModel.html#a7790a66391ecf05f0b0e38dc4ca52e6e">  208</a></span>&#160;  <span class="keyword">const</span> <a class="code" href="classqbpp_1_1Vector.html">qbpp::Vector&lt;qbpp::Vector&lt;qbpp::Var&gt;</a>&gt; <a class="code" href="classqbpp_1_1tsp_1_1TSPModel.html#a7790a66391ecf05f0b0e38dc4ca52e6e">x</a>;</div>
<div class="line"><a name="l00209"></a><span class="lineno">  209</span>&#160; </div>
<div class="line"><a name="l00214"></a><span class="lineno">  214</span>&#160;  std::tuple&lt;qbpp::QuadModel, bool, qbpp::Vector&lt;qbpp::Vector&lt;qbpp::Var&gt;&gt;&gt;</div>
<div class="line"><a name="l00215"></a><span class="lineno">  215</span>&#160;  <a class="code" href="classqbpp_1_1tsp_1_1TSPModel.html#aad24b168b00baea06d9ff61cb0e1897c">helper_func</a>(<span class="keyword">const</span> <a class="code" href="classqbpp_1_1tsp_1_1TSPMap.html">TSPMap</a> &amp;map, <span class="keywordtype">bool</span> <a class="code" href="classqbpp_1_1tsp_1_1TSPModel.html#a46141e00a4cc6554171bbd57406132a3">fix_first</a>);</div>
<div class="line"><a name="l00216"></a><span class="lineno">  216</span>&#160; </div>
<div class="line"><a name="l00217"></a><span class="lineno"><a class="line" href="classqbpp_1_1tsp_1_1TSPModel.html#ae351f642ac8277b8641ae322d8559fd2">  217</a></span>&#160;  <a class="code" href="classqbpp_1_1tsp_1_1TSPModel.html#ae351f642ac8277b8641ae322d8559fd2">TSPModel</a>(std::tuple&lt;<a class="code" href="classqbpp_1_1QuadModel.html">qbpp::QuadModel</a>, <span class="keywordtype">bool</span>,</div>
<div class="line"><a name="l00218"></a><span class="lineno">  218</span>&#160;                      <a class="code" href="classqbpp_1_1Vector.html">qbpp::Vector</a>&lt;<a class="code" href="classqbpp_1_1Vector.html">qbpp::Vector&lt;qbpp::Var&gt;</a>&gt;&gt; &amp;&amp;tuple)</div>
<div class="line"><a name="l00219"></a><span class="lineno">  219</span>&#160;      : <a class="code" href="namespaceqbpp.html">qbpp</a>::<a class="code" href="classqbpp_1_1QuadModel.html">QuadModel</a>(std::move(std::get&lt;0&gt;(tuple))),</div>
<div class="line"><a name="l00220"></a><span class="lineno">  220</span>&#160;        <a class="code" href="classqbpp_1_1tsp_1_1TSPModel.html#a46141e00a4cc6554171bbd57406132a3">fix_first</a>(std::get&lt;1&gt;(tuple)), <a class="code" href="classqbpp_1_1tsp_1_1TSPModel.html#a7790a66391ecf05f0b0e38dc4ca52e6e">x</a>(std::move(std::get&lt;2&gt;(tuple))) {}</div>
<div class="line"><a name="l00221"></a><span class="lineno">  221</span>&#160; </div>
<div class="line"><a name="l00222"></a><span class="lineno">  222</span>&#160;<span class="keyword">public</span>:</div>
<div class="line"><a name="l00227"></a><span class="lineno"><a class="line" href="classqbpp_1_1tsp_1_1TSPModel.html#a7b4d3f9919401a3ea106e318eb627d50">  227</a></span>&#160;  <a class="code" href="classqbpp_1_1tsp_1_1TSPModel.html#a7b4d3f9919401a3ea106e318eb627d50">TSPModel</a>(<span class="keyword">const</span> <a class="code" href="classqbpp_1_1tsp_1_1TSPMap.html">TSPMap</a> &amp;map, <span class="keywordtype">bool</span> <a class="code" href="classqbpp_1_1tsp_1_1TSPModel.html#a46141e00a4cc6554171bbd57406132a3">fix_first</a> = <span class="keyword">false</span>)</div>
<div class="line"><a name="l00228"></a><span class="lineno">  228</span>&#160;      : <a class="code" href="classqbpp_1_1tsp_1_1TSPModel.html">TSPModel</a>(<a class="code" href="classqbpp_1_1tsp_1_1TSPModel.html#aad24b168b00baea06d9ff61cb0e1897c">helper_func</a>(map, <a class="code" href="classqbpp_1_1tsp_1_1TSPModel.html#a46141e00a4cc6554171bbd57406132a3">fix_first</a>)) {}</div>
<div class="line"><a name="l00229"></a><span class="lineno">  229</span>&#160; </div>
<div class="line"><a name="l00232"></a><span class="lineno"><a class="line" href="classqbpp_1_1tsp_1_1TSPModel.html#a2ea1cc5abb5a7448081a83e7355c2ce9">  232</a></span>&#160;  uint32_t <a class="code" href="classqbpp_1_1tsp_1_1TSPModel.html#a2ea1cc5abb5a7448081a83e7355c2ce9">node_count</a>()<span class="keyword"> const </span>{ <span class="keywordflow">return</span> <span class="keyword">static_cast&lt;</span>uint32_t<span class="keyword">&gt;</span>(<a class="code" href="classqbpp_1_1tsp_1_1TSPModel.html#a7790a66391ecf05f0b0e38dc4ca52e6e">x</a>.<a class="code" href="classqbpp_1_1Vector.html#af719287ecdb5f44bbd0aa5d9ba3aaea1">size</a>()); }</div>
<div class="line"><a name="l00233"></a><span class="lineno">  233</span>&#160; </div>
<div class="line"><a name="l00235"></a><span class="lineno"><a class="line" href="classqbpp_1_1tsp_1_1TSPModel.html#a1290b8facf3b542b4b239915609fbe91">  235</a></span>&#160;  <a class="code" href="classqbpp_1_1Var.html">qbpp::Var</a> <a class="code" href="classqbpp_1_1tsp_1_1TSPModel.html#a1290b8facf3b542b4b239915609fbe91">get_var</a>(uint32_t i, uint32_t j)<span class="keyword"> const </span>{ <span class="keywordflow">return</span> <a class="code" href="classqbpp_1_1tsp_1_1TSPModel.html#a7790a66391ecf05f0b0e38dc4ca52e6e">x</a>[i][j]; }</div>
<div class="line"><a name="l00236"></a><span class="lineno">  236</span>&#160; </div>
<div class="line"><a name="l00238"></a><span class="lineno"><a class="line" href="classqbpp_1_1tsp_1_1TSPModel.html#ab9eb1bfad69c9c02e21db6dee0e61e38">  238</a></span>&#160;  <span class="keywordtype">bool</span> <a class="code" href="classqbpp_1_1tsp_1_1TSPModel.html#ab9eb1bfad69c9c02e21db6dee0e61e38">get_fix_first</a>()<span class="keyword"> const </span>{ <span class="keywordflow">return</span> <a class="code" href="classqbpp_1_1tsp_1_1TSPModel.html#a46141e00a4cc6554171bbd57406132a3">fix_first</a>; }</div>
<div class="line"><a name="l00239"></a><span class="lineno">  239</span>&#160;};</div>
<div class="line"><a name="l00240"></a><span class="lineno">  240</span>&#160; </div>
<div class="line"><a name="l00244"></a><span class="lineno"><a class="line" href="classqbpp_1_1tsp_1_1DrawSimpleGraph.html">  244</a></span>&#160;<span class="keyword">class </span><a class="code" href="classqbpp_1_1tsp_1_1DrawSimpleGraph.html">DrawSimpleGraph</a> {</div>
<div class="line"><a name="l00247"></a><span class="lineno"><a class="line" href="classqbpp_1_1tsp_1_1DrawSimpleGraph.html#ad6b478c8f7377c4e808411484b7ca760">  247</a></span>&#160;  std::vector&lt;std::tuple&lt;int, int, std::string&gt;&gt; <a class="code" href="classqbpp_1_1tsp_1_1DrawSimpleGraph.html#ad6b478c8f7377c4e808411484b7ca760">nodes</a>;</div>
<div class="line"><a name="l00249"></a><span class="lineno"><a class="line" href="classqbpp_1_1tsp_1_1DrawSimpleGraph.html#aa2b616d4c3e3931eaca5620c03fbebb6">  249</a></span>&#160;  std::vector&lt;std::tuple&lt;int, int&gt;&gt; <a class="code" href="classqbpp_1_1tsp_1_1DrawSimpleGraph.html#aa2b616d4c3e3931eaca5620c03fbebb6">edges</a>;</div>
<div class="line"><a name="l00250"></a><span class="lineno">  250</span>&#160; </div>
<div class="line"><a name="l00251"></a><span class="lineno">  251</span>&#160;<span class="keyword">public</span>:</div>
<div class="line"><a name="l00253"></a><span class="lineno">  253</span>&#160;  <a class="code" href="classqbpp_1_1tsp_1_1DrawSimpleGraph.html#a993b9fd24edfc32e994c8b242b131df6">DrawSimpleGraph</a>() = <span class="keywordflow">default</span>;</div>
<div class="line"><a name="l00254"></a><span class="lineno">  254</span>&#160; </div>
<div class="line"><a name="l00259"></a><span class="lineno"><a class="line" href="classqbpp_1_1tsp_1_1DrawSimpleGraph.html#ae58cb44d06b764de7fae386abf86c0de">  259</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="classqbpp_1_1tsp_1_1DrawSimpleGraph.html#ae58cb44d06b764de7fae386abf86c0de">add_node</a>(<span class="keywordtype">int</span> x, <span class="keywordtype">int</span> y, <span class="keyword">const</span> std::string &amp;label = <span class="stringliteral">&quot;&quot;</span>) {</div>
<div class="line"><a name="l00260"></a><span class="lineno">  260</span>&#160;    <a class="code" href="classqbpp_1_1tsp_1_1DrawSimpleGraph.html#ad6b478c8f7377c4e808411484b7ca760">nodes</a>.push_back(std::make_tuple(x, y, label));</div>
<div class="line"><a name="l00261"></a><span class="lineno">  261</span>&#160;  }</div>
<div class="line"><a name="l00265"></a><span class="lineno"><a class="line" href="classqbpp_1_1tsp_1_1DrawSimpleGraph.html#a2f637a33d00cbb8790512e49ccfafa76">  265</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="classqbpp_1_1tsp_1_1DrawSimpleGraph.html#a2f637a33d00cbb8790512e49ccfafa76">add_node</a>(std::pair&lt;int, int&gt; node, <span class="keyword">const</span> std::string &amp;label = <span class="stringliteral">&quot;&quot;</span>) {</div>
<div class="line"><a name="l00266"></a><span class="lineno">  266</span>&#160;    <a class="code" href="classqbpp_1_1tsp_1_1DrawSimpleGraph.html#ae58cb44d06b764de7fae386abf86c0de">add_node</a>(node.first, node.second, label);</div>
<div class="line"><a name="l00267"></a><span class="lineno">  267</span>&#160;  }</div>
<div class="line"><a name="l00268"></a><span class="lineno">  268</span>&#160; </div>
<div class="line"><a name="l00273"></a><span class="lineno"><a class="line" href="classqbpp_1_1tsp_1_1DrawSimpleGraph.html#a99955897a283a208c4093d4f747491d5">  273</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="classqbpp_1_1tsp_1_1DrawSimpleGraph.html#a99955897a283a208c4093d4f747491d5">add_edge</a>(<span class="keywordtype">unsigned</span> <span class="keywordtype">int</span> node1, <span class="keywordtype">unsigned</span> <span class="keywordtype">int</span> node2) {</div>
<div class="line"><a name="l00274"></a><span class="lineno">  274</span>&#160;    <a class="code" href="classqbpp_1_1tsp_1_1DrawSimpleGraph.html#aa2b616d4c3e3931eaca5620c03fbebb6">edges</a>.push_back(std::make_pair(node1, node2));</div>
<div class="line"><a name="l00275"></a><span class="lineno">  275</span>&#160;  }</div>
<div class="line"><a name="l00277"></a><span class="lineno"><a class="line" href="classqbpp_1_1tsp_1_1DrawSimpleGraph.html#acd1ab585bb6256dfc7d5b44adeabd923">  277</a></span>&#160;  uint32_t <a class="code" href="classqbpp_1_1tsp_1_1DrawSimpleGraph.html#acd1ab585bb6256dfc7d5b44adeabd923">node_count</a>()<span class="keyword"> const </span>{ <span class="keywordflow">return</span> <span class="keyword">static_cast&lt;</span>uint32_t<span class="keyword">&gt;</span>(<a class="code" href="classqbpp_1_1tsp_1_1DrawSimpleGraph.html#ad6b478c8f7377c4e808411484b7ca760">nodes</a>.size()); }</div>
<div class="line"><a name="l00278"></a><span class="lineno">  278</span>&#160; </div>
<div class="line"><a name="l00280"></a><span class="lineno"><a class="line" href="classqbpp_1_1tsp_1_1DrawSimpleGraph.html#ae7df1d1a76995fe4d7d1b978f32ef67a">  280</a></span>&#160;  uint32_t <a class="code" href="classqbpp_1_1tsp_1_1DrawSimpleGraph.html#ae7df1d1a76995fe4d7d1b978f32ef67a">edge_count</a>()<span class="keyword"> const </span>{ <span class="keywordflow">return</span> <span class="keyword">static_cast&lt;</span>uint32_t<span class="keyword">&gt;</span>(<a class="code" href="classqbpp_1_1tsp_1_1DrawSimpleGraph.html#aa2b616d4c3e3931eaca5620c03fbebb6">edges</a>.size()); }</div>
<div class="line"><a name="l00281"></a><span class="lineno">  281</span>&#160; </div>
<div class="line"><a name="l00286"></a><span class="lineno"><a class="line" href="classqbpp_1_1tsp_1_1DrawSimpleGraph.html#ae4a6e9be4fa3e068d2dd4a4ace7548b5">  286</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="classqbpp_1_1tsp_1_1DrawSimpleGraph.html#ae4a6e9be4fa3e068d2dd4a4ace7548b5">draw</a>(std::string filename) {</div>
<div class="line"><a name="l00287"></a><span class="lineno">  287</span>&#160;    std::ostringstream dot_stream;</div>
<div class="line"><a name="l00288"></a><span class="lineno">  288</span>&#160;    dot_stream &lt;&lt; <span class="stringliteral">&quot;graph G {\n&quot;</span></div>
<div class="line"><a name="l00289"></a><span class="lineno">  289</span>&#160;               &lt;&lt; <span class="stringliteral">&quot;node [shape=circle, fixedsize=true, width=5, fontsize=200, &quot;</span></div>
<div class="line"><a name="l00290"></a><span class="lineno">  290</span>&#160;                  <span class="stringliteral">&quot;penwidth=10];\n&quot;</span></div>
<div class="line"><a name="l00291"></a><span class="lineno">  291</span>&#160;               &lt;&lt; <span class="stringliteral">&quot;edge [penwidth=10];\n&quot;</span>;</div>
<div class="line"><a name="l00292"></a><span class="lineno">  292</span>&#160;    <span class="keywordtype">int</span> index = 0;</div>
<div class="line"><a name="l00293"></a><span class="lineno">  293</span>&#160;    <span class="keywordflow">for</span> (<span class="keyword">auto</span> [x, y, s] : <a class="code" href="classqbpp_1_1tsp_1_1DrawSimpleGraph.html#ad6b478c8f7377c4e808411484b7ca760">nodes</a>) {</div>
<div class="line"><a name="l00294"></a><span class="lineno">  294</span>&#160;      dot_stream &lt;&lt; index &lt;&lt; <span class="stringliteral">&quot; [label = \&quot;&quot;</span> &lt;&lt; index &lt;&lt; <span class="stringliteral">&quot;\&quot;, pos = \&quot;&quot;</span> &lt;&lt; x</div>
<div class="line"><a name="l00295"></a><span class="lineno">  295</span>&#160;                 &lt;&lt; <span class="stringliteral">&quot;,&quot;</span> &lt;&lt; y &lt;&lt; <span class="stringliteral">&quot;!\&quot;&quot;</span>;</div>
<div class="line"><a name="l00296"></a><span class="lineno">  296</span>&#160;      <span class="keywordflow">if</span> (s != <span class="stringliteral">&quot;&quot;</span>)</div>
<div class="line"><a name="l00297"></a><span class="lineno">  297</span>&#160;        dot_stream &lt;&lt; <span class="stringliteral">&quot; &quot;</span> &lt;&lt; s;</div>
<div class="line"><a name="l00298"></a><span class="lineno">  298</span>&#160;      dot_stream &lt;&lt; <span class="stringliteral">&quot;];\n&quot;</span>;</div>
<div class="line"><a name="l00299"></a><span class="lineno">  299</span>&#160;      ++index;</div>
<div class="line"><a name="l00300"></a><span class="lineno">  300</span>&#160;    }</div>
<div class="line"><a name="l00301"></a><span class="lineno">  301</span>&#160;    <span class="keywordflow">for</span> (<span class="keyword">auto</span> [node1, node2] : <a class="code" href="classqbpp_1_1tsp_1_1DrawSimpleGraph.html#aa2b616d4c3e3931eaca5620c03fbebb6">edges</a>) {</div>
<div class="line"><a name="l00302"></a><span class="lineno">  302</span>&#160;      dot_stream &lt;&lt; node1 &lt;&lt; <span class="stringliteral">&quot; -- &quot;</span> &lt;&lt; node2 &lt;&lt; <span class="stringliteral">&quot;\n&quot;</span>;</div>
<div class="line"><a name="l00303"></a><span class="lineno">  303</span>&#160;    }</div>
<div class="line"><a name="l00304"></a><span class="lineno">  304</span>&#160;    dot_stream &lt;&lt; <span class="stringliteral">&quot;}\n&quot;</span>;</div>
<div class="line"><a name="l00305"></a><span class="lineno">  305</span>&#160;    std::string command = <span class="stringliteral">&quot;neato -T&quot;</span> +</div>
<div class="line"><a name="l00306"></a><span class="lineno">  306</span>&#160;                          filename.substr(filename.rfind(<span class="charliteral">&#39;.&#39;</span>) + 1) + <span class="stringliteral">&quot; -o &quot;</span> +</div>
<div class="line"><a name="l00307"></a><span class="lineno">  307</span>&#160;                          filename;</div>
<div class="line"><a name="l00308"></a><span class="lineno">  308</span>&#160; </div>
<div class="line"><a name="l00309"></a><span class="lineno">  309</span>&#160;    std::unique_ptr&lt;FILE, qbpp::misc::PcloseDeleter&gt; pipe(</div>
<div class="line"><a name="l00310"></a><span class="lineno">  310</span>&#160;        popen(command.c_str(), <span class="stringliteral">&quot;w&quot;</span>));</div>
<div class="line"><a name="l00311"></a><span class="lineno">  311</span>&#160; </div>
<div class="line"><a name="l00312"></a><span class="lineno">  312</span>&#160;    <span class="keywordflow">if</span> (!pipe) {</div>
<div class="line"><a name="l00313"></a><span class="lineno">  313</span>&#160;      <span class="keywordflow">throw</span> std::runtime_error(<a class="code" href="qbpp_8hpp.html#a265e0828fcea8e75fbc9c0be99f7156e">THROW_MESSAGE</a>(<span class="stringliteral">&quot;popen() failed!&quot;</span>));</div>
<div class="line"><a name="l00314"></a><span class="lineno">  314</span>&#160;    }</div>
<div class="line"><a name="l00315"></a><span class="lineno">  315</span>&#160;    fprintf(pipe.get(), <span class="stringliteral">&quot;%s&quot;</span>, dot_stream.str().c_str());</div>
<div class="line"><a name="l00316"></a><span class="lineno">  316</span>&#160;  }</div>
<div class="line"><a name="l00317"></a><span class="lineno">  317</span>&#160;};</div>
<div class="line"><a name="l00318"></a><span class="lineno">  318</span>&#160; </div>
<div class="line"><a name="l00319"></a><span class="lineno">  319</span>&#160;<span class="comment">//==============================</span></div>
<div class="line"><a name="l00320"></a><span class="lineno">  320</span>&#160;<span class="comment">// TSPMap member functions</span></div>
<div class="line"><a name="l00321"></a><span class="lineno">  321</span>&#160;<span class="comment">//==============================</span></div>
<div class="line"><a name="l00322"></a><span class="lineno">  322</span>&#160; </div>
<div class="line"><a name="l00323"></a><span class="lineno"><a class="line" href="classqbpp_1_1tsp_1_1TSPMap.html#ace1b4039b9447fae50b62c325acefc6e">  323</a></span>&#160;<span class="keyword">inline</span> <span class="keywordtype">void</span> <a class="code" href="classqbpp_1_1tsp_1_1TSPMap.html#ace1b4039b9447fae50b62c325acefc6e">TSPMap::gen_random_map</a>(uint32_t n) {</div>
<div class="line"><a name="l00324"></a><span class="lineno">  324</span>&#160;  <a class="code" href="classqbpp_1_1tsp_1_1TSPMap.html#acfc9451587483492e4f4382933f1508c">nodes</a>.reserve(n);</div>
<div class="line"><a name="l00325"></a><span class="lineno">  325</span>&#160;  uint32_t x, y;</div>
<div class="line"><a name="l00326"></a><span class="lineno">  326</span>&#160;  <span class="keywordflow">for</span> (uint32_t i = 0; i &lt; n; i++) {</div>
<div class="line"><a name="l00327"></a><span class="lineno">  327</span>&#160;    uint32_t counter = 0;</div>
<div class="line"><a name="l00328"></a><span class="lineno">  328</span>&#160;    uint32_t max_dist = 1;</div>
<div class="line"><a name="l00329"></a><span class="lineno">  329</span>&#160;    <span class="comment">// Terminate if dist&gt;=max_dist is satisfied 10 times.</span></div>
<div class="line"><a name="l00330"></a><span class="lineno">  330</span>&#160;    <span class="keywordflow">while</span> (counter &lt; 10) {</div>
<div class="line"><a name="l00331"></a><span class="lineno">  331</span>&#160;      x = <a class="code" href="classqbpp_1_1misc_1_1RandomGenerator.html#a95308c2300aeef9a881ba97786367977">qbpp::misc::RandomGenerator::gen</a>(<a class="code" href="classqbpp_1_1tsp_1_1TSPMap.html#adf14df1ea49eaf026cf00f23cc62383f">grid_size</a>);</div>
<div class="line"><a name="l00332"></a><span class="lineno">  332</span>&#160;      y = <a class="code" href="classqbpp_1_1misc_1_1RandomGenerator.html#a95308c2300aeef9a881ba97786367977">qbpp::misc::RandomGenerator::gen</a>(<a class="code" href="classqbpp_1_1tsp_1_1TSPMap.html#adf14df1ea49eaf026cf00f23cc62383f">grid_size</a>);</div>
<div class="line"><a name="l00333"></a><span class="lineno">  333</span>&#160;      uint32_t <a class="code" href="classqbpp_1_1tsp_1_1TSPMap.html#a6ca32411c34b040e6f09790f8376b0d7">dist</a> = <a class="code" href="classqbpp_1_1tsp_1_1TSPMap.html#a611e61c620a84590c1ff675cc3a6e332">min_dist</a>(x, y);</div>
<div class="line"><a name="l00334"></a><span class="lineno">  334</span>&#160;      <span class="keywordflow">if</span> (<a class="code" href="classqbpp_1_1tsp_1_1TSPMap.html#a6ca32411c34b040e6f09790f8376b0d7">dist</a> &gt;= max_dist) {</div>
<div class="line"><a name="l00335"></a><span class="lineno">  335</span>&#160;        max_dist = <a class="code" href="classqbpp_1_1tsp_1_1TSPMap.html#a6ca32411c34b040e6f09790f8376b0d7">dist</a>;</div>
<div class="line"><a name="l00336"></a><span class="lineno">  336</span>&#160;        counter++;</div>
<div class="line"><a name="l00337"></a><span class="lineno">  337</span>&#160;      }</div>
<div class="line"><a name="l00338"></a><span class="lineno">  338</span>&#160;    }</div>
<div class="line"><a name="l00339"></a><span class="lineno">  339</span>&#160;    <a class="code" href="classqbpp_1_1tsp_1_1TSPMap.html#abb5ed5d5beb5b8c639384b42adae35d5">add_node</a>(x, y);</div>
<div class="line"><a name="l00340"></a><span class="lineno">  340</span>&#160;  }</div>
<div class="line"><a name="l00341"></a><span class="lineno">  341</span>&#160;}</div>
<div class="line"><a name="l00342"></a><span class="lineno">  342</span>&#160; </div>
<div class="line"><a name="l00343"></a><span class="lineno">  343</span>&#160;<span class="comment">//===============================</span></div>
<div class="line"><a name="l00344"></a><span class="lineno">  344</span>&#160;<span class="comment">// TSPQuadModel member functions</span></div>
<div class="line"><a name="l00345"></a><span class="lineno">  345</span>&#160;<span class="comment">//===============================</span></div>
<div class="line"><a name="l00346"></a><span class="lineno">  346</span>&#160; </div>
<div class="line"><a name="l00347"></a><span class="lineno">  347</span>&#160;<span class="keyword">inline</span> std::tuple&lt;qbpp::Model, bool, qbpp::Vector&lt;qbpp::Vector&lt;qbpp::Var&gt;&gt;&gt;</div>
<div class="line"><a name="l00348"></a><span class="lineno"><a class="line" href="classqbpp_1_1tsp_1_1TSPQuadModel.html#afa2124847942c070b4cb95ab9ae59284">  348</a></span>&#160;<a class="code" href="classqbpp_1_1tsp_1_1TSPQuadModel.html#afa2124847942c070b4cb95ab9ae59284">TSPQuadModel::helper_func</a>(<span class="keyword">const</span> <a class="code" href="classqbpp_1_1tsp_1_1TSPMap.html">TSPMap</a> &amp;tsp_map, <span class="keywordtype">bool</span> fix_first) {</div>
<div class="line"><a name="l00349"></a><span class="lineno">  349</span>&#160;  <span class="keyword">auto</span> <a class="code" href="classqbpp_1_1tsp_1_1TSPQuadModel.html#a3c1e511fe6fbd822ec7f871484cfd67a">node_count</a> = tsp_map.<a class="code" href="classqbpp_1_1tsp_1_1TSPMap.html#ac6e6a348dc5646adb11b4a1a6ee6eab0">node_count</a>();</div>
<div class="line"><a name="l00350"></a><span class="lineno">  350</span>&#160;  <span class="keyword">auto</span> <a class="code" href="classqbpp_1_1tsp_1_1TSPQuadModel.html#a9612c7de2af2dbafb0e232124441aed5">x</a> = <a class="code" href="namespaceqbpp.html#a1cf3f678bcc7eee089c75d4bc5a0b959">qbpp::var</a>(<span class="stringliteral">&quot;x&quot;</span>, <a class="code" href="classqbpp_1_1tsp_1_1TSPQuadModel.html#a3c1e511fe6fbd822ec7f871484cfd67a">node_count</a>, <a class="code" href="classqbpp_1_1tsp_1_1TSPQuadModel.html#a3c1e511fe6fbd822ec7f871484cfd67a">node_count</a>);</div>
<div class="line"><a name="l00351"></a><span class="lineno">  351</span>&#160;  std::cout &lt;&lt; <span class="stringliteral">&quot;Generating QUBO expression for permutation.&quot;</span> &lt;&lt; std::endl;</div>
<div class="line"><a name="l00352"></a><span class="lineno">  352</span>&#160;  <span class="keyword">auto</span> permutation_expr = <a class="code" href="namespaceqbpp.html#a9041f8ee7d3f4a7c04a0d385babae285">qbpp::sum</a>(</div>
<div class="line"><a name="l00353"></a><span class="lineno">  353</span>&#160;      (<a class="code" href="namespaceqbpp.html#a008af6dfb07e3e463ee6d83c547c3ff0">qbpp::vector_sum</a>(<a class="code" href="classqbpp_1_1tsp_1_1TSPQuadModel.html#a9612c7de2af2dbafb0e232124441aed5">x</a>) == 1) + (<a class="code" href="namespaceqbpp.html#a008af6dfb07e3e463ee6d83c547c3ff0">qbpp::vector_sum</a>(<a class="code" href="namespaceqbpp.html#a47715eec8c4942d0179134ce224be6c5">qbpp::transpose</a>(<a class="code" href="classqbpp_1_1tsp_1_1TSPQuadModel.html#a9612c7de2af2dbafb0e232124441aed5">x</a>)) == 1));</div>
<div class="line"><a name="l00354"></a><span class="lineno">  354</span>&#160;  std::cout &lt;&lt; <span class="stringliteral">&quot;Generating QUBO expressions for tour distances.&quot;</span> &lt;&lt; std::endl;</div>
<div class="line"><a name="l00355"></a><span class="lineno">  355</span>&#160; </div>
<div class="line"><a name="l00356"></a><span class="lineno">  356</span>&#160;  <a class="code" href="classqbpp_1_1Vector.html">qbpp::Vector&lt;qbpp::Expr&gt;</a> exprs(<a class="code" href="classqbpp_1_1tsp_1_1TSPQuadModel.html#a3c1e511fe6fbd822ec7f871484cfd67a">node_count</a>);</div>
<div class="line"><a name="l00357"></a><span class="lineno">  357</span>&#160;  tbb::parallel_for(decltype(<a class="code" href="classqbpp_1_1tsp_1_1TSPQuadModel.html#a3c1e511fe6fbd822ec7f871484cfd67a">node_count</a>)(0), <a class="code" href="classqbpp_1_1tsp_1_1TSPQuadModel.html#a3c1e511fe6fbd822ec7f871484cfd67a">node_count</a>, [&amp;](<span class="keywordtype">int</span> i) {</div>
<div class="line"><a name="l00358"></a><span class="lineno">  358</span>&#160;    uint32_t next_i = (i + 1) % <a class="code" href="classqbpp_1_1tsp_1_1TSPQuadModel.html#a3c1e511fe6fbd822ec7f871484cfd67a">node_count</a>;</div>
<div class="line"><a name="l00359"></a><span class="lineno">  359</span>&#160;    <span class="keyword">auto</span> &amp;<a class="code" href="classqbpp_1_1Model.html#a18d86c7c55ae188f35cfafb9cb1f8f85">expr</a> = exprs[i];</div>
<div class="line"><a name="l00360"></a><span class="lineno">  360</span>&#160; </div>
<div class="line"><a name="l00361"></a><span class="lineno">  361</span>&#160;    <span class="keywordflow">for</span> (uint32_t j = 0; j &lt; <a class="code" href="classqbpp_1_1tsp_1_1TSPQuadModel.html#a3c1e511fe6fbd822ec7f871484cfd67a">node_count</a>; j++) {</div>
<div class="line"><a name="l00362"></a><span class="lineno">  362</span>&#160;      <span class="keywordflow">for</span> (uint32_t k = 0; k &lt; <a class="code" href="classqbpp_1_1tsp_1_1TSPQuadModel.html#a3c1e511fe6fbd822ec7f871484cfd67a">node_count</a>; k++) {</div>
<div class="line"><a name="l00363"></a><span class="lineno">  363</span>&#160;        <span class="keywordflow">if</span> (j == k)</div>
<div class="line"><a name="l00364"></a><span class="lineno">  364</span>&#160;          <span class="keywordflow">continue</span>;</div>
<div class="line"><a name="l00365"></a><span class="lineno">  365</span>&#160;        <a class="code" href="classqbpp_1_1Model.html#a18d86c7c55ae188f35cfafb9cb1f8f85">expr</a> += tsp_map.<a class="code" href="classqbpp_1_1tsp_1_1TSPMap.html#a6ca32411c34b040e6f09790f8376b0d7">dist</a>(j, k) * <a class="code" href="classqbpp_1_1tsp_1_1TSPQuadModel.html#a9612c7de2af2dbafb0e232124441aed5">x</a>[i][j] * <a class="code" href="classqbpp_1_1tsp_1_1TSPQuadModel.html#a9612c7de2af2dbafb0e232124441aed5">x</a>[next_i][k];</div>
<div class="line"><a name="l00366"></a><span class="lineno">  366</span>&#160;      }</div>
<div class="line"><a name="l00367"></a><span class="lineno">  367</span>&#160;    }</div>
<div class="line"><a name="l00368"></a><span class="lineno">  368</span>&#160;  });</div>
<div class="line"><a name="l00369"></a><span class="lineno">  369</span>&#160; </div>
<div class="line"><a name="l00370"></a><span class="lineno">  370</span>&#160;  <span class="keyword">auto</span> tsp_expr = <a class="code" href="namespaceqbpp.html#a9041f8ee7d3f4a7c04a0d385babae285">qbpp::sum</a>(exprs) + permutation_expr * tsp_map.<a class="code" href="classqbpp_1_1tsp_1_1TSPMap.html#ae04b2ce04ee9f652f04b83ff18ce1704">get_grid_size</a>();</div>
<div class="line"><a name="l00371"></a><span class="lineno">  371</span>&#160; </div>
<div class="line"><a name="l00372"></a><span class="lineno">  372</span>&#160;  <span class="keywordflow">if</span> (<a class="code" href="classqbpp_1_1tsp_1_1TSPQuadModel.html#af77e9c5a87a8b14aaf11ecac2da2d285">fix_first</a>) {</div>
<div class="line"><a name="l00373"></a><span class="lineno">  373</span>&#160;    std::cout &lt;&lt; <span class="stringliteral">&quot;Fixing the first visiting node.&quot;</span> &lt;&lt; std::endl;</div>
<div class="line"><a name="l00374"></a><span class="lineno">  374</span>&#160;    <a class="code" href="namespaceqbpp.html#aeea468454b74e8cf74539f77454bef6b">qbpp::MapList</a> fix0 = {{<a class="code" href="classqbpp_1_1tsp_1_1TSPQuadModel.html#a9612c7de2af2dbafb0e232124441aed5">x</a>[0][0], 1}};</div>
<div class="line"><a name="l00375"></a><span class="lineno">  375</span>&#160;    <span class="keywordflow">for</span> (uint32_t i = 1; i &lt; <a class="code" href="classqbpp_1_1tsp_1_1TSPQuadModel.html#a3c1e511fe6fbd822ec7f871484cfd67a">node_count</a>; i++) {</div>
<div class="line"><a name="l00376"></a><span class="lineno">  376</span>&#160;      fix0.<a class="code" href="classqbpp_1_1Vector.html#a23f6c18870445548d8db321606a0a9fb">push_back</a>({<a class="code" href="classqbpp_1_1tsp_1_1TSPQuadModel.html#a9612c7de2af2dbafb0e232124441aed5">x</a>[0][i], 0});</div>
<div class="line"><a name="l00377"></a><span class="lineno">  377</span>&#160;      fix0.push_back({<a class="code" href="classqbpp_1_1tsp_1_1TSPQuadModel.html#a9612c7de2af2dbafb0e232124441aed5">x</a>[i][0], 0});</div>
<div class="line"><a name="l00378"></a><span class="lineno">  378</span>&#160;    }</div>
<div class="line"><a name="l00379"></a><span class="lineno">  379</span>&#160;    tsp_expr.replace(fix0);</div>
<div class="line"><a name="l00380"></a><span class="lineno">  380</span>&#160;  }</div>
<div class="line"><a name="l00381"></a><span class="lineno">  381</span>&#160; </div>
<div class="line"><a name="l00382"></a><span class="lineno">  382</span>&#160;  std::cout &lt;&lt; <span class="stringliteral">&quot;Simplifying the QUBO expression.&quot;</span> &lt;&lt; std::endl;</div>
<div class="line"><a name="l00383"></a><span class="lineno">  383</span>&#160; </div>
<div class="line"><a name="l00384"></a><span class="lineno">  384</span>&#160;  tsp_expr.simplify_as_binary();</div>
<div class="line"><a name="l00385"></a><span class="lineno">  385</span>&#160; </div>
<div class="line"><a name="l00386"></a><span class="lineno">  386</span>&#160;  <span class="keywordflow">return</span> {tsp_expr, <a class="code" href="classqbpp_1_1tsp_1_1TSPQuadModel.html#af77e9c5a87a8b14aaf11ecac2da2d285">fix_first</a>, <a class="code" href="classqbpp_1_1tsp_1_1TSPQuadModel.html#a9612c7de2af2dbafb0e232124441aed5">x</a>};</div>
<div class="line"><a name="l00387"></a><span class="lineno">  387</span>&#160;}</div>
<div class="line"><a name="l00388"></a><span class="lineno">  388</span>&#160; </div>
<div class="line"><a name="l00389"></a><span class="lineno">  389</span>&#160;<span class="comment">//=========================</span></div>
<div class="line"><a name="l00390"></a><span class="lineno">  390</span>&#160;<span class="comment">// TSPSol member functions</span></div>
<div class="line"><a name="l00391"></a><span class="lineno">  391</span>&#160;<span class="comment">//=========================</span></div>
<div class="line"><a name="l00392"></a><span class="lineno">  392</span>&#160; </div>
<div class="line"><a name="l00393"></a><span class="lineno">  393</span>&#160;<span class="keyword">inline</span> std::vector&lt;uint32_t&gt;</div>
<div class="line"><a name="l00394"></a><span class="lineno"><a class="line" href="classqbpp_1_1tsp_1_1TSPSol.html#a850469299d3a30962180e13657e16452">  394</a></span>&#160;<a class="code" href="classqbpp_1_1tsp_1_1TSPSol.html#a850469299d3a30962180e13657e16452">TSPSol::gen_tour</a>(<span class="keyword">const</span> <a class="code" href="classqbpp_1_1tsp_1_1TSPQuadModel.html">TSPQuadModel</a> &amp;tsp_quad_model, <span class="keyword">const</span> <a class="code" href="classqbpp_1_1Sol.html">Sol</a> &amp;sol) {</div>
<div class="line"><a name="l00395"></a><span class="lineno">  395</span>&#160;  std::vector&lt;uint32_t&gt; <a class="code" href="classqbpp_1_1tsp_1_1TSPSol.html#af5a209140f85294b3728cf54d3603781">tour</a>;</div>
<div class="line"><a name="l00396"></a><span class="lineno">  396</span>&#160;  <span class="keywordflow">for</span> (uint32_t i = 0; i &lt; <a class="code" href="classqbpp_1_1tsp_1_1TSPSol.html#a5c80e1d34f42b58098d9738725a6d3d8">tsp_quad_model</a>.<a class="code" href="classqbpp_1_1tsp_1_1TSPQuadModel.html#a3c1e511fe6fbd822ec7f871484cfd67a">node_count</a>(); i++) {</div>
<div class="line"><a name="l00397"></a><span class="lineno">  397</span>&#160;    <span class="keywordflow">if</span> (<a class="code" href="classqbpp_1_1tsp_1_1TSPSol.html#a5c80e1d34f42b58098d9738725a6d3d8">tsp_quad_model</a>.<a class="code" href="classqbpp_1_1tsp_1_1TSPQuadModel.html#a3beca816eb5a9d8591a88da727d7bbf9">get_fix_first</a>() &amp;&amp; i == 0) {</div>
<div class="line"><a name="l00398"></a><span class="lineno">  398</span>&#160;      <a class="code" href="classqbpp_1_1tsp_1_1TSPSol.html#af5a209140f85294b3728cf54d3603781">tour</a>.push_back(0);</div>
<div class="line"><a name="l00399"></a><span class="lineno">  399</span>&#160;      <span class="keywordflow">continue</span>;</div>
<div class="line"><a name="l00400"></a><span class="lineno">  400</span>&#160;    }</div>
<div class="line"><a name="l00401"></a><span class="lineno">  401</span>&#160;    uint32_t count = 0;</div>
<div class="line"><a name="l00402"></a><span class="lineno">  402</span>&#160;    uint32_t node;</div>
<div class="line"><a name="l00403"></a><span class="lineno">  403</span>&#160;    <span class="keywordflow">for</span> (uint32_t j = (<a class="code" href="classqbpp_1_1tsp_1_1TSPSol.html#a5c80e1d34f42b58098d9738725a6d3d8">tsp_quad_model</a>.<a class="code" href="classqbpp_1_1tsp_1_1TSPQuadModel.html#a3beca816eb5a9d8591a88da727d7bbf9">get_fix_first</a>() ? 1 : 0);</div>
<div class="line"><a name="l00404"></a><span class="lineno">  404</span>&#160;         j &lt; <a class="code" href="classqbpp_1_1tsp_1_1TSPSol.html#a5c80e1d34f42b58098d9738725a6d3d8">tsp_quad_model</a>.<a class="code" href="classqbpp_1_1tsp_1_1TSPQuadModel.html#a3c1e511fe6fbd822ec7f871484cfd67a">node_count</a>(); j++) {</div>
<div class="line"><a name="l00405"></a><span class="lineno">  405</span>&#160;      <span class="keywordflow">if</span> (<a class="code" href="classqbpp_1_1tsp_1_1TSPSol.html#a260cb897debad81d09142ee2ed559f16">sol</a>.<a class="code" href="classqbpp_1_1Sol.html#abdb4f6b779678720715f7845f264440f">get</a>(<a class="code" href="classqbpp_1_1tsp_1_1TSPSol.html#a5c80e1d34f42b58098d9738725a6d3d8">tsp_quad_model</a>.<a class="code" href="classqbpp_1_1tsp_1_1TSPQuadModel.html#a9b20af5a44b761114e4f7eefe8bde336">get_var</a>(i, j)) == 1) {</div>
<div class="line"><a name="l00406"></a><span class="lineno">  406</span>&#160;        node = j;</div>
<div class="line"><a name="l00407"></a><span class="lineno">  407</span>&#160;        count++;</div>
<div class="line"><a name="l00408"></a><span class="lineno">  408</span>&#160;      }</div>
<div class="line"><a name="l00409"></a><span class="lineno">  409</span>&#160;    }</div>
<div class="line"><a name="l00410"></a><span class="lineno">  410</span>&#160;    <span class="keywordflow">if</span> (count != 1)</div>
<div class="line"><a name="l00411"></a><span class="lineno">  411</span>&#160;      node = <a class="code" href="namespaceqbpp_1_1tsp.html#a42bd2cc57ea78b95b2fe53599c143990">uint32_limit</a>;</div>
<div class="line"><a name="l00412"></a><span class="lineno">  412</span>&#160;    <a class="code" href="classqbpp_1_1tsp_1_1TSPSol.html#af5a209140f85294b3728cf54d3603781">tour</a>.push_back(node);</div>
<div class="line"><a name="l00413"></a><span class="lineno">  413</span>&#160;  }</div>
<div class="line"><a name="l00414"></a><span class="lineno">  414</span>&#160;  <span class="keywordflow">return</span> <a class="code" href="classqbpp_1_1tsp_1_1TSPSol.html#af5a209140f85294b3728cf54d3603781">tour</a>;</div>
<div class="line"><a name="l00415"></a><span class="lineno">  415</span>&#160;}</div>
<div class="line"><a name="l00416"></a><span class="lineno">  416</span>&#160; </div>
<div class="line"><a name="l00417"></a><span class="lineno">  417</span>&#160;} <span class="comment">// namespace tsp</span></div>
<div class="line"><a name="l00418"></a><span class="lineno">  418</span>&#160;} <span class="comment">// namespace qbpp</span></div>
</div><!-- fragment --></div><!-- contents -->
<div class="ttc" id="aclassqbpp_1_1Vector_html_af719287ecdb5f44bbd0aa5d9ba3aaea1"><div class="ttname"><a href="classqbpp_1_1Vector.html#af719287ecdb5f44bbd0aa5d9ba3aaea1">qbpp::Vector::size</a></div><div class="ttdeci">size_t size() const</div><div class="ttdef"><b>Definition:</b> <a href="qbpp_8hpp_source.html#l00591">qbpp.hpp:591</a></div></div>
<div class="ttc" id="aclassqbpp_1_1tsp_1_1TSPMap_html_ad5f7eccec59b5b6ae69ce654fb07d8d8"><div class="ttname"><a href="classqbpp_1_1tsp_1_1TSPMap.html#ad5f7eccec59b5b6ae69ce654fb07d8d8">qbpp::tsp::TSPMap::operator[]</a></div><div class="ttdeci">std::pair&lt; int32_t, int32_t &gt; &amp; operator[](uint32_t index)</div><div class="ttdoc">Get the position of the node at index i.</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__tsp_8hpp_source.html#l00097">qbpp_tsp.hpp:97</a></div></div>
<div class="ttc" id="aclassqbpp_1_1tsp_1_1DrawSimpleGraph_html_a99955897a283a208c4093d4f747491d5"><div class="ttname"><a href="classqbpp_1_1tsp_1_1DrawSimpleGraph.html#a99955897a283a208c4093d4f747491d5">qbpp::tsp::DrawSimpleGraph::add_edge</a></div><div class="ttdeci">void add_edge(unsigned int node1, unsigned int node2)</div><div class="ttdoc">Add an edge to the graph.</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__tsp_8hpp_source.html#l00273">qbpp_tsp.hpp:273</a></div></div>
<div class="ttc" id="aclassqbpp_1_1tsp_1_1TSPModel_html_a7790a66391ecf05f0b0e38dc4ca52e6e"><div class="ttname"><a href="classqbpp_1_1tsp_1_1TSPModel.html#a7790a66391ecf05f0b0e38dc4ca52e6e">qbpp::tsp::TSPModel::x</a></div><div class="ttdeci">const qbpp::Vector&lt; qbpp::Vector&lt; qbpp::Var &gt; &gt; x</div><div class="ttdoc">Variables for the TSP.</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__tsp_8hpp_source.html#l00208">qbpp_tsp.hpp:208</a></div></div>
<div class="ttc" id="aclassqbpp_1_1Sol_html_abdb4f6b779678720715f7845f264440f"><div class="ttname"><a href="classqbpp_1_1Sol.html#abdb4f6b779678720715f7845f264440f">qbpp::Sol::get</a></div><div class="ttdeci">var_val_t get(vindex_t index) const</div><div class="ttdef"><b>Definition:</b> <a href="qbpp_8hpp_source.html#l02243">qbpp.hpp:2243</a></div></div>
<div class="ttc" id="aclassqbpp_1_1tsp_1_1TSPSol_html_a16eb43543fe8d4af32107abc3a2c9595"><div class="ttname"><a href="classqbpp_1_1tsp_1_1TSPSol.html#a16eb43543fe8d4af32107abc3a2c9595">qbpp::tsp::TSPSol::node_count</a></div><div class="ttdeci">uint32_t node_count() const</div><div class="ttdoc">Get the number of nodes in the tour.</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__tsp_8hpp_source.html#l00173">qbpp_tsp.hpp:173</a></div></div>
<div class="ttc" id="anamespaceqbpp_html_a1cf3f678bcc7eee089c75d4bc5a0b959"><div class="ttname"><a href="namespaceqbpp.html#a1cf3f678bcc7eee089c75d4bc5a0b959">qbpp::var</a></div><div class="ttdeci">Var var(const std::string &amp;var_str)</div><div class="ttdef"><b>Definition:</b> <a href="qbpp_8hpp_source.html#l02887">qbpp.hpp:2887</a></div></div>
<div class="ttc" id="aclassqbpp_1_1QuadModel_html"><div class="ttname"><a href="classqbpp_1_1QuadModel.html">qbpp::QuadModel</a></div><div class="ttdef"><b>Definition:</b> <a href="qbpp_8hpp_source.html#l01892">qbpp.hpp:1892</a></div></div>
<div class="ttc" id="aclassqbpp_1_1tsp_1_1TSPSol_html_ab2abe5283ba0f9fefad71b1beee340e3"><div class="ttname"><a href="classqbpp_1_1tsp_1_1TSPSol.html#ab2abe5283ba0f9fefad71b1beee340e3">qbpp::tsp::TSPSol::print</a></div><div class="ttdeci">void print() const</div><div class="ttdoc">Print the tour.</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__tsp_8hpp_source.html#l00176">qbpp_tsp.hpp:176</a></div></div>
<div class="ttc" id="aclassqbpp_1_1tsp_1_1TSPModel_html_ab9eb1bfad69c9c02e21db6dee0e61e38"><div class="ttname"><a href="classqbpp_1_1tsp_1_1TSPModel.html#ab9eb1bfad69c9c02e21db6dee0e61e38">qbpp::tsp::TSPModel::get_fix_first</a></div><div class="ttdeci">bool get_fix_first() const</div><div class="ttdoc">Returns true if the first node is fixed to node 0.</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__tsp_8hpp_source.html#l00238">qbpp_tsp.hpp:238</a></div></div>
<div class="ttc" id="aclassqbpp_1_1tsp_1_1TSPMap_html_adf14df1ea49eaf026cf00f23cc62383f"><div class="ttname"><a href="classqbpp_1_1tsp_1_1TSPMap.html#adf14df1ea49eaf026cf00f23cc62383f">qbpp::tsp::TSPMap::grid_size</a></div><div class="ttdeci">const uint32_t grid_size</div><div class="ttdoc">Size of the grid. grid_size x grid_size coordinates are used for the map.</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__tsp_8hpp_source.html#l00033">qbpp_tsp.hpp:33</a></div></div>
<div class="ttc" id="aclassqbpp_1_1tsp_1_1TSPMap_html_a9de8188d7bf2ccb04463168265557ae7"><div class="ttname"><a href="classqbpp_1_1tsp_1_1TSPMap.html#a9de8188d7bf2ccb04463168265557ae7">qbpp::tsp::TSPMap::dist</a></div><div class="ttdeci">uint32_t dist(uint32_t i, uint32_t j) const</div><div class="ttdoc">Compute the Euclidean distance between two nodes.</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__tsp_8hpp_source.html#l00068">qbpp_tsp.hpp:68</a></div></div>
<div class="ttc" id="aclassqbpp_1_1tsp_1_1TSPQuadModel_html_af77e9c5a87a8b14aaf11ecac2da2d285"><div class="ttname"><a href="classqbpp_1_1tsp_1_1TSPQuadModel.html#af77e9c5a87a8b14aaf11ecac2da2d285">qbpp::tsp::TSPQuadModel::fix_first</a></div><div class="ttdeci">const bool fix_first</div><div class="ttdoc">true if the first node is fixed to node 0.</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__tsp_8hpp_source.html#l00106">qbpp_tsp.hpp:106</a></div></div>
<div class="ttc" id="anamespaceqbpp_1_1tsp_html_a42bd2cc57ea78b95b2fe53599c143990"><div class="ttname"><a href="namespaceqbpp_1_1tsp.html#a42bd2cc57ea78b95b2fe53599c143990">qbpp::tsp::uint32_limit</a></div><div class="ttdeci">constexpr uint32_t uint32_limit</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__tsp_8hpp_source.html#l00024">qbpp_tsp.hpp:24</a></div></div>
<div class="ttc" id="aclassqbpp_1_1tsp_1_1TSPSol_html"><div class="ttname"><a href="classqbpp_1_1tsp_1_1TSPSol.html">qbpp::tsp::TSPSol</a></div><div class="ttdoc">Class to store a Tour of the TSP.</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__tsp_8hpp_source.html#l00143">qbpp_tsp.hpp:143</a></div></div>
<div class="ttc" id="aclassqbpp_1_1tsp_1_1TSPModel_html_a46141e00a4cc6554171bbd57406132a3"><div class="ttname"><a href="classqbpp_1_1tsp_1_1TSPModel.html#a46141e00a4cc6554171bbd57406132a3">qbpp::tsp::TSPModel::fix_first</a></div><div class="ttdeci">const bool fix_first</div><div class="ttdoc">true if the first node is fixed to node 0.</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__tsp_8hpp_source.html#l00206">qbpp_tsp.hpp:206</a></div></div>
<div class="ttc" id="aclassqbpp_1_1Var_html"><div class="ttname"><a href="classqbpp_1_1Var.html">qbpp::Var</a></div><div class="ttdef"><b>Definition:</b> <a href="qbpp_8hpp_source.html#l00729">qbpp.hpp:729</a></div></div>
<div class="ttc" id="aclassqbpp_1_1tsp_1_1TSPModel_html_a1290b8facf3b542b4b239915609fbe91"><div class="ttname"><a href="classqbpp_1_1tsp_1_1TSPModel.html#a1290b8facf3b542b4b239915609fbe91">qbpp::tsp::TSPModel::get_var</a></div><div class="ttdeci">qbpp::Var get_var(uint32_t i, uint32_t j) const</div><div class="ttdoc">Get the variable at (i, j).</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__tsp_8hpp_source.html#l00235">qbpp_tsp.hpp:235</a></div></div>
<div class="ttc" id="aclassqbpp_1_1tsp_1_1TSPQuadModel_html_a9b20af5a44b761114e4f7eefe8bde336"><div class="ttname"><a href="classqbpp_1_1tsp_1_1TSPQuadModel.html#a9b20af5a44b761114e4f7eefe8bde336">qbpp::tsp::TSPQuadModel::get_var</a></div><div class="ttdeci">qbpp::Var get_var(uint32_t i, uint32_t j) const</div><div class="ttdoc">Get the variable at (i, j).</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__tsp_8hpp_source.html#l00136">qbpp_tsp.hpp:136</a></div></div>
<div class="ttc" id="aclassqbpp_1_1tsp_1_1TSPModel_html_ae351f642ac8277b8641ae322d8559fd2"><div class="ttname"><a href="classqbpp_1_1tsp_1_1TSPModel.html#ae351f642ac8277b8641ae322d8559fd2">qbpp::tsp::TSPModel::TSPModel</a></div><div class="ttdeci">TSPModel(std::tuple&lt; qbpp::QuadModel, bool, qbpp::Vector&lt; qbpp::Vector&lt; qbpp::Var &gt;&gt;&gt; &amp;&amp;tuple)</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__tsp_8hpp_source.html#l00217">qbpp_tsp.hpp:217</a></div></div>
<div class="ttc" id="aclassqbpp_1_1tsp_1_1TSPQuadModel_html_a3c1e511fe6fbd822ec7f871484cfd67a"><div class="ttname"><a href="classqbpp_1_1tsp_1_1TSPQuadModel.html#a3c1e511fe6fbd822ec7f871484cfd67a">qbpp::tsp::TSPQuadModel::node_count</a></div><div class="ttdeci">uint32_t node_count() const</div><div class="ttdoc">Returns the number of nodes in the TSP.</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__tsp_8hpp_source.html#l00133">qbpp_tsp.hpp:133</a></div></div>
<div class="ttc" id="aclassqbpp_1_1tsp_1_1DrawSimpleGraph_html_ae7df1d1a76995fe4d7d1b978f32ef67a"><div class="ttname"><a href="classqbpp_1_1tsp_1_1DrawSimpleGraph.html#ae7df1d1a76995fe4d7d1b978f32ef67a">qbpp::tsp::DrawSimpleGraph::edge_count</a></div><div class="ttdeci">uint32_t edge_count() const</div><div class="ttdoc">Get the number of edges in the graph.</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__tsp_8hpp_source.html#l00280">qbpp_tsp.hpp:280</a></div></div>
<div class="ttc" id="anamespaceqbpp_html_a47715eec8c4942d0179134ce224be6c5"><div class="ttname"><a href="namespaceqbpp.html#a47715eec8c4942d0179134ce224be6c5">qbpp::transpose</a></div><div class="ttdeci">Vector&lt; Vector&lt; Expr &gt; &gt; transpose(const Vector&lt; Vector&lt; T &gt;&gt; &amp;vec)</div><div class="ttdef"><b>Definition:</b> <a href="qbpp_8hpp_source.html#l04374">qbpp.hpp:4374</a></div></div>
<div class="ttc" id="aclassqbpp_1_1tsp_1_1TSPSol_html_aeaddec0d0368bef74ebcbda49fe863fe"><div class="ttname"><a href="classqbpp_1_1tsp_1_1TSPSol.html#aeaddec0d0368bef74ebcbda49fe863fe">qbpp::tsp::TSPSol::print_matrix</a></div><div class="ttdeci">void print_matrix() const</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__tsp_8hpp_source.html#l00186">qbpp_tsp.hpp:186</a></div></div>
<div class="ttc" id="aclassqbpp_1_1tsp_1_1TSPSol_html_a260cb897debad81d09142ee2ed559f16"><div class="ttname"><a href="classqbpp_1_1tsp_1_1TSPSol.html#a260cb897debad81d09142ee2ed559f16">qbpp::tsp::TSPSol::sol</a></div><div class="ttdeci">const Sol sol</div><div class="ttdoc">Solution of the QUBO model.</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__tsp_8hpp_source.html#l00147">qbpp_tsp.hpp:147</a></div></div>
<div class="ttc" id="anamespaceqbpp_html_aeea468454b74e8cf74539f77454bef6b"><div class="ttname"><a href="namespaceqbpp.html#aeea468454b74e8cf74539f77454bef6b">qbpp::MapList</a></div><div class="ttdeci">std::list&lt; std::pair&lt; std::variant&lt; Var, VarInt &gt;, Expr &gt; &gt; MapList</div><div class="ttdef"><b>Definition:</b> <a href="qbpp_8hpp_source.html#l00141">qbpp.hpp:141</a></div></div>
<div class="ttc" id="aclassqbpp_1_1tsp_1_1TSPMap_html_ac6e6a348dc5646adb11b4a1a6ee6eab0"><div class="ttname"><a href="classqbpp_1_1tsp_1_1TSPMap.html#ac6e6a348dc5646adb11b4a1a6ee6eab0">qbpp::tsp::TSPMap::node_count</a></div><div class="ttdeci">uint32_t node_count() const</div><div class="ttdoc">Gets the number of nodes in the map.</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__tsp_8hpp_source.html#l00088">qbpp_tsp.hpp:88</a></div></div>
<div class="ttc" id="aclassqbpp_1_1tsp_1_1TSPSol_html_a850469299d3a30962180e13657e16452"><div class="ttname"><a href="classqbpp_1_1tsp_1_1TSPSol.html#a850469299d3a30962180e13657e16452">qbpp::tsp::TSPSol::gen_tour</a></div><div class="ttdeci">static std::vector&lt; uint32_t &gt; gen_tour(const TSPQuadModel &amp;tsp_quad_model, const Sol &amp;sol)</div><div class="ttdoc">helper function to generate a tour from the solution.</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__tsp_8hpp_source.html#l00394">qbpp_tsp.hpp:394</a></div></div>
<div class="ttc" id="aclassqbpp_1_1misc_1_1RandomGenerator_html_a95308c2300aeef9a881ba97786367977"><div class="ttname"><a href="classqbpp_1_1misc_1_1RandomGenerator.html#a95308c2300aeef9a881ba97786367977">qbpp::misc::RandomGenerator::gen</a></div><div class="ttdeci">static uint64_t gen()</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__misc_8hpp_source.html#l00116">qbpp_misc.hpp:116</a></div></div>
<div class="ttc" id="aclassqbpp_1_1tsp_1_1TSPSol_html_a5c80e1d34f42b58098d9738725a6d3d8"><div class="ttname"><a href="classqbpp_1_1tsp_1_1TSPSol.html#a5c80e1d34f42b58098d9738725a6d3d8">qbpp::tsp::TSPSol::tsp_quad_model</a></div><div class="ttdeci">const TSPQuadModel tsp_quad_model</div><div class="ttdoc">QUBO expression for the Traveling Salesman Problem (TSP).</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__tsp_8hpp_source.html#l00145">qbpp_tsp.hpp:145</a></div></div>
<div class="ttc" id="aclassqbpp_1_1tsp_1_1DrawSimpleGraph_html_a2f637a33d00cbb8790512e49ccfafa76"><div class="ttname"><a href="classqbpp_1_1tsp_1_1DrawSimpleGraph.html#a2f637a33d00cbb8790512e49ccfafa76">qbpp::tsp::DrawSimpleGraph::add_node</a></div><div class="ttdeci">void add_node(std::pair&lt; int, int &gt; node, const std::string &amp;label=&quot;&quot;)</div><div class="ttdoc">Add a node to the graph.</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__tsp_8hpp_source.html#l00265">qbpp_tsp.hpp:265</a></div></div>
<div class="ttc" id="aclassqbpp_1_1tsp_1_1TSPSol_html_a06ad43713331494cb541dde344c04780"><div class="ttname"><a href="classqbpp_1_1tsp_1_1TSPSol.html#a06ad43713331494cb541dde344c04780">qbpp::tsp::TSPSol::TSPSol</a></div><div class="ttdeci">TSPSol(const TSPQuadModel &amp;tsp_quad_model, const qbpp::Sol &amp;sol)</div><div class="ttdoc">Generates a solution of the Traveling Salesman Problem (TSP) from a QUBO model and its solution.</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__tsp_8hpp_source.html#l00163">qbpp_tsp.hpp:163</a></div></div>
<div class="ttc" id="aclassqbpp_1_1Vector_html"><div class="ttname"><a href="classqbpp_1_1Vector.html">qbpp::Vector</a></div><div class="ttdef"><b>Definition:</b> <a href="qbpp_8hpp_source.html#l00056">qbpp.hpp:56</a></div></div>
<div class="ttc" id="aclassqbpp_1_1tsp_1_1TSPQuadModel_html_afa2124847942c070b4cb95ab9ae59284"><div class="ttname"><a href="classqbpp_1_1tsp_1_1TSPQuadModel.html#afa2124847942c070b4cb95ab9ae59284">qbpp::tsp::TSPQuadModel::helper_func</a></div><div class="ttdeci">std::tuple&lt; qbpp::Model, bool, qbpp::Vector&lt; qbpp::Vector&lt; qbpp::Var &gt; &gt; &gt; helper_func(const TSPMap &amp;map, bool fix_first)</div><div class="ttdoc">Generate a QUBO expression for the Traveling Salesman Problem (TSP).</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__tsp_8hpp_source.html#l00348">qbpp_tsp.hpp:348</a></div></div>
<div class="ttc" id="aclassqbpp_1_1tsp_1_1TSPModel_html_aad24b168b00baea06d9ff61cb0e1897c"><div class="ttname"><a href="classqbpp_1_1tsp_1_1TSPModel.html#aad24b168b00baea06d9ff61cb0e1897c">qbpp::tsp::TSPModel::helper_func</a></div><div class="ttdeci">std::tuple&lt; qbpp::QuadModel, bool, qbpp::Vector&lt; qbpp::Vector&lt; qbpp::Var &gt; &gt; &gt; helper_func(const TSPMap &amp;map, bool fix_first)</div><div class="ttdoc">Generate a QUBO expression for the Traveling Salesman Problem (TSP).</div></div>
<div class="ttc" id="aqbpp_8hpp_html"><div class="ttname"><a href="qbpp_8hpp.html">qbpp.hpp</a></div><div class="ttdoc">QUBO++, a C++ library for generating expressions for binary and spin variables.</div></div>
<div class="ttc" id="aclassqbpp_1_1tsp_1_1TSPMap_html_a6ca32411c34b040e6f09790f8376b0d7"><div class="ttname"><a href="classqbpp_1_1tsp_1_1TSPMap.html#a6ca32411c34b040e6f09790f8376b0d7">qbpp::tsp::TSPMap::dist</a></div><div class="ttdeci">uint32_t dist(const std::pair&lt; int32_t, int32_t &gt; &amp;p1, const std::pair&lt; int32_t, int32_t &gt; &amp;p2) const</div><div class="ttdoc">Compute the Euclidean distance between two nodes.</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__tsp_8hpp_source.html#l00057">qbpp_tsp.hpp:57</a></div></div>
<div class="ttc" id="aclassqbpp_1_1tsp_1_1TSPMap_html_a1a60e3abe1643aae812789c53f678111"><div class="ttname"><a href="classqbpp_1_1tsp_1_1TSPMap.html#a1a60e3abe1643aae812789c53f678111">qbpp::tsp::TSPMap::TSPMap</a></div><div class="ttdeci">TSPMap(uint32_t grid_size=100)</div><div class="ttdoc">Constructor: Creates an empty map.</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__tsp_8hpp_source.html#l00041">qbpp_tsp.hpp:41</a></div></div>
<div class="ttc" id="aclassqbpp_1_1tsp_1_1TSPSol_html_af5a209140f85294b3728cf54d3603781"><div class="ttname"><a href="classqbpp_1_1tsp_1_1TSPSol.html#af5a209140f85294b3728cf54d3603781">qbpp::tsp::TSPSol::tour</a></div><div class="ttdeci">const std::vector&lt; uint32_t &gt; tour</div><div class="ttdoc">A vector of nodes representing the tour.</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__tsp_8hpp_source.html#l00149">qbpp_tsp.hpp:149</a></div></div>
<div class="ttc" id="aqbpp__misc_8hpp_html"><div class="ttname"><a href="qbpp__misc_8hpp.html">qbpp_misc.hpp</a></div><div class="ttdoc">A miscellaneous library used for sample programs of the QUBO++ library.</div></div>
<div class="ttc" id="aclassqbpp_1_1tsp_1_1TSPModel_html"><div class="ttname"><a href="classqbpp_1_1tsp_1_1TSPModel.html">qbpp::tsp::TSPModel</a></div><div class="ttdoc">Class to store the QUBO expression for the Traveling Salesman Problem (TSP).</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__tsp_8hpp_source.html#l00204">qbpp_tsp.hpp:204</a></div></div>
<div class="ttc" id="aclassqbpp_1_1tsp_1_1TSPMap_html_a611e61c620a84590c1ff675cc3a6e332"><div class="ttname"><a href="classqbpp_1_1tsp_1_1TSPMap.html#a611e61c620a84590c1ff675cc3a6e332">qbpp::tsp::TSPMap::min_dist</a></div><div class="ttdeci">uint32_t min_dist(uint32_t x, uint32_t y) const</div><div class="ttdoc">Compute the minimum distance between a new node and all other nodes.</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__tsp_8hpp_source.html#l00077">qbpp_tsp.hpp:77</a></div></div>
<div class="ttc" id="aclassqbpp_1_1tsp_1_1TSPMap_html_acfc9451587483492e4f4382933f1508c"><div class="ttname"><a href="classqbpp_1_1tsp_1_1TSPMap.html#acfc9451587483492e4f4382933f1508c">qbpp::tsp::TSPMap::nodes</a></div><div class="ttdeci">std::vector&lt; std::pair&lt; int32_t, int32_t &gt; &gt; nodes</div><div class="ttdoc">List of nodes with coordinate (x, y)</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__tsp_8hpp_source.html#l00035">qbpp_tsp.hpp:35</a></div></div>
<div class="ttc" id="aclassqbpp_1_1tsp_1_1TSPQuadModel_html_a9612c7de2af2dbafb0e232124441aed5"><div class="ttname"><a href="classqbpp_1_1tsp_1_1TSPQuadModel.html#a9612c7de2af2dbafb0e232124441aed5">qbpp::tsp::TSPQuadModel::x</a></div><div class="ttdeci">const qbpp::Vector&lt; qbpp::Vector&lt; qbpp::Var &gt; &gt; x</div><div class="ttdoc">Variables for the TSP.</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__tsp_8hpp_source.html#l00108">qbpp_tsp.hpp:108</a></div></div>
<div class="ttc" id="aclassqbpp_1_1tsp_1_1TSPSol_html_a8d9b29b1cc361cc18950421e2dc2221d"><div class="ttname"><a href="classqbpp_1_1tsp_1_1TSPSol.html#a8d9b29b1cc361cc18950421e2dc2221d">qbpp::tsp::TSPSol::operator[]</a></div><div class="ttdeci">uint32_t operator[](uint32_t index) const</div><div class="ttdoc">Get the node at index in the tour.</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__tsp_8hpp_source.html#l00170">qbpp_tsp.hpp:170</a></div></div>
<div class="ttc" id="aclassqbpp_1_1tsp_1_1TSPMap_html_abb5ed5d5beb5b8c639384b42adae35d5"><div class="ttname"><a href="classqbpp_1_1tsp_1_1TSPMap.html#abb5ed5d5beb5b8c639384b42adae35d5">qbpp::tsp::TSPMap::add_node</a></div><div class="ttdeci">void add_node(uint32_t x, uint32_t y)</div><div class="ttdoc">Add a node to the map.</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__tsp_8hpp_source.html#l00050">qbpp_tsp.hpp:50</a></div></div>
<div class="ttc" id="aclassqbpp_1_1tsp_1_1TSPQuadModel_html_a3beca816eb5a9d8591a88da727d7bbf9"><div class="ttname"><a href="classqbpp_1_1tsp_1_1TSPQuadModel.html#a3beca816eb5a9d8591a88da727d7bbf9">qbpp::tsp::TSPQuadModel::get_fix_first</a></div><div class="ttdeci">bool get_fix_first() const</div><div class="ttdoc">Returns true if the first node is fixed to node 0.</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__tsp_8hpp_source.html#l00139">qbpp_tsp.hpp:139</a></div></div>
<div class="ttc" id="aclassqbpp_1_1tsp_1_1TSPModel_html_a2ea1cc5abb5a7448081a83e7355c2ce9"><div class="ttname"><a href="classqbpp_1_1tsp_1_1TSPModel.html#a2ea1cc5abb5a7448081a83e7355c2ce9">qbpp::tsp::TSPModel::node_count</a></div><div class="ttdeci">uint32_t node_count() const</div><div class="ttdoc">Returns the number of nodes in the TSP.</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__tsp_8hpp_source.html#l00232">qbpp_tsp.hpp:232</a></div></div>
<div class="ttc" id="anamespaceqbpp_html_a008af6dfb07e3e463ee6d83c547c3ff0"><div class="ttname"><a href="namespaceqbpp.html#a008af6dfb07e3e463ee6d83c547c3ff0">qbpp::vector_sum</a></div><div class="ttdeci">Expr vector_sum(const T &amp;items[[maybe_unused]])</div><div class="ttdef"><b>Definition:</b> <a href="qbpp_8hpp_source.html#l03998">qbpp.hpp:3998</a></div></div>
<div class="ttc" id="anamespaceqbpp_html"><div class="ttname"><a href="namespaceqbpp.html">qbpp</a></div><div class="ttdoc">Generates a QUBO Expression for the Graph Coloring Problem using QUBO++ library.</div><div class="ttdef"><b>Definition:</b> <a href="qbpp_8hpp_source.html#l00052">qbpp.hpp:52</a></div></div>
<div class="ttc" id="aclassqbpp_1_1tsp_1_1TSPQuadModel_html"><div class="ttname"><a href="classqbpp_1_1tsp_1_1TSPQuadModel.html">qbpp::tsp::TSPQuadModel</a></div><div class="ttdoc">Class to store the QUBO expression for the Traveling Salesman Problem (TSP).</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__tsp_8hpp_source.html#l00104">qbpp_tsp.hpp:104</a></div></div>
<div class="ttc" id="aclassqbpp_1_1tsp_1_1DrawSimpleGraph_html_a993b9fd24edfc32e994c8b242b131df6"><div class="ttname"><a href="classqbpp_1_1tsp_1_1DrawSimpleGraph.html#a993b9fd24edfc32e994c8b242b131df6">qbpp::tsp::DrawSimpleGraph::DrawSimpleGraph</a></div><div class="ttdeci">DrawSimpleGraph()=default</div><div class="ttdoc">Default constructor to create an empty graph.</div></div>
<div class="ttc" id="aclassqbpp_1_1tsp_1_1DrawSimpleGraph_html_ad6b478c8f7377c4e808411484b7ca760"><div class="ttname"><a href="classqbpp_1_1tsp_1_1DrawSimpleGraph.html#ad6b478c8f7377c4e808411484b7ca760">qbpp::tsp::DrawSimpleGraph::nodes</a></div><div class="ttdeci">std::vector&lt; std::tuple&lt; int, int, std::string &gt; &gt; nodes</div><div class="ttdoc">List of nodes with coordinate (x, y)</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__tsp_8hpp_source.html#l00247">qbpp_tsp.hpp:247</a></div></div>
<div class="ttc" id="aclassqbpp_1_1tsp_1_1DrawSimpleGraph_html_ae58cb44d06b764de7fae386abf86c0de"><div class="ttname"><a href="classqbpp_1_1tsp_1_1DrawSimpleGraph.html#ae58cb44d06b764de7fae386abf86c0de">qbpp::tsp::DrawSimpleGraph::add_node</a></div><div class="ttdeci">void add_node(int x, int y, const std::string &amp;label=&quot;&quot;)</div><div class="ttdoc">Add a node to the graph.</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__tsp_8hpp_source.html#l00259">qbpp_tsp.hpp:259</a></div></div>
<div class="ttc" id="aclassqbpp_1_1tsp_1_1TSPMap_html"><div class="ttname"><a href="classqbpp_1_1tsp_1_1TSPMap.html">qbpp::tsp::TSPMap</a></div><div class="ttdoc">Class to generates a random map for the TSP with n nodes.</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__tsp_8hpp_source.html#l00030">qbpp_tsp.hpp:30</a></div></div>
<div class="ttc" id="aclassqbpp_1_1tsp_1_1TSPQuadModel_html_af1e28b60e4d5f8454483f9aff3f86a51"><div class="ttname"><a href="classqbpp_1_1tsp_1_1TSPQuadModel.html#af1e28b60e4d5f8454483f9aff3f86a51">qbpp::tsp::TSPQuadModel::TSPQuadModel</a></div><div class="ttdeci">TSPQuadModel(std::tuple&lt; qbpp::QuadModel, bool, qbpp::Vector&lt; qbpp::Vector&lt; qbpp::Var &gt;&gt;&gt; tuple)</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__tsp_8hpp_source.html#l00117">qbpp_tsp.hpp:117</a></div></div>
<div class="ttc" id="aclassqbpp_1_1tsp_1_1TSPMap_html_ace1b4039b9447fae50b62c325acefc6e"><div class="ttname"><a href="classqbpp_1_1tsp_1_1TSPMap.html#ace1b4039b9447fae50b62c325acefc6e">qbpp::tsp::TSPMap::gen_random_map</a></div><div class="ttdeci">void gen_random_map(uint32_t n)</div><div class="ttdoc">Generate a random map with n nodes.</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__tsp_8hpp_source.html#l00323">qbpp_tsp.hpp:323</a></div></div>
<div class="ttc" id="aclassqbpp_1_1Sol_html_a3ad455870df155ffed4701ce070cf335"><div class="ttname"><a href="classqbpp_1_1Sol.html#a3ad455870df155ffed4701ce070cf335">qbpp::Sol::energy</a></div><div class="ttdeci">energy_t energy() const</div><div class="ttdef"><b>Definition:</b> <a href="qbpp_8hpp_source.html#l02299">qbpp.hpp:2299</a></div></div>
<div class="ttc" id="aclassqbpp_1_1tsp_1_1DrawSimpleGraph_html_ae4a6e9be4fa3e068d2dd4a4ace7548b5"><div class="ttname"><a href="classqbpp_1_1tsp_1_1DrawSimpleGraph.html#ae4a6e9be4fa3e068d2dd4a4ace7548b5">qbpp::tsp::DrawSimpleGraph::draw</a></div><div class="ttdeci">void draw(std::string filename)</div><div class="ttdoc">Draw the graph in a file.</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__tsp_8hpp_source.html#l00286">qbpp_tsp.hpp:286</a></div></div>
<div class="ttc" id="aclassqbpp_1_1Sol_html"><div class="ttname"><a href="classqbpp_1_1Sol.html">qbpp::Sol</a></div><div class="ttdef"><b>Definition:</b> <a href="qbpp_8hpp_source.html#l02182">qbpp.hpp:2182</a></div></div>
<div class="ttc" id="aclassqbpp_1_1tsp_1_1TSPMap_html_ae04b2ce04ee9f652f04b83ff18ce1704"><div class="ttname"><a href="classqbpp_1_1tsp_1_1TSPMap.html#ae04b2ce04ee9f652f04b83ff18ce1704">qbpp::tsp::TSPMap::get_grid_size</a></div><div class="ttdeci">uint32_t get_grid_size() const</div><div class="ttdoc">Gets the size of the grid.</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__tsp_8hpp_source.html#l00092">qbpp_tsp.hpp:92</a></div></div>
<div class="ttc" id="aqbpp_8hpp_html_a265e0828fcea8e75fbc9c0be99f7156e"><div class="ttname"><a href="qbpp_8hpp.html#a265e0828fcea8e75fbc9c0be99f7156e">THROW_MESSAGE</a></div><div class="ttdeci">#define THROW_MESSAGE(...)</div><div class="ttdef"><b>Definition:</b> <a href="qbpp_8hpp_source.html#l00091">qbpp.hpp:91</a></div></div>
<div class="ttc" id="aclassqbpp_1_1Model_html_a18d86c7c55ae188f35cfafb9cb1f8f85"><div class="ttname"><a href="classqbpp_1_1Model.html#a18d86c7c55ae188f35cfafb9cb1f8f85">qbpp::Model::expr</a></div><div class="ttdeci">const Expr &amp; expr() const</div><div class="ttdef"><b>Definition:</b> <a href="qbpp_8hpp_source.html#l01869">qbpp.hpp:1869</a></div></div>
<div class="ttc" id="aclassqbpp_1_1Vector_html_a23f6c18870445548d8db321606a0a9fb"><div class="ttname"><a href="classqbpp_1_1Vector.html#a23f6c18870445548d8db321606a0a9fb">qbpp::Vector::push_back</a></div><div class="ttdeci">void push_back(const T &amp;t)</div><div class="ttdef"><b>Definition:</b> <a href="qbpp_8hpp_source.html#l00581">qbpp.hpp:581</a></div></div>
<div class="ttc" id="aclassqbpp_1_1tsp_1_1TSPQuadModel_html_a0d47d1ade744a192570817a242475c1c"><div class="ttname"><a href="classqbpp_1_1tsp_1_1TSPQuadModel.html#a0d47d1ade744a192570817a242475c1c">qbpp::tsp::TSPQuadModel::TSPQuadModel</a></div><div class="ttdeci">TSPQuadModel(const TSPMap &amp;map, bool fix_first=false)</div><div class="ttdoc">Generate a QUBO expression for the Traveling Salesman Problem (TSP) from a map.</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__tsp_8hpp_source.html#l00128">qbpp_tsp.hpp:128</a></div></div>
<div class="ttc" id="aclassqbpp_1_1tsp_1_1TSPModel_html_a7b4d3f9919401a3ea106e318eb627d50"><div class="ttname"><a href="classqbpp_1_1tsp_1_1TSPModel.html#a7b4d3f9919401a3ea106e318eb627d50">qbpp::tsp::TSPModel::TSPModel</a></div><div class="ttdeci">TSPModel(const TSPMap &amp;map, bool fix_first=false)</div><div class="ttdoc">Generate a QUBO expression for the Traveling Salesman Problem (TSP) from a map.</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__tsp_8hpp_source.html#l00227">qbpp_tsp.hpp:227</a></div></div>
<div class="ttc" id="anamespaceqbpp_html_a9041f8ee7d3f4a7c04a0d385babae285"><div class="ttname"><a href="namespaceqbpp.html#a9041f8ee7d3f4a7c04a0d385babae285">qbpp::sum</a></div><div class="ttdeci">Expr sum(const Vector&lt; T &gt; &amp;items)</div><div class="ttdef"><b>Definition:</b> <a href="qbpp_8hpp_source.html#l03925">qbpp.hpp:3925</a></div></div>
<div class="ttc" id="aclassqbpp_1_1tsp_1_1DrawSimpleGraph_html_acd1ab585bb6256dfc7d5b44adeabd923"><div class="ttname"><a href="classqbpp_1_1tsp_1_1DrawSimpleGraph.html#acd1ab585bb6256dfc7d5b44adeabd923">qbpp::tsp::DrawSimpleGraph::node_count</a></div><div class="ttdeci">uint32_t node_count() const</div><div class="ttdoc">Get the number of nodes in the graph.</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__tsp_8hpp_source.html#l00277">qbpp_tsp.hpp:277</a></div></div>
<div class="ttc" id="aclassqbpp_1_1tsp_1_1DrawSimpleGraph_html"><div class="ttname"><a href="classqbpp_1_1tsp_1_1DrawSimpleGraph.html">qbpp::tsp::DrawSimpleGraph</a></div><div class="ttdoc">Class to draw a simple undirected graph.</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__tsp_8hpp_source.html#l00244">qbpp_tsp.hpp:244</a></div></div>
<div class="ttc" id="aclassqbpp_1_1tsp_1_1DrawSimpleGraph_html_aa2b616d4c3e3931eaca5620c03fbebb6"><div class="ttname"><a href="classqbpp_1_1tsp_1_1DrawSimpleGraph.html#aa2b616d4c3e3931eaca5620c03fbebb6">qbpp::tsp::DrawSimpleGraph::edges</a></div><div class="ttdeci">std::vector&lt; std::tuple&lt; int, int &gt; &gt; edges</div><div class="ttdoc">List of edges (node1, node2)</div><div class="ttdef"><b>Definition:</b> <a href="qbpp__tsp_8hpp_source.html#l00249">qbpp_tsp.hpp:249</a></div></div>
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.17
</small></address>
</body>
</html>
