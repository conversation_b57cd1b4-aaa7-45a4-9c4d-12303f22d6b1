<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.17"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>QUBO++ Library with QUBO Solver APIs: include/qbpp_easy_solver.hpp File Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/x-mathjax-config">
  MathJax.Hub.Config({
    extensions: ["tex2jax.js", "TeX/AMSmath.js", "TeX/AMSsymbols.js"],
    jax: ["input/TeX","output/HTML-CSS"],
});
</script>
<script type="text/javascript" async="async" src="https://cdn.jsdelivr.net/npm/mathjax@2/MathJax.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">QUBO++ Library with QUBO Solver APIs
   </div>
   <div id="projectbrief">Author: Koji Nakano, License: Non-commercial research and evaluation purposes without any guarantees.</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.17 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_d44c64559bbebec7f509842c48db8b23.html">include</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#nested-classes">Classes</a> &#124;
<a href="#namespaces">Namespaces</a> &#124;
<a href="#typedef-members">Typedefs</a> &#124;
<a href="#func-members">Functions</a>  </div>
  <div class="headertitle">
<div class="title">qbpp_easy_solver.hpp File Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p>Easy QUBO Solver for solving QUBO problems.  
<a href="#details">More...</a></p>
<div class="textblock"><code>#include &lt;tbb/blocked_range.h&gt;</code><br />
<code>#include &lt;tbb/parallel_for.h&gt;</code><br />
<code>#include &lt;boost/circular_buffer.hpp&gt;</code><br />
<code>#include &lt;limits&gt;</code><br />
<code>#include &lt;random&gt;</code><br />
<code>#include &lt;set&gt;</code><br />
<code>#include &lt;thread&gt;</code><br />
<code>#include &lt;vector&gt;</code><br />
<code>#include &quot;<a class="el" href="qbpp_8hpp_source.html">qbpp.hpp</a>&quot;</code><br />
<code>#include &quot;<a class="el" href="qbpp__misc_8hpp_source.html">qbpp_misc.hpp</a>&quot;</code><br />
<code>#include &quot;xxhash.h&quot;</code><br />
</div><div class="textblock"><div class="dynheader">
Include dependency graph for qbpp_easy_solver.hpp:</div>
<div class="dyncontent">
<div class="center"><img src="qbpp__easy__solver_8hpp__incl.png" border="0" usemap="#include_2qbpp__easy__solver_8hpp" alt=""/></div>
<map name="include_2qbpp__easy__solver_8hpp" id="include_2qbpp__easy__solver_8hpp">
<area shape="rect" title="Easy QUBO Solver for solving QUBO problems." alt="" coords="2135,5,2331,32"/>
<area shape="rect" title=" " alt="" coords="5,251,144,278"/>
<area shape="rect" title=" " alt="" coords="168,251,285,278"/>
<area shape="rect" title=" " alt="" coords="2151,162,2315,189"/>
<area shape="rect" title=" " alt="" coords="309,251,363,278"/>
<area shape="rect" title=" " alt="" coords="2339,162,2405,189"/>
<area shape="rect" title=" " alt="" coords="2429,162,2469,189"/>
<area shape="rect" title=" " alt="" coords="387,251,445,278"/>
<area shape="rect" title=" " alt="" coords="3009,251,3068,278"/>
<area shape="rect" href="qbpp_8hpp.html" title="QUBO++, a C++ library for generating expressions for binary and spin variables." alt="" coords="2001,162,2076,189"/>
<area shape="rect" href="qbpp__misc_8hpp.html" title="A miscellaneous library used for sample programs of the QUBO++ library." alt="" coords="2451,80,2562,107"/>
<area shape="rect" title=" " alt="" coords="2587,80,2661,107"/>
<area shape="rect" title=" " alt="" coords="2685,80,2755,107"/>
<area shape="rect" title=" " alt="" coords="2779,80,2843,107"/>
<area shape="rect" title=" " alt="" coords="555,251,698,278"/>
<area shape="rect" title=" " alt="" coords="723,251,848,278"/>
<area shape="rect" title=" " alt="" coords="3092,251,3167,278"/>
<area shape="rect" title=" " alt="" coords="872,244,1019,285"/>
<area shape="rect" title=" " alt="" coords="1043,244,1189,285"/>
<area shape="rect" title=" " alt="" coords="1214,251,1402,278"/>
<area shape="rect" title=" " alt="" coords="1426,251,1571,278"/>
<area shape="rect" title=" " alt="" coords="1596,244,1735,285"/>
<area shape="rect" title=" " alt="" coords="1759,251,1820,278"/>
<area shape="rect" title=" " alt="" coords="3191,251,3249,278"/>
<area shape="rect" title=" " alt="" coords="1845,251,1942,278"/>
<area shape="rect" title=" " alt="" coords="1967,251,2033,278"/>
<area shape="rect" title=" " alt="" coords="3273,251,3345,278"/>
<area shape="rect" title=" " alt="" coords="2058,251,2097,278"/>
<area shape="rect" title=" " alt="" coords="2121,251,2191,278"/>
<area shape="rect" title=" " alt="" coords="2215,251,2273,278"/>
<area shape="rect" title=" " alt="" coords="2297,251,2364,278"/>
<area shape="rect" title=" " alt="" coords="2388,251,2457,278"/>
<area shape="rect" title=" " alt="" coords="2482,251,2537,278"/>
<area shape="rect" title=" " alt="" coords="2561,251,2644,278"/>
<area shape="rect" title=" " alt="" coords="2669,251,2779,278"/>
<area shape="rect" title=" " alt="" coords="2804,251,2908,278"/>
<area shape="rect" title=" " alt="" coords="2932,251,2985,278"/>
<area shape="rect" title=" " alt="" coords="469,251,531,278"/>
<area shape="rect" title=" " alt="" coords="2696,162,2757,189"/>
<area shape="rect" title=" " alt="" coords="2782,162,2951,189"/>
<area shape="rect" title=" " alt="" coords="2975,155,3123,196"/>
<area shape="rect" title=" " alt="" coords="3147,155,3295,196"/>
<area shape="rect" title=" " alt="" coords="3319,162,3387,189"/>
</map>
</div>
</div><div class="textblock"><div class="dynheader">
This graph shows which files directly or indirectly include this file:</div>
<div class="dyncontent">
<div class="center"><img src="qbpp__easy__solver_8hpp__dep__incl.png" border="0" usemap="#include_2qbpp__easy__solver_8hppdep" alt=""/></div>
<map name="include_2qbpp__easy__solver_8hppdep" id="include_2qbpp__easy__solver_8hppdep">
<area shape="rect" title="Easy QUBO Solver for solving QUBO problems." alt="" coords="607,5,803,32"/>
<area shape="rect" href="nqueen__easy_8cpp.html" title="Solves the N&#45;Queens problem using ABS2 QUBO solver from QUBO++ library." alt="" coords="5,87,173,114"/>
<area shape="rect" href="partition__easy_8cpp.html" title="Solves the Partitioning problem using the QUBO++ Easy solver." alt="" coords="198,87,370,114"/>
<area shape="rect" href="graph__color__easy_8cpp.html" title="Solves randomly generated Graph Coloring Problem using QUBO++ Easy Solver." alt="" coords="394,80,529,121"/>
<area shape="rect" href="ilp__easy_8cpp.html" title="Solves an Integer Linear Programming (ILP) problem using EasySolver through QUBO++ library." alt="" coords="553,87,692,114"/>
<area shape="rect" href="tsp__easy_8cpp.html" title="Solves randomly generated Traveling Salesman Problem (TSP) using the QUBO++ Easy solver." alt="" coords="716,87,860,114"/>
<area shape="rect" href="simple__factorization__easy_8cpp.html" title="Simple factorization example using EasySolver." alt="" coords="884,80,1068,121"/>
<area shape="rect" href="factorization_8cpp.html" title="Solves factorization and multiplication problems for two prime numbers using multiple QUBO solvers." alt="" coords="1093,87,1257,114"/>
<area shape="rect" href="bin__packing__easy_8cpp.html" title="This file contains an example of solving the bin packing problem using the EasySolver." alt="" coords="1281,80,1418,121"/>
</map>
</div>
</div>
<p><a href="qbpp__easy__solver_8hpp_source.html">Go to the source code of this file.</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="nested-classes"></a>
Classes</h2></td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">struct &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structqbpp_1_1easy__solver_1_1SolHash.html">qbpp::easy_solver::SolHash</a></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">class &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classqbpp_1_1easy__solver_1_1BestSols.html">qbpp::easy_solver::BestSols</a></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">class &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classqbpp_1_1easy__solver_1_1SolDelta.html">qbpp::easy_solver::SolDelta</a></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">class &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classqbpp_1_1easy__solver_1_1TabuSolDelta.html">qbpp::easy_solver::TabuSolDelta</a></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">class &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classqbpp_1_1easy__solver_1_1PosMinSolDelta.html">qbpp::easy_solver::PosMinSolDelta</a></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">class &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classqbpp_1_1easy__solver_1_1EasySolver.html">qbpp::easy_solver::EasySolver</a></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="namespaces"></a>
Namespaces</h2></td></tr>
<tr class="memitem:namespaceqbpp"><td class="memItemLeft" align="right" valign="top"> &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html">qbpp</a></td></tr>
<tr class="memdesc:namespaceqbpp"><td class="mdescLeft">&#160;</td><td class="mdescRight">Generates a QUBO Expression for the Graph Coloring Problem using QUBO++ library. <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:namespaceqbpp_1_1easy__solver"><td class="memItemLeft" align="right" valign="top"> &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp_1_1easy__solver.html">qbpp::easy_solver</a></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="typedef-members"></a>
Typedefs</h2></td></tr>
<tr class="memitem:ae5027899639f91d80883ae1ca2cf1ddd"><td class="memItemLeft" align="right" valign="top">using&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp_1_1easy__solver.html#ae5027899639f91d80883ae1ca2cf1ddd">qbpp::easy_solver::HashType</a> = uint64_t</td></tr>
<tr class="separator:ae5027899639f91d80883ae1ca2cf1ddd"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:a99d432f46cb5703bcf15a858bc58c742"><td class="memItemLeft" align="right" valign="top">HashType&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp_1_1easy__solver.html#a99d432f46cb5703bcf15a858bc58c742">qbpp::easy_solver::bit_vector_hash</a> (const <a class="el" href="classqbpp_1_1impl_1_1BitVector.html">qbpp::impl::BitVector</a> &amp;bit_vector)</td></tr>
<tr class="separator:a99d432f46cb5703bcf15a858bc58c742"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p>Easy QUBO Solver for solving QUBO problems. </p>
<dl class="section author"><dt>Author</dt><dd>Koji Nakano</dd></dl>
<p>EasySolver is a simple QUBO solver that utilizes a greedy algorithm and the Positive Min algorithm with Tabu search. Despite its simplicity, the algorithm is highly effective at escaping local minima. This solver is designed primarily for testing the QUBO++ library, as well as for experimentation, evaluation, and learning purposes. For more details on the Positive Min algorithm, please refer to the following paper:</p><ul>
<li>Hiroshi Kagawa et al., "High-throughput FPGA implementation for
  quadratic unconstrained binary optimization", Concurr. Comput. Pract. Exp. 35(14) (2023), <a href="https://doi.org/10.1002/cpe.6565">https://doi.org/10.1002/cpe.6565</a></li>
</ul>
<dl class="section copyright"><dt>Copyright</dt><dd>2025, Koji Nakano </dd></dl>
<dl class="section version"><dt>Version</dt><dd>2025-04-30 </dd></dl>

<p class="definition">Definition in file <a class="el" href="qbpp__easy__solver_8hpp_source.html">qbpp_easy_solver.hpp</a>.</p>
</div></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.17
</small></address>
</body>
</html>
