<map id="include/qbpp_easy_solver.hpp" name="include/qbpp_easy_solver.hpp">
<area shape="rect" id="node1" title="Easy QUBO Solver for solving QUBO problems." alt="" coords="607,5,803,32"/>
<area shape="rect" id="node2" href="$nqueen__easy_8cpp.html" title="Solves the N&#45;Queens problem using ABS2 QUBO solver from QUBO++ library." alt="" coords="5,87,173,114"/>
<area shape="rect" id="node3" href="$partition__easy_8cpp.html" title="Solves the Partitioning problem using the QUBO++ Easy solver." alt="" coords="198,87,370,114"/>
<area shape="rect" id="node4" href="$graph__color__easy_8cpp.html" title="Solves randomly generated Graph Coloring Problem using QUBO++ Easy Solver." alt="" coords="394,80,529,121"/>
<area shape="rect" id="node5" href="$ilp__easy_8cpp.html" title="Solves an Integer Linear Programming (ILP) problem using EasySolver through QUBO++ library." alt="" coords="553,87,692,114"/>
<area shape="rect" id="node6" href="$tsp__easy_8cpp.html" title="Solves randomly generated Traveling Salesman Problem (TSP) using the QUBO++ Easy solver." alt="" coords="716,87,860,114"/>
<area shape="rect" id="node7" href="$simple__factorization__easy_8cpp.html" title="Simple factorization example using EasySolver." alt="" coords="884,80,1068,121"/>
<area shape="rect" id="node8" href="$factorization_8cpp.html" title="Solves factorization and multiplication problems for two prime numbers using multiple QUBO solvers." alt="" coords="1093,87,1257,114"/>
<area shape="rect" id="node9" href="$bin__packing__easy_8cpp.html" title="This file contains an example of solving the bin packing problem using the EasySolver." alt="" coords="1281,80,1418,121"/>
</map>
