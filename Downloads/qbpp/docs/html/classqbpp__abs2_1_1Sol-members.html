<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.17"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>QUBO++ Library with QUBO Solver APIs: Member List</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/x-mathjax-config">
  MathJax.Hub.Config({
    extensions: ["tex2jax.js", "TeX/AMSmath.js", "TeX/AMSsymbols.js"],
    jax: ["input/TeX","output/HTML-CSS"],
});
</script>
<script type="text/javascript" async="async" src="https://cdn.jsdelivr.net/npm/mathjax@2/MathJax.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">QUBO++ Library with QUBO Solver APIs
   </div>
   <div id="projectbrief">Author: Koji Nakano, License: Non-commercial research and evaluation purposes without any guarantees.</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.17 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="namespaceqbpp__abs2.html">qbpp_abs2</a></li><li class="navelem"><a class="el" href="classqbpp__abs2_1_1Sol.html">Sol</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">qbpp_abs2::Sol Member List</div>  </div>
</div><!--header-->
<div class="contents">

<p>This is the complete list of members for <a class="el" href="classqbpp__abs2_1_1Sol.html">qbpp_abs2::Sol</a>, including all inherited members.</p>
<table class="directory">
  <tr class="even"><td class="entry"><a class="el" href="classqbpp__abs2_1_1Sol.html#a2979356a3719cec1699dab0ba5c80ea7">abs2sol_ptr</a></td><td class="entry"><a class="el" href="classqbpp__abs2_1_1Sol.html">qbpp_abs2::Sol</a></td><td class="entry"><span class="mlabel">private</span></td></tr>
  <tr><td class="entry"><a class="el" href="classqbpp_1_1Sol.html#aa8b89bea21691be375e3e33d152df3b8">add_energy</a>(energy_t energy)</td><td class="entry"><a class="el" href="classqbpp_1_1Sol.html">qbpp::Sol</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classqbpp_1_1Sol.html#aae14179040e70ddf134c560790b602aa">bit_vector</a>() const</td><td class="entry"><a class="el" href="classqbpp_1_1Sol.html">qbpp::Sol</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr><td class="entry"><a class="el" href="classqbpp_1_1Sol.html#af2b8d3ee4c5097b2b44306c05dd0a6d1">bit_vector</a>()</td><td class="entry"><a class="el" href="classqbpp_1_1Sol.html">qbpp::Sol</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classqbpp_1_1Sol.html#a97a3c5acd6aff38c15164146e453b2bf">bit_vector_</a></td><td class="entry"><a class="el" href="classqbpp_1_1Sol.html">qbpp::Sol</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr><td class="entry"><a class="el" href="classqbpp_1_1Sol.html#a78255413d025557d55c0c16f4076f9fe">bit_vector_flip</a>(vindex_t index)</td><td class="entry"><a class="el" href="classqbpp_1_1Sol.html">qbpp::Sol</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classqbpp_1_1Sol.html#a8acf56be292028a9601a6d7a3517ba7b">clear</a>()</td><td class="entry"><a class="el" href="classqbpp_1_1Sol.html">qbpp::Sol</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr><td class="entry"><a class="el" href="classqbpp_1_1Sol.html#a90c464b3df40d670646cb6f940462275">comp_energy</a>() const</td><td class="entry"><a class="el" href="classqbpp_1_1Sol.html">qbpp::Sol</a></td><td class="entry"><span class="mlabel">inline</span><span class="mlabel">protected</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classqbpp_1_1Sol.html#a776c4f721be7e67bc2c801c126989953">constant</a>() const</td><td class="entry"><a class="el" href="classqbpp_1_1Sol.html">qbpp::Sol</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr><td class="entry"><a class="el" href="classqbpp_1_1Sol.html#a3ad455870df155ffed4701ce070cf335">energy</a>() const</td><td class="entry"><a class="el" href="classqbpp_1_1Sol.html">qbpp::Sol</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classqbpp_1_1Sol.html#a5dc3ec6002a1d823ad1bd525e9f6fa3e">energy_</a></td><td class="entry"><a class="el" href="classqbpp_1_1Sol.html">qbpp::Sol</a></td><td class="entry"><span class="mlabel">mutable</span><span class="mlabel">protected</span></td></tr>
  <tr><td class="entry"><a class="el" href="classqbpp_1_1Sol.html#a1407e31d02d063a89ea14c3872bdae26">energy_has_value</a>() const</td><td class="entry"><a class="el" href="classqbpp_1_1Sol.html">qbpp::Sol</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classqbpp_1_1Sol.html#a4f4c097837ad52f4fd368e048e79aa66">flip</a>(vindex_t index)</td><td class="entry"><a class="el" href="classqbpp_1_1Sol.html">qbpp::Sol</a></td><td class="entry"><span class="mlabel">inline</span><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classqbpp_1_1Sol.html#aae6d875ede912bcbf6254b74ffc66018">flip_bit_add_delta</a>(vindex_t index, energy_t delta)</td><td class="entry"><a class="el" href="classqbpp_1_1Sol.html">qbpp::Sol</a></td><td class="entry"><span class="mlabel">inline</span><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classqbpp_1_1Sol.html#abdb4f6b779678720715f7845f264440f">get</a>(vindex_t index) const</td><td class="entry"><a class="el" href="classqbpp_1_1Sol.html">qbpp::Sol</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr><td class="entry"><a class="el" href="classqbpp_1_1Sol.html#a6543e96e459b50574cb17643c961b93a">get</a>(Var var) const</td><td class="entry"><a class="el" href="classqbpp_1_1Sol.html">qbpp::Sol</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classqbpp_1_1Sol.html#a427f81eb5091b76b4f197f3c42b5a1c5">get</a>(const Expr &amp;expr) const</td><td class="entry"><a class="el" href="classqbpp_1_1Sol.html">qbpp::Sol</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr><td class="entry"><a class="el" href="classqbpp_1_1Sol.html#a97b2220c9849ce534280e5a54944a54b">get</a>(const Vector&lt; T &gt; &amp;vec) const</td><td class="entry"><a class="el" href="classqbpp_1_1Sol.html">qbpp::Sol</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classqbpp__abs2_1_1Sol.html#a8de3bebb198a0820db0446f9686e914d">get_abs2sol_ptr</a>() const</td><td class="entry"><a class="el" href="classqbpp__abs2_1_1Sol.html">qbpp_abs2::Sol</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr><td class="entry"><a class="el" href="classqbpp_1_1Sol.html#a8755c0c3c5f2da04d9f5fc7ad031d1d4">get_map_list</a>() const</td><td class="entry"><a class="el" href="classqbpp_1_1Sol.html">qbpp::Sol</a></td><td class="entry"><span class="mlabel">inline</span><span class="mlabel">protected</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classqbpp__abs2_1_1Sol.html#ae35741170c9c1e91c8c700221fb053e2">get_tts</a>() const</td><td class="entry"><a class="el" href="classqbpp__abs2_1_1Sol.html">qbpp_abs2::Sol</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr><td class="entry"><a class="el" href="classqbpp_1_1Sol.html#a3db07b94f467d91e0be73e15fe5bd453">get_var</a>(vindex_t index) const</td><td class="entry"><a class="el" href="classqbpp_1_1Sol.html">qbpp::Sol</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classqbpp_1_1Sol.html#a62ab1f54864dd39a79cfd1a3cb4a838c">has</a>(Var var) const</td><td class="entry"><a class="el" href="classqbpp_1_1Sol.html">qbpp::Sol</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr><td class="entry"><a class="el" href="classqbpp_1_1Sol.html#a24f5a53fa68071dd9bcda910533bbad7">index</a>(Var var) const</td><td class="entry"><a class="el" href="classqbpp_1_1Sol.html">qbpp::Sol</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classqbpp_1_1Sol.html#aa8c0a0717fbba880622a517e80bb86c7">is_null</a>() const</td><td class="entry"><a class="el" href="classqbpp_1_1Sol.html">qbpp::Sol</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr><td class="entry"><a class="el" href="classqbpp_1_1Sol.html#a16e5cbcab9500ff5bf032b6cb5ec5e8d">nullify</a>()</td><td class="entry"><a class="el" href="classqbpp_1_1Sol.html">qbpp::Sol</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classqbpp_1_1Sol.html#a3e3cf991914a1e6743a4e3e0b987f2ec">operator MapList</a>() const</td><td class="entry"><a class="el" href="classqbpp_1_1Sol.html">qbpp::Sol</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr><td class="entry"><a class="el" href="classqbpp_1_1Sol.html#a1325a24d1d0d1a51223f2ef02608cc3e">operator&lt;</a>(const Sol &amp;sol) const</td><td class="entry"><a class="el" href="classqbpp_1_1Sol.html">qbpp::Sol</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classqbpp_1_1Sol.html#a55ecdaaa939441fb5dbf2f00b4f3d9e0">operator=</a>(const Sol &amp;sol)</td><td class="entry"><a class="el" href="classqbpp_1_1Sol.html">qbpp::Sol</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr><td class="entry"><a class="el" href="classqbpp_1_1Sol.html#a35c53b70c6918b46bb857f36c8d0f921">operator=</a>(Sol &amp;&amp;sol)</td><td class="entry"><a class="el" href="classqbpp_1_1Sol.html">qbpp::Sol</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classqbpp_1_1Sol.html#ae30384ae030eaf5b08209f857ae14e97">operator==</a>(const Sol &amp;sol) const</td><td class="entry"><a class="el" href="classqbpp_1_1Sol.html">qbpp::Sol</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr><td class="entry"><a class="el" href="classqbpp_1_1Sol.html#a2a8846783606a331e5892f4ae022bf86">operator[]</a>(size_t i) const</td><td class="entry"><a class="el" href="classqbpp_1_1Sol.html">qbpp::Sol</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classqbpp_1_1Sol.html#aade76db5219653d4c8f89c8971dcf35c">operator[]</a>(size_t i)</td><td class="entry"><a class="el" href="classqbpp_1_1Sol.html">qbpp::Sol</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr><td class="entry"><a class="el" href="classqbpp_1_1Sol.html#a12f17cd3af83d8fab14667f103f5f21e">popcount</a>() const</td><td class="entry"><a class="el" href="classqbpp_1_1Sol.html">qbpp::Sol</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classqbpp__abs2_1_1Sol.html#a32d5e94f5ec2e824cbb8669e5bb1c69a">print</a>(const std::string &amp;attrs) const</td><td class="entry"><a class="el" href="classqbpp__abs2_1_1Sol.html">qbpp_abs2::Sol</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr><td class="entry"><a class="el" href="classqbpp_1_1Sol.html#a11752b0b3742bec8da842db87ba94230">quad_model_</a></td><td class="entry"><a class="el" href="classqbpp_1_1Sol.html">qbpp::Sol</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classqbpp__abs2_1_1Sol.html#a48d9116f9fcbe00f84640847ee49df23">set</a>(int index, bool value)</td><td class="entry"><a class="el" href="classqbpp__abs2_1_1Sol.html">qbpp_abs2::Sol</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr><td class="entry"><a class="el" href="classqbpp__abs2_1_1Sol.html#a04aabe5387a17b86a90acaa607175545">set</a>(qbpp::Var var, bool value)</td><td class="entry"><a class="el" href="classqbpp__abs2_1_1Sol.html">qbpp_abs2::Sol</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classqbpp_1_1Sol.html#a2f7494eb4a254dd19b0261e801bacffb">qbpp::Sol::set</a>(vindex_t index, bool value)</td><td class="entry"><a class="el" href="classqbpp_1_1Sol.html">qbpp::Sol</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr><td class="entry"><a class="el" href="classqbpp_1_1Sol.html#a0a976747032bda528cc78f2400f601d8">qbpp::Sol::set</a>(const Sol &amp;sol)</td><td class="entry"><a class="el" href="classqbpp_1_1Sol.html">qbpp::Sol</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classqbpp_1_1Sol.html#ae753a79a37102490b4818c3e04d9348d">qbpp::Sol::set</a>(const MapList &amp;map_list)</td><td class="entry"><a class="el" href="classqbpp_1_1Sol.html">qbpp::Sol</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr><td class="entry"><a class="el" href="classqbpp_1_1Sol.html#a27f590351140f1f04049f1500b7ada31">set64</a>(vindex_t index64, uint64_t value)</td><td class="entry"><a class="el" href="classqbpp_1_1Sol.html">qbpp::Sol</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classqbpp_1_1Sol.html#a16504ce467c6fbfcefea4171a492bef1">set_energy</a>(energy_t energy)</td><td class="entry"><a class="el" href="classqbpp_1_1Sol.html">qbpp::Sol</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr><td class="entry"><a class="el" href="group__qbpp__abs2.html#gaba8e35f84b2f822cd2633b61358370db">Sol</a>(const QuadModel &amp;quad_model, const abs2::Sol &amp;sol)</td><td class="entry"><a class="el" href="classqbpp__abs2_1_1Sol.html">qbpp_abs2::Sol</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="group__qbpp__abs2.html#gac27ae911e72fdeb424a950d41fbef95c">Sol</a>(const qbpp::Sol &amp;sol)</td><td class="entry"><a class="el" href="classqbpp__abs2_1_1Sol.html">qbpp_abs2::Sol</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr><td class="entry"><a class="el" href="classqbpp__abs2_1_1Sol.html#ac7b3defb0e488e8570785400889f4c77">Sol</a>(const Sol &amp;sol)=default</td><td class="entry"><a class="el" href="classqbpp__abs2_1_1Sol.html">qbpp_abs2::Sol</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classqbpp_1_1Sol.html#afb26ebcc595aa1391f18c2968713fdba">qbpp::Sol::Sol</a>()=delete</td><td class="entry"><a class="el" href="classqbpp_1_1Sol.html">qbpp::Sol</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr><td class="entry"><a class="el" href="classqbpp_1_1Sol.html#a6ea6682ca2e2b26a2abd4fa3d14f8645">qbpp::Sol::Sol</a>(Sol &amp;&amp;sol)=default</td><td class="entry"><a class="el" href="classqbpp_1_1Sol.html">qbpp::Sol</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classqbpp_1_1Sol.html#a4ec6ba1b6e23f124af7f6647b22e5df4">qbpp::Sol::Sol</a>(const QuadModel &amp;quad_model)</td><td class="entry"><a class="el" href="classqbpp_1_1Sol.html">qbpp::Sol</a></td><td class="entry"><span class="mlabel">inline</span><span class="mlabel">explicit</span></td></tr>
  <tr><td class="entry"><a class="el" href="classqbpp_1_1Sol.html#a8ba5c3a0ee5047188467e56382851f8f">qbpp::Sol::Sol</a>(const QuadModel &amp;quad_model, const impl::BitVector &amp;bit_vector)</td><td class="entry"><a class="el" href="classqbpp_1_1Sol.html">qbpp::Sol</a></td><td class="entry"><span class="mlabel">inline</span><span class="mlabel">explicit</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classqbpp_1_1Sol.html#a06cbfd519670efbc74363592d5e826b2">var_count</a>() const</td><td class="entry"><a class="el" href="classqbpp_1_1Sol.html">qbpp::Sol</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr><td class="entry"><a class="el" href="classqbpp_1_1Sol.html#aa30d20ad2e694414af861b297ca60c2a">~Sol</a>()=default</td><td class="entry"><a class="el" href="classqbpp_1_1Sol.html">qbpp::Sol</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
</table></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.17
</small></address>
</body>
</html>
