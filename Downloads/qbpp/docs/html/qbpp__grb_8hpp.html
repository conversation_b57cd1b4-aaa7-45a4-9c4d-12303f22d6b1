<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.17"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>QUBO++ Library with QUBO Solver APIs: include/qbpp_grb.hpp File Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/x-mathjax-config">
  MathJax.Hub.Config({
    extensions: ["tex2jax.js", "TeX/AMSmath.js", "TeX/AMSsymbols.js"],
    jax: ["input/TeX","output/HTML-CSS"],
});
</script>
<script type="text/javascript" async="async" src="https://cdn.jsdelivr.net/npm/mathjax@2/MathJax.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">QUBO++ Library with QUBO Solver APIs
   </div>
   <div id="projectbrief">Author: Koji Nakano, License: Non-commercial research and evaluation purposes without any guarantees.</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.17 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_d44c64559bbebec7f509842c48db8b23.html">include</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#nested-classes">Classes</a> &#124;
<a href="#namespaces">Namespaces</a> &#124;
<a href="#define-members">Macros</a>  </div>
  <div class="headertitle">
<div class="title">qbpp_grb.hpp File Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p>QUBO++ interface to call Gurobi Optimizer.  
<a href="#details">More...</a></p>
<div class="textblock"><code>#include &lt;cmath&gt;</code><br />
<code>#include &quot;<a class="el" href="abs2_8hpp_source.html">abs2.hpp</a>&quot;</code><br />
<code>#include &quot;gurobi_c++.h&quot;</code><br />
<code>#include &quot;<a class="el" href="qbpp_8hpp_source.html">qbpp.hpp</a>&quot;</code><br />
</div><div class="textblock"><div class="dynheader">
Include dependency graph for qbpp_grb.hpp:</div>
<div class="dyncontent">
<div class="center"><img src="qbpp__grb_8hpp__incl.png" border="0" usemap="#include_2qbpp__grb_8hpp" alt=""/></div>
<map name="include_2qbpp__grb_8hpp" id="include_2qbpp__grb_8hpp">
<area shape="rect" title="QUBO++ interface to call Gurobi Optimizer." alt="" coords="695,5,841,32"/>
<area shape="rect" title=" " alt="" coords="5,162,64,189"/>
<area shape="rect" href="abs2_8hpp.html" title="API for the ABS2 GPU QUBO Solver." alt="" coords="224,80,299,107"/>
<area shape="rect" title=" " alt="" coords="1295,80,1393,107"/>
<area shape="rect" href="qbpp_8hpp.html" title="QUBO++, a C++ library for generating expressions for binary and spin variables." alt="" coords="1196,80,1271,107"/>
<area shape="rect" title=" " alt="" coords="88,162,160,189"/>
<area shape="rect" title=" " alt="" coords="184,162,253,189"/>
<area shape="rect" title=" " alt="" coords="278,162,333,189"/>
<area shape="rect" title=" " alt="" coords="1555,162,1693,189"/>
<area shape="rect" title=" " alt="" coords="1717,162,1835,189"/>
<area shape="rect" title=" " alt="" coords="1858,162,2001,189"/>
<area shape="rect" title=" " alt="" coords="2025,162,2151,189"/>
<area shape="rect" title=" " alt="" coords="2175,162,2249,189"/>
<area shape="rect" title=" " alt="" coords="2273,155,2420,196"/>
<area shape="rect" title=" " alt="" coords="2444,155,2591,196"/>
<area shape="rect" title=" " alt="" coords="2615,162,2803,189"/>
<area shape="rect" title=" " alt="" coords="2827,162,2973,189"/>
<area shape="rect" title=" " alt="" coords="2997,155,3136,196"/>
<area shape="rect" title=" " alt="" coords="3160,162,3221,189"/>
<area shape="rect" title=" " alt="" coords="3246,162,3343,189"/>
<area shape="rect" title=" " alt="" coords="357,162,424,189"/>
<area shape="rect" title=" " alt="" coords="448,162,501,189"/>
<area shape="rect" title=" " alt="" coords="526,162,565,189"/>
<area shape="rect" title=" " alt="" coords="589,162,648,189"/>
<area shape="rect" title=" " alt="" coords="672,162,739,189"/>
<area shape="rect" title=" " alt="" coords="763,162,832,189"/>
<area shape="rect" title=" " alt="" coords="856,162,915,189"/>
<area shape="rect" title=" " alt="" coords="939,162,1021,189"/>
<area shape="rect" title=" " alt="" coords="1046,162,1157,189"/>
<area shape="rect" title=" " alt="" coords="1181,162,1285,189"/>
<area shape="rect" title=" " alt="" coords="1309,162,1363,189"/>
<area shape="rect" title=" " alt="" coords="1387,162,1448,189"/>
<area shape="rect" title=" " alt="" coords="1472,162,1531,189"/>
</map>
</div>
</div><div class="textblock"><div class="dynheader">
This graph shows which files directly or indirectly include this file:</div>
<div class="dyncontent">
<div class="center"><img src="qbpp__grb_8hpp__dep__incl.png" border="0" usemap="#include_2qbpp__grb_8hppdep" alt=""/></div>
<map name="include_2qbpp__grb_8hppdep" id="include_2qbpp__grb_8hppdep">
<area shape="rect" title="QUBO++ interface to call Gurobi Optimizer." alt="" coords="315,5,460,32"/>
<area shape="rect" href="graph__color__grb_8cpp.html" title="Solves randomly generated Graph Coloring Problem using QUBO++ Gurobi Optimizer." alt="" coords="5,80,140,121"/>
<area shape="rect" href="ilp__grb_8cpp.html" title="Solves an Integer Linear Programming (ILP) problem using Gurobi Optimizer through QUBO++ library." alt="" coords="165,87,295,114"/>
<area shape="rect" href="tsp__grb_8cpp.html" title="Solves randomly generated Traveling Salesman Problem (TSP) using the QUBO++ Easy solver." alt="" coords="319,87,455,114"/>
<area shape="rect" href="simple__factorization__grb_8cpp.html" title="Simple factorization example using Gurobi Optimizer." alt="" coords="479,80,663,121"/>
<area shape="rect" href="factorization_8cpp.html" title="Solves factorization and multiplication problems for two prime numbers using multiple QUBO solvers." alt="" coords="688,87,852,114"/>
</map>
</div>
</div>
<p><a href="qbpp__grb_8hpp_source.html">Go to the source code of this file.</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="nested-classes"></a>
Classes</h2></td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">class &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classqbpp__grb_1_1QuadModel.html">qbpp_grb::QuadModel</a></td></tr>
<tr class="memdesc:"><td class="mdescLeft">&#160;</td><td class="mdescRight">Class to store a QUBO model using Gurobi Optimizer through QUBO++ library.  <a href="classqbpp__grb_1_1QuadModel.html#details">More...</a><br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">class &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classqbpp__grb_1_1Sol.html">qbpp_grb::Sol</a></td></tr>
<tr class="memdesc:"><td class="mdescLeft">&#160;</td><td class="mdescRight">Class to store a solution of a QUBO model using Gurobi Optimizer.  <a href="classqbpp__grb_1_1Sol.html#details">More...</a><br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">class &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classqbpp__grb_1_1Callback.html">qbpp_grb::Callback</a></td></tr>
<tr class="memdesc:"><td class="mdescLeft">&#160;</td><td class="mdescRight">Class to manage a callback function called by Gurobi Optimizer.  <a href="classqbpp__grb_1_1Callback.html#details">More...</a><br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="namespaces"></a>
Namespaces</h2></td></tr>
<tr class="memitem:namespaceqbpp__grb"><td class="memItemLeft" align="right" valign="top"> &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp__grb.html">qbpp_grb</a></td></tr>
<tr class="memdesc:namespaceqbpp__grb"><td class="mdescLeft">&#160;</td><td class="mdescRight">Namespace to use Gurobi optimizer from QUBO++ library. <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="define-members"></a>
Macros</h2></td></tr>
<tr class="memitem:aed547c72240001f6a5ef86636bb62d66"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="qbpp__grb_8hpp.html#aed547c72240001f6a5ef86636bb62d66">GRB_SAFE_CALL</a>(func)</td></tr>
<tr class="separator:aed547c72240001f6a5ef86636bb62d66"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p>QUBO++ interface to call Gurobi Optimizer. </p>
<p>This file provides interfaces to call Gurobi Optimizer from QUBO++ </p><dl class="section author"><dt>Author</dt><dd>Koji Nakano </dd></dl>
<dl class="section version"><dt>Version</dt><dd>2025-04-21 </dd></dl>

<p class="definition">Definition in file <a class="el" href="qbpp__grb_8hpp_source.html">qbpp_grb.hpp</a>.</p>
</div><h2 class="groupheader">Macro Definition Documentation</h2>
<a id="aed547c72240001f6a5ef86636bb62d66"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aed547c72240001f6a5ef86636bb62d66">&#9670;&nbsp;</a></span>GRB_SAFE_CALL</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GRB_SAFE_CALL</td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname">func</td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<b>Value:</b><div class="fragment"><div class="line">  <span class="keywordflow">try</span> {                                                                   \</div>
<div class="line">    func;                                                                 \</div>
<div class="line">  } <span class="keywordflow">catch</span> (GRBException e) {                                              \</div>
<div class="line">    std::cerr &lt;&lt; e.getErrorCode() &lt;&lt; <span class="stringliteral">&quot;: &quot;</span> &lt;&lt; e.getMessage() &lt;&lt; std::endl; \</div>
<div class="line">    exit(1);                                                              \</div>
<div class="line">  }</div>
</div><!-- fragment -->
<p class="definition">Definition at line <a class="el" href="qbpp__grb_8hpp_source.html#l00016">16</a> of file <a class="el" href="qbpp__grb_8hpp_source.html">qbpp_grb.hpp</a>.</p>

</div>
</div>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.17
</small></address>
</body>
</html>
