var searchData=
[
  ['sample_20programs_369',['Sample Programs',['../md_SAMPLE.html',1,'']]],
  ['sample_2emd_370',['SAMPLE.md',['../SAMPLE_8md.html',1,'']]],
  ['search_371',['search',['../classqbpp_1_1easy__solver_1_1PosMinSolDelta.html#aff102bc17a0d06a78a285f1ab2c9632e',1,'qbpp::easy_solver::PosMinSolDelta::search()'],['../classqbpp_1_1easy__solver_1_1EasySolver.html#a9a61ebfb6366c734e7af2b08890753f0',1,'qbpp::easy_solver::EasySolver::search(bool has_initial_sol=false)'],['../classqbpp_1_1easy__solver_1_1EasySolver.html#a8d5bc85759b94bc1c1bf2db59fc323b0',1,'qbpp::easy_solver::EasySolver::search(const qbpp::Sol &amp;initial_sol)'],['../classqbpp_1_1exhaustive__solver_1_1ExhaustiveSolver.html#a967d19602b7de9fef4198508924c7a25',1,'qbpp::exhaustive_solver::ExhaustiveSolver::search()'],['../classqbpp_1_1exhaustive__solver_1_1SearchAlgorithm.html#a3a2b22a2908c11bf374b4f06b2ad1815',1,'qbpp::exhaustive_solver::SearchAlgorithm::search()'],['../classqbpp_1_1exhaustive__solver_1_1SolDelta.html#a926e9faffb265b7208b75663e76f77da',1,'qbpp::exhaustive_solver::SolDelta::search(vindex_t index)'],['../classqbpp_1_1exhaustive__solver_1_1SolDelta.html#a3849dd84e16e534fe2e69b63f0079149',1,'qbpp::exhaustive_solver::SolDelta::search()']]],
  ['search_5falgorithm_5f_372',['search_algorithm_',['../classqbpp_1_1exhaustive__solver_1_1SolDelta.html#a12934fe6cf615d24a00a6d4129a7caeb',1,'qbpp::exhaustive_solver::SolDelta']]],
  ['search_5fall_5fsolutions_373',['search_all_solutions',['../classqbpp_1_1exhaustive__solver_1_1ExhaustiveSolver.html#a639fd8773458972ddeabd24e66809d70',1,'qbpp::exhaustive_solver::ExhaustiveSolver::search_all_solutions()'],['../classqbpp_1_1exhaustive__solver_1_1SearchAlgorithm.html#a61cba268dd5876ce4c474c219d17410a',1,'qbpp::exhaustive_solver::SearchAlgorithm::search_all_solutions()']]],
  ['search_5foptimal_5fsolutions_374',['search_optimal_solutions',['../classqbpp_1_1exhaustive__solver_1_1ExhaustiveSolver.html#a1c26cfdda7d9d8f854170c6430a55e57',1,'qbpp::exhaustive_solver::ExhaustiveSolver::search_optimal_solutions()'],['../classqbpp_1_1exhaustive__solver_1_1SearchAlgorithm.html#ae29feffc0f9bef93caf80bb05b114728',1,'qbpp::exhaustive_solver::SearchAlgorithm::search_optimal_solutions()']]],
  ['searchalgorithm_375',['SearchAlgorithm',['../classqbpp_1_1exhaustive__solver_1_1SearchAlgorithm.html',1,'qbpp::exhaustive_solver::SearchAlgorithm'],['../classqbpp_1_1exhaustive__solver_1_1SearchAlgorithm.html#ab1e6c2357f3c1a1ee4a61d45c73a3b55',1,'qbpp::exhaustive_solver::SearchAlgorithm::SearchAlgorithm()']]],
  ['select_376',['select',['../classqbpp_1_1VarOnehot.html#ab42ffce76c277ff8b8a6833e772c87f6',1,'qbpp::VarOnehot::select(const std::vector&lt; T &gt; &amp;values) const'],['../classqbpp_1_1VarOnehot.html#a80b4dae4246ddeed964b00b99ae68017',1,'qbpp::VarOnehot::select(std::initializer_list&lt; T &gt; values) const']]],
  ['select_5fat_5frandom_377',['select_at_random',['../classqbpp_1_1misc_1_1RandomSet.html#a320528cb75db363c41628fa043cf7f66',1,'qbpp::misc::RandomSet::select_at_random()'],['../classqbpp_1_1misc_1_1MinHeap.html#acba907f46558aefc996cc8aafa381249',1,'qbpp::misc::MinHeap::select_at_random()'],['../classqbpp_1_1misc_1_1RandomMinSet.html#a2fa219f7819ed65ad0c01fea3ea97c79',1,'qbpp::misc::RandomMinSet::select_at_random()']]],
  ['seq_5fthreshold_378',['SEQ_THRESHOLD',['../namespaceqbpp.html#ac9ce864e91548a7dbda1d8928d117df2',1,'qbpp']]],
  ['set_379',['set',['../classqbpp_1_1impl_1_1BitVector.html#a4694931fe6e873494026bbbe9334f848',1,'qbpp::impl::BitVector::set()'],['../classqbpp_1_1Sol.html#a2f7494eb4a254dd19b0261e801bacffb',1,'qbpp::Sol::set(vindex_t index, bool value)'],['../classqbpp_1_1Sol.html#a171de5b36bce9d446849cde5b4324673',1,'qbpp::Sol::set(Var var, bool value)'],['../classqbpp_1_1Sol.html#a0a976747032bda528cc78f2400f601d8',1,'qbpp::Sol::set(const Sol &amp;sol)'],['../classqbpp_1_1Sol.html#ae753a79a37102490b4818c3e04d9348d',1,'qbpp::Sol::set(const MapList &amp;map_list)'],['../classabs2_1_1Model.html#a80ea6686c6c711fbb81d2fefe5f21b7f',1,'abs2::Model::set(int i, int j, int64_t val)'],['../classabs2_1_1Model.html#a8d817979c91972c68ea7b709b18dfead',1,'abs2::Model::set(const std::string &amp;key, const std::string &amp;val)'],['../classabs2_1_1Param.html#a7fd8ca3ff207475b8ad4a3d5040ccd01',1,'abs2::Param::set(const std::string &amp;key, const std::string &amp;val)'],['../classabs2_1_1Param.html#ac6856973bb938f98d0015cdcefc9ad08',1,'abs2::Param::set(Callback &amp;callback)'],['../classabs2_1_1Sol.html#ad92f5385ee3f741163cc3fbd4a5316f8',1,'abs2::Sol::set(int i, bool val)'],['../classabs2_1_1Sol.html#abfc03eec73363653a2e0e9f706dea598',1,'abs2::Sol::set(const std::string &amp;key, const std::string &amp;val)'],['../classabs2_1_1Callback.html#a365f96a6e4f72d37ae897841d07e55ab',1,'abs2::Callback::set(const std::string &amp;operation)'],['../classabs2_1_1Callback.html#a3074d3e4e013b9ada73767cc19b8b202',1,'abs2::Callback::set(const std::string &amp;operation, const std::string &amp;operand)'],['../classabs2_1_1Callback.html#a59d6234d348d2876ac9eb79d7bda4cd0',1,'abs2::Callback::set(const Sol &amp;hint)'],['../classqbpp__abs2_1_1Sol.html#a48d9116f9fcbe00f84640847ee49df23',1,'qbpp_abs2::Sol::set(int index, bool value)'],['../classqbpp__abs2_1_1Sol.html#a04aabe5387a17b86a90acaa607175545',1,'qbpp_abs2::Sol::set(qbpp::Var var, bool value)'],['../group__qbpp__grb.html#ga896e8acb781305f1539e390d360a12de',1,'qbpp_grb::QuadModel::set(const std::string &amp;key, const std::string &amp;val)'],['../group__qbpp__grb.html#ga26577449808e4be323597053db5adeff',1,'qbpp_grb::QuadModel::set(Callback &amp;cb)']]],
  ['set64_380',['set64',['../classqbpp_1_1impl_1_1BitVector.html#afc8a61ad7129c36b0cc12165264b490d',1,'qbpp::impl::BitVector::set64()'],['../classqbpp_1_1Sol.html#a27f590351140f1f04049f1500b7ada31',1,'qbpp::Sol::set64()']]],
  ['set_5f_381',['set_',['../classqbpp_1_1misc_1_1MinSet.html#a0a9fee085659a2b0421af613ec5f4433',1,'qbpp::misc::MinSet']]],
  ['set_5fall_5fsolutions_382',['set_all_solutions',['../classqbpp_1_1exhaustive__solver_1_1Sol.html#afba551e637e1ea61ec0c674e6379f436',1,'qbpp::exhaustive_solver::Sol']]],
  ['set_5farithmetic_5fbits_383',['set_arithmetic_bits',['../group__qbpp__abs2.html#ga2754c68b51c3d0452731e5da95920fe2',1,'qbpp_abs2::Param']]],
  ['set_5fbound_384',['set_bound',['../classqbpp_1_1SolHolderTemplate.html#ac3cbdf42cb4cb8646c5e1b8f5b3da13a',1,'qbpp::SolHolderTemplate::set_bound()'],['../classqbpp__grb_1_1Sol.html#ab77394cbb7dade280051026987c264c9',1,'qbpp_grb::Sol::set_bound()'],['../classqbpp_1_1factorization_1_1SolHolder.html#aefbe214d7248a7c50a35d94a9bc5d07d',1,'qbpp::factorization::SolHolder::set_bound()']]],
  ['set_5fcapacity_385',['set_capacity',['../classqbpp_1_1misc_1_1Tabu.html#abb4a9c6ae969ee3dcafbbe2260fdc921',1,'qbpp::misc::Tabu']]],
  ['set_5fcolor_5fhistogram_386',['set_color_histogram',['../classqbpp_1_1graph__color_1_1GraphColorMap.html#ad90af91eeca9f99c1a611d144cae1934',1,'qbpp::graph_color::GraphColorMap']]],
  ['set_5fenergy_387',['set_energy',['../classqbpp_1_1Sol.html#a16504ce467c6fbfcefea4171a492bef1',1,'qbpp::Sol']]],
  ['set_5fhint_388',['set_hint',['../group__qbpp__abs2.html#gacde6f2007700c2ac188f3a88c5bba1d0',1,'qbpp_abs2::Callback']]],
  ['set_5fif_5fbetter_389',['set_if_better',['../classqbpp_1_1SolHolderTemplate.html#ac633c4a38cd0088e2d72deda90c349bc',1,'qbpp::SolHolderTemplate::set_if_better()'],['../classqbpp_1_1easy__solver_1_1SolDelta.html#ac0f902682f6255272a79333a947e1a24',1,'qbpp::easy_solver::SolDelta::set_if_better()'],['../classqbpp_1_1factorization_1_1SolHolder.html#a46e8fb339ad5d7983403f1785f5c24e5',1,'qbpp::factorization::SolHolder::set_if_better()']]],
  ['set_5fseed_390',['set_seed',['../classqbpp_1_1misc_1_1RandomGenerator.html#a1f4f3d675aee33dce174c8222c3bc22a',1,'qbpp::misc::RandomGenerator']]],
  ['set_5fsol_391',['set_sol',['../classqbpp_1_1SolHolderTemplate.html#a0ecfd57376bca0dfe5447272259fd904',1,'qbpp::SolHolderTemplate::set_sol()'],['../classqbpp_1_1easy__solver_1_1EasySolver.html#a959d4f5bf764f394c9a99cb1cb6194f7',1,'qbpp::easy_solver::EasySolver::set_sol()']]],
  ['set_5ftarget_5fenergy_392',['set_target_energy',['../group__qbpp__abs2.html#gac5b2aaa6db9827d709125edcbe0265a4',1,'qbpp_abs2::Param::set_target_energy()'],['../classqbpp__grb_1_1Callback.html#acc6b06db26a7cf40e83cb1521c3431f3',1,'qbpp_grb::Callback::set_target_energy()'],['../classqbpp_1_1easy__solver_1_1EasySolver.html#aceec9cd9048576610b0cc1fba0b792c5',1,'qbpp::easy_solver::EasySolver::set_target_energy()']]],
  ['set_5fthread_5fcount_393',['set_thread_count',['../classqbpp_1_1easy__solver_1_1EasySolver.html#a963bc1625873dc0e7351ec72bf7e6fe5',1,'qbpp::easy_solver::EasySolver']]],
  ['set_5ftime_5flimit_394',['set_time_limit',['../group__qbpp__abs2.html#ga33c85eefb81407d622de58bd2775598c',1,'qbpp_abs2::Param::set_time_limit()'],['../group__qbpp__grb.html#ga3e57391fc84e3e58a8fee50b0e511af1',1,'qbpp_grb::QuadModel::set_time_limit()'],['../classqbpp_1_1easy__solver_1_1EasySolver.html#ae8fcef1929f94e0a05dd1b039a151d57',1,'qbpp::easy_solver::EasySolver::set_time_limit()']]],
  ['simple_5ffactorization_5fabs2_2ecpp_395',['simple_factorization_abs2.cpp',['../simple__factorization__abs2_8cpp.html',1,'']]],
  ['simple_5ffactorization_5feasy_2ecpp_396',['simple_factorization_easy.cpp',['../simple__factorization__easy_8cpp.html',1,'']]],
  ['simple_5ffactorization_5fgrb_2ecpp_397',['simple_factorization_grb.cpp',['../simple__factorization__grb_8cpp.html',1,'']]],
  ['simplify_398',['simplify',['../classqbpp_1_1Vector.html#a3012b501cdf4d76312c87d052996a66c',1,'qbpp::Vector::simplify()'],['../classqbpp_1_1Expr.html#afd67aef307d047fbe58759c6c39aba13',1,'qbpp::Expr::simplify()'],['../namespaceqbpp.html#a9456e7fa594d0169fcc26f37fdda29ad',1,'qbpp::simplify(const Expr &amp;expr, Vars(*sort_vars_func)(const Vars &amp;))'],['../namespaceqbpp.html#aa7464c250db0ba827d6582d3efa9a272',1,'qbpp::simplify(const Vector&lt; T &gt; &amp;vec, Vars(*sort_vars_func)(const Vars &amp;)=sort_vars)']]],
  ['simplify_5fas_5fbinary_399',['simplify_as_binary',['../classqbpp_1_1Vector.html#ac31d4aa7fe7203fe6b9fcf113d5431a7',1,'qbpp::Vector::simplify_as_binary()'],['../classqbpp_1_1Expr.html#a21b1c63a0930ff6ff426d852b2ccd011',1,'qbpp::Expr::simplify_as_binary()'],['../namespaceqbpp.html#aa3ce65464a38ba36e0b6e99b97234245',1,'qbpp::simplify_as_binary(const Expr &amp;expr)'],['../namespaceqbpp.html#a2d100408fb06c3d1530caff5553bb290',1,'qbpp::simplify_as_binary(const Vector&lt; T &gt; &amp;arg)']]],
  ['simplify_5fas_5fspin_400',['simplify_as_spin',['../classqbpp_1_1Vector.html#a86c92c064e891af5b9a9c68354e107c2',1,'qbpp::Vector::simplify_as_spin()'],['../classqbpp_1_1Expr.html#ac211496d7eaa75f675ed5431fdaeed07',1,'qbpp::Expr::simplify_as_spin()'],['../namespaceqbpp.html#a13c9a177636866d79431415283122768',1,'qbpp::simplify_as_spin(const Expr &amp;expr)'],['../namespaceqbpp.html#af325ae9d6616b9bcb16dd6ded7fd9b12',1,'qbpp::simplify_as_spin(const Vector&lt; T &gt; &amp;arg)']]],
  ['simplify_5fseq_401',['simplify_seq',['../namespaceqbpp.html#abbed8c57213e7a8eefe7aa9fd60281ac',1,'qbpp']]],
  ['single_5fsearch_402',['single_search',['../classqbpp_1_1easy__solver_1_1EasySolver.html#a329a03e4e5c401ec471e0f7f4c42cb51',1,'qbpp::easy_solver::EasySolver']]],
  ['size_403',['size',['../classqbpp_1_1Vector.html#af719287ecdb5f44bbd0aa5d9ba3aaea1',1,'qbpp::Vector::size()'],['../classqbpp_1_1Var.html#a39b4a37f86fc995a9d95cb830435e0a7',1,'qbpp::Var::size()'],['../classqbpp_1_1impl_1_1VarArray2.html#a52b796f80d22866002363096a98d68a7',1,'qbpp::impl::VarArray2::size()'],['../classqbpp_1_1Terms.html#ab427d760f1039a63802e35e336d2fc32',1,'qbpp::Terms::size()'],['../classqbpp_1_1Expr.html#a78d84078c6294d18aa58205b7a015541',1,'qbpp::Expr::size()'],['../classqbpp_1_1impl_1_1BitVector.html#a4342f7ccbb9019a1e47a68504ad64c93',1,'qbpp::impl::BitVector::size()'],['../classqbpp_1_1misc_1_1Tabu.html#a21e6168be034685d15b35252352e6b6a',1,'qbpp::misc::Tabu::size()'],['../classqbpp_1_1easy__solver_1_1BestSols.html#a2acd14bf69fb55561c2c859d8c2ed771',1,'qbpp::easy_solver::BestSols::size()'],['../classqbpp_1_1exhaustive__solver_1_1Sol.html#a14bfd7576baad77821d830e8fb638442',1,'qbpp::exhaustive_solver::Sol::size()']]],
  ['size64_404',['size64',['../classqbpp_1_1impl_1_1BitVector.html#a6326d5d93d29fad3e96035a56abbee98',1,'qbpp::impl::BitVector']]],
  ['size64_5f_405',['size64_',['../classqbpp_1_1impl_1_1BitVector.html#a5c384da355104fa2f6d06cafd887b2d1',1,'qbpp::impl::BitVector']]],
  ['size_5f_406',['size_',['../classqbpp_1_1misc_1_1RandomPermutation.html#a1d2ae800e63cf7b14aee887235bc53d8',1,'qbpp::misc::RandomPermutation']]],
  ['sol_407',['Sol',['../classabs2_1_1Sol.html',1,'abs2::Sol'],['../classqbpp_1_1exhaustive__solver_1_1Sol.html',1,'qbpp::exhaustive_solver::Sol'],['../classqbpp_1_1Sol.html',1,'qbpp::Sol'],['../classqbpp__abs2_1_1Sol.html',1,'qbpp_abs2::Sol'],['../classqbpp__grb_1_1Sol.html',1,'qbpp_grb::Sol'],['../classqbpp_1_1Sol.html#afb26ebcc595aa1391f18c2968713fdba',1,'qbpp::Sol::Sol()=delete'],['../classqbpp_1_1Sol.html#a3f0ad8193decaecc670bf31a2e2d1b8e',1,'qbpp::Sol::Sol(const Sol &amp;sol)=default'],['../classqbpp_1_1Sol.html#a6ea6682ca2e2b26a2abd4fa3d14f8645',1,'qbpp::Sol::Sol(Sol &amp;&amp;sol)=default'],['../classqbpp_1_1Sol.html#a4ec6ba1b6e23f124af7f6647b22e5df4',1,'qbpp::Sol::Sol(const QuadModel &amp;quad_model)'],['../classqbpp_1_1Sol.html#a8ba5c3a0ee5047188467e56382851f8f',1,'qbpp::Sol::Sol(const QuadModel &amp;quad_model, const impl::BitVector &amp;bit_vector)'],['../classabs2_1_1Sol.html#a14968be30887fc1a1883743d2834ee9a',1,'abs2::Sol::Sol(int size)'],['../classabs2_1_1Sol.html#a531910fe2cda7ececbce6f42961c8a19',1,'abs2::Sol::Sol()'],['../classabs2_1_1Sol.html#a55ef95da3925aad139de5d5a5f40b24f',1,'abs2::Sol::Sol(const Sol &amp;sol)'],['../classabs2_1_1Sol.html#a2aed68864780d3211e753dff1a873b61',1,'abs2::Sol::Sol(Sol &amp;&amp;sol)'],['../group__qbpp__abs2.html#gaba8e35f84b2f822cd2633b61358370db',1,'qbpp_abs2::Sol::Sol(const QuadModel &amp;quad_model, const abs2::Sol &amp;sol)'],['../group__qbpp__abs2.html#gac27ae911e72fdeb424a950d41fbef95c',1,'qbpp_abs2::Sol::Sol(const qbpp::Sol &amp;sol)'],['../classqbpp__abs2_1_1Sol.html#ac7b3defb0e488e8570785400889f4c77',1,'qbpp_abs2::Sol::Sol(const Sol &amp;sol)=default'],['../group__qbpp__grb.html#gab69ed0d8b924d9bae6c49c975ba51dcc',1,'qbpp_grb::Sol::Sol()'],['../classqbpp_1_1exhaustive__solver_1_1Sol.html#ab0722463fa799d5892594aada8e2ec4a',1,'qbpp::exhaustive_solver::Sol::Sol(const QuadModel &amp;quad_model)'],['../classqbpp_1_1exhaustive__solver_1_1Sol.html#ae2e1c0deca36bc9d89bd7642c966883e',1,'qbpp::exhaustive_solver::Sol::Sol(const Sol &amp;)=default'],['../classqbpp_1_1exhaustive__solver_1_1Sol.html#aa02294768bcb7c74b6e594529fff0658',1,'qbpp::exhaustive_solver::Sol::Sol(Sol &amp;&amp;)=default'],['../classqbpp_1_1tsp_1_1TSPSol.html#a260cb897debad81d09142ee2ed559f16',1,'qbpp::tsp::TSPSol::sol()']]],
  ['sol_5f_408',['sol_',['../classqbpp_1_1SolHolderTemplate.html#a6acc556e9b2917167d60bbe7f3f8bc93',1,'qbpp::SolHolderTemplate']]],
  ['sol_5fdeltas_409',['sol_deltas',['../classqbpp_1_1exhaustive__solver_1_1SearchAlgorithm.html#ad9287726921c354099815b6e8ec9bb4d',1,'qbpp::exhaustive_solver::SearchAlgorithm']]],
  ['sol_5fholder_5f_410',['sol_holder_',['../classqbpp_1_1exhaustive__solver_1_1SearchAlgorithm.html#ac8874b804e5c1d9911df5c2f6cd4998b',1,'qbpp::exhaustive_solver::SearchAlgorithm']]],
  ['sol_5fholder_5fptr_411',['sol_holder_ptr',['../classqbpp_1_1easy__solver_1_1EasySolver.html#ada3fbb50b6993a825681a4a712fd61b7',1,'qbpp::easy_solver::EasySolver']]],
  ['sol_5fholder_5fptr_5f_412',['sol_holder_ptr_',['../classqbpp_1_1easy__solver_1_1SolDelta.html#a9f9ac387600b8004d161c6c4a17e6446',1,'qbpp::easy_solver::SolDelta::sol_holder_ptr_()'],['../classqbpp_1_1easy__solver_1_1EasySolver.html#ae6e3c634ea34e47131d8e9a407906f4a',1,'qbpp::easy_solver::EasySolver::sol_holder_ptr_()'],['../classqbpp_1_1factorization_1_1ABS2Callback.html#a82e9bc3ced7994ad1630460c2822f16b',1,'qbpp::factorization::ABS2Callback::sol_holder_ptr_()'],['../classqbpp_1_1factorization_1_1GRB__Callback.html#acf63d1d541591c989da90cc23f773bb6',1,'qbpp::factorization::GRB_Callback::sol_holder_ptr_()']]],
  ['sol_5fset_5f_413',['sol_set_',['../classqbpp_1_1easy__solver_1_1BestSols.html#a9b53d76ce7a7edbbf9b17229f51ec0b9',1,'qbpp::easy_solver::BestSols']]],
  ['sol_5fvector_5f_414',['sol_vector_',['../classqbpp_1_1easy__solver_1_1BestSols.html#aea0db6e737aa21fc745af8a781cdc9a4',1,'qbpp::easy_solver::BestSols']]],
  ['soldelta_415',['SolDelta',['../classqbpp_1_1easy__solver_1_1SolDelta.html',1,'qbpp::easy_solver::SolDelta'],['../classqbpp_1_1exhaustive__solver_1_1SolDelta.html',1,'qbpp::exhaustive_solver::SolDelta'],['../classqbpp_1_1easy__solver_1_1SolDelta.html#a2a6c5b08bc697bd6f633b68fcdd550e1',1,'qbpp::easy_solver::SolDelta::SolDelta()'],['../classqbpp_1_1exhaustive__solver_1_1SolDelta.html#a6f368c3a2224a71803d9ee2711effa9b',1,'qbpp::exhaustive_solver::SolDelta::SolDelta(SearchAlgorithm &amp;search_algorithm)'],['../classqbpp_1_1exhaustive__solver_1_1SolDelta.html#a49ba1403c1f05b28fc8921b0ae427f4e',1,'qbpp::exhaustive_solver::SolDelta::SolDelta(const SolDelta &amp;)=default']]],
  ['solhash_416',['SolHash',['../structqbpp_1_1easy__solver_1_1SolHash.html',1,'qbpp::easy_solver']]],
  ['solholder_417',['SolHolder',['../classqbpp_1_1factorization_1_1SolHolder.html',1,'qbpp::factorization::SolHolder'],['../classqbpp_1_1factorization_1_1SolHolder.html#a54c192f1b77c2d713db46bf414e51e7d',1,'qbpp::factorization::SolHolder::SolHolder(const qbpp::QuadModel &amp;quad_model, bool fix_lsb, const qbpp::Vector&lt; qbpp::Var &gt; &amp;var_x, const qbpp::Vector&lt; qbpp::Var &gt; &amp;var_y)'],['../classqbpp_1_1factorization_1_1SolHolder.html#af17b79f6ddf11e299a12d7012c24736d',1,'qbpp::factorization::SolHolder::SolHolder(const qbpp::QuadModel &amp;quad_model, bool fix_lsb, const qbpp::Vector&lt; qbpp::Var &gt; &amp;var_z)'],['../classqbpp_1_1factorization_1_1SolHolder.html#af034a5bb1b85cdf9bf6dfc54d685426c',1,'qbpp::factorization::SolHolder::SolHolder(const SolHolder &amp;sol_holder)=default'],['../namespaceqbpp.html#aadb70b10d2f569db737618d6dcb70bf8',1,'qbpp::SolHolder()']]],
  ['solholdertemplate_418',['SolHolderTemplate',['../classqbpp_1_1SolHolderTemplate.html',1,'qbpp::SolHolderTemplate&lt; T, U &gt;'],['../classqbpp_1_1SolHolderTemplate.html#a6892cbc807bfae37b9c58fc1a8552978',1,'qbpp::SolHolderTemplate::SolHolderTemplate(const QuadModel &amp;quad_model)'],['../classqbpp_1_1SolHolderTemplate.html#a8af1ad9b04050191d7a1e53445313298',1,'qbpp::SolHolderTemplate::SolHolderTemplate(const T &amp;sol)'],['../classqbpp_1_1SolHolderTemplate.html#a3d32ed953eff0b351eeec9bc4a79b7ae',1,'qbpp::SolHolderTemplate::SolHolderTemplate(const SolHolderTemplate&lt; T, U &gt; &amp;other)=delete']]],
  ['solholdertemplate_3c_20sol_2c_20energy_5ft_20_3e_419',['SolHolderTemplate&lt; Sol, energy_t &gt;',['../classqbpp_1_1SolHolderTemplate.html',1,'qbpp']]],
  ['solver_420',['Solver',['../classabs2_1_1Solver.html',1,'abs2::Solver'],['../classqbpp__abs2_1_1Solver.html',1,'qbpp_abs2::Solver'],['../classabs2_1_1Solver.html#a13c1993c4be047756707826adaca2865',1,'abs2::Solver::Solver()']]],
  ['solver_5f_421',['solver_',['../classqbpp_1_1SolHolderTemplate.html#ad5bd11f4a7175718f359da47b956ba36',1,'qbpp::SolHolderTemplate']]],
  ['sort_5fvars_422',['sort_vars',['../namespaceqbpp.html#ae29f30ff45dbdf092fde15f9dd895322',1,'qbpp']]],
  ['sort_5fvars_5fas_5fbinary_423',['sort_vars_as_binary',['../namespaceqbpp.html#a65a9c464505d996431ed4fa7b3556f2b',1,'qbpp']]],
  ['sort_5fvars_5fas_5fspin_424',['sort_vars_as_spin',['../namespaceqbpp.html#a698b1ce8e67635b6563cf589d9689667',1,'qbpp']]],
  ['sort_5fvars_5fin_5fplace_425',['sort_vars_in_place',['../namespaceqbpp.html#a5d56d3d39b9f223f9dd2eb85fe3d2256',1,'qbpp']]],
  ['spin_5fto_5fbinary_426',['spin_to_binary',['../classqbpp_1_1Vector.html#afdd07cc64c0c98ea6808efd1dddc547e',1,'qbpp::Vector::spin_to_binary()'],['../classqbpp_1_1Expr.html#ad72e191219081766dd77bd9ac592cb19',1,'qbpp::Expr::spin_to_binary()'],['../namespaceqbpp.html#a17ebda985a4b0bb22a6b4485b59c3774',1,'qbpp::spin_to_binary(const Expr &amp;expr)'],['../namespaceqbpp.html#a3bb73161c6a14db48c8c953f32824991',1,'qbpp::spin_to_binary(const Vector&lt; T &gt; &amp;arg)'],['../namespaceqbpp.html#abdcfd6050efc38e03291631576bf83ca',1,'qbpp::spin_to_binary(const Vector&lt; Vector&lt; T &gt;&gt; &amp;arg) -&gt; Vector&lt; decltype(spin_to_binary(arg[0]))&gt;']]],
  ['sqr_427',['sqr',['../classqbpp_1_1Vector.html#aec3465690dae80fdada264f377e8d459',1,'qbpp::Vector::sqr()'],['../classqbpp_1_1Expr.html#a9a047dd887283ef589ac2e825fd1b68c',1,'qbpp::Expr::sqr()'],['../namespaceqbpp.html#abd03cc17ab61f6ab2be95751c7b92626',1,'qbpp::sqr(const Vector&lt; T &gt; &amp;arg)'],['../namespaceqbpp.html#a5d4f11504851d06a715bc3f3e194b1eb',1,'qbpp::sqr(const Vector&lt; Vector&lt; T &gt;&gt; &amp;arg) -&gt; Vector&lt; decltype(sqr(arg[0]))&gt;'],['../namespaceqbpp.html#a97185e5bf8d17779523cb3f13b1d54d7',1,'qbpp::sqr(const Expr &amp;expr)']]],
  ['start_5ftime_5f_428',['start_time_',['../classqbpp_1_1easy__solver_1_1EasySolver.html#a9a4b07b571358cf4627c0c5d2b138222',1,'qbpp::easy_solver::EasySolver']]],
  ['startx_5f_429',['startx_',['../classqbpp_1_1SolHolderTemplate.html#aa3f5a47679d94c142f5ca1cff3144a64',1,'qbpp::SolHolderTemplate']]],
  ['str_430',['str',['../classqbpp_1_1impl_1_1VarSet.html#a9caf4bffd9ec02f1c165dff7ddc66065',1,'qbpp::impl::VarSet::str()'],['../classqbpp_1_1impl_1_1IndexVarMapper.html#a8551c2e54c33cb1d39a81e33aaf6d725',1,'qbpp::impl::IndexVarMapper::str()'],['../classqbpp_1_1impl_1_1BitVector.html#a10861e50fb65eb56c5a0752e11db2415',1,'qbpp::impl::BitVector::str()'],['../classqbpp_1_1exhaustive__solver_1_1Sol.html#a86c91d760b8ae016acad899e82e96241',1,'qbpp::exhaustive_solver::Sol::str()'],['../namespaceqbpp.html#a41f8243940eb837c282aed85287bd3af',1,'qbpp::str(Var var)'],['../namespaceqbpp.html#a56a890256565a72b2af729722223b083',1,'qbpp::str(const char *param)'],['../namespaceqbpp.html#aba7ff8d4f4db81f830505e827bd954ef',1,'qbpp::str(const Vars &amp;vars)'],['../namespaceqbpp.html#adffe2965c67b8069b628aa9801412972',1,'qbpp::str(const Term &amp;term)'],['../namespaceqbpp.html#a22ee18cfbe43228cdb8deb8eadadeae2',1,'qbpp::str(const Expr &amp;expr, const std::string &amp;prefix)'],['../namespaceqbpp.html#a6ecdcc61418dea3e7792fc9209924b73',1,'qbpp::str(const Model &amp;model)'],['../namespaceqbpp.html#a8fa3af2cffc1996530310aac6e4b0576',1,'qbpp::str(const QuadModel &amp;quad_model)'],['../namespaceqbpp.html#a0a822c8155843fe78c8ac4ddd95f1681',1,'qbpp::str(const Sol &amp;sol)'],['../namespaceqbpp.html#a60ad3ecc8ad6743503ca04e7da2166d1',1,'qbpp::str(const MapList &amp;map_list)'],['../namespaceqbpp.html#ad09e205acc6322f572528407e4f4a9b7',1,'qbpp::str(int8_t val)'],['../namespaceqbpp.html#a3c1abcaeeb48eb53474f8f6203883ed2',1,'qbpp::str(const T &amp;val) -&gt; decltype(std::to_string(val))'],['../namespaceqbpp.html#aab442c5b6a214c5886d4dfdee199530a',1,'qbpp::str(T val) -&gt; decltype(val.str())'],['../namespaceqbpp.html#a14c16118187b4556c1a59a34d5a9d4fa',1,'qbpp::str(const impl::BitVector &amp;bit_vector)'],['../namespaceqbpp.html#acced023ee901b8bc9a1a2968ec852ab4',1,'qbpp::str(const Expr &amp;expr)'],['../namespaceqbpp.html#a9ddf64f23c738a48e826d286d24716a1',1,'qbpp::str(const Terms &amp;terms)'],['../namespaceqbpp.html#a8e549a11c0fee368294581bdf7d66958',1,'qbpp::str(const Expr &amp;expr, const std::string &amp;prefix, const std::string &amp;separator[[maybe_unused]]=&quot;,&quot;)'],['../namespaceqbpp.html#a18a943c23db4a5d7b987636d75766548',1,'qbpp::str(const Vector&lt; T &gt; &amp;vec, const std::string &amp;prefix=&quot;&quot;, const std::string &amp;separator=&quot;,&quot;)']]],
  ['str_5fimpl_431',['str_impl',['../namespaceqbpp.html#a717e98de5c1866aeef5498c1fd6e48b9',1,'qbpp::str_impl(const Vars &amp;vars, std::function&lt; std::string(Var)&gt; str)'],['../namespaceqbpp.html#add12c2325f44f6bb0fd5c9921462c8cc',1,'qbpp::str_impl(const Term &amp;term, std::function&lt; std::string(Var)&gt; str)'],['../namespaceqbpp.html#a3c27b7667bb88c80b66822f59aef9241',1,'qbpp::str_impl(const Terms &amp;terms, std::function&lt; std::string(Var)&gt; str)'],['../namespaceqbpp.html#a375a2df55223523ba27620ac306cf598',1,'qbpp::str_impl(const Expr &amp;expr, std::function&lt; std::string(Var)&gt; str)']]],
  ['str_5fshort_432',['str_short',['../namespaceqbpp.html#a75ec4f70aaf822424842c81e415834c6',1,'qbpp']]],
  ['sum_433',['sum',['../namespaceqbpp.html#a9041f8ee7d3f4a7c04a0d385babae285',1,'qbpp::sum(const Vector&lt; T &gt; &amp;items)'],['../namespaceqbpp.html#aa7311e85eb4a80742f90b6225a5ebde6',1,'qbpp::sum(const T &amp;arg[[maybe_unused]])'],['../namespaceqbpp.html#acd23df7887fd6024222eaf8ac8d3e84a',1,'qbpp::sum(const Vector&lt; Vector&lt; T &gt;&gt; &amp;arg[[maybe_unused]])'],['../namespaceqbpp.html#a53957f0090d3aab0d81c3141c321ad2a',1,'qbpp::sum(const Vector&lt; Var &gt; &amp;vars)'],['../namespaceqbpp.html#a852d3ca4caf367d8475ccb839f021557',1,'qbpp::sum(const Vector&lt; Expr &gt; &amp;expr)']]],
  ['swap_434',['swap',['../classqbpp_1_1misc_1_1RandomSet.html#a64eed5f84e5a5b9bfdf2aed95dc10da0',1,'qbpp::misc::RandomSet']]],
  ['swap_5fheap_435',['swap_heap',['../classqbpp_1_1misc_1_1MinHeap.html#a2378512cf4e99296fe714ef41f3df930',1,'qbpp::misc::MinHeap']]]
];
