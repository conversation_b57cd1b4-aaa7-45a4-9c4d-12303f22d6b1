var searchData=
[
  ['quadmodel_857',['QuadModel',['../classqbpp_1_1QuadModel.html#aaefff91919d789ab6578503a47d85bab',1,'qbpp::QuadModel::QuadModel(const QuadModel &amp;)=default'],['../classqbpp_1_1QuadModel.html#a6de1afc6dc23a7c0a9c3dcec92a7cce4',1,'qbpp::QuadModel::QuadModel(QuadModel &amp;&amp;) noexcept=default'],['../classqbpp_1_1QuadModel.html#ad0ca14644db1067f7e33380353deeca2',1,'qbpp::QuadModel::QuadModel(const Model &amp;model)'],['../classqbpp_1_1QuadModel.html#abbed05282ead0c8c2303d219fb7d89e6',1,'qbpp::QuadModel::QuadModel(const Expr &amp;expr, const std::shared_ptr&lt; const impl::IndexVarMapper &gt; &amp;index_var_mapper_ptr)'],['../classqbpp_1_1QuadModel.html#ae474875e413a833050001a2e374c2b36',1,'qbpp::QuadModel::QuadModel(Model &amp;&amp;model)'],['../classqbpp_1_1QuadModel.html#a7991782c2f6948eaab0a205a8476262b',1,'qbpp::QuadModel::QuadModel(const Expr &amp;expr)'],['../classqbpp_1_1QuadModel.html#a0540e5b1d48432fedcfeb124d1c1d0ad',1,'qbpp::QuadModel::QuadModel(Expr &amp;&amp;expr)'],['../classqbpp__abs2_1_1QuadModel.html#ae407053ddc1028ef6253ee3f5d23073d',1,'qbpp_abs2::QuadModel::QuadModel()=delete'],['../group__qbpp__abs2.html#gab125241142be7f65e2a913158e00394a',1,'qbpp_abs2::QuadModel::QuadModel(const qbpp::QuadModel &amp;quad_model)'],['../classqbpp__abs2_1_1QuadModel.html#ae79cbaf57fdafd9fd834aba1e0e615f5',1,'qbpp_abs2::QuadModel::QuadModel(const QuadModel &amp;quad_model)'],['../group__qbpp__grb.html#ga3e5106bc14a17c6521c932753c76fe96',1,'qbpp_grb::QuadModel::QuadModel(const qbpp::QuadModel &amp;quad_model, bool verbose=false)'],['../group__qbpp__grb.html#ga5298973847913cfbe7f7639fa0d1416c',1,'qbpp_grb::QuadModel::QuadModel(const QuadModel &amp;grb_model)=default']]],
  ['quadratic_858',['quadratic',['../classqbpp_1_1QuadModel.html#a6624038d4ff117176c37de2d1260f471',1,'qbpp::QuadModel::quadratic() const'],['../classqbpp_1_1QuadModel.html#acbed633d6a032fc2fb51411850e76762',1,'qbpp::QuadModel::quadratic(vindex_t i, vindex_t j) const']]]
];
