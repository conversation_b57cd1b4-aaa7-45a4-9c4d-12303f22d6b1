var searchData=
[
  ['rand_5fprime_859',['rand_prime',['../namespaceqbpp_1_1factorization.html#a0cee6f6d9d06e2d8e83d151d62ed652b',1,'qbpp::factorization']]],
  ['random_5fflip_860',['random_flip',['../classqbpp_1_1easy__solver_1_1SolDelta.html#ad0505230c61ae317b5414f06327daeb4',1,'qbpp::easy_solver::SolDelta']]],
  ['randomengine_861',['RandomEngine',['../classqbpp_1_1misc_1_1RandomEngine.html#a425f33f093cefe100a65fa7c8f6a221f',1,'qbpp::misc::RandomEngine']]],
  ['randomgenerator_862',['RandomGenerator',['../classqbpp_1_1misc_1_1RandomGenerator.html#a6ab877662e0f56dad77f70ded6528ded',1,'qbpp::misc::RandomGenerator::RandomGenerator()'],['../classqbpp_1_1misc_1_1RandomGenerator.html#a4b961ce442c1f214b17df0a5e3466e5d',1,'qbpp::misc::RandomGenerator::RandomGenerator(const RandomGenerator &amp;)=delete']]],
  ['randomminset_863',['RandomMinSet',['../classqbpp_1_1misc_1_1RandomMinSet.html#a793253f32a42615244d2e353d5ff98eb',1,'qbpp::misc::RandomMinSet']]],
  ['randompermutation_864',['RandomPermutation',['../classqbpp_1_1misc_1_1RandomPermutation.html#accf8ebcfcad81a695023782a4226e43f',1,'qbpp::misc::RandomPermutation']]],
  ['randomset_865',['RandomSet',['../classqbpp_1_1misc_1_1RandomSet.html#a78525e8ab3790dc530a04db4a3e2665e',1,'qbpp::misc::RandomSet']]],
  ['rd_5fseed_866',['rd_seed',['../classqbpp_1_1misc_1_1RandomGenerator.html#a513a93d293d9fa08a7fee2bba9cb73ff',1,'qbpp::misc::RandomGenerator']]],
  ['reduce_867',['reduce',['../classqbpp_1_1Vector.html#ac3bab7f853a0261449daea0ef614ccb9',1,'qbpp::Vector::reduce()'],['../classqbpp_1_1Expr.html#ac7fd3e8384b8c236ac17e74916d0456e',1,'qbpp::Expr::reduce()'],['../namespaceqbpp.html#ac05f27a511fa1e2870091ea5d696a3c3',1,'qbpp::reduce(const Expr &amp;expr)'],['../namespaceqbpp.html#a6aba041bba1df7d99d41703115fc4da3',1,'qbpp::reduce(const Vector&lt; T &gt; &amp;arg)'],['../namespaceqbpp.html#a22773d3d3891d1859780eb828735ea3d',1,'qbpp::reduce(const Vector&lt; Vector&lt; T &gt;&gt; &amp;arg)']]],
  ['reduce_5fcascade_868',['reduce_cascade',['../namespaceqbpp.html#aba363d0725955ba9ec04d0f955a034d8',1,'qbpp']]],
  ['reduce_5fsum_869',['reduce_sum',['../namespaceqbpp.html#aebff5c6be40335125221f44731cee406',1,'qbpp']]],
  ['register_5fnew_5fsol_870',['register_new_sol',['../classqbpp_1_1exhaustive__solver_1_1SearchAlgorithm.html#a73cc42b1cd9e72aa4a56eb25354ca21e',1,'qbpp::exhaustive_solver::SearchAlgorithm']]],
  ['replace_871',['replace',['../classqbpp_1_1Vector.html#ade810d182796645f5ad6a7c86f2105ad',1,'qbpp::Vector::replace()'],['../classqbpp_1_1Expr.html#a73c90484dacb24529c129a00da3d8611',1,'qbpp::Expr::replace()'],['../namespaceqbpp.html#a6b8a3b941d1962c59512643b6c30b8da',1,'qbpp::replace(const Term &amp;term, const MapDict &amp;map_dict)'],['../namespaceqbpp.html#ab3efb11e8167e5a1b4ec8cc2f5a98da3',1,'qbpp::replace(const Expr &amp;expr, const MapList &amp;map_list)'],['../namespaceqbpp.html#a7f95a13d75087651ef3476dd1712e255',1,'qbpp::replace(const Vector&lt; T &gt; &amp;arg, const MapList &amp;map_list)'],['../namespaceqbpp.html#a45adc5201c42b24d17beffaf1531b3c0',1,'qbpp::replace(const Vector&lt; Vector&lt; T &gt;&gt; &amp;arg, const MapList &amp;map_list)']]],
  ['reserve_872',['reserve',['../classqbpp_1_1Vector.html#a5e8bb55fb9236e098401e23a758b5927',1,'qbpp::Vector::reserve()'],['../classqbpp_1_1Terms.html#aa58935ac10fd34559b129801cf4c4b65',1,'qbpp::Terms::reserve()']]],
  ['resize_873',['resize',['../classqbpp_1_1Vector.html#a9cdb9d63576d4e8f28b882e22a56a815',1,'qbpp::Vector::resize()'],['../classqbpp_1_1Terms.html#a8c00d28cedf65dadeff5b4b5bf00492b',1,'qbpp::Terms::resize(size_t n)'],['../classqbpp_1_1Terms.html#a088772354cae41d11758e1a5d3030ef0',1,'qbpp::Terms::resize(size_t n, const Term &amp;value)']]]
];
