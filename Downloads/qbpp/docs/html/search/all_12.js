var searchData=
[
  ['terms_20and_20conditions_436',['Terms and conditions',['../md_LICENSE.html',1,'']]],
  ['tabu_437',['Tabu',['../classqbpp_1_1misc_1_1Tabu.html',1,'qbpp::misc::Tabu'],['../classqbpp_1_1misc_1_1Tabu.html#afb324d3b3057cdd6872cd29fd8bb92f8',1,'qbpp::misc::Tabu::Tabu()']]],
  ['tabu_5f_438',['tabu_',['../classqbpp_1_1easy__solver_1_1TabuSolDelta.html#ac17653919bfa66975c06ed71e41d5b68',1,'qbpp::easy_solver::TabuSolDelta']]],
  ['tabu_5fhas_439',['tabu_has',['../classqbpp_1_1easy__solver_1_1TabuSolDelta.html#a514b9fd7511de63ea113064e40c50862',1,'qbpp::easy_solver::TabuSolDelta']]],
  ['tabu_5flist_440',['tabu_list',['../classqbpp_1_1misc_1_1Tabu.html#a144ee6fa4a8a40960b22e7aa732c7882',1,'qbpp::misc::Tabu']]],
  ['tabu_5fset_5f_441',['tabu_set_',['../classqbpp_1_1misc_1_1Tabu.html#a0219883d6003e19f7cf6167f43b639d8',1,'qbpp::misc::Tabu']]],
  ['tabu_5fsize_5f_442',['tabu_size_',['../classqbpp_1_1easy__solver_1_1TabuSolDelta.html#a1381fbf8ed64faf60c3ad0556a37b400',1,'qbpp::easy_solver::TabuSolDelta']]],
  ['tabusoldelta_443',['TabuSolDelta',['../classqbpp_1_1easy__solver_1_1TabuSolDelta.html',1,'qbpp::easy_solver::TabuSolDelta'],['../classqbpp_1_1easy__solver_1_1TabuSolDelta.html#af665d76e9e237ecc6e0280f3b5db2679',1,'qbpp::easy_solver::TabuSolDelta::TabuSolDelta()']]],
  ['target_5fenergy_444',['target_energy',['../classqbpp__abs2_1_1Param.html#a39060f29eb41259853f58b5c06348da5',1,'qbpp_abs2::Param::target_energy()'],['../classqbpp__grb_1_1Callback.html#ae44d18cfb962fc7ccf90d5305e76f288',1,'qbpp_grb::Callback::target_energy()'],['../classGRB__Callback.html#a11672ce1bfd6aedb900b3dc581fa9d9d',1,'GRB_Callback::target_energy()']]],
  ['target_5fenergy_5f_445',['target_energy_',['../classqbpp_1_1easy__solver_1_1EasySolver.html#a5114362571468d84f507f9e3d17e0995',1,'qbpp::easy_solver::EasySolver']]],
  ['term_446',['Term',['../classqbpp_1_1Term.html',1,'qbpp::Term'],['../classqbpp_1_1Expr.html#a333495a9ec0925353fa5c35611cf0e40',1,'qbpp::Expr::term(size_t i)'],['../classqbpp_1_1Expr.html#a8d4247ebf642805c3542b2bb09bb229b',1,'qbpp::Expr::term(size_t i) const'],['../classqbpp_1_1Term.html#aaf39494be72b60ccebaae82598e05f00',1,'qbpp::Term::Term()=default'],['../classqbpp_1_1Term.html#a2dbe4af0c1545eb2f7f5c141cba9caf6',1,'qbpp::Term::Term(const Term &amp;)=default'],['../classqbpp_1_1Term.html#a839603ba79e165c71b9d3d47905f4734',1,'qbpp::Term::Term(Term &amp;&amp;) noexcept=default'],['../classqbpp_1_1Term.html#a996ae106286c85c64a5023b5b97ce756',1,'qbpp::Term::Term(T val)'],['../classqbpp_1_1Term.html#a74a7b6d6118a5708a2087407975ac195',1,'qbpp::Term::Term(Var var)'],['../classqbpp_1_1Term.html#a2477c401e25e7e5ea93fec5b07b65feb',1,'qbpp::Term::Term(Var var, T val)'],['../classqbpp_1_1Term.html#a45baea5bc9bd9eebdc503aafc99e7c53',1,'qbpp::Term::Term(Var var1, Var var2)'],['../classqbpp_1_1Term.html#a89c6d1d0ea9b83de65779c1a384af621',1,'qbpp::Term::Term(const Vars &amp;vars)'],['../classqbpp_1_1Term.html#a76046652c80764750aa7be2115e83623',1,'qbpp::Term::Term(Vars &amp;&amp;vars) noexcept'],['../classqbpp_1_1Term.html#ae910d5a107568751d0a1670d81750c6f',1,'qbpp::Term::Term(const Vars &amp;vars, T val)'],['../classqbpp_1_1Term.html#a36119913d2a15d8127e505430a172b22',1,'qbpp::Term::Term(Vars &amp;&amp;vars, T val)']]],
  ['term_5fcapacity_447',['TERM_CAPACITY',['../namespaceqbpp.html#a0a9efb4a220eaef22429d00be02aeee2',1,'qbpp']]],
  ['term_5fcount_448',['term_count',['../classqbpp_1_1Expr.html#a39a41ce95a2323c0098ae64ece919fd8',1,'qbpp::Expr::term_count() const'],['../classqbpp_1_1Expr.html#ac6b458207601269f54271682aaab22f6',1,'qbpp::Expr::term_count(vindex_t size)'],['../classqbpp_1_1Model.html#a701f2442e6b2aa7e27d924e10883504f',1,'qbpp::Model::term_count()'],['../classqbpp_1_1QuadModel.html#a16af851530b1f63aca9baa8ec67ed01f',1,'qbpp::QuadModel::term_count(vindex_t deg) const'],['../classqbpp_1_1QuadModel.html#a5eabcb9b840eb2cc6083fe943996f230',1,'qbpp::QuadModel::term_count() const override']]],
  ['terms_449',['Terms',['../classqbpp_1_1Terms.html',1,'qbpp::Terms'],['../classqbpp_1_1Expr.html#a300c7ea971b7d4f4630fc91b02b29582',1,'qbpp::Expr::terms() const'],['../classqbpp_1_1Expr.html#aac629be77ffd0a161dc970865b6e318a',1,'qbpp::Expr::terms()'],['../classqbpp_1_1Model.html#a24930e1898939d5f4a6f6d0c9d56cca1',1,'qbpp::Model::terms()'],['../classqbpp_1_1Terms.html#aeb38bbfe02fde2089752e60511cad44a',1,'qbpp::Terms::Terms()'],['../classqbpp_1_1Terms.html#ab0ae746d707f1bd3520b163f8253d1f5',1,'qbpp::Terms::Terms(std::initializer_list&lt; Term &gt; init)'],['../classqbpp_1_1Terms.html#aaa80e305f5d24072c9faf1cd177dd864',1,'qbpp::Terms::Terms(Var var)'],['../classqbpp_1_1Terms.html#a89ea46c5b82c3e01c1d7ad34d435697d',1,'qbpp::Terms::Terms(Var var, T val)'],['../classqbpp_1_1Terms.html#ac8639730b9087811581e3aa746bce8b3',1,'qbpp::Terms::Terms(const Term &amp;term)'],['../classqbpp_1_1Terms.html#a0ceb691797159682b9679498673f88ec',1,'qbpp::Terms::Terms(Term &amp;&amp;term)'],['../classqbpp_1_1Terms.html#ad0b0bc836e48c8cd61be6e23c8546e0e',1,'qbpp::Terms::Terms(const Term &amp;term1, const Term &amp;term2)'],['../classqbpp_1_1Terms.html#a148fdbb0398d527d85b6ffdac6ee1c10',1,'qbpp::Terms::Terms(const Term &amp;term1, Term &amp;&amp;term2)'],['../classqbpp_1_1Terms.html#a7e9ead2337010cd637c921337a409ea9',1,'qbpp::Terms::Terms(Term &amp;&amp;term1, const Term &amp;term2)'],['../classqbpp_1_1Terms.html#a5472efbe7d3849ea2c0ebbf65c5c3456',1,'qbpp::Terms::Terms(Term &amp;&amp;term1, Term &amp;&amp;term2)']]],
  ['terms_5f_450',['terms_',['../classqbpp_1_1Terms.html#ac475c9cc063593182dbdee0155987a8a',1,'qbpp::Terms::terms_()'],['../classqbpp_1_1Expr.html#a668faad7eadb1d14f8ac68a352b79112',1,'qbpp::Expr::terms_()']]],
  ['thread_5fcount_5f_451',['thread_count_',['../classqbpp_1_1easy__solver_1_1EasySolver.html#aced832de618720dce416415ba5f58c5c',1,'qbpp::easy_solver::EasySolver']]],
  ['throw_5fmessage_452',['THROW_MESSAGE',['../qbpp_8hpp.html#a265e0828fcea8e75fbc9c0be99f7156e',1,'qbpp.hpp']]],
  ['time_5flimit_5f_453',['time_limit_',['../classqbpp_1_1easy__solver_1_1EasySolver.html#ab99254a861b99ba22b5132ffa67416c2',1,'qbpp::easy_solver::EasySolver']]],
  ['tobinarystring_454',['toBinaryString',['../namespaceqbpp_1_1factorization.html#aecc49a3c82690965dc325f720cb3b079',1,'qbpp::factorization']]],
  ['toexpr_455',['toExpr',['../namespaceqbpp.html#a56a6b58eb5025738b3d0ea99718917e0',1,'qbpp::toExpr(const T &amp;arg)'],['../namespaceqbpp.html#aaf3ddb99a2a36e2a6bd70bc775d6fada',1,'qbpp::toExpr(const Expr &amp;arg)'],['../namespaceqbpp.html#afee14d78f20b8b693bf97bfa56a06123',1,'qbpp::toExpr(const Vector&lt; Expr &gt; &amp;arg)'],['../namespaceqbpp.html#a771f1a64025858d915df0123983aa772',1,'qbpp::toExpr(const Vector&lt; T &gt; &amp;arg)'],['../namespaceqbpp.html#a272d30a9680bd1ef3e63e06ef7cdf01b',1,'qbpp::toExpr(const std::vector&lt; T &gt; &amp;arg)'],['../namespaceqbpp.html#a5b024ae1db7da6bff011ad43b14fb8ec',1,'qbpp::toExpr(const std::initializer_list&lt; Expr &gt; &amp;list)'],['../namespaceqbpp.html#a054ed56666c40a3b5d948d4292c91496',1,'qbpp::toExpr(const std::initializer_list&lt; T &gt; &amp;list)'],['../namespaceqbpp.html#ac60fbc687c985a61cd3c755bc8ad2f45',1,'qbpp::toExpr(const std::initializer_list&lt; std::initializer_list&lt; Expr &gt;&gt; &amp;list)'],['../namespaceqbpp.html#a08b806375d2c80f115a165e2dcb36cb9',1,'qbpp::toExpr(const std::initializer_list&lt; std::initializer_list&lt; std::initializer_list&lt; Expr &gt;&gt;&gt; &amp;list)'],['../namespaceqbpp.html#ae06a111bb3040263deff02c501635c10',1,'qbpp::toExpr(const std::initializer_list&lt; std::initializer_list&lt; std::initializer_list&lt; std::initializer_list&lt; Expr &gt;&gt;&gt;&gt; &amp;list)']]],
  ['toint_456',['toInt',['../namespaceqbpp.html#ad69bc1ad0ee4d44292f527727cecea19',1,'qbpp::toInt(const Expr &amp;expr)'],['../namespaceqbpp.html#ae112d85a9bed2bf373934acae380c874',1,'qbpp::toInt(const Vector&lt; T &gt; &amp;arg)']]],
  ['total_5fproduct_457',['total_product',['../namespaceqbpp.html#ab7c36bf877df8c8164450e96d5c4a2a4',1,'qbpp::total_product(const Vector&lt; Vector&lt; T &gt;&gt; &amp;items)'],['../namespaceqbpp.html#a7c589f0c4bd359baa6ebaf0ad135a9f6',1,'qbpp::total_product(const T &amp;arg[[maybe_unused]])'],['../namespaceqbpp.html#ae232eee49d6bc829974d79d90845d58d',1,'qbpp::total_product(const Vector&lt; T &gt; &amp;arg[[maybe_unused]])']]],
  ['total_5fproduct_5fimpl_458',['total_product_impl',['../namespaceqbpp.html#add07cae928b6584059b1276e52821836',1,'qbpp::total_product_impl(const T &amp;item)'],['../namespaceqbpp.html#a01a03d608bbf2ba6be63203f0a3263b1',1,'qbpp::total_product_impl(const Vector&lt; T &gt; &amp;items)']]],
  ['total_5fsum_459',['total_sum',['../namespaceqbpp.html#a2733233894731d32bf82bd70ed665a2a',1,'qbpp::total_sum(const T &amp;arg[[maybe_unused]])'],['../namespaceqbpp.html#ada72da121769827fbb6febcd7e42e391',1,'qbpp::total_sum(const Vector&lt; T &gt; &amp;arg[[maybe_unused]])'],['../namespaceqbpp.html#a253092d9eb405392a07ad60df7c26be4',1,'qbpp::total_sum(const Vector&lt; Vector&lt; T &gt;&gt; &amp;items)']]],
  ['total_5fsum_5fimpl_460',['total_sum_impl',['../namespaceqbpp.html#ae7d9881501e697d70678c1782afccaea',1,'qbpp::total_sum_impl(const T &amp;item)'],['../namespaceqbpp.html#ad2b6fc4c0549f9535eaad97fe6c40e4e',1,'qbpp::total_sum_impl(const Vector&lt; T &gt; &amp;items)']]],
  ['tour_461',['tour',['../classqbpp_1_1tsp_1_1TSPSol.html#af5a209140f85294b3728cf54d3603781',1,'qbpp::tsp::TSPSol']]],
  ['transpose_462',['transpose',['../classqbpp_1_1Vector.html#a10733d47660a917451c5c891be6b3c8a',1,'qbpp::Vector::transpose()'],['../namespaceqbpp.html#a47715eec8c4942d0179134ce224be6c5',1,'qbpp::transpose()']]],
  ['tsp_5fabs2_2ecpp_463',['tsp_abs2.cpp',['../tsp__abs2_8cpp.html',1,'']]],
  ['tsp_5feasy_2ecpp_464',['tsp_easy.cpp',['../tsp__easy_8cpp.html',1,'']]],
  ['tsp_5fgrb_2ecpp_465',['tsp_grb.cpp',['../tsp__grb_8cpp.html',1,'']]],
  ['tsp_5fquad_5fmodel_466',['tsp_quad_model',['../classqbpp_1_1tsp_1_1TSPSol.html#a5c80e1d34f42b58098d9738725a6d3d8',1,'qbpp::tsp::TSPSol::tsp_quad_model()'],['../classABS2Callback.html#aef33841d15a29711f6bc53bee2ff48ba',1,'ABS2Callback::tsp_quad_model()'],['../classMyEasySolver.html#a7e3815660af44b18edef8cd7a2629270',1,'MyEasySolver::tsp_quad_model()'],['../classGRB__Callback.html#a12eb524eb639f63bfea8f59c750f9035',1,'GRB_Callback::tsp_quad_model()']]],
  ['tspmap_467',['TSPMap',['../classqbpp_1_1tsp_1_1TSPMap.html',1,'qbpp::tsp::TSPMap'],['../classqbpp_1_1tsp_1_1TSPMap.html#a1a60e3abe1643aae812789c53f678111',1,'qbpp::tsp::TSPMap::TSPMap()']]],
  ['tspmodel_468',['TSPModel',['../classqbpp_1_1tsp_1_1TSPModel.html',1,'qbpp::tsp::TSPModel'],['../classqbpp_1_1tsp_1_1TSPModel.html#ae351f642ac8277b8641ae322d8559fd2',1,'qbpp::tsp::TSPModel::TSPModel(std::tuple&lt; qbpp::QuadModel, bool, qbpp::Vector&lt; qbpp::Vector&lt; qbpp::Var &gt;&gt;&gt; &amp;&amp;tuple)'],['../classqbpp_1_1tsp_1_1TSPModel.html#a7b4d3f9919401a3ea106e318eb627d50',1,'qbpp::tsp::TSPModel::TSPModel(const TSPMap &amp;map, bool fix_first=false)']]],
  ['tspquadmodel_469',['TSPQuadModel',['../classqbpp_1_1tsp_1_1TSPQuadModel.html',1,'qbpp::tsp::TSPQuadModel'],['../classqbpp_1_1tsp_1_1TSPQuadModel.html#af1e28b60e4d5f8454483f9aff3f86a51',1,'qbpp::tsp::TSPQuadModel::TSPQuadModel(std::tuple&lt; qbpp::QuadModel, bool, qbpp::Vector&lt; qbpp::Vector&lt; qbpp::Var &gt;&gt;&gt; tuple)'],['../classqbpp_1_1tsp_1_1TSPQuadModel.html#a0d47d1ade744a192570817a242475c1c',1,'qbpp::tsp::TSPQuadModel::TSPQuadModel(const TSPMap &amp;map, bool fix_first=false)']]],
  ['tspsol_470',['TSPSol',['../classqbpp_1_1tsp_1_1TSPSol.html',1,'qbpp::tsp::TSPSol'],['../classqbpp_1_1tsp_1_1TSPSol.html#a06ad43713331494cb541dde344c04780',1,'qbpp::tsp::TSPSol::TSPSol()']]],
  ['tts_5f_471',['tts_',['../classqbpp_1_1SolHolderTemplate.html#a3f62ec4077b8ad0ae2ae6ac7bdbf9eac',1,'qbpp::SolHolderTemplate']]]
];
