<map id="sample/qbpp_graph_color.hpp" name="sample/qbpp_graph_color.hpp">
<area shape="rect" id="node1" title=" " alt="" coords="134,5,329,32"/>
<area shape="rect" id="node2" href="$graph__color__easy_8cpp.html" title="Solves randomly generated Graph Coloring Problem using QUBO++ Easy Solver." alt="" coords="5,80,140,121"/>
<area shape="rect" id="node3" href="$graph__color__abs2_8cpp.html" title="Solves randomly generated Graph Coloring Problem using ABS2 QUBO Solver." alt="" coords="164,80,299,121"/>
<area shape="rect" id="node4" href="$graph__color__grb_8cpp.html" title="Solves randomly generated Graph Coloring Problem using QUBO++ Gurobi Optimizer." alt="" coords="323,80,457,121"/>
</map>
