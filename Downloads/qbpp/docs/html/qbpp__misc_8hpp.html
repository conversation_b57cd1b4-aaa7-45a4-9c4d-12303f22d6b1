<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.17"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>QUBO++ Library with QUBO Solver APIs: include/qbpp_misc.hpp File Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/x-mathjax-config">
  MathJax.Hub.Config({
    extensions: ["tex2jax.js", "TeX/AMSmath.js", "TeX/AMSsymbols.js"],
    jax: ["input/TeX","output/HTML-CSS"],
});
</script>
<script type="text/javascript" async="async" src="https://cdn.jsdelivr.net/npm/mathjax@2/MathJax.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">QUBO++ Library with QUBO Solver APIs
   </div>
   <div id="projectbrief">Author: Koji Nakano, License: Non-commercial research and evaluation purposes without any guarantees.</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.17 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_d44c64559bbebec7f509842c48db8b23.html">include</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#nested-classes">Classes</a> &#124;
<a href="#namespaces">Namespaces</a>  </div>
  <div class="headertitle">
<div class="title">qbpp_misc.hpp File Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p>A miscellaneous library used for sample programs of the QUBO++ library.  
<a href="#details">More...</a></p>
<div class="textblock"><code>#include &lt;algorithm&gt;</code><br />
<code>#include &lt;atomic&gt;</code><br />
<code>#include &lt;boost/circular_buffer.hpp&gt;</code><br />
<code>#include &lt;boost/random/taus88.hpp&gt;</code><br />
<code>#include &lt;boost/random/uniform_int_distribution.hpp&gt;</code><br />
<code>#include &lt;boost/random/uniform_real_distribution.hpp&gt;</code><br />
<code>#include &lt;cmath&gt;</code><br />
<code>#include &lt;iostream&gt;</code><br />
<code>#include &lt;numeric&gt;</code><br />
<code>#include &lt;random&gt;</code><br />
<code>#include &lt;set&gt;</code><br />
<code>#include &lt;vector&gt;</code><br />
<code>#include &quot;<a class="el" href="qbpp_8hpp_source.html">qbpp.hpp</a>&quot;</code><br />
</div><div class="textblock"><div class="dynheader">
Include dependency graph for qbpp_misc.hpp:</div>
<div class="dyncontent">
<div class="center"><img src="qbpp__misc_8hpp__incl.png" border="0" usemap="#include_2qbpp__misc_8hpp" alt=""/></div>
<map name="include_2qbpp__misc_8hpp" id="include_2qbpp__misc_8hpp">
<area shape="rect" title="A miscellaneous library used for sample programs of the QUBO++ library." alt="" coords="1223,5,1377,32"/>
<area shape="rect" title=" " alt="" coords="5,177,80,203"/>
<area shape="rect" title=" " alt="" coords="1132,87,1193,114"/>
<area shape="rect" title=" " alt="" coords="1218,87,1382,114"/>
<area shape="rect" title=" " alt="" coords="1406,87,1575,114"/>
<area shape="rect" title=" " alt="" coords="1599,80,1747,121"/>
<area shape="rect" title=" " alt="" coords="1771,80,1919,121"/>
<area shape="rect" title=" " alt="" coords="104,177,163,203"/>
<area shape="rect" title=" " alt="" coords="187,177,259,203"/>
<area shape="rect" title=" " alt="" coords="1943,87,2011,114"/>
<area shape="rect" title=" " alt="" coords="2035,87,2101,114"/>
<area shape="rect" title=" " alt="" coords="2125,87,2165,114"/>
<area shape="rect" title=" " alt="" coords="283,177,341,203"/>
<area shape="rect" href="qbpp_8hpp.html" title="QUBO++, a C++ library for generating expressions for binary and spin variables." alt="" coords="1033,87,1108,114"/>
<area shape="rect" title=" " alt="" coords="1563,177,1701,203"/>
<area shape="rect" title=" " alt="" coords="1725,177,1843,203"/>
<area shape="rect" title=" " alt="" coords="1866,177,2009,203"/>
<area shape="rect" title=" " alt="" coords="2033,177,2159,203"/>
<area shape="rect" title=" " alt="" coords="2183,169,2329,211"/>
<area shape="rect" title=" " alt="" coords="2353,169,2500,211"/>
<area shape="rect" title=" " alt="" coords="2525,177,2713,203"/>
<area shape="rect" title=" " alt="" coords="2737,177,2882,203"/>
<area shape="rect" title=" " alt="" coords="2907,169,3045,211"/>
<area shape="rect" title=" " alt="" coords="3069,177,3131,203"/>
<area shape="rect" title=" " alt="" coords="3155,177,3253,203"/>
<area shape="rect" title=" " alt="" coords="3277,177,3344,203"/>
<area shape="rect" title=" " alt="" coords="365,177,419,203"/>
<area shape="rect" title=" " alt="" coords="443,177,482,203"/>
<area shape="rect" title=" " alt="" coords="507,177,576,203"/>
<area shape="rect" title=" " alt="" coords="600,177,659,203"/>
<area shape="rect" title=" " alt="" coords="683,177,749,203"/>
<area shape="rect" title=" " alt="" coords="773,177,843,203"/>
<area shape="rect" title=" " alt="" coords="867,177,922,203"/>
<area shape="rect" title=" " alt="" coords="947,177,1005,203"/>
<area shape="rect" title=" " alt="" coords="1029,177,1112,203"/>
<area shape="rect" title=" " alt="" coords="1137,177,1247,203"/>
<area shape="rect" title=" " alt="" coords="1272,177,1376,203"/>
<area shape="rect" title=" " alt="" coords="1400,177,1453,203"/>
<area shape="rect" title=" " alt="" coords="1477,177,1539,203"/>
</map>
</div>
</div><div class="textblock"><div class="dynheader">
This graph shows which files directly or indirectly include this file:</div>
<div class="dyncontent">
<div class="center"><img src="qbpp__misc_8hpp__dep__incl.png" border="0" usemap="#include_2qbpp__misc_8hppdep" alt=""/></div>
<map name="include_2qbpp__misc_8hppdep" id="include_2qbpp__misc_8hppdep">
<area shape="rect" title="A miscellaneous library used for sample programs of the QUBO++ library." alt="" coords="1227,5,1382,32"/>
<area shape="rect" href="qbpp__easy__solver_8hpp.html" title="Easy QUBO Solver for solving QUBO problems." alt="" coords="1027,80,1223,107"/>
<area shape="rect" href="partition__easy_8cpp.html" title="Solves the Partitioning problem using the QUBO++ Easy solver." alt="" coords="481,162,653,189"/>
<area shape="rect" href="graph__color__easy_8cpp.html" title="Solves randomly generated Graph Coloring Problem using QUBO++ Easy Solver." alt="" coords="323,155,457,196"/>
<area shape="rect" href="tsp__easy_8cpp.html" title="Solves randomly generated Traveling Salesman Problem (TSP) using the QUBO++ Easy solver." alt="" coords="1591,162,1735,189"/>
<area shape="rect" href="factorization_8cpp.html" title="Solves factorization and multiplication problems for two prime numbers using multiple QUBO solvers." alt="" coords="1403,162,1567,189"/>
<area shape="rect" href="partition__exhaustive_8cpp.html" title="Solves the Partitioning problem using the QUBO++ Exhaustive solver." alt="" coords="1863,80,2072,107"/>
<area shape="rect" href="qbpp__graph__color_8hpp.html" title=" " alt="" coords="166,80,361,107"/>
<area shape="rect" href="graph__color__abs2_8cpp.html" title="Solves randomly generated Graph Coloring Problem using ABS2 QUBO Solver." alt="" coords="5,155,140,196"/>
<area shape="rect" href="graph__color__grb_8cpp.html" title="Solves randomly generated Graph Coloring Problem using QUBO++ Gurobi Optimizer." alt="" coords="164,155,299,196"/>
<area shape="rect" href="qbpp__tsp_8hpp.html" title="Generates a QUBO Expression for the Traveling Salesman Problem (TSP) using QUBO++ library." alt="" coords="1590,80,1737,107"/>
<area shape="rect" href="tsp__abs2_8cpp.html" title="Solves randomly generated Traveling Salesman Problem (TSP) using the ABS2 QUBO solver." alt="" coords="1759,162,1906,189"/>
<area shape="rect" href="tsp__grb_8cpp.html" title="Solves randomly generated Traveling Salesman Problem (TSP) using the QUBO++ Easy solver." alt="" coords="1930,162,2066,189"/>
<area shape="rect" href="nqueen__easy_8cpp.html" title="Solves the N&#45;Queens problem using ABS2 QUBO solver from QUBO++ library." alt="" coords="678,162,846,189"/>
<area shape="rect" href="ilp__easy_8cpp.html" title="Solves an Integer Linear Programming (ILP) problem using EasySolver through QUBO++ library." alt="" coords="870,162,1009,189"/>
<area shape="rect" href="simple__factorization__easy_8cpp.html" title="Simple factorization example using EasySolver." alt="" coords="1033,155,1217,196"/>
<area shape="rect" href="bin__packing__easy_8cpp.html" title="This file contains an example of solving the bin packing problem using the EasySolver." alt="" coords="1241,155,1379,196"/>
</map>
</div>
</div>
<p><a href="qbpp__misc_8hpp_source.html">Go to the source code of this file.</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="nested-classes"></a>
Classes</h2></td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">class &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classqbpp_1_1misc_1_1RandomEngine.html">qbpp::misc::RandomEngine</a></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">class &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classqbpp_1_1misc_1_1RandomGenerator.html">qbpp::misc::RandomGenerator</a></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">class &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classqbpp_1_1misc_1_1RandomPermutation.html">qbpp::misc::RandomPermutation</a></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">struct &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structqbpp_1_1misc_1_1PcloseDeleter.html">qbpp::misc::PcloseDeleter</a></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">class &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classqbpp_1_1misc_1_1RandomSet.html">qbpp::misc::RandomSet</a></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">class &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classqbpp_1_1misc_1_1MinSet.html">qbpp::misc::MinSet&lt; T &gt;</a></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">class &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classqbpp_1_1misc_1_1MinHeap.html">qbpp::misc::MinHeap&lt; T &gt;</a></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">class &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classqbpp_1_1misc_1_1RandomMinSet.html">qbpp::misc::RandomMinSet&lt; T &gt;</a></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">class &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classqbpp_1_1misc_1_1Tabu.html">qbpp::misc::Tabu</a></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="namespaces"></a>
Namespaces</h2></td></tr>
<tr class="memitem:namespaceqbpp"><td class="memItemLeft" align="right" valign="top"> &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp.html">qbpp</a></td></tr>
<tr class="memdesc:namespaceqbpp"><td class="mdescLeft">&#160;</td><td class="mdescRight">Generates a QUBO Expression for the Graph Coloring Problem using QUBO++ library. <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:namespaceqbpp_1_1misc"><td class="memItemLeft" align="right" valign="top"> &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp_1_1misc.html">qbpp::misc</a></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p>A miscellaneous library used for sample programs of the QUBO++ library. </p>
<dl class="section author"><dt>Author</dt><dd>Koji Nakano</dd></dl>
<p>This library includes miscellaneous classes and functions used for sample programs of the QUBO++ library. The classes include random number generators and graph drawing functions. </p><dl class="section copyright"><dt>Copyright</dt><dd>2025, Koji Nakano </dd></dl>
<dl class="section version"><dt>Version</dt><dd>2025-04-21 </dd></dl>

<p class="definition">Definition in file <a class="el" href="qbpp__misc_8hpp_source.html">qbpp_misc.hpp</a>.</p>
</div></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.17
</small></address>
</body>
</html>
