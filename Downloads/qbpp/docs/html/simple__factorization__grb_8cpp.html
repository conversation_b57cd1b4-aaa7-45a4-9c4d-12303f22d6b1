<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.17"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>QUBO++ Library with QUBO Solver APIs: sample/simple_factorization_grb.cpp File Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/x-mathjax-config">
  MathJax.Hub.Config({
    extensions: ["tex2jax.js", "TeX/AMSmath.js", "TeX/AMSsymbols.js"],
    jax: ["input/TeX","output/HTML-CSS"],
});
</script>
<script type="text/javascript" async="async" src="https://cdn.jsdelivr.net/npm/mathjax@2/MathJax.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">QUBO++ Library with QUBO Solver APIs
   </div>
   <div id="projectbrief">Author: Koji Nakano, License: Non-commercial research and evaluation purposes without any guarantees.</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.17 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_c29eeb5af533606caea8a09e66794740.html">sample</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#func-members">Functions</a>  </div>
  <div class="headertitle">
<div class="title">simple_factorization_grb.cpp File Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p>Simple factorization example using Gurobi Optimizer.  
<a href="#details">More...</a></p>
<div class="textblock"><code>#include &quot;<a class="el" href="qbpp_8hpp_source.html">qbpp.hpp</a>&quot;</code><br />
<code>#include &quot;<a class="el" href="qbpp__grb_8hpp_source.html">qbpp_grb.hpp</a>&quot;</code><br />
</div><div class="textblock"><div class="dynheader">
Include dependency graph for simple_factorization_grb.cpp:</div>
<div class="dyncontent">
<div class="center"><img src="simple__factorization__grb_8cpp__incl.png" border="0" usemap="#sample_2simple__factorization__grb_8cpp" alt=""/></div>
<map name="sample_2simple__factorization__grb_8cpp" id="sample_2simple__factorization__grb_8cpp">
<area shape="rect" title="Simple factorization example using Gurobi Optimizer." alt="" coords="2005,5,2189,47"/>
<area shape="rect" href="qbpp_8hpp.html" title="QUBO++, a C++ library for generating expressions for binary and spin variables." alt="" coords="2016,169,2091,196"/>
<area shape="rect" href="qbpp__grb_8hpp.html" title="QUBO++ interface to call Gurobi Optimizer." alt="" coords="2091,95,2191,121"/>
<area shape="rect" title=" " alt="" coords="88,251,227,278"/>
<area shape="rect" title=" " alt="" coords="251,251,368,278"/>
<area shape="rect" title=" " alt="" coords="391,251,534,278"/>
<area shape="rect" title=" " alt="" coords="559,251,684,278"/>
<area shape="rect" title=" " alt="" coords="708,251,783,278"/>
<area shape="rect" title=" " alt="" coords="807,244,953,285"/>
<area shape="rect" title=" " alt="" coords="977,244,1124,285"/>
<area shape="rect" title=" " alt="" coords="1149,251,1337,278"/>
<area shape="rect" title=" " alt="" coords="1361,251,1506,278"/>
<area shape="rect" title=" " alt="" coords="1531,244,1669,285"/>
<area shape="rect" title=" " alt="" coords="1693,251,1755,278"/>
<area shape="rect" title=" " alt="" coords="5,251,64,278"/>
<area shape="rect" title=" " alt="" coords="1779,251,1877,278"/>
<area shape="rect" title=" " alt="" coords="1901,251,1968,278"/>
<area shape="rect" title=" " alt="" coords="3099,251,3171,278"/>
<area shape="rect" title=" " alt="" coords="1992,251,2045,278"/>
<area shape="rect" title=" " alt="" coords="2070,251,2109,278"/>
<area shape="rect" title=" " alt="" coords="3195,251,3264,278"/>
<area shape="rect" title=" " alt="" coords="2133,251,2192,278"/>
<area shape="rect" title=" " alt="" coords="2216,251,2283,278"/>
<area shape="rect" title=" " alt="" coords="2307,251,2376,278"/>
<area shape="rect" title=" " alt="" coords="3289,251,3343,278"/>
<area shape="rect" title=" " alt="" coords="2400,251,2459,278"/>
<area shape="rect" title=" " alt="" coords="2483,251,2565,278"/>
<area shape="rect" title=" " alt="" coords="2590,251,2701,278"/>
<area shape="rect" title=" " alt="" coords="2725,251,2829,278"/>
<area shape="rect" title=" " alt="" coords="2853,251,2907,278"/>
<area shape="rect" title=" " alt="" coords="2931,251,2992,278"/>
<area shape="rect" title=" " alt="" coords="3016,251,3075,278"/>
<area shape="rect" href="abs2_8hpp.html" title="API for the ABS2 GPU QUBO Solver." alt="" coords="3097,169,3172,196"/>
<area shape="rect" title=" " alt="" coords="3196,169,3295,196"/>
</map>
</div>
</div>
<p><a href="simple__factorization__grb_8cpp_source.html">Go to the source code of this file.</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:ae66f6b31b5ad750f1fe042a706a4e3d4"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="simple__factorization__grb_8cpp.html#ae66f6b31b5ad750f1fe042a706a4e3d4">main</a> ()</td></tr>
<tr class="separator:ae66f6b31b5ad750f1fe042a706a4e3d4"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p>Simple factorization example using Gurobi Optimizer. </p>
<p>This is a simple example of factorization using Gurobi Optimizer. It solves the equation x * y = 97 * 89. </p><dl class="section author"><dt>Author</dt><dd>Koji Nakano </dd></dl>
<dl class="section version"><dt>Version</dt><dd>2024-10-05 </dd></dl>

<p class="definition">Definition in file <a class="el" href="simple__factorization__grb_8cpp_source.html">simple_factorization_grb.cpp</a>.</p>
</div><h2 class="groupheader">Function Documentation</h2>
<a id="ae66f6b31b5ad750f1fe042a706a4e3d4"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ae66f6b31b5ad750f1fe042a706a4e3d4">&#9670;&nbsp;</a></span>main()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int main </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p class="definition">Definition at line <a class="el" href="simple__factorization__grb_8cpp_source.html#l00011">11</a> of file <a class="el" href="simple__factorization__grb_8cpp_source.html">simple_factorization_grb.cpp</a>.</p>
<div class="dynheader">
Here is the call graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="simple__factorization__grb_8cpp_ae66f6b31b5ad750f1fe042a706a4e3d4_cgraph.png" border="0" usemap="#simple__factorization__grb_8cpp_ae66f6b31b5ad750f1fe042a706a4e3d4_cgraph" alt=""/></div>
<map name="simple__factorization__grb_8cpp_ae66f6b31b5ad750f1fe042a706a4e3d4_cgraph" id="simple__factorization__grb_8cpp_ae66f6b31b5ad750f1fe042a706a4e3d4_cgraph">
<area shape="rect" title=" " alt="" coords="5,335,56,361"/>
<area shape="rect" href="namespaceqbpp.html#ac05f27a511fa1e2870091ea5d696a3c3" title=" " alt="" coords="139,233,236,260"/>
<area shape="rect" href="namespaceqbpp.html#aa3ce65464a38ba36e0b6e99b97234245" title=" " alt="" coords="104,335,271,361"/>
<area shape="rect" href="namespaceqbpp.html#a34b0039c6486ef9f1373775d5a897bae" title=" " alt="" coords="139,385,236,412"/>
<area shape="rect" href="classqbpp_1_1Term.html#a589931e409c1ad96f525510edb6815f9" title=" " alt="" coords="541,309,665,336"/>
<area shape="rect" href="namespaceqbpp.html#add51cb422256f9e89483b8cd0deb4721" title=" " alt="" coords="354,309,437,336"/>
<area shape="rect" href="namespaceqbpp.html#aba363d0725955ba9ec04d0f955a034d8" title=" " alt="" coords="319,132,472,159"/>
<area shape="rect" href="classqbpp_1_1Term.html#a577a458ac7dfd7654e52c46a588cf613" title=" " alt="" coords="527,5,679,32"/>
<area shape="rect" href="namespaceqbpp.html#aebff5c6be40335125221f44731cee406" title=" " alt="" coords="331,208,460,235"/>
<area shape="rect" href="namespaceqbpp.html#a9897ad8c594d3572669f7e64cb73c6d6" title=" " alt="" coords="564,56,643,83"/>
<area shape="rect" href="namespaceqbpp.html#a1cf3f678bcc7eee089c75d4bc5a0b959" title=" " alt="" coords="565,107,641,133"/>
<area shape="rect" href="classqbpp_1_1Term.html#a43a5c7603e1fd4eb8f415c74e4b92654" title=" " alt="" coords="543,157,663,184"/>
<area shape="rect" href="namespaceqbpp.html#a844402a465157e00bca032af58c0a795" title=" " alt="" coords="763,107,839,133"/>
<area shape="rect" href="namespaceqbpp.html#ad67847f7e95244f0da53a9d4eb5de9e9" title=" " alt="" coords="539,208,667,235"/>
<area shape="rect" href="namespaceqbpp.html#a2d100408fb06c3d1530caff5553bb290" title=" " alt="" coords="520,259,687,285"/>
<area shape="rect" href="namespaceqbpp.html#aa7464c250db0ba827d6582d3efa9a272" title=" " alt="" coords="750,387,853,413"/>
<area shape="rect" href="namespaceqbpp.html#a65a9c464505d996431ed4fa7b3556f2b" title=" " alt="" coords="735,298,868,339"/>
<area shape="rect" href="classqbpp_1_1Vector.html#af719287ecdb5f44bbd0aa5d9ba3aaea1" title=" " alt="" coords="917,387,1045,413"/>
<area shape="rect" href="namespaceqbpp.html#a5d56d3d39b9f223f9dd2eb85fe3d2256" title=" " alt="" coords="916,298,1045,339"/>
</map>
</div>

</div>
</div>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.17
</small></address>
</body>
</html>
