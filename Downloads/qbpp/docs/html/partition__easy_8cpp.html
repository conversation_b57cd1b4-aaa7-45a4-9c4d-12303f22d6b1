<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.17"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>QUBO++ Library with QUBO Solver APIs: sample/partition_easy.cpp File Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/x-mathjax-config">
  MathJax.Hub.Config({
    extensions: ["tex2jax.js", "TeX/AMSmath.js", "TeX/AMSsymbols.js"],
    jax: ["input/TeX","output/HTML-CSS"],
});
</script>
<script type="text/javascript" async="async" src="https://cdn.jsdelivr.net/npm/mathjax@2/MathJax.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">QUBO++ Library with QUBO Solver APIs
   </div>
   <div id="projectbrief">Author: Koji Nakano, License: Non-commercial research and evaluation purposes without any guarantees.</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.17 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_c29eeb5af533606caea8a09e66794740.html">sample</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#func-members">Functions</a>  </div>
  <div class="headertitle">
<div class="title">partition_easy.cpp File Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p>Solves the Partitioning problem using the QUBO++ Easy solver.  
<a href="#details">More...</a></p>
<div class="textblock"><code>#include &lt;boost/program_options.hpp&gt;</code><br />
<code>#include &quot;<a class="el" href="qbpp_8hpp_source.html">qbpp.hpp</a>&quot;</code><br />
<code>#include &quot;<a class="el" href="qbpp__easy__solver_8hpp_source.html">qbpp_easy_solver.hpp</a>&quot;</code><br />
<code>#include &quot;<a class="el" href="qbpp__misc_8hpp_source.html">qbpp_misc.hpp</a>&quot;</code><br />
</div><div class="textblock"><div class="dynheader">
Include dependency graph for partition_easy.cpp:</div>
<div class="dyncontent">
<div class="center"><img src="partition__easy_8cpp__incl.png" border="0" usemap="#sample_2partition__easy_8cpp" alt=""/></div>
<map name="sample_2partition__easy_8cpp" id="sample_2partition__easy_8cpp">
<area shape="rect" title="Solves the Partitioning problem using the QUBO++ Easy solver." alt="" coords="1982,5,2154,32"/>
<area shape="rect" title=" " alt="" coords="1676,80,1855,107"/>
<area shape="rect" href="qbpp_8hpp.html" title="QUBO++, a C++ library for generating expressions for binary and spin variables." alt="" coords="2081,237,2156,263"/>
<area shape="rect" href="qbpp__easy__solver_8hpp.html" title="Easy QUBO Solver for solving QUBO problems." alt="" coords="2194,80,2345,107"/>
<area shape="rect" href="qbpp__misc_8hpp.html" title="A miscellaneous library used for sample programs of the QUBO++ library." alt="" coords="2714,155,2825,181"/>
<area shape="rect" title=" " alt="" coords="5,326,144,353"/>
<area shape="rect" title=" " alt="" coords="168,326,285,353"/>
<area shape="rect" title=" " alt="" coords="387,326,530,353"/>
<area shape="rect" title=" " alt="" coords="555,326,680,353"/>
<area shape="rect" title=" " alt="" coords="3009,326,3084,353"/>
<area shape="rect" title=" " alt="" coords="704,319,851,360"/>
<area shape="rect" title=" " alt="" coords="875,319,1021,360"/>
<area shape="rect" title=" " alt="" coords="1046,326,1234,353"/>
<area shape="rect" title=" " alt="" coords="1258,326,1403,353"/>
<area shape="rect" title=" " alt="" coords="1428,319,1567,360"/>
<area shape="rect" title=" " alt="" coords="1591,326,1652,353"/>
<area shape="rect" title=" " alt="" coords="3108,326,3167,353"/>
<area shape="rect" title=" " alt="" coords="1677,326,1774,353"/>
<area shape="rect" title=" " alt="" coords="1799,326,1865,353"/>
<area shape="rect" title=" " alt="" coords="3191,326,3263,353"/>
<area shape="rect" title=" " alt="" coords="309,326,363,353"/>
<area shape="rect" title=" " alt="" coords="1890,326,1929,353"/>
<area shape="rect" title=" " alt="" coords="1953,326,2023,353"/>
<area shape="rect" title=" " alt="" coords="2047,326,2105,353"/>
<area shape="rect" title=" " alt="" coords="2129,326,2196,353"/>
<area shape="rect" title=" " alt="" coords="2220,326,2289,353"/>
<area shape="rect" title=" " alt="" coords="2314,326,2369,353"/>
<area shape="rect" title=" " alt="" coords="2927,326,2985,353"/>
<area shape="rect" title=" " alt="" coords="2393,326,2476,353"/>
<area shape="rect" title=" " alt="" coords="2501,326,2611,353"/>
<area shape="rect" title=" " alt="" coords="2636,326,2740,353"/>
<area shape="rect" title=" " alt="" coords="2764,326,2817,353"/>
<area shape="rect" title=" " alt="" coords="2841,326,2903,353"/>
<area shape="rect" title=" " alt="" coords="3287,326,3345,353"/>
<area shape="rect" title=" " alt="" coords="2286,237,2450,263"/>
<area shape="rect" title=" " alt="" coords="2489,237,2554,263"/>
<area shape="rect" title=" " alt="" coords="2587,237,2627,263"/>
<area shape="rect" title=" " alt="" coords="2433,155,2508,181"/>
<area shape="rect" title=" " alt="" coords="2532,155,2601,181"/>
<area shape="rect" title=" " alt="" coords="2625,155,2689,181"/>
<area shape="rect" title=" " alt="" coords="2857,237,2919,263"/>
<area shape="rect" title=" " alt="" coords="2943,237,3113,263"/>
<area shape="rect" title=" " alt="" coords="3137,229,3285,271"/>
<area shape="rect" title=" " alt="" coords="3309,229,3457,271"/>
<area shape="rect" title=" " alt="" coords="3481,237,3549,263"/>
</map>
</div>
</div>
<p><a href="partition__easy_8cpp_source.html">Go to the source code of this file.</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:a3c04138a5bfe5d72780bb7e82a18e627"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="partition__easy_8cpp.html#a3c04138a5bfe5d72780bb7e82a18e627">main</a> (int argc, char **argv)</td></tr>
<tr class="memdesc:a3c04138a5bfe5d72780bb7e82a18e627"><td class="mdescLeft">&#160;</td><td class="mdescRight">Solves the Partitioning problem using the QUBO++ Easy Solver.  <a href="partition__easy_8cpp.html#a3c04138a5bfe5d72780bb7e82a18e627">More...</a><br /></td></tr>
<tr class="separator:a3c04138a5bfe5d72780bb7e82a18e627"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p>Solves the Partitioning problem using the QUBO++ Easy solver. </p>
<dl class="section author"><dt>Author</dt><dd>Koji Nakano </dd></dl>
<dl class="section version"><dt>Version</dt><dd>2025-01-15 </dd></dl>

<p class="definition">Definition in file <a class="el" href="partition__easy_8cpp_source.html">partition_easy.cpp</a>.</p>
</div><h2 class="groupheader">Function Documentation</h2>
<a id="a3c04138a5bfe5d72780bb7e82a18e627"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a3c04138a5bfe5d72780bb7e82a18e627">&#9670;&nbsp;</a></span>main()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int main </td>
          <td>(</td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>argc</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">char **&#160;</td>
          <td class="paramname"><em>argv</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Solves the Partitioning problem using the QUBO++ Easy Solver. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">argc</td><td>Number of command-line arguments </td></tr>
    <tr><td class="paramname">argv</td><td>Command-line arguments </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>Exit code </dd></dl>
<p>The size of the input set</p>
<p>The maximum value of the input set</p>
<p>The time limit for the solver</p>
<p>A vector to store the input set</p>
<p>Generates the input set</p>
<p>Prints the input set</p>

<p class="definition">Definition at line <a class="el" href="partition__easy_8cpp_source.html#l00018">18</a> of file <a class="el" href="partition__easy_8cpp_source.html">partition_easy.cpp</a>.</p>
<div class="dynheader">
Here is the call graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="partition__easy_8cpp_a3c04138a5bfe5d72780bb7e82a18e627_cgraph.png" border="0" usemap="#partition__easy_8cpp_a3c04138a5bfe5d72780bb7e82a18e627_cgraph" alt=""/></div>
<map name="partition__easy_8cpp_a3c04138a5bfe5d72780bb7e82a18e627_cgraph" id="partition__easy_8cpp_a3c04138a5bfe5d72780bb7e82a18e627_cgraph">
<area shape="rect" title="Solves the Partitioning problem using the QUBO++ Easy Solver." alt="" coords="5,208,56,235"/>
<area shape="rect" href="classqbpp_1_1Vector.html#a05545411bd760deb7206b0079f4f5833" title=" " alt="" coords="125,5,313,32"/>
<area shape="rect" href="namespaceqbpp.html#ab5da6d0960920098998bb7347082ee6b" title=" " alt="" coords="177,56,260,83"/>
<area shape="rect" href="classqbpp_1_1misc_1_1RandomGenerator.html#a95308c2300aeef9a881ba97786367977" title=" " alt="" coords="104,233,333,260"/>
<area shape="rect" href="classqbpp_1_1misc_1_1RandomGenerator.html#a1f4f3d675aee33dce174c8222c3bc22a" title=" " alt="" coords="119,285,319,326"/>
<area shape="rect" href="namespaceqbpp.html#abd03cc17ab61f6ab2be95751c7b92626" title=" " alt="" coords="181,107,257,133"/>
<area shape="rect" href="namespaceqbpp.html#a41f8243940eb837c282aed85287bd3af" title=" " alt="" coords="182,351,255,377"/>
<area shape="rect" href="namespaceqbpp.html#a1cf3f678bcc7eee089c75d4bc5a0b959" title=" " alt="" coords="443,401,519,428"/>
<area shape="rect" href="namespaceqbpp.html#a9041f8ee7d3f4a7c04a0d385babae285" title=" " alt="" coords="177,183,260,209"/>
<area shape="rect" href="namespaceqbpp.html#add51cb422256f9e89483b8cd0deb4721" title=" " alt="" coords="671,19,754,45"/>
<area shape="rect" href="classqbpp_1_1misc_1_1RandomGenerator.html#a6b19f6bde3a3b6ff087a908d11ece3c1" title=" " alt="" coords="381,285,581,326"/>
<area shape="rect" href="namespaceqbpp.html#a3ef6a17c9562b454351f51950aab4392" title=" " alt="" coords="412,81,551,108"/>
<area shape="rect" href="namespaceqbpp.html#a97185e5bf8d17779523cb3f13b1d54d7" title=" " alt="" coords="443,31,519,57"/>
<area shape="rect" href="classqbpp_1_1Vector.html#af719287ecdb5f44bbd0aa5d9ba3aaea1" title=" " alt="" coords="649,95,777,121"/>
<area shape="rect" href="classqbpp_1_1impl_1_1VarSet.html#a9caf4bffd9ec02f1c165dff7ddc66065" title=" " alt="" coords="405,351,557,377"/>
<area shape="rect" href="classqbpp_1_1Var.html#ac7b09bf9026a6082d8d6d63201610c24" title=" " alt="" coords="655,296,771,323"/>
<area shape="rect" href="classqbpp_1_1impl_1_1VarSet.html#a7be1fbd47c0dd05c35131c17a78a7a33" title=" " alt="" coords="1021,390,1151,431"/>
<area shape="rect" href="classqbpp_1_1impl_1_1VarSet.html#ac34a1b486a4509a9435314c7ff52339a" title=" " alt="" coords="635,397,790,424"/>
<area shape="rect" href="classqbpp_1_1impl_1_1VarSet.html#a38d1746ee4a96d7dda2ce4220b6853ec" title=" " alt="" coords="844,449,973,490"/>
<area shape="rect" href="namespaceqbpp.html#a844402a465157e00bca032af58c0a795" title=" " alt="" coords="675,448,751,475"/>
<area shape="rect" href="classqbpp_1_1Terms.html#a8c00d28cedf65dadeff5b4b5bf00492b" title=" " alt="" coords="413,183,550,209"/>
<area shape="rect" href="namespaceqbpp.html#ae06a111bb3040263deff02c501635c10" title=" " alt="" coords="433,233,529,260"/>
<area shape="rect" href="classqbpp_1_1Vector.html#a23f6c18870445548d8db321606a0a9fb" title=" " alt="" coords="629,187,796,213"/>
<area shape="rect" href="classqbpp_1_1Vector.html#a5e8bb55fb9236e098401e23a758b5927" title=" " alt="" coords="639,237,786,264"/>
</map>
</div>

</div>
</div>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.17
</small></address>
</body>
</html>
