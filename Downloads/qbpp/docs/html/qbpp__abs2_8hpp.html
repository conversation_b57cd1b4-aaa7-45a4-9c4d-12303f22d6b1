<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.17"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>QUBO++ Library with QUBO Solver APIs: include/qbpp_abs2.hpp File Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/x-mathjax-config">
  MathJax.Hub.Config({
    extensions: ["tex2jax.js", "TeX/AMSmath.js", "TeX/AMSsymbols.js"],
    jax: ["input/TeX","output/HTML-CSS"],
});
</script>
<script type="text/javascript" async="async" src="https://cdn.jsdelivr.net/npm/mathjax@2/MathJax.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">QUBO++ Library with QUBO Solver APIs
   </div>
   <div id="projectbrief">Author: Koji Nakano, License: Non-commercial research and evaluation purposes without any guarantees.</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.17 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_d44c64559bbebec7f509842c48db8b23.html">include</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#nested-classes">Classes</a> &#124;
<a href="#namespaces">Namespaces</a>  </div>
  <div class="headertitle">
<div class="title">qbpp_abs2.hpp File Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p>QUBO++ interface to call ABS2 GPU QUBO solver.  
<a href="#details">More...</a></p>
<div class="textblock"><code>#include &lt;algorithm&gt;</code><br />
<code>#include &quot;<a class="el" href="abs2_8hpp_source.html">abs2.hpp</a>&quot;</code><br />
<code>#include &quot;<a class="el" href="qbpp_8hpp_source.html">qbpp.hpp</a>&quot;</code><br />
</div><div class="textblock"><div class="dynheader">
Include dependency graph for qbpp_abs2.hpp:</div>
<div class="dyncontent">
<div class="center"><img src="qbpp__abs2_8hpp__incl.png" border="0" usemap="#include_2qbpp__abs2_8hpp" alt=""/></div>
<map name="include_2qbpp__abs2_8hpp" id="include_2qbpp__abs2_8hpp">
<area shape="rect" title="QUBO++ interface to call ABS2 GPU QUBO solver." alt="" coords="157,5,312,32"/>
<area shape="rect" title=" " alt="" coords="5,162,80,189"/>
<area shape="rect" href="abs2_8hpp.html" title="API for the ABS2 GPU QUBO Solver." alt="" coords="197,80,272,107"/>
<area shape="rect" href="qbpp_8hpp.html" title="QUBO++, a C++ library for generating expressions for binary and spin variables." alt="" coords="1263,80,1337,107"/>
<area shape="rect" title=" " alt="" coords="104,162,176,189"/>
<area shape="rect" title=" " alt="" coords="200,162,269,189"/>
<area shape="rect" title=" " alt="" coords="294,162,349,189"/>
<area shape="rect" title=" " alt="" coords="1571,162,1709,189"/>
<area shape="rect" title=" " alt="" coords="1733,162,1851,189"/>
<area shape="rect" title=" " alt="" coords="1874,162,2017,189"/>
<area shape="rect" title=" " alt="" coords="2041,162,2167,189"/>
<area shape="rect" title=" " alt="" coords="2191,155,2337,196"/>
<area shape="rect" title=" " alt="" coords="2361,155,2508,196"/>
<area shape="rect" title=" " alt="" coords="2533,162,2721,189"/>
<area shape="rect" title=" " alt="" coords="2745,162,2890,189"/>
<area shape="rect" title=" " alt="" coords="2915,155,3053,196"/>
<area shape="rect" title=" " alt="" coords="3077,162,3139,189"/>
<area shape="rect" title=" " alt="" coords="3163,162,3221,189"/>
<area shape="rect" title=" " alt="" coords="3246,162,3343,189"/>
<area shape="rect" title=" " alt="" coords="373,162,440,189"/>
<area shape="rect" title=" " alt="" coords="464,162,517,189"/>
<area shape="rect" title=" " alt="" coords="542,162,581,189"/>
<area shape="rect" title=" " alt="" coords="605,162,664,189"/>
<area shape="rect" title=" " alt="" coords="688,162,755,189"/>
<area shape="rect" title=" " alt="" coords="779,162,848,189"/>
<area shape="rect" title=" " alt="" coords="872,162,931,189"/>
<area shape="rect" title=" " alt="" coords="955,162,1037,189"/>
<area shape="rect" title=" " alt="" coords="1062,162,1173,189"/>
<area shape="rect" title=" " alt="" coords="1197,162,1301,189"/>
<area shape="rect" title=" " alt="" coords="1325,162,1379,189"/>
<area shape="rect" title=" " alt="" coords="1403,162,1464,189"/>
<area shape="rect" title=" " alt="" coords="1488,162,1547,189"/>
</map>
</div>
</div><div class="textblock"><div class="dynheader">
This graph shows which files directly or indirectly include this file:</div>
<div class="dyncontent">
<div class="center"><img src="qbpp__abs2_8hpp__dep__incl.png" border="0" usemap="#include_2qbpp__abs2_8hppdep" alt=""/></div>
<map name="include_2qbpp__abs2_8hppdep" id="include_2qbpp__abs2_8hppdep">
<area shape="rect" title="QUBO++ interface to call ABS2 GPU QUBO solver." alt="" coords="326,5,481,32"/>
<area shape="rect" href="graph__color__abs2_8cpp.html" title="Solves randomly generated Graph Coloring Problem using ABS2 QUBO Solver." alt="" coords="5,80,140,121"/>
<area shape="rect" href="ilp__abs2_8cpp.html" title="Solves an Integer Linear Programming (ILP) problem using ABS2 solver through QUBO++ library." alt="" coords="165,87,306,114"/>
<area shape="rect" href="tsp__abs2_8cpp.html" title="Solves randomly generated Traveling Salesman Problem (TSP) using the ABS2 QUBO solver." alt="" coords="330,87,477,114"/>
<area shape="rect" href="simple__factorization__abs2_8cpp.html" title="Simple factorization example using ABS2 QUBO Solver." alt="" coords="501,80,685,121"/>
<area shape="rect" href="factorization_8cpp.html" title="Solves factorization and multiplication problems for two prime numbers using multiple QUBO solvers." alt="" coords="709,87,873,114"/>
</map>
</div>
</div>
<p><a href="qbpp__abs2_8hpp_source.html">Go to the source code of this file.</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="nested-classes"></a>
Classes</h2></td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">class &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classqbpp__abs2_1_1Solver.html">qbpp_abs2::Solver</a></td></tr>
<tr class="memdesc:"><td class="mdescLeft">&#160;</td><td class="mdescRight">A class for calling the ABS2 QUBO solver.  <a href="classqbpp__abs2_1_1Solver.html#details">More...</a><br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">class &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classqbpp__abs2_1_1QuadModel.html">qbpp_abs2::QuadModel</a></td></tr>
<tr class="memdesc:"><td class="mdescLeft">&#160;</td><td class="mdescRight">A class for storing both the <a class="el" href="classqbpp_1_1QuadModel.html">qbpp::QuadModel</a> and <a class="el" href="classabs2_1_1Model.html" title="Class to store and manipulate a QUBO model.">abs2::Model</a>.  <a href="classqbpp__abs2_1_1QuadModel.html#details">More...</a><br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">class &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classqbpp__abs2_1_1Param.html">qbpp_abs2::Param</a></td></tr>
<tr class="memdesc:"><td class="mdescLeft">&#160;</td><td class="mdescRight">A class for setting parameters for the ABS2 QUBO solver.  <a href="classqbpp__abs2_1_1Param.html#details">More...</a><br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">class &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classqbpp__abs2_1_1Sol.html">qbpp_abs2::Sol</a></td></tr>
<tr class="memdesc:"><td class="mdescLeft">&#160;</td><td class="mdescRight">A class for storing the ABS2 QUBO solution.  <a href="classqbpp__abs2_1_1Sol.html#details">More...</a><br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">class &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classqbpp__abs2_1_1Callback.html">qbpp_abs2::Callback</a></td></tr>
<tr class="memdesc:"><td class="mdescLeft">&#160;</td><td class="mdescRight">A class for defining the ABS2 callback function.  <a href="classqbpp__abs2_1_1Callback.html#details">More...</a><br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="namespaces"></a>
Namespaces</h2></td></tr>
<tr class="memitem:namespaceqbpp__abs2"><td class="memItemLeft" align="right" valign="top"> &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceqbpp__abs2.html">qbpp_abs2</a></td></tr>
<tr class="memdesc:namespaceqbpp__abs2"><td class="mdescLeft">&#160;</td><td class="mdescRight">Namespace to use ABS2 QUBO solver from QUBO++ library. <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p>QUBO++ interface to call ABS2 GPU QUBO solver. </p>
<p>This file provides interfaces to call ABS2 QUBO solver from QUBO++ </p><dl class="section author"><dt>Author</dt><dd>Koji Nakano </dd></dl>
<dl class="section version"><dt>Version</dt><dd>2025-04-21 </dd></dl>

<p class="definition">Definition in file <a class="el" href="qbpp__abs2_8hpp_source.html">qbpp_abs2.hpp</a>.</p>
</div></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.17
</small></address>
</body>
</html>
